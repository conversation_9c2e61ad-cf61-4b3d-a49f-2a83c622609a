import { create } from 'zustand';

interface VFOState {
  frequency: number | null;
  demodulation: string | null;
  bandwidth: number | null;
  audioRange: number[] | null;
  waterfallRange: number[] | null;
  zoom: number | null;
}

interface AppState {
  // Main frequency and mode state
  frequency: string;
  demodulation: string;
  bandwidth: string;
  link: string;
  
  // Dialog states
  showBookmarkDialog: boolean;
  showBandsMenu: boolean;
  audioGateVisible: boolean;
  showTutorial: boolean;
  
  // VFO state
  currentVFO: 'A' | 'B';
  vfoA: VFOState;
  vfoB: VFOState;
  vfoSwitchNotification: string;
  
  // Audio panel states
  NREnabled: boolean;
  NBEnabled: boolean;
  ANEnabled: boolean;
  CTCSSSupressEnabled: boolean;
  mute: boolean;
  
  // Update functions
  setFrequency: (frequency: string) => void;
  setDemodulation: (demodulation: string) => void;
  setBandwidth: (bandwidth: string) => void;
  setLink: (link: string) => void;
  setShowBookmarkDialog: (show: boolean) => void;
  setShowBandsMenu: (show: boolean) => void;
  setAudioGateVisible: (visible: boolean) => void;
  setShowTutorial: (show: boolean) => void;
  setCurrentVFO: (vfo: 'A' | 'B') => void;
  setVFOSwitchNotification: (notification: string) => void;
  updateVFO: (vfo: 'A' | 'B', state: Partial<VFOState>) => void;
  setAudioPanelState: (state: {
    NREnabled?: boolean;
    NBEnabled?: boolean;
    ANEnabled?: boolean;
    CTCSSSupressEnabled?: boolean;
    mute?: boolean;
  }) => void;
}

const defaultVFOState: VFOState = {
  frequency: null,
  demodulation: null,
  bandwidth: null,
  audioRange: null,
  waterfallRange: null,
  zoom: null,
};

export const useAppStore = create<AppState>((set) => ({
  // Initial state
  frequency: "14074.00",
  demodulation: "USB",
  bandwidth: "2.8",
  link: "",
  showBookmarkDialog: false,
  showBandsMenu: false,
  audioGateVisible: true,
  showTutorial: false,
  currentVFO: 'A',
  vfoA: { ...defaultVFOState },
  vfoB: { ...defaultVFOState },
  vfoSwitchNotification: "",
  NREnabled: false,
  NBEnabled: false,
  ANEnabled: false,
  CTCSSSupressEnabled: false,
  mute: false,

  // Update functions
  setFrequency: (frequency) => set({ frequency }),
  setDemodulation: (demodulation) => set({ demodulation }),
  setBandwidth: (bandwidth) => set({ bandwidth }),
  setLink: (link) => set({ link }),
  setShowBookmarkDialog: (showBookmarkDialog) => set({ showBookmarkDialog }),
  setShowBandsMenu: (showBandsMenu) => set({ showBandsMenu }),
  setAudioGateVisible: (audioGateVisible) => set({ audioGateVisible }),
  setShowTutorial: (showTutorial) => set({ showTutorial }),
  setCurrentVFO: (currentVFO) => set({ currentVFO }),
  setVFOSwitchNotification: (vfoSwitchNotification) => set({ vfoSwitchNotification }),
  
  updateVFO: (vfo, state) => set((current) => ({
    [vfo === 'A' ? 'vfoA' : 'vfoB']: {
      ...current[vfo === 'A' ? 'vfoA' : 'vfoB'],
      ...state
    }
  })),
  
  setAudioPanelState: (state) => set((current) => ({
    ...current,
    ...state
  })),
}));
