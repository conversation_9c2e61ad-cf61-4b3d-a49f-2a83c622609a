import { AutomationEventList } from './classes/automation-event-list';
import { createCancelAndHoldAutomationEvent } from './functions/create-cancel-and-hold-automation-event';
import { createCancelScheduledValuesAutomationEvent } from './functions/create-cancel-scheduled-values-automation-event';
import { createExponentialRampToValueAutomationEvent } from './functions/create-exponential-ramp-to-value-automation-event';
import { createLinearRampToValueAutomationEvent } from './functions/create-linear-ramp-to-value-automation-event';
import { createSetTargetAutomationEvent } from './functions/create-set-target-automation-event';
import { createSetValueAutomationEvent } from './functions/create-set-value-automation-event';
import { createSetValueCurveAutomationEvent } from './functions/create-set-value-curve-automation-event';
export * from './interfaces/index';
export * from './types/index';
export { AutomationEventList };
export { createCancelAndHoldAutomationEvent };
export { createCancelScheduledValuesAutomationEvent };
export { createExponentialRampToValueAutomationEvent };
export { createLinearRampToValueAutomationEvent };
export { createSetTargetAutomationEvent };
export { createSetValueAutomationEvent };
export { createSetValueCurveAutomationEvent };
//# sourceMappingURL=module.d.ts.map