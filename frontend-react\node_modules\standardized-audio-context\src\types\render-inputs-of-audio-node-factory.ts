import { TGetAudioNodeConnectionsFunction } from './get-audio-node-connections-function';
import { TGetAudioNodeRendererFunction } from './get-audio-node-renderer-function';
import { TIsPartOfACycleFunction } from './is-part-of-a-cycle-function';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';

export type TRenderInputsOfAudioNodeFactory = (
    getAudioNodeConnections: TGetAudioNodeConnectionsFunction,
    getAudioNodeRenderer: TGetAudioNodeRendererFunction,
    isPartOfACycle: TIsPartOfACycleFunction
) => TRenderInputsOfAudioNodeFunction;
