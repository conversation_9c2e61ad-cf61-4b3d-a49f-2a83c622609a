import { TCycleCounters } from './cycle-counters';
import { TDisconnectNativeAudioNodeFromNativeAudioNodeFunction } from './disconnect-native-audio-node-from-native-audio-node-function';
import { TGetAudioNodeConnectionsFunction } from './get-audio-node-connections-function';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TGetNativeAudioParamFunction } from './get-native-audio-param-function';
import { TIncrementCycleCounterFactory } from './increment-cycle-counter-factory';
import { TIsActiveAudioNodeFunction } from './is-active-audio-node-function';

export type TIncrementCycleCounterFactoryFactory = (
    cycleCounters: TCycleCounters,
    disconnectNativeAudioNodeFromNativeAudioNode: TDisconnectNativeAudioNodeFromNativeAudioNodeFunction,
    getAudioNodeConnections: TGetAudioNodeConnectionsFunction,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    getNativeAudioParam: TGetNativeAudioParamFunction,
    isActiveAudioNode: TIsActiveAudioNodeFunction
) => TIncrementCycleCounterFactory;
