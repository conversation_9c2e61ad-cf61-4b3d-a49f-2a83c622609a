import { TAudioNodeConstructor } from './audio-node-constructor';
import { TGetNativeContextFunction } from './get-native-context-function';
import { TMediaStreamTrackAudioSourceNodeConstructor } from './media-stream-track-audio-source-node-constructor';
import { TNativeMediaStreamTrackAudioSourceNodeFactory } from './native-media-stream-track-audio-source-node-factory';

export type TMediaStreamTrackAudioSourceNodeConstructorFactory = (
    audioNodeConstructor: TAudioNodeConstructor,
    createNativeMediaStreamTrackAudioSourceNode: TNativeMediaStreamTrackAudioSourceNodeFactory,
    getNativeContext: TGetNativeContextFunction
) => TMediaStreamTrackAudioSourceNodeConstructor;
