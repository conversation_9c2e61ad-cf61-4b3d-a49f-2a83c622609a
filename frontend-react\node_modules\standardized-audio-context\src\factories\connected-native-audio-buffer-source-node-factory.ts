import { TConnectedNativeAudioBufferSourceNodeFactoryFactory } from '../types';

export const createConnectedNativeAudioBufferSourceNodeFactory: TConnectedNativeAudioBufferSourceNodeFactoryFactory = (
    createNativeAudioBufferSourceNode
) => {
    return (nativeContext, nativeAudioNode) => {
        const nativeAudioBufferSourceNode = createNativeAudioBufferSourceNode(nativeContext, {
            buffer: null,
            channelCount: 2,
            channelCountMode: 'max',
            channelInterpretation: 'speakers',
            loop: false,
            loopEnd: 0,
            loopStart: 0,
            playbackRate: 1
        });
        const nativeAudioBuffer = nativeContext.createBuffer(1, 2, 44100);

        nativeAudioBufferSourceNode.buffer = nativeAudioBuffer;
        nativeAudioBufferSourceNode.loop = true;

        nativeAudioBufferSourceNode.connect(nativeAudioNode);
        nativeAudioBufferSourceNode.start();

        return () => {
            nativeAudioBufferSourceNode.stop();
            nativeAudioBufferSourceNode.disconnect(nativeAudioNode);
        };
    };
};
