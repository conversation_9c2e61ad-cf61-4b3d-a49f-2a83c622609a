import { TConnectNativeAudioNodeToNativeAudioNodeFunction } from './connect-native-audio-node-to-native-audio-node-function';
import { TDisconnectNativeAudioNodeFromNativeAudioNodeFunction } from './disconnect-native-audio-node-from-native-audio-node-function';
import { TGetFirstSampleFunction } from './get-first-sample-function';
import { TInvalidStateErrorFactory } from './invalid-state-error-factory';
import { TMonitorConnectionsFunction } from './monitor-connections-function';
import { TNativeChannelMergerNodeFactory } from './native-channel-merger-node-factory';
import { TNativeGainNodeFactory } from './native-gain-node-factory';
import { TNativePannerNodeFakerFactory } from './native-panner-node-faker-factory';
import { TNativeScriptProcessorNodeFactory } from './native-script-processor-node-factory';
import { TNativeWaveShaperNodeFactory } from './native-wave-shaper-node-factory';
import { TNotSupportedErrorFactory } from './not-supported-error-factory';

export type TNativePannerNodeFakerFactoryFactory = (
    connectNativeAudioNodeToNativeAudioNode: TConnectNativeAudioNodeToNativeAudioNodeFunction,
    createInvalidStateError: TInvalidStateErrorFactory,
    createNativeChannelMergerNode: TNativeChannelMergerNodeFactory,
    createNativeGainNode: TNativeGainNodeFactory,
    createNativeScriptProcessorNode: TNativeScriptProcessorNodeFactory,
    createNativeWaveShaperNode: TNativeWaveShaperNodeFactory,
    createNotSupportedError: TNotSupportedErrorFactory,
    disconnectNativeAudioNodeToNativeAudioNode: TDisconnectNativeAudioNodeFromNativeAudioNodeFunction,
    getFirstSample: TGetFirstSampleFunction,
    monitorConnections: TMonitorConnectionsFunction
) => TNativePannerNodeFakerFactory;
