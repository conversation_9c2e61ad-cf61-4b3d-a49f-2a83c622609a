!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,_="function"==typeof Symbol?Symbol:{},a=_.iterator||"@@iterator",o=_.toStringTag||"@@toStringTag";function s(e,_,a,o){var s=_&&_.prototype instanceof c?_:c,d=Object.create(s.prototype);return t(d,"_invoke",function(e,t,_){var a,o,s,c=0,d=_||[],u=!1,f={p:0,n:0,v:n,a:b,f:b.bind(n,4),d:function(e,t){return a=e,o=0,s=n,f.n=t,i}};function b(e,t){for(o=e,s=t,r=0;!u&&c&&!_&&r<d.length;r++){var _,a=d[r],b=f.p,m=a[2];e>3?(_=m===t)&&(s=a[(o=a[4])?5:(o=3,3)],a[4]=a[5]=n):a[0]<=b&&((_=e<2&&b<a[1])?(o=0,f.v=t,f.n=a[1]):b<m&&(_=e<3||a[0]>t||t>m)&&(a[4]=e,a[5]=t,f.n=m,o=0))}if(_||e>1)return i;throw u=!0,t}return function(_,d,m){if(c>1)throw TypeError("Generator is already running");for(u&&1===d&&b(d,m),o=d,s=m;(r=o<2?n:s)||!u;){a||(o?o<3?(o>1&&(f.n=-1),b(o,s)):f.n=s:f.v=s);try{if(c=2,a){if(o||(_="next"),r=a[_]){if(!(r=r.call(a,s)))throw TypeError("iterator result is not an object");if(!r.done)return r;s=r.value,o<2&&(o=0)}else 1===o&&(r=a.return)&&r.call(a),o<2&&(s=TypeError("The iterator does not provide a '"+_+"' method"),o=1);a=n}else if((r=(u=f.n<0)?s:e.call(t,f))!==i)break}catch(r){a=n,o=1,s=r}finally{c=1}}return{value:r,done:u}}}(e,a,o),!0),d}var i={};function c(){}function d(){}function u(){}r=Object.getPrototypeOf;var f=[][a]?r(r([][a]())):(t(r={},a,function(){return this}),r),b=u.prototype=c.prototype=Object.create(f);function m(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,t(e,o,"GeneratorFunction")),e.prototype=Object.create(b),e}return d.prototype=u,t(b,"constructor",u),t(u,"constructor",d),d.displayName="GeneratorFunction",t(u,o,"GeneratorFunction"),t(b),t(b,o,"Generator"),t(b,a,function(){return this}),t(b,"toString",function(){return"[object Generator]"}),(e=function(){return{w:s,m:m}})()}function t(e,n,r,_){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,r,_){function o(n,r){t(e,n,function(e){return this._invoke(n,r,e)})}n?a?a(e,n,{value:r,enumerable:!_,configurable:!_,writable:!_}):e[n]=r:(o("next",0),o("throw",1),o("return",2))},t(e,n,r,_)}function n(e,t,n,r,_,a,o){try{var s=e[a](o),i=s.value}catch(e){return void n(e)}s.done?t(i):Promise.resolve(i).then(r,_)}function r(e){return function(){var t=this,r=arguments;return new Promise(function(_,a){var o=e.apply(t,r);function s(e){n(o,_,a,s,i,"next",e)}function i(e){n(o,_,a,s,i,"throw",e)}s(void 0)})}}System.register(["./index-legacy-lBrMbuR-.js"],function(t,n){"use strict";var _,a,o,s,i,c,d,u,f,b,m,w,l,g,h,p;return{setters:[function(e){_=e._,a=e.b,o=e.c,s=e.d,i=e.e,c=e.f,d=e.g,u=e.h,f=e.i,b=e.k,m=e.l,w=e.m,l=e.n,g=e.o,h=e.p}],execute:(p=r(e().m(function n(){var p,y;return e().w(function(n){for(;;)switch(n.n){case 0:return p=function(){var t=r(e().m(function t(){var n,r,_,a,o,s,i,c,d,u,f=arguments;return e().w(function(e){for(;;)switch(e.n){case 0:if(n=f.length>0&&void 0!==f[0]?f[0]:{},!(r=f.length>1?f[1]:void 0).startsWith("data:")){e.n=5;break}if(a=r.replace(/^data:.*?base64,/,""),"function"!=typeof Buffer||"function"!=typeof Buffer.from){e.n=1;break}o=Buffer.from(a,"base64"),e.n=3;break;case 1:if("function"!=typeof atob){e.n=2;break}for(s=atob(a),o=new Uint8Array(s.length),i=0;i<s.length;i++)o[i]=s.charCodeAt(i);e.n=3;break;case 2:throw new Error("Cannot decode base64-encoded data URL");case 3:return e.n=4,WebAssembly.instantiate(o,n);case 4:_=e.v,e.n=11;break;case 5:return e.n=6,fetch(r);case 6:if(c=e.v,d=c.headers.get("Content-Type")||"",!("instantiateStreaming"in WebAssembly)||!d.startsWith("application/wasm")){e.n=8;break}return e.n=7,WebAssembly.instantiateStreaming(c,n);case 7:_=e.v,e.n=11;break;case 8:return e.n=9,c.arrayBuffer();case 9:return u=e.v,e.n=10,WebAssembly.instantiate(u,n);case 10:_=e.v;case 11:return e.a(2,_.instance.exports)}},t)}));return function(){return t.apply(this,arguments)}}(),URL=globalThis.URL,n.n=1,p({"./phantomsdrdsp_bg.js":{__wbindgen_object_drop_ref:h,__wbindgen_object_clone_ref:g,__wbg_alert_379870a8a3d45260:l,__wbg_new_abda76e883ba8a5f:w,__wbg_stack_658279fe44541cf6:m,__wbg_error_f851667af71bcfc6:b,__wbg_call_b3ca7c6051f9bec1:f,__wbg_buffer_12d079cc21e14bdb:u,__wbg_newwithbyteoffsetandlength_aa4a17c33a06e5cb:d,__wbg_new_63b92bc8671ed464:c,__wbg_newwithbyteoffsetandlength_4a659d079a1650e0:i,__wbg_new_9efabd6b6d2ce46d:s,__wbg_newwithlength_1e8b839a06de01c5:o,__wbindgen_throw:a,__wbindgen_memory:_}},"/assets/phantomsdrdsp_bg-Bh04HLJe.wasm");case 1:y=n.v,t("memory",y.memory),t("__wbg_foxenflacdecoder_free",y.__wbg_foxenflacdecoder_free),t("foxenflacdecoder_new",y.foxenflacdecoder_new),t("__wbg_audio_free",y.__wbg_audio_free),t("audio_new",y.audio_new),t("audio_decode",y.audio_decode),t("audio_set_nr",y.audio_set_nr),t("audio_set_nb",y.audio_set_nb),t("audio_set_an",y.audio_set_an),t("audio_set_decoded_callback",y.audio_set_decoded_callback),t("__wbg_zstdstreamdecoder_free",y.__wbg_zstdstreamdecoder_free),t("zstdstreamdecoder_new",y.zstdstreamdecoder_new),t("zstdstreamdecoder_clear",y.zstdstreamdecoder_clear),t("zstdstreamdecoder_decode",y.zstdstreamdecoder_decode),t("greet",y.greet),t("main",y.main),t("firdes_kaiser_lowpass",y.firdes_kaiser_lowpass),t("rust_zstd_wasm_shim_free",y.rust_zstd_wasm_shim_free),t("rust_zstd_wasm_shim_malloc",y.rust_zstd_wasm_shim_malloc),t("rust_zstd_wasm_shim_qsort",y.rust_zstd_wasm_shim_qsort),t("rust_zstd_wasm_shim_memcmp",y.rust_zstd_wasm_shim_memcmp),t("rust_zstd_wasm_shim_calloc",y.rust_zstd_wasm_shim_calloc),t("rust_zstd_wasm_shim_memcpy",y.rust_zstd_wasm_shim_memcpy),t("rust_zstd_wasm_shim_memmove",y.rust_zstd_wasm_shim_memmove),t("rust_zstd_wasm_shim_memset",y.rust_zstd_wasm_shim_memset),t("__wbindgen_malloc",y.__wbindgen_malloc),t("__wbindgen_add_to_stack_pointer",y.__wbindgen_add_to_stack_pointer),t("__wbindgen_free",y.__wbindgen_free),t("__wbindgen_realloc",y.__wbindgen_realloc),t("__wbindgen_exn_store",y.__wbindgen_exn_store),t("__wbindgen_start",y.__wbindgen_start);case 2:return n.a(2)}},n)})),function(){return p.apply(this,arguments)})}})}();
