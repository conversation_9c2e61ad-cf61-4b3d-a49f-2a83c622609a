import { TConnectAudioParamFunction } from './connect-audio-param-function';
import { TDynamicsCompressorNodeRendererFactory } from './dynamics-compressor-node-renderer-factory';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeDynamicsCompressorNodeFactory } from './native-dynamics-compressor-node-factory';
import { TRenderAutomationFunction } from './render-automation-function';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';

export type TDynamicsCompressorNodeRendererFactoryFactory = (
    connectAudioParam: TConnectAudioParamFunction,
    createNativeDynamicsCompressorNode: TNativeDynamicsCompressorNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    renderAutomation: TRenderAutomationFunction,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction
) => TDynamicsCompressorNodeRendererFactory;
