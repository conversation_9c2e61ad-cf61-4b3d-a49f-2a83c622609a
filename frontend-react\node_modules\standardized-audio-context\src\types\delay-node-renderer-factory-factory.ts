import { TConnectAudioParamFunction } from './connect-audio-param-function';
import { TDelayNodeRendererFactory } from './delay-node-renderer-factory';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeDelayNodeFactory } from './native-delay-node-factory';
import { TRenderAutomationFunction } from './render-automation-function';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';

export type TDelayNodeRendererFactoryFactory = (
    connectAudioParam: TConnectAudioParamFunction,
    createNativeDelayNode: TNativeDelayNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    renderAutomation: TRenderAutomationFunction,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction
) => TDelayNodeRendererFactory;
