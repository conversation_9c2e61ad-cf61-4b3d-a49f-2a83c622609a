import { TIsNativeAudioContextFunction } from './is-native-audio-context-function';
import { TIsNativeContextFunction } from './is-native-context-function';
import { TIsNativeOfflineAudioContextFunction } from './is-native-offline-audio-context-function';

export type TIsNativeContextFactory = (
    isNativeAudioContext: TIsNativeAudioContextFunction,
    isNativeOfflineAudioContext: TIsNativeOfflineAudioContextFunction
) => TIsNativeContextFunction;
