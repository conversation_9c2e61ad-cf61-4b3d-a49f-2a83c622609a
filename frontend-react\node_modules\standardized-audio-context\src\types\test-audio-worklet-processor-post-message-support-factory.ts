import { TNativeAudioWorkletNodeConstructor } from './native-audio-worklet-node-constructor';
import { TNativeOfflineAudioContextConstructor } from './native-offline-audio-context-constructor';

export type TTestAudioWorkletProcessorPostMessageSupportFactory = (
    nativeAudioWorkletNodeConstructor: null | TNativeAudioWorkletNodeConstructor,
    nativeOfflineAudioContextConstructor: null | TNativeOfflineAudioContextConstructor
) => () => Promise<boolean>;
