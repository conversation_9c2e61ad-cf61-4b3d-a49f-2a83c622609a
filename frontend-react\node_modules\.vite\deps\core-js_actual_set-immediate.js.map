{"version": 3, "sources": ["../../core-js/stable/set-immediate.js", "../../core-js/actual/set-immediate.js"], "sourcesContent": ["'use strict';\nrequire('../modules/web.immediate');\nvar path = require('../internals/path');\n\nmodule.exports = path.setImmediate;\n", "'use strict';\nvar parent = require('../stable/set-immediate');\n\nmodule.exports = parent;\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA,QAAI,OAAO;AAEX,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACJtB,IAAAA,yBAAA;AAAA;AACA,QAAI,SAAS;AAEb,WAAO,UAAU;AAAA;AAAA;", "names": ["require_set_immediate"]}