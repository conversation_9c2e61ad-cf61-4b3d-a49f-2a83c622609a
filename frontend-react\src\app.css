@tailwind base;
@tailwind components;
@tailwind utilities;

.thick-line-through {
    text-decoration-thickness: 2px;
}

.basic-button {
    @apply text-blue-500 border border-blue-500 font-bold uppercase transition-all duration-100 text-center text-xs px-2 py-1
          peer-checked:bg-blue-600 peer-checked:text-white;
}
.basic-button:hover {
    @apply border-blue-400 text-white;
}

.click-button {
    @apply text-blue-500 border border-blue-500 font-bold uppercase transition-all duration-100 text-center text-xs px-2 py-1;
}
.click-button:active {
    @apply bg-blue-600 text-white;
}

.glass-username {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Main layout */
.main-container {
    @apply w-full h-screen bg-gray-900 text-white;
}

.app-content {
    @apply flex flex-col h-full;
}

.content-inner {
    @apply flex-1 flex flex-col overflow-hidden;
}

/* Server info */
.server-info {
    @apply bg-gray-800 p-4 border-b border-gray-700;
}

.server-info-content {
    @apply flex justify-between items-center;
}

.server-name {
    @apply text-xl font-bold text-blue-400;
}

.server-details {
    @apply text-sm text-gray-300;
}

.frequency-info {
    @apply flex gap-4;
}

/* Display stack */
.display-stack {
    @apply flex-1 flex flex-col relative;
}

.spectrum-container {
    @apply h-32 bg-gray-800 border-b border-gray-700;
}

.waterfall-container {
    @apply flex-1 relative bg-black;
}

.spectrum-display,
.waterfall-display {
    @apply w-full h-full;
}

.spectrum-canvas,
.waterfall-canvas {
    @apply w-full h-full;
}

/* Frequency input */
.frequency-input-container {
    @apply absolute top-4 left-4 z-10;
}

.frequency-label {
    @apply block text-sm font-medium text-gray-300 mb-1;
}

.frequency-input {
    @apply bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white focus:border-blue-500 focus:outline-none;
}

/* Passband tuner */
.passband-tuner {
    @apply absolute bottom-4 left-4 right-4 z-10;
}

.passband-container {
    @apply relative h-8 bg-gray-800 rounded border border-gray-600;
}

.passband-range {
    @apply absolute top-0 bottom-0 bg-blue-500 opacity-30 rounded;
}

.passband-handle {
    @apply absolute top-0 bottom-0 w-2 bg-blue-500 cursor-ew-resize rounded;
}

.passband-labels {
    @apply flex justify-between text-xs text-gray-300 mt-1;
}

/* Frequency marker */
.frequency-marker {
    @apply absolute inset-0 pointer-events-none z-20;
}

.marker-line {
    @apply absolute top-0 bottom-0 w-px bg-red-500;
}

.marker-label {
    @apply absolute top-2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-2 py-1 rounded;
}

/* Audio panel */
.audio-panel {
    @apply bg-gray-800 p-4 border-t border-gray-700;
}

.audio-controls {
    @apply flex flex-wrap gap-6 items-center;
}

.volume-control,
.mode-control,
.signal-meter,
.recording-control {
    @apply flex flex-col gap-2;
}

.volume-slider-container {
    @apply flex items-center gap-2;
}

.mute-button {
    @apply bg-gray-700 hover:bg-gray-600 px-2 py-1 rounded text-sm;
}

.mute-button.muted {
    @apply bg-red-600 hover:bg-red-500;
}

.volume-slider {
    @apply w-24;
}

.volume-value {
    @apply text-sm text-gray-300 min-w-[3rem];
}

.mode-buttons {
    @apply flex gap-1;
}

.mode-button {
    @apply px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700 transition-colors;
}

.mode-button.active {
    @apply bg-blue-600 border-blue-500 text-white;
}

.meter-container {
    @apply relative w-32 h-4 bg-gray-700 rounded overflow-hidden;
}

.meter-bar {
    @apply h-full bg-green-500 transition-all duration-100;
}

.meter-value {
    @apply text-xs text-gray-300;
}

.record-button {
    @apply px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded transition-colors;
}

.record-button.recording {
    @apply bg-red-600 hover:bg-red-500 animate-pulse;
}

/* Waterfall controls */
.waterfall-controls {
    @apply bg-gray-800 p-4 border-t border-gray-700;
}

.control-group {
    @apply flex items-center gap-3 mb-2;
}

.control-group label {
    @apply text-sm font-medium text-gray-300 min-w-[5rem];
}

.control-group input[type="range"] {
    @apply flex-1;
}

.control-group select {
    @apply bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white;
}

.control-group span {
    @apply text-sm text-gray-300 min-w-[3rem];
}

/* Tutorial overlay */
.tutorial-overlay {
    @apply fixed inset-0 z-50 flex items-center justify-center;
}

.tutorial-backdrop {
    @apply absolute inset-0 bg-black bg-opacity-50;
}

.tutorial-content {
    @apply relative bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 border border-gray-600;
}

.tutorial-header {
    @apply flex justify-between items-center p-4 border-b border-gray-600;
}

.tutorial-header h3 {
    @apply text-lg font-semibold text-white;
}

.tutorial-close {
    @apply text-gray-400 hover:text-white text-xl font-bold;
}

.tutorial-body {
    @apply p-4;
}

.tutorial-body p {
    @apply text-gray-300 leading-relaxed;
}

.tutorial-footer {
    @apply flex justify-between items-center p-4 border-t border-gray-600;
}

.tutorial-progress {
    @apply text-sm text-gray-400;
}

.tutorial-buttons {
    @apply flex gap-2;
}

.tutorial-btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
}

.tutorial-btn-primary {
    @apply bg-blue-600 hover:bg-blue-500 text-white;
}

.tutorial-btn-secondary {
    @apply bg-gray-600 hover:bg-gray-500 text-white;
}

/* Audio gate overlay */
.overlay-container {
    @apply fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50;
}

.content-card {
    @apply bg-gray-800 rounded-lg p-8 text-center max-w-sm mx-4 border border-gray-600;
}

.icon-container {
    @apply mb-4;
}

.overlay-title {
    @apply text-2xl font-bold text-white mb-2;
}

.overlay-text {
    @apply text-gray-300;
}

/* Bands menu */
.bands-menu {
    @apply relative;
}

.bands-toggle {
    @apply bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded transition-colors;
}

.bands-dropdown {
    @apply absolute top-full left-0 mt-1 bg-gray-800 border border-gray-600 rounded shadow-lg z-30;
}

.bands-list {
    @apply max-h-64 overflow-y-auto;
}

.band-item {
    @apply block w-full text-left px-4 py-3 hover:bg-gray-700 border-b border-gray-600 last:border-b-0;
}

.band-name {
    @apply font-semibold text-blue-400;
}

.band-frequency {
    @apply text-sm text-white;
}

.band-range {
    @apply text-xs text-gray-400;
}

/* Bookmark dialog */
.bookmark-dialog-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.bookmark-dialog {
    @apply bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 border border-gray-600;
}

.bookmark-header {
    @apply flex justify-between items-center p-4 border-b border-gray-600;
}

.bookmark-header h3 {
    @apply text-lg font-semibold text-white;
}

.close-button {
    @apply text-gray-400 hover:text-white text-xl font-bold;
}

.bookmark-content {
    @apply p-4;
}

.bookmark-list {
    @apply max-h-64 overflow-y-auto mb-4;
}

.bookmark-item {
    @apply flex justify-between items-center p-3 bg-gray-700 rounded mb-2 hover:bg-gray-600;
    cursor: pointer;
}

.bookmark-info {
    @apply flex-1;
}

.bookmark-name {
    @apply font-semibold text-white;
}

.bookmark-frequency {
    @apply text-sm text-blue-400;
}

.bookmark-description {
    @apply text-xs text-gray-400;
}

.delete-bookmark {
    @apply text-red-400 hover:text-red-300 ml-2;
}

.add-bookmark-form {
    @apply space-y-3;
}

.add-bookmark-form input {
    @apply w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:border-blue-500 focus:outline-none;
}

.form-buttons {
    @apply flex gap-2;
}

.form-buttons button {
    @apply flex-1 py-2 rounded font-medium transition-colors;
}

.form-buttons button:first-child {
    @apply bg-blue-600 hover:bg-blue-500 text-white;
}

.form-buttons button:last-child {
    @apply bg-gray-600 hover:bg-gray-500 text-white;
}

.add-bookmark-button {
    @apply w-full bg-blue-600 hover:bg-blue-500 text-white py-2 rounded font-medium transition-colors;
}

/* Chat panel */
.chat-panel {
    @apply bg-gray-800 border border-gray-600 rounded-lg shadow-xl max-w-md w-full;
}

.chat-header {
    @apply flex justify-between items-center p-4 border-b border-gray-600;
}

.chat-header h3 {
    @apply text-lg font-semibold text-white;
}

.chat-username {
    @apply p-3 border-b border-gray-600;
}

.chat-username input {
    @apply w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:border-blue-500 focus:outline-none;
}

.chat-messages {
    @apply h-64 overflow-y-auto p-3 space-y-2;
}

.chat-message {
    @apply bg-gray-700 rounded p-2;
}

.message-header {
    @apply flex justify-between items-center mb-1;
}

.message-username {
    @apply font-semibold text-blue-400 text-sm;
}

.message-timestamp {
    @apply text-xs text-gray-400;
}

.message-text {
    @apply text-sm text-gray-200;
}

.chat-input {
    @apply p-3 border-t border-gray-600 flex gap-2;
}

.chat-input textarea {
    @apply flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white resize-none focus:border-blue-500 focus:outline-none;
}

.chat-input button {
    @apply bg-blue-600 hover:bg-blue-500 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded text-white font-medium transition-colors;
}

/* Mobile bottom bar */
.mobile-bottom-bar {
    @apply flex bg-gray-800 border-t border-gray-700;
}

.mobile-tab {
    @apply flex-1 flex flex-col items-center py-2 text-gray-400 hover:text-white hover:bg-gray-700 transition-colors;
}

.mobile-tab.active {
    @apply text-blue-400 bg-gray-700;
}

.tab-icon {
    @apply text-lg mb-1;
}

.tab-label {
    @apply text-xs;
}

/* App footer */
.app-footer {
    @apply bg-gray-800 border-t border-gray-700 p-4;
}

.footer-content {
    @apply flex justify-between items-center text-sm text-gray-400;
}

.footer-section {
    @apply flex items-center gap-4;
}

.footer-section a {
    @apply hover:text-white transition-colors;
}

.version {
    @apply text-xs bg-gray-700 px-2 py-1 rounded;
}

/* Responsive design */
@media (max-width: 768px) {
    .audio-controls {
        @apply flex-col gap-4;
    }
    
    .waterfall-controls {
        @apply hidden;
    }
    
    .frequency-input-container {
        @apply relative top-auto left-auto mb-4;
    }
    
    .passband-tuner {
        @apply relative bottom-auto left-auto right-auto;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    @apply w-2;
}

::-webkit-scrollbar-track {
    @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
    @apply bg-gray-600 rounded;
}

::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
}
