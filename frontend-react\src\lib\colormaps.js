import colormap from 'colormap'
import colorScale from 'colormap/colorScale.js'

const turboColormap = [
  [0.18995, 0.07176, 0.23217],
  [0.19483, 0.08339, 0.26149],
  [0.19956, 0.09498, 0.29024],
  [0.20415, 0.10652, 0.31844],
  [0.20860, 0.11802, 0.34607],
  [0.21291, 0.12947, 0.37314],
  [0.21708, 0.14087, 0.39964],
  [0.22111, 0.15223, 0.42558],
  [0.22500, 0.16354, 0.45096],
  [0.22875, 0.17481, 0.47578],
  [0.23236, 0.18603, 0.50004],
  [0.23582, 0.19720, 0.52373],
  [0.23915, 0.20833, 0.54686],
  [0.24234, 0.21941, 0.56942],
  [0.24539, 0.23044, 0.59142],
  [0.24830, 0.24143, 0.61286],
  [0.25107, 0.25237, 0.63374],
  [0.25369, 0.26327, 0.65406],
  [0.25618, 0.27412, 0.67381],
  [0.25853, 0.28492, 0.69300],
  [0.26074, 0.29568, 0.71162],
  [0.26280, 0.30639, 0.72968],
  [0.26473, 0.31706, 0.74718],
  [0.26652, 0.32768, 0.76412],
  [0.26816, 0.33825, 0.78050],
  [0.26967, 0.34878, 0.79631],
  [0.27103, 0.35926, 0.81156],
  [0.27226, 0.36970, 0.82624],
  [0.27334, 0.38008, 0.84037],
  [0.27429, 0.39043, 0.85393],
  [0.27509, 0.40072, 0.86692],
  [0.27576, 0.41097, 0.87936],
  [0.27628, 0.42118, 0.89123],
  [0.27667, 0.43134, 0.90254],
  [0.27691, 0.44145, 0.91328],
  [0.27701, 0.45152, 0.92347],
  [0.27698, 0.46153, 0.93309],
  [0.27680, 0.47151, 0.94214],
  [0.27648, 0.48144, 0.95064],
  [0.27603, 0.49132, 0.95857],
  [0.27543, 0.50115, 0.96594],
  [0.27469, 0.51094, 0.97275],
  [0.27381, 0.52069, 0.97899],
  [0.27273, 0.53040, 0.98461],
  [0.27106, 0.54015, 0.98930],
  [0.26878, 0.54995, 0.99303],
  [0.26592, 0.55979, 0.99583],
  [0.26252, 0.56967, 0.99773],
  [0.25862, 0.57958, 0.99876],
  [0.25425, 0.58950, 0.99896],
  [0.24946, 0.59943, 0.99835],
  [0.24427, 0.60937, 0.99697],
  [0.23874, 0.61931, 0.99485],
  [0.23288, 0.62923, 0.99202],
  [0.22676, 0.63913, 0.98851],
  [0.22039, 0.64901, 0.98436],
  [0.21382, 0.65886, 0.97959],
  [0.20708, 0.66866, 0.97423],
  [0.20021, 0.67842, 0.96833],
  [0.19326, 0.68812, 0.96190],
  [0.18625, 0.69775, 0.95498],
  [0.17923, 0.70732, 0.94761],
  [0.17223, 0.71680, 0.93981],
  [0.16529, 0.72620, 0.93161],
  [0.15844, 0.73551, 0.92305],
  [0.15173, 0.74472, 0.91416],
  [0.14519, 0.75381, 0.90496],
  [0.13886, 0.76279, 0.89550],
  [0.13278, 0.77165, 0.88580],
  [0.12698, 0.78037, 0.87590],
  [0.12151, 0.78896, 0.86581],
  [0.11639, 0.79740, 0.85559],
  [0.11167, 0.80569, 0.84525],
  [0.10738, 0.81381, 0.83484],
  [0.10357, 0.82177, 0.82437],
  [0.10026, 0.82955, 0.81389],
  [0.09750, 0.83714, 0.80342],
  [0.09532, 0.84455, 0.79299],
  [0.09377, 0.85175, 0.78264],
  [0.09287, 0.85875, 0.77240],
  [0.09267, 0.86554, 0.76230],
  [0.09320, 0.87211, 0.75237],
  [0.09451, 0.87844, 0.74265],
  [0.09662, 0.88454, 0.73316],
  [0.09958, 0.89040, 0.72393],
  [0.10342, 0.89600, 0.71500],
  [0.10815, 0.90142, 0.70599],
  [0.11374, 0.90673, 0.69651],
  [0.12014, 0.91193, 0.68660],
  [0.12733, 0.91701, 0.67627],
  [0.13526, 0.92197, 0.66556],
  [0.14391, 0.92680, 0.65448],
  [0.15323, 0.93151, 0.64308],
  [0.16319, 0.93609, 0.63137],
  [0.17377, 0.94053, 0.61938],
  [0.18491, 0.94484, 0.60713],
  [0.19659, 0.94901, 0.59466],
  [0.20877, 0.95304, 0.58199],
  [0.22142, 0.95692, 0.56914],
  [0.23449, 0.96065, 0.55614],
  [0.24797, 0.96423, 0.54303],
  [0.26180, 0.96765, 0.52981],
  [0.27597, 0.97092, 0.51653],
  [0.29042, 0.97403, 0.50321],
  [0.30513, 0.97697, 0.48987],
  [0.32006, 0.97974, 0.47654],
  [0.33517, 0.98234, 0.46325],
  [0.35043, 0.98477, 0.45002],
  [0.36581, 0.98702, 0.43688],
  [0.38127, 0.98909, 0.42386],
  [0.39678, 0.99098, 0.41098],
  [0.41229, 0.99268, 0.39826],
  [0.42778, 0.99419, 0.38575],
  [0.44321, 0.99551, 0.37345],
  [0.45854, 0.99663, 0.36140],
  [0.47375, 0.99755, 0.34963],
  [0.48879, 0.99828, 0.33816],
  [0.50362, 0.99879, 0.32701],
  [0.51822, 0.99910, 0.31622],
  [0.53255, 0.99919, 0.30581],
  [0.54658, 0.99907, 0.29581],
  [0.56026, 0.99873, 0.28623],
  [0.57357, 0.99817, 0.27712],
  [0.58646, 0.99739, 0.26849],
  [0.59891, 0.99638, 0.26038],
  [0.61088, 0.99514, 0.25280],
  [0.62233, 0.99366, 0.24579],
  [0.63323, 0.99195, 0.23937],
  [0.64362, 0.98999, 0.23356],
  [0.65394, 0.98775, 0.22835],
  [0.66428, 0.98524, 0.22370],
  [0.67462, 0.98246, 0.21960],
  [0.68494, 0.97941, 0.21602],
  [0.69525, 0.97610, 0.21294],
  [0.70553, 0.97255, 0.21032],
  [0.71577, 0.96875, 0.20815],
  [0.72596, 0.96470, 0.20640],
  [0.73610, 0.96043, 0.20504],
  [0.74617, 0.95593, 0.20406],
  [0.75617, 0.95121, 0.20343],
  [0.76608, 0.94627, 0.20311],
  [0.77591, 0.94113, 0.20310],
  [0.78563, 0.93579, 0.20336],
  [0.79524, 0.93025, 0.20386],
  [0.80473, 0.92452, 0.20459],
  [0.81410, 0.91861, 0.20552],
  [0.82333, 0.91253, 0.20663],
  [0.83241, 0.90627, 0.20788],
  [0.84133, 0.89986, 0.20926],
  [0.85010, 0.89328, 0.21074],
  [0.85868, 0.88655, 0.21230],
  [0.86709, 0.87968, 0.21391],
  [0.87530, 0.87267, 0.21555],
  [0.88331, 0.86553, 0.21719],
  [0.89112, 0.85826, 0.21880],
  [0.89870, 0.85087, 0.22038],
  [0.90605, 0.84337, 0.22188],
  [0.91317, 0.83576, 0.22328],
  [0.92004, 0.82806, 0.22456],
  [0.92666, 0.82025, 0.22570],
  [0.93301, 0.81236, 0.22667],
  [0.93909, 0.80439, 0.22744],
  [0.94489, 0.79634, 0.22800],
  [0.95039, 0.78823, 0.22831],
  [0.95560, 0.78005, 0.22836],
  [0.96049, 0.77181, 0.22811],
  [0.96507, 0.76352, 0.22754],
  [0.96931, 0.75519, 0.22663],
  [0.97323, 0.74682, 0.22536],
  [0.97679, 0.73842, 0.22369],
  [0.98000, 0.73000, 0.22161],
  [0.98289, 0.72140, 0.21918],
  [0.98549, 0.71250, 0.21650],
  [0.98781, 0.70330, 0.21358],
  [0.98986, 0.69382, 0.21043],
  [0.99163, 0.68408, 0.20706],
  [0.99314, 0.67408, 0.20348],
  [0.99438, 0.66386, 0.19971],
  [0.99535, 0.65341, 0.19577],
  [0.99607, 0.64277, 0.19165],
  [0.99654, 0.63193, 0.18738],
  [0.99675, 0.62093, 0.18297],
  [0.99672, 0.60977, 0.17842],
  [0.99644, 0.59846, 0.17376],
  [0.99593, 0.58703, 0.16899],
  [0.99517, 0.57549, 0.16412],
  [0.99419, 0.56386, 0.15918],
  [0.99297, 0.55214, 0.15417],
  [0.99153, 0.54036, 0.14910],
  [0.98987, 0.52854, 0.14398],
  [0.98799, 0.51667, 0.13883],
  [0.98590, 0.50479, 0.13367],
  [0.98360, 0.49291, 0.12849],
  [0.98108, 0.48104, 0.12332],
  [0.97837, 0.46920, 0.11817],
  [0.97545, 0.45740, 0.11305],
  [0.97234, 0.44565, 0.10797],
  [0.96904, 0.43399, 0.10294],
  [0.96555, 0.42241, 0.09798],
  [0.96187, 0.41093, 0.09310],
  [0.95801, 0.39958, 0.08831],
  [0.95398, 0.38836, 0.08362],
  [0.94977, 0.37729, 0.07905],
  [0.94538, 0.36638, 0.07461],
  [0.94084, 0.35566, 0.07031],
  [0.93612, 0.34513, 0.06616],
  [0.93125, 0.33482, 0.06218],
  [0.92623, 0.32473, 0.05837],
  [0.92105, 0.31489, 0.05475],
  [0.91572, 0.30530, 0.05134],
  [0.91024, 0.29599, 0.04814],
  [0.90463, 0.28696, 0.04516],
  [0.89888, 0.27824, 0.04243],
  [0.89298, 0.26981, 0.03993],
  [0.88691, 0.26152, 0.03753],
  [0.88066, 0.25334, 0.03521],
  [0.87422, 0.24526, 0.03297],
  [0.86760, 0.23730, 0.03082],
  [0.86079, 0.22945, 0.02875],
  [0.85380, 0.22170, 0.02677],
  [0.84662, 0.21407, 0.02487],
  [0.83926, 0.20654, 0.02305],
  [0.83172, 0.19912, 0.02131],
  [0.82399, 0.19182, 0.01966],
  [0.81608, 0.18462, 0.01809],
  [0.80799, 0.17753, 0.01660],
  [0.79971, 0.17055, 0.01520],
  [0.79125, 0.16368, 0.01387],
  [0.78260, 0.15693, 0.01264],
  [0.77377, 0.15028, 0.01148],
  [0.76476, 0.14374, 0.01041],
  [0.75556, 0.13731, 0.00942],
  [0.74617, 0.13098, 0.00851],
  [0.73661, 0.12477, 0.00769],
  [0.72686, 0.11867, 0.00695],
  [0.71692, 0.11268, 0.00629],
  [0.70680, 0.10680, 0.00571],
  [0.69650, 0.10102, 0.00522],
  [0.68602, 0.09536, 0.00481],
  [0.67535, 0.08980, 0.00449],
  [0.66449, 0.08436, 0.00424],
  [0.65345, 0.07902, 0.00408],
  [0.64223, 0.07380, 0.00401],
  [0.63082, 0.06868, 0.00401],
  [0.61923, 0.06367, 0.00410],
  [0.60746, 0.05878, 0.00427],
  [0.59550, 0.05399, 0.00453],
  [0.58336, 0.04931, 0.00486],
  [0.57103, 0.04474, 0.00529],
  [0.55852, 0.04028, 0.00579],
  [0.54583, 0.03593, 0.00638],
  [0.53295, 0.03169, 0.00705],
  [0.51989, 0.02756, 0.00780],
  [0.50664, 0.02354, 0.00863],
  [0.49321, 0.01963, 0.00955],
  [0.47960, 0.01583, 0.01055]
]



const twenteColorMapFull = [
  [0.000000, 0.000000, 0.000000],
  [0.000000, 0.000000, 0.007843],
  [0.000000, 0.000000, 0.015686],
  [0.000000, 0.000000, 0.023529],
  [0.000000, 0.000000, 0.031373],
  [0.000000, 0.000000, 0.039216],
  [0.000000, 0.000000, 0.047059],
  [0.000000, 0.000000, 0.054902],
  [0.000000, 0.000000, 0.062745],
  [0.000000, 0.000000, 0.070588],
  [0.000000, 0.000000, 0.078431],
  [0.000000, 0.000000, 0.086275],
  [0.000000, 0.000000, 0.094118],
  [0.000000, 0.000000, 0.101961],
  [0.000000, 0.000000, 0.109804],
  [0.000000, 0.000000, 0.117647],
  [0.000000, 0.000000, 0.125490],
  [0.000000, 0.000000, 0.133333],
  [0.000000, 0.000000, 0.141176],
  [0.000000, 0.000000, 0.149020],
  [0.000000, 0.000000, 0.156863],
  [0.000000, 0.000000, 0.164706],
  [0.000000, 0.000000, 0.172549],
  [0.000000, 0.000000, 0.180392],
  [0.000000, 0.000000, 0.188235],
  [0.000000, 0.000000, 0.196078],
  [0.000000, 0.000000, 0.203922],
  [0.000000, 0.000000, 0.211765],
  [0.000000, 0.000000, 0.219608],
  [0.000000, 0.000000, 0.227451],
  [0.000000, 0.000000, 0.235294],
  [0.000000, 0.000000, 0.243137],
  [0.000000, 0.000000, 0.250980],
  [0.000000, 0.000000, 0.258824],
  [0.000000, 0.000000, 0.266667],
  [0.000000, 0.000000, 0.274510],
  [0.000000, 0.000000, 0.282353],
  [0.000000, 0.000000, 0.290196],
  [0.000000, 0.000000, 0.298039],
  [0.000000, 0.000000, 0.305882],
  [0.000000, 0.000000, 0.313725],
  [0.000000, 0.000000, 0.321569],
  [0.000000, 0.000000, 0.329412],
  [0.000000, 0.000000, 0.337255],
  [0.000000, 0.000000, 0.345098],
  [0.000000, 0.000000, 0.352941],
  [0.000000, 0.000000, 0.360784],
  [0.000000, 0.000000, 0.368627],
  [0.000000, 0.000000, 0.376471],
  [0.000000, 0.000000, 0.384314],
  [0.000000, 0.000000, 0.392157],
  [0.000000, 0.000000, 0.400000],
  [0.000000, 0.000000, 0.407843],
  [0.000000, 0.000000, 0.415686],
  [0.000000, 0.000000, 0.423529],
  [0.000000, 0.000000, 0.431373],
  [0.000000, 0.000000, 0.439216],
  [0.000000, 0.000000, 0.447059],
  [0.000000, 0.000000, 0.454902],
  [0.000000, 0.000000, 0.462745],
  [0.000000, 0.000000, 0.470588],
  [0.000000, 0.000000, 0.478431],
  [0.000000, 0.000000, 0.486275],
  [0.000000, 0.000000, 0.494118],
  [0.000000, 0.000000, 0.501961],
  [0.011765, 0.000000, 0.509804],
  [0.023529, 0.000000, 0.517647],
  [0.035294, 0.000000, 0.525490],
  [0.047059, 0.000000, 0.533333],
  [0.058824, 0.000000, 0.541176],
  [0.070588, 0.000000, 0.549020],
  [0.082353, 0.000000, 0.556863],
  [0.094118, 0.000000, 0.564706],
  [0.105882, 0.000000, 0.572549],
  [0.117647, 0.000000, 0.580392],
  [0.129412, 0.000000, 0.588235],
  [0.141176, 0.000000, 0.596078],
  [0.152941, 0.000000, 0.603922],
  [0.164706, 0.000000, 0.611765],
  [0.176471, 0.000000, 0.619608],
  [0.188235, 0.000000, 0.627451],
  [0.200000, 0.000000, 0.635294],
  [0.211765, 0.000000, 0.643137],
  [0.223529, 0.000000, 0.650980],
  [0.235294, 0.000000, 0.658824],
  [0.247059, 0.000000, 0.666667],
  [0.258824, 0.000000, 0.674510],
  [0.270588, 0.000000, 0.682353],
  [0.282353, 0.000000, 0.690196],
  [0.294118, 0.000000, 0.698039],
  [0.305882, 0.000000, 0.705882],
  [0.317647, 0.000000, 0.713725],
  [0.329412, 0.000000, 0.721569],
  [0.341176, 0.000000, 0.729412],
  [0.352941, 0.000000, 0.737255],
  [0.364706, 0.000000, 0.745098],
  [0.376471, 0.000000, 0.752941],
  [0.388235, 0.000000, 0.760784],
  [0.400000, 0.000000, 0.768627],
  [0.411765, 0.000000, 0.776471],
  [0.423529, 0.000000, 0.784314],
  [0.435294, 0.000000, 0.792157],
  [0.447059, 0.000000, 0.800000],
  [0.458824, 0.000000, 0.807843],
  [0.470588, 0.000000, 0.815686],
  [0.482353, 0.000000, 0.823529],
  [0.494118, 0.000000, 0.831373],
  [0.505882, 0.000000, 0.839216],
  [0.517647, 0.000000, 0.847059],
  [0.529412, 0.000000, 0.854902],
  [0.541176, 0.000000, 0.862745],
  [0.552941, 0.000000, 0.870588],
  [0.564706, 0.000000, 0.878431],
  [0.576471, 0.000000, 0.886275],
  [0.588235, 0.000000, 0.894118],
  [0.600000, 0.000000, 0.901961],
  [0.611765, 0.000000, 0.909804],
  [0.623529, 0.000000, 0.917647],
  [0.635294, 0.000000, 0.925490],
  [0.647059, 0.000000, 0.933333],
  [0.658824, 0.000000, 0.941176],
  [0.670588, 0.000000, 0.949020],
  [0.682353, 0.000000, 0.956863],
  [0.694118, 0.000000, 0.964706],
  [0.705882, 0.000000, 0.972549],
  [0.717647, 0.000000, 0.980392],
  [0.729412, 0.000000, 0.988235],
  [0.741176, 0.000000, 0.996078],
  [0.752941, 0.000000, 1.000000],
  [0.756863, 0.125490, 0.992157],
  [0.760784, 0.176471, 0.984314],
  [0.764706, 0.215686, 0.976471],
  [0.768627, 0.250980, 0.968627],
  [0.772549, 0.278431, 0.960784],
  [0.776471, 0.305882, 0.952941],
  [0.780392, 0.329412, 0.945098],
  [0.784314, 0.352941, 0.937255],
  [0.788235, 0.376471, 0.929412],
  [0.792157, 0.396078, 0.921569],
  [0.796078, 0.415686, 0.913725],
  [0.800000, 0.431373, 0.905882],
  [0.803922, 0.450980, 0.898039],
  [0.807843, 0.466667, 0.890196],
  [0.811765, 0.482353, 0.882353],
  [0.815686, 0.501961, 0.874510],
  [0.819608, 0.513725, 0.866667],
  [0.823529, 0.529412, 0.858824],
  [0.827451, 0.545098, 0.850980],
  [0.831373, 0.560784, 0.843137],
  [0.835294, 0.572549, 0.835294],
  [0.839216, 0.588235, 0.827451],
  [0.843137, 0.600000, 0.819608],
  [0.847059, 0.611765, 0.811765],
  [0.850980, 0.627451, 0.803922],
  [0.854902, 0.639216, 0.796078],
  [0.858824, 0.650980, 0.788235],
  [0.862745, 0.662745, 0.780392],
  [0.866667, 0.674510, 0.772549],
  [0.870588, 0.686275, 0.764706],
  [0.874510, 0.698039, 0.756863],
  [0.878431, 0.709804, 0.749020],
  [0.882353, 0.717647, 0.741176],
  [0.886275, 0.729412, 0.733333],
  [0.890196, 0.741176, 0.725490],
  [0.894118, 0.752941, 0.717647],
  [0.898039, 0.760784, 0.709804],
  [0.901961, 0.772549, 0.701961],
  [0.905882, 0.780392, 0.694118],
  [0.909804, 0.792157, 0.686275],
  [0.913725, 0.800000, 0.678431],
  [0.917647, 0.811765, 0.670588],
  [0.921569, 0.819608, 0.662745],
  [0.925490, 0.831373, 0.654902],
  [0.929412, 0.839216, 0.647059],
  [0.933333, 0.850980, 0.639216],
  [0.937255, 0.858824, 0.631373],
  [0.941176, 0.866667, 0.623529],
  [0.945098, 0.878431, 0.615686],
  [0.949020, 0.886275, 0.607843],
  [0.952941, 0.894118, 0.600000],
  [0.956863, 0.901961, 0.592157],
  [0.960784, 0.909804, 0.584314],
  [0.964706, 0.921569, 0.576471],
  [0.968627, 0.929412, 0.568627],
  [0.972549, 0.937255, 0.560784],
  [0.976471, 0.945098, 0.552941],
  [0.980392, 0.952941, 0.545098],
  [0.984314, 0.960784, 0.537255],
  [0.988235, 0.968627, 0.529412],
  [0.992157, 0.976471, 0.521569],
  [0.996078, 0.984314, 0.513725],
  [1.000000, 0.992157, 0.505882],
  [1.000000, 1.000000, 0.501961],
  [1.000000, 1.000000, 0.509804],
  [1.000000, 1.000000, 0.517647],
  [1.000000, 1.000000, 0.525490],
  [1.000000, 1.000000, 0.533333],
  [1.000000, 1.000000, 0.541176],
  [1.000000, 1.000000, 0.549020],
  [1.000000, 1.000000, 0.556863],
  [1.000000, 1.000000, 0.564706],
  [1.000000, 1.000000, 0.572549],
  [1.000000, 1.000000, 0.580392],
  [1.000000, 1.000000, 0.588235],
  [1.000000, 1.000000, 0.596078],
  [1.000000, 1.000000, 0.603922],
  [1.000000, 1.000000, 0.611765],
  [1.000000, 1.000000, 0.619608],
  [1.000000, 1.000000, 0.627451],
  [1.000000, 1.000000, 0.635294],
  [1.000000, 1.000000, 0.643137],
  [1.000000, 1.000000, 0.650980],
  [1.000000, 1.000000, 0.658824],
  [1.000000, 1.000000, 0.666667],
  [1.000000, 1.000000, 0.674510],
  [1.000000, 1.000000, 0.682353],
  [1.000000, 1.000000, 0.690196],
  [1.000000, 1.000000, 0.698039],
  [1.000000, 1.000000, 0.705882],
  [1.000000, 1.000000, 0.713725],
  [1.000000, 1.000000, 0.721569],
  [1.000000, 1.000000, 0.729412],
  [1.000000, 1.000000, 0.737255],
  [1.000000, 1.000000, 0.745098],
  [1.000000, 1.000000, 0.752941],
  [1.000000, 1.000000, 0.760784],
  [1.000000, 1.000000, 0.768627],
  [1.000000, 1.000000, 0.776471],
  [1.000000, 1.000000, 0.784314],
  [1.000000, 1.000000, 0.792157],
  [1.000000, 1.000000, 0.800000],
  [1.000000, 1.000000, 0.807843],
  [1.000000, 1.000000, 0.815686],
  [1.000000, 1.000000, 0.823529],
  [1.000000, 1.000000, 0.831373],
  [1.000000, 1.000000, 0.839216],
  [1.000000, 1.000000, 0.847059],
  [1.000000, 1.000000, 0.854902],
  [1.000000, 1.000000, 0.862745],
  [1.000000, 1.000000, 0.870588],
  [1.000000, 1.000000, 0.878431],
  [1.000000, 1.000000, 0.886275],
  [1.000000, 1.000000, 0.894118],
  [1.000000, 1.000000, 0.901961],
  [1.000000, 1.000000, 0.909804],
  [1.000000, 1.000000, 0.917647],
  [1.000000, 1.000000, 0.925490],
  [1.000000, 1.000000, 0.933333],
  [1.000000, 1.000000, 0.941176],
  [1.000000, 1.000000, 0.949020],
  [1.000000, 1.000000, 0.956863],
  [1.000000, 1.000000, 0.964706],
  [1.000000, 1.000000, 0.972549],
  [1.000000, 1.000000, 0.980392],
  [1.000000, 1.000000, 0.988235],
  [1.000000, 1.000000, 0.996078],
];


const PhantomPlus = [
  [0.00000, 0.00392, 0.12157],
  [0.00000, 0.00392, 0.12411],
  [0.00000, 0.00392, 0.12664],
  [0.00000, 0.00392, 0.12918],
  [0.00000, 0.00392, 0.13172],
  [0.00000, 0.00392, 0.13426],
  [0.00000, 0.00392, 0.13679],
  [0.00000, 0.00392, 0.13933],
  [0.00000, 0.00392, 0.14187],
  [0.00000, 0.00392, 0.14441],
  [0.00000, 0.00392, 0.14694],
  [0.00000, 0.00392, 0.14948],
  [0.00000, 0.00392, 0.15202],
  [0.00000, 0.00392, 0.15456],
  [0.00000, 0.00392, 0.15709],
  [0.00000, 0.00392, 0.15963],
  [0.00000, 0.00392, 0.16217],
  [0.00000, 0.00392, 0.16471],
  [0.00000, 0.00392, 0.16724],
  [0.00000, 0.00392, 0.16978],
  [0.00000, 0.00392, 0.17232],
  [0.00000, 0.00392, 0.17486],
  [0.00000, 0.00392, 0.17739],
  [0.00000, 0.00392, 0.17993],
  [0.00014, 0.00406, 0.18108],
  [0.00031, 0.00423, 0.18193],
  [0.00048, 0.00440, 0.18278],
  [0.00065, 0.00457, 0.18362],
  [0.00082, 0.00474, 0.18447],
  [0.00098, 0.00491, 0.18531],
  [0.00115, 0.00507, 0.18616],
  [0.00132, 0.00524, 0.18700],
  [0.00149, 0.00541, 0.18785],
  [0.00166, 0.00558, 0.18870],
  [0.00183, 0.00575, 0.18954],
  [0.00200, 0.00592, 0.19039],
  [0.00217, 0.00609, 0.19123],
  [0.00234, 0.00626, 0.19208],
  [0.00251, 0.00643, 0.19293],
  [0.00268, 0.00660, 0.19377],
  [0.00285, 0.00677, 0.19462],
  [0.00301, 0.00694, 0.19546],
  [0.00318, 0.00710, 0.19631],
  [0.00335, 0.00727, 0.19715],
  [0.00352, 0.00744, 0.19800],
  [0.00369, 0.00761, 0.19885],
  [0.00386, 0.00778, 0.19969],
  [0.00392, 0.00784, 0.20161],
  [0.00392, 0.00784, 0.20415],
  [0.00392, 0.00784, 0.20669],
  [0.00392, 0.00784, 0.20923],
  [0.00392, 0.00784, 0.21176],
  [0.00392, 0.00784, 0.21430],
  [0.00392, 0.00784, 0.21684],
  [0.00392, 0.00784, 0.21938],
  [0.00392, 0.00784, 0.22191],
  [0.00392, 0.00784, 0.22445],
  [0.00392, 0.00784, 0.22699],
  [0.00392, 0.00784, 0.22953],
  [0.00392, 0.00784, 0.23206],
  [0.00392, 0.00784, 0.23460],
  [0.00392, 0.00784, 0.23714],
  [0.00392, 0.00784, 0.23968],
  [0.00392, 0.00784, 0.24221],
  [0.00392, 0.00784, 0.24475],
  [0.00392, 0.00784, 0.24729],
  [0.00392, 0.00784, 0.24983],
  [0.00392, 0.00784, 0.25236],
  [0.00392, 0.00784, 0.25490],
  [0.00392, 0.00784, 0.25744],
  [0.00384, 0.00784, 0.25982],
  [0.00368, 0.00784, 0.26202],
  [0.00351, 0.00784, 0.26422],
  [0.00334, 0.00784, 0.26642],
  [0.00317, 0.00784, 0.26862],
  [0.00300, 0.00784, 0.27082],
  [0.00283, 0.00784, 0.27302],
  [0.00266, 0.00784, 0.27522],
  [0.00249, 0.00784, 0.27742],
  [0.00232, 0.00784, 0.27962],
  [0.00215, 0.00784, 0.28181],
  [0.00198, 0.00784, 0.28401],
  [0.00181, 0.00784, 0.28621],
  [0.00165, 0.00784, 0.28841],
  [0.00148, 0.00784, 0.29061],
  [0.00131, 0.00784, 0.29281],
  [0.00114, 0.00784, 0.29501],
  [0.00097, 0.00784, 0.29721],
  [0.00080, 0.00784, 0.29941],
  [0.00063, 0.00784, 0.30161],
  [0.00046, 0.00784, 0.30381],
  [0.00029, 0.00784, 0.30601],
  [0.00012, 0.00784, 0.30820],
  [0.00014, 0.00798, 0.31003],
  [0.00065, 0.00849, 0.31088],
  [0.00115, 0.00900, 0.31173],
  [0.00166, 0.00950, 0.31257],
  [0.00217, 0.01001, 0.31342],
  [0.00268, 0.01052, 0.31426],
  [0.00318, 0.01103, 0.31511],
  [0.00369, 0.01153, 0.31596],
  [0.00420, 0.01204, 0.31680],
  [0.00471, 0.01255, 0.31765],
  [0.00521, 0.01306, 0.31849],
  [0.00572, 0.01356, 0.31934],
  [0.00623, 0.01407, 0.32018],
  [0.00674, 0.01458, 0.32103],
  [0.00724, 0.01509, 0.32188],
  [0.00775, 0.01559, 0.32272],
  [0.00826, 0.01610, 0.32357],
  [0.00877, 0.01661, 0.32441],
  [0.00927, 0.01712, 0.32526],
  [0.00978, 0.01762, 0.32611],
  [0.01029, 0.01813, 0.32695],
  [0.01080, 0.01864, 0.32780],
  [0.01130, 0.01915, 0.32864],
  [0.01173, 0.01958, 0.32964],
  [0.01140, 0.01924, 0.33218],
  [0.01106, 0.01890, 0.33472],
  [0.01072, 0.01856, 0.33725],
  [0.01038, 0.01822, 0.33979],
  [0.01004, 0.01789, 0.34233],
  [0.00970, 0.01755, 0.34487],
  [0.00937, 0.01721, 0.34740],
  [0.00903, 0.01687, 0.34994],
  [0.00869, 0.01653, 0.35248],
  [0.00835, 0.01619, 0.35502],
  [0.00801, 0.01586, 0.35755],
  [0.00767, 0.01552, 0.36009],
  [0.00734, 0.01518, 0.36263],
  [0.00700, 0.01484, 0.36517],
  [0.00666, 0.01450, 0.36770],
  [0.00632, 0.01416, 0.37024],
  [0.00598, 0.01383, 0.37278],
  [0.00564, 0.01349, 0.37532],
  [0.00531, 0.01315, 0.37785],
  [0.00497, 0.01281, 0.38039],
  [0.00463, 0.01247, 0.38293],
  [0.00429, 0.01213, 0.38547],
  [0.00395, 0.01180, 0.38800],
  [0.00423, 0.03499, 0.41223],
  [0.00457, 0.06053, 0.43862],
  [0.00491, 0.08607, 0.46501],
  [0.00524, 0.11162, 0.49140],
  [0.00558, 0.13716, 0.51779],
  [0.00592, 0.16271, 0.54418],
  [0.00626, 0.18825, 0.57057],
  [0.00660, 0.21379, 0.59696],
  [0.00694, 0.23934, 0.62334],
  [0.00727, 0.26488, 0.64973],
  [0.00761, 0.29043, 0.67612],
  [0.00795, 0.31597, 0.70251],
  [0.00829, 0.34151, 0.72890],
  [0.00863, 0.36706, 0.75529],
  [0.00897, 0.39260, 0.78168],
  [0.00930, 0.41815, 0.80807],
  [0.00964, 0.44369, 0.83446],
  [0.00998, 0.46923, 0.86085],
  [0.01032, 0.49478, 0.88724],
  [0.01066, 0.52032, 0.91363],
  [0.01100, 0.54587, 0.94002],
  [0.01133, 0.57141, 0.96641],
  [0.01167, 0.59696, 0.99280],
  [0.01915, 0.60417, 0.99717],
  [0.02930, 0.60451, 0.99328],
  [0.03945, 0.60484, 0.98939],
  [0.04960, 0.60518, 0.98550],
  [0.05975, 0.60552, 0.98161],
  [0.06990, 0.60586, 0.97772],
  [0.08005, 0.60620, 0.97383],
  [0.09020, 0.60654, 0.96993],
  [0.10035, 0.60687, 0.96604],
  [0.11050, 0.60721, 0.96215],
  [0.12065, 0.60755, 0.95826],
  [0.13080, 0.60789, 0.95437],
  [0.14095, 0.60823, 0.95048],
  [0.15110, 0.60857, 0.94659],
  [0.16125, 0.60890, 0.94270],
  [0.17140, 0.60924, 0.93881],
  [0.18155, 0.60958, 0.93492],
  [0.19170, 0.60992, 0.93103],
  [0.20185, 0.61026, 0.92714],
  [0.21200, 0.61060, 0.92324],
  [0.22215, 0.61093, 0.91935],
  [0.23230, 0.61127, 0.91546],
  [0.24245, 0.61161, 0.91157],
  [0.26321, 0.62016, 0.89430],
  [0.29281, 0.63556, 0.86588],
  [0.32241, 0.65095, 0.83746],
  [0.35202, 0.66634, 0.80904],
  [0.38162, 0.68174, 0.78062],
  [0.41123, 0.69713, 0.75220],
  [0.44083, 0.71253, 0.72378],
  [0.47043, 0.72792, 0.69536],
  [0.50004, 0.74331, 0.66694],
  [0.52964, 0.75871, 0.63852],
  [0.55925, 0.77410, 0.61010],
  [0.58885, 0.78950, 0.58168],
  [0.61845, 0.80489, 0.55326],
  [0.64806, 0.82028, 0.52484],
  [0.67766, 0.83568, 0.49642],
  [0.70727, 0.85107, 0.46800],
  [0.73687, 0.86647, 0.43958],
  [0.76647, 0.88186, 0.41116],
  [0.79608, 0.89725, 0.38275],
  [0.82568, 0.91265, 0.35433],
  [0.85529, 0.92804, 0.32591],
  [0.88489, 0.94344, 0.29749],
  [0.91449, 0.95883, 0.26907],
  [0.93370, 0.96894, 0.25276],
  [0.93472, 0.96978, 0.25767],
  [0.93573, 0.97063, 0.26258],
  [0.93675, 0.97147, 0.26748],
  [0.93776, 0.97232, 0.27239],
  [0.93878, 0.97316, 0.27729],
  [0.93979, 0.97401, 0.28220],
  [0.94081, 0.97486, 0.28710],
  [0.94182, 0.97570, 0.29201],
  [0.94284, 0.97655, 0.29692],
  [0.94385, 0.97739, 0.30182],
  [0.94487, 0.97824, 0.30673],
  [0.94588, 0.97908, 0.31163],
  [0.94690, 0.97993, 0.31654],
  [0.94791, 0.98078, 0.32145],
  [0.94893, 0.98162, 0.32635],
  [0.94994, 0.98247, 0.33126],
  [0.95096, 0.98331, 0.33616],
  [0.95197, 0.98416, 0.34107],
  [0.95299, 0.98501, 0.34597],
  [0.95400, 0.98585, 0.35088],
  [0.95502, 0.98670, 0.35579],
  [0.95603, 0.98754, 0.36069],
  [0.95699, 0.98833, 0.36560],
  [0.95766, 0.98884, 0.37050],
  [0.95834, 0.98934, 0.37541],
  [0.95902, 0.98985, 0.38032],
  [0.95969, 0.99036, 0.38522],
  [0.96037, 0.99087, 0.39013],
  [0.96105, 0.99137, 0.39503],
  [0.96172, 0.99188, 0.39994],
  [0.96240, 0.99239, 0.40484],
  [0.96308, 0.99290, 0.40975],
  [0.96375, 0.99340, 0.41466],
  [0.96443, 0.99391, 0.41956],
  [0.96511, 0.99442, 0.42447],
  [0.96578, 0.99493, 0.42937],
  [0.96646, 0.99543, 0.43428],
  [0.96714, 0.99594, 0.43918],
  [0.96781, 0.99645, 0.44409],
  [0.96849, 0.99696, 0.44900],
  [0.96917, 0.99746, 0.45390],
  [0.96984, 0.99797, 0.45881],
  [0.97052, 0.99848, 0.46371],
  [0.97120, 0.99899, 0.46862],
  [0.97187, 0.99949, 0.47353],
  [0.97255, 1.00000, 0.47843]
];

// https://github.com/csete/gqrx/blob/master/src/qtgui/plotter.cpp
const gqrxData = []
for (let i = 0; i < 256; i++) {
  if (i < 20) {
    // level 0: black background
    gqrxData.push([0, 0, 0])
  } else if ((i >= 20) && (i < 70)) {
    // level 1: black -> blue
    gqrxData.push([0, 0, 140 * (i - 20) / 50])
  } else if ((i >= 70) && (i < 100)) {
    // level 2: blue -> light-blue / greenish
    gqrxData.push([60 * (i - 70) / 30, 125 * (i - 70) / 30, 115 * (i - 70) / 30 + 140])
  } else if ((i >= 100) && (i < 150)) {
    // level 3: light blue -> yellow
    gqrxData.push([195 * (i - 100) / 50 + 60, 130 * (i - 100) / 50 + 125, 255 - (255 * (i - 100) / 50)])
  } else if ((i >= 150) && (i < 250)) {
    // level 4: yellow -> red
    gqrxData.push([255, 255 - 255 * (i - 150) / 100, 0])
  } else if (i >= 250) {
    // level 5: red -> white
    gqrxData.push([255, 255 * (i - 250) / 5, 255 * (i - 250) / 5])
  }
  gqrxData[i] = gqrxData[i].map(x => x / 255)
}

// Replica of WebSDR Colormap to replicate the cool look of these colors!
const twenteData = [];
for (let i = 0; i < 256; i++) {
  if (i < 105) {
    // level 0: black to dark blue (#0d0086)
    const r = 13 * i / 105;
    const g = 0;
    const b = 134 * i / 105;
    twenteData.push([r, g, b]);
  }  else if (i < 192) {
    // level 2: dark purple (#6200b0) to yellow (#fffec3)
    const r = 98 + (255 - 98) * (i - 96) / 63;
    const g = 0 + (254 - 0) * (i - 96) / 63;
    const b = 176 + (195 - 176) * (i - 96) / 63;
    twenteData.push([r, g, b]);
  } else {
    // level 3: yellow (#fffec3)
    const r = 255;
    const g = 254;
    const b = 195;
    twenteData.push([r, g, b]);
  }
  // Normalize RGB values to the range 0-1
  twenteData[i] = twenteData[i].map(x => x / 255);
}

// SpectraVU Colormap
const spectraVUData = [];

for (let i = 0; i < 256; i++) {
  if (i < 64) {
    // Dark blue to blue
    const r = 0;
    const g = 0;
    const b = 128 + (i * 2);
    spectraVUData.push([r, g, b]);
  } else if (i < 128) {
    // Blue to green
    const r = 0;
    const g = (i - 64) * 4;
    const b = 255 - ((i - 64) * 4);
    spectraVUData.push([r, g, b]);
  } else if (i < 192) {
    // Green to yellow
    const r = (i - 128) * 4;
    const g = 255;
    const b = 0;
    spectraVUData.push([r, g, b]);
  } else {
    // Yellow to red
    const r = 255;
    const g = 255 - ((i - 192) * 4);
    const b = 0;
    spectraVUData.push([r, g, b]);
  }
  // Normalize RGB values to the range 0-1
  spectraVUData[i] = spectraVUData[i].map(x => Math.min(x, 255) / 255);
}



const definedColormaps = {
  turbo: turboColormap,
  gqrx: gqrxData,
  twente: twenteData,
  twentev2: twenteColorMapFull,
  SpectraVU: spectraVUData,
}

export const availableColormaps = [
  'turbo', 'gqrx', 'twente', 'twentev2','SpectraVU'
]


export function computeColormapArray (colormap) {
  return colormap.map((rgb) => {
    const rgbcolor = new Uint8ClampedArray(4)
    if (rgb.length < 4) {
      rgb = [...rgb, 255]
    }
    rgbcolor.set(rgb.map(x => Math.round(x * 255)))
    return rgbcolor
  })
}

export function drawColormapPreview (name, elem) {
  const ctx = elem.getContext('2d')
  const height = elem.height
  const colormapArray = computeColormapArray(getColormap(name))
  for (let i = 0; i < 256; i++) {
    const [r, g, b, a] = colormapArray[i]
    ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${a})`
    ctx.fillRect(i, 0, 1, height)
  }
}

export default function getColormap (name) {
  let colors
  if (name in definedColormaps) {
    colors = definedColormaps[name]
  } else {
    colors = colormap({
      colormap: name,
      nshades: 256,
      format: 'float',
      alpha: 1
    })
  }
  return colors
}
