!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,l,s,i=[],o=!0,c=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;o=!1}else for(;!(o=(r=l.call(n)).done)&&(i.push(r.value),i.length!==t);o=!0);}catch(e){c=!0,a=e}finally{try{if(!o&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,a(r.key),r)}}function a(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}System.register(["./index-legacy-lBrMbuR-.js"],function(e,n){"use strict";var l,s,i,o;return{setters:[function(e){l=e.r,s=e.u,i=e.a,o=e.j}],execute:function(){var n=new(function(){return e=function e(){var t,n,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,r={},(n=a(n="events"))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r},(t=[{key:"subscribe",value:function(e,t){var n=this;return this.events[e]||(this.events[e]=[]),this.events[e].push(t),function(){n.events[e]&&(n.events[e]=n.events[e].filter(function(e){return e!==t}))}}},{key:"publish",value:function(e,t){this.events[e]&&this.events[e].forEach(function(e){return e(t)})}},{key:"unsubscribe",value:function(e,t){this.events[e]&&(this.events[e]=this.events[e].filter(function(e){return e!==t}))}},{key:"clear",value:function(){this.events={}}}])&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}());e("default",l.forwardRef(function(e,r){var a=e.onStateChange,c=t(l.useState(65),2),d=c[0],u=c[1],h=t(l.useState(!1),2),f=h[0],b=h[1],v=t(l.useState(-50),2),m=v[0],x=v[1],g=t(l.useState(!1),2),p=g[0],j=g[1],N=t(l.useState(!1),2),C=N[0],S=N[1],y=t(l.useState(!1),2),k=y[0],w=y[1],E=s(),M=E.mute,A=E.NREnabled,R=E.NBEnabled,T=E.ANEnabled,B=E.CTCSSSupressEnabled,L=E.setAudioPanelState,P=l.useCallback(function(e,t){n.publish(e,t)},[]),q=l.useCallback(function(e){var t=document.getElementById("sMeter");if(t){var n=t.getContext("2d");if(n){t.width=300,t.height=40;var r=t.width,a=t.height;n.clearRect(0,0,r,a);var l=15,s=r/2;n.strokeStyle="#0071e3",n.lineWidth=2,n.beginPath(),n.moveTo(0,l),n.lineTo(s,l),n.stroke(),n.strokeStyle="#ff3b30",n.beginPath(),n.moveTo(s,l),n.lineTo(268,l),n.stroke();for(var i=0;i<30;i++){var o=9*i;n.fillStyle=i<e?i<17?"#0071e3":"#ff3b30":i<17?"rgba(0, 113, 227, 0.2)":"rgba(255, 59, 48, 0.2)",n.fillRect(o,0,6,8)}n.font="11px -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', sans-serif",n.textAlign="center";for(var c=["S1","3","5","7","9","+20","+40","+60dB"],d=0;d<=16;d++){var u=16.6970588235*d;n.fillStyle=u<=s?"#0071e3":"#ff3b30",d%2==1?(n.fillRect(u,l,1,7),(d-1)/2<c.length&&n.fillText(c[(d-1)/2],u,33)):n.fillRect(u,l,1,5)}}}},[]),F=l.useCallback(function(e){e=Math.min(Math.max(e,-100),0);var t=Math.round(35*(e+100)/100);q(t)},[q]),D=l.useCallback(function(){var e=!M;L({mute:e}),i.setMute(e),null==a||a({mute:e,NREnabled:A,NBEnabled:R,ANEnabled:T,CTCSSSupressEnabled:B}),P("muteStateChanged",e)},[M,A,R,T,B,L,a,P]),W=l.useCallback(function(e){u(e),i.setGain(Math.pow(10,(e-50)/50+2.6))},[]),z=l.useCallback(function(){var e=!f;b(e),i.setSquelch(e)},[f]),I=l.useCallback(function(e){x(e),i.setSquelchThreshold(e)},[]),O=l.useCallback(function(){var e=!A;L({NREnabled:e}),i.decoder.set_nr(e),null==a||a({mute:M,NREnabled:e,NBEnabled:R,ANEnabled:T,CTCSSSupressEnabled:B})},[A,M,R,T,B,L,a]),H=l.useCallback(function(){var e=!R;L({NBEnabled:e}),i.nb=e,i.decoder.set_nb(e),null==a||a({mute:M,NREnabled:A,NBEnabled:e,ANEnabled:T,CTCSSSupressEnabled:B})},[R,M,A,T,B,L,a]),_=l.useCallback(function(){var e=!T;L({ANEnabled:e}),i.decoder.set_an(e),null==a||a({mute:M,NREnabled:A,NBEnabled:R,ANEnabled:e,CTCSSSupressEnabled:B})},[T,M,A,R,B,L,a]),U=l.useCallback(function(){var e=!B;L({CTCSSSupressEnabled:e}),i.setCTCSSFilter(e),null==a||a({mute:M,NREnabled:A,NBEnabled:R,ANEnabled:T,CTCSSSupressEnabled:e})},[B,M,A,R,T,L,a]),G=l.useCallback(function(e){j(e),i.setFT8Decoding(e)},[]),Q=l.useCallback(function(){C?(i.stopRecording(),S(!1),w(!0)):(i.startRecording(),S(!0),w(!1))},[C]),V=l.useCallback(function(){i.downloadRecording()},[]),$=l.useCallback(function(){var e=i.getPowerDb()/150*100+i.smeter_offset;F(e)},[F]),J=l.useCallback(function(){W(d)},[d,W]);return l.useImperativeHandle(r,function(){return{updateSignalMeter:$,initializeAudio:J,handleMuteChange:D,handleNRChange:O,handleNBChange:H,handleANChange:_,handleCTCSSChange:U,handleSquelchChange:z,handleRecordingChange:Q,NREnabled:A,NBEnabled:R,ANEnabled:T,CTCSSSupressEnabled:B,mute:M}},[$,J,D,O,H,_,U,z,Q,A,R,T,B,M]),o.jsxs("div",{className:"audio-controls",children:[o.jsx("h2",{className:"section-title",children:"Audio Controls"}),o.jsxs("div",{className:"control-group",id:"volume-slider",children:[o.jsxs("div",{className:"control-header",children:[o.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"control-icon",children:[o.jsx("path",{d:"M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z",fill:"currentColor"}),o.jsx("path",{d:"M11.5 4.5a4 4 0 010 7M13 3a6 6 0 010 10",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]}),o.jsx("span",{className:"control-label",children:"Volume"}),o.jsxs("span",{className:"control-value",children:[d,"%"]})]}),o.jsxs("div",{className:"slider-container",children:[o.jsx("button",{className:"mute-button ".concat(M?"active":""),onClick:D,"aria-label":M?"Unmute":"Mute",children:o.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:M?o.jsxs(o.Fragment,{children:[o.jsx("path",{d:"M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z",fill:"currentColor"}),o.jsx("path",{d:"M11 6l4 4m0-4l-4 4",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]}):o.jsxs(o.Fragment,{children:[o.jsx("path",{d:"M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z",fill:"currentColor"}),o.jsx("path",{d:"M11.5 4.5a4 4 0 010 7M13 3a6 6 0 010 10",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]})})}),o.jsxs("div",{className:"slider-wrapper",children:[o.jsx("input",{type:"range",value:d,onChange:function(e){return W(Number(e.target.value))},disabled:M,min:"0",max:"100",step:"1",className:"slider volume-slider"}),o.jsx("div",{className:"slider-track",style:{width:"".concat(d,"%")}})]})]})]}),o.jsxs("div",{className:"control-group",id:"squelch-slider",children:[o.jsxs("div",{className:"control-header",children:[o.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"control-icon",children:[o.jsx("path",{d:"M8 1a7 7 0 110 14A7 7 0 018 1zM8 5a3 3 0 110 6 3 3 0 010-6z",stroke:"currentColor",strokeWidth:"1.5"}),o.jsx("circle",{cx:"8",cy:"8",r:"1.5",fill:"currentColor"})]}),o.jsx("span",{className:"control-label",children:"Squelch"}),o.jsxs("span",{className:"control-value",children:[m," dB"]})]}),o.jsxs("div",{className:"slider-container",children:[o.jsx("button",{className:"squelch-button ".concat(f?"active":""),onClick:z,"aria-label":f?"Disable squelch":"Enable squelch",children:o.jsx("span",{children:"SQ"})}),o.jsxs("div",{className:"slider-wrapper",children:[o.jsx("input",{type:"range",value:m,onChange:function(e){return I(Number(e.target.value))},min:"-150",max:"0",step:"1",className:"slider squelch-slider"}),o.jsx("div",{className:"slider-track green",style:{width:"".concat((m+150)/150*100,"%")}})]})]})]}),o.jsxs("div",{className:"decoder-section",children:[o.jsxs("div",{className:"section-header",children:[o.jsx("div",{className:"accent-dot bg-green"}),o.jsx("h3",{className:"section-label",children:"Decoder"})]}),o.jsx("div",{className:"decoder-controls",children:o.jsx("div",{className:"decoder-row",children:o.jsxs("label",{className:"decoder-label",children:[o.jsx("input",{type:"checkbox",checked:p,onChange:function(e){return G(e.target.checked)},className:"decoder-checkbox"}),o.jsx("span",{className:"checkmark"}),"FT8 Decoder"]})})}),p&&o.jsxs("div",{className:"ft8-panel",children:[o.jsxs("div",{className:"ft8-header",children:[o.jsx("span",{className:"ft8-title",children:"FT8 Messages"}),o.jsx("div",{className:"ft8-stats",children:o.jsx("span",{id:"farthest-distance",children:"0 km"})})]}),o.jsx("div",{id:"ft8MessagesList",className:"ft8-messages-list"})]})]}),o.jsxs("div",{className:"recording-section",children:[o.jsxs("div",{className:"section-header",children:[o.jsx("div",{className:"accent-dot bg-red"}),o.jsx("h3",{className:"section-label",children:"Recording"})]}),o.jsxs("div",{className:"recording-controls",children:[o.jsxs("button",{className:"recording-button ".concat(C?"recording":""),onClick:Q,title:C?"Stop Recording":"Start Recording",children:[o.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:C?o.jsx("rect",{x:"4",y:"4",width:"8",height:"8",fill:"currentColor",rx:"1"}):o.jsx("circle",{cx:"8",cy:"8",r:"4",fill:"currentColor"})}),o.jsx("span",{children:C?"Stop":"Record"})]}),k&&o.jsxs("button",{className:"download-button",onClick:V,title:"Download Recording",children:[o.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[o.jsx("path",{d:"M8 1v10m0 0l3-3m-3 3L5 8",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),o.jsx("path",{d:"M2 12v2a2 2 0 002 2h8a2 2 0 002-2v-2",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]}),o.jsx("span",{children:"Download"})]})]})]})]})})).displayName="AudioPanel"}}})}();
