import { TCacheTestResultFunction } from './cache-test-result-function';

export type TIsSupportedPromiseFactory = (
    cacheTestResult: TCacheTestResultFunction,
    testAudioBufferCopyChannelMethodsSubarraySupport: () => boolean,
    testAudioContextCloseMethodSupport: () => boolean,
    testAudioContextDecodeAudioDataMethodTypeErrorSupport: () => Promise<boolean>,
    testAudioContextOptionsSupport: () => boolean,
    testAudioNodeConnectMethodSupport: () => boolean,
    testAudioWorkletProcessorNoOutputsSupport: () => Promise<boolean>,
    testChannelMergerNodeChannelCountSupport: () => boolean,
    testConstantSourceNodeAccurateSchedulingSupport: () => boolean,
    testConvolverNodeBufferReassignabilitySupport: () => boolean,
    testConvolverNodeChannelCountSupport: () => boolean,
    testDomExceptionContrucorSupport: () => boolean,
    testIsSecureContextSupport: () => boolean,
    testMediaStreamAudioSourceNodeMediaStreamWithoutAudioTrackSupport: () => boolean,
    testStereoPannerNodeDefaultValueSupport: () => Promise<boolean>,
    testTransferablesSupport: () => Promise<boolean>
) => Promise<boolean>;
