import { TAudioParamMap, T<PERSON>ontext, TErrorEventHandler } from '../types';
import { IAudioNode } from './audio-node';
import { IAudioWorkletNodeEventMap } from './audio-worklet-node-event-map';

export interface IAudioWorkletNode<T extends TContext> extends IAudioNode<T, IAudioWorkletNodeEventMap> {
    onprocessorerror: null | TErrorEventHandler<this>;

    readonly parameters: TAudioParamMap;

    readonly port: MessagePort;
}
