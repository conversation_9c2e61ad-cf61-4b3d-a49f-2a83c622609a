// Type declarations for JavaScript modules

declare module '../audio' {
  export default class SpectrumAudio {
    constructor(uri: string);
    init(): Promise<void>;
    setUserID(id: string): void;
    settings: any;
    enable(): void;
    setMute(mute: boolean): void;
    setGain(gain: number): void;
    setSquelch(enable: boolean): void;
    setSquelchThreshold(threshold: number): void;
    setCTCSSFilter(enable: boolean): void;
    setFT8Decoding(enable: boolean): void;
    startRecording(): void;
    stopRecording(): void;
    downloadRecording(): void;
    getPowerDb(): number;
    smeter_offset: number;
    getAudioRange(): number[];
    setAudioRange(...args: any[]): void;
    decoder: {
      set_nr(enable: boolean): void;
      set_nb(enable: boolean): void;
      set_an(enable: boolean): void;
    };
    nb: boolean;
    stop(): void;
  }
}

declare module '../waterfall' {
  export default class SpectrumWaterfall {
    constructor(uri: string);
    init(): Promise<void>;
    setUserID(id: string): void;
    getWaterfallRange(): [number, number];
    setWaterfallRange(l: number, r: number): void;
    waterfallMaxSize: number;
    canvasWidth: number;
    canvasWheel(event: any): void;
    setClients(clients: any): void;
    updateGraduation(): void;
    drawClients(): void;
    checkBandAndSetMode?: (frequency: number) => void;
    stop(): void;
  }
}

declare module '../events' {
  export default class SpectrumEvents {
    constructor(uri: string);
    init(): Promise<void>;
    setUserID(id: string): void;
    getLastModified(): number;
    getSignalClients(): any;
  }
}

declare module './wrappers' {
  export default function initWrappers(): Promise<void>;
}

declare module './storage' {
  export function constructLink(obj: any): string;
  export function parseLink(query: string): any;
  export function storeInLocalStorage(obj: any): void;
}

declare module './colormaps' {
  // Add colormap types as needed
  export const colormaps: any;
}

declare module './hammeractions' {
  // Add hammer action types as needed
  export const hammerActions: any;
}

declare module 'efficient-rolling-stats' {
  export class RollingMax {
    constructor(windowSize: number);
    call(value: number): number;
  }
}
