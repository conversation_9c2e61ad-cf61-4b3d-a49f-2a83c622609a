import { TAudioParamAudioNodeStore } from './audio-param-audio-node-store';
import { TDetectCyclesFunction } from './detect-cycles-function';
import { TGetAudioNodeConnectionsFunction } from './get-audio-node-connections-function';
import { TGetValueForKeyFunction } from './get-value-for-key-function';

export type TDetectCyclesFactory = (
    audioParamAudioNodeStore: TAudioParamAudioNodeStore,
    getAudioNodeConnections: TGetAudioNodeConnectionsFunction,
    getValueForKey: TGetValueForKeyFunction
) => TDetectCyclesFunction;
