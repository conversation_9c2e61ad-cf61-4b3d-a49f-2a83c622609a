import { create } from 'zustand';

interface FrequencySteps {
  mouseWheelStep: number;
  keyboardStep: number;
  keyboardShiftStep: number;
}

interface FrequencyStepsState {
  frequencySteps: FrequencySteps;
  updateFrequencySteps: (steps: Partial<FrequencySteps>) => void;
}

const defaultFrequencySteps: FrequencySteps = {
  mouseWheelStep: 50,
  keyboardStep: 50,
  keyboardShiftStep: 100,
};

export const useFrequencyStepsStore = create<FrequencyStepsState>((set) => ({
  frequencySteps: defaultFrequencySteps,
  
  updateFrequencySteps: (steps: Partial<FrequencySteps>) => {
    set((state) => ({
      frequencySteps: { ...state.frequencySteps, ...steps }
    }));
  },
}));
