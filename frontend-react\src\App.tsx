import { useState, useEffect, useCallback, useRef, lazy } from 'react';
import { useAppStore } from './stores/appStore';
import { init } from './lib/backend';
import ErrorBoundary from './components/ErrorBoundary';
import LazyWrapper from './components/LazyWrapper';

// Lazy load components for code splitting
const AudioPanel = lazy(() => import('./components/AudioPanel'));

function App() {
  // Global state
  const {
    frequency,
    demodulation,
    bandwidth,
    currentVFO,
    vfoSwitchNotification,
    NREnabled,
    NBEnabled,
    ANEnabled,
    mute,
    setFrequency,
    setAudioPanelState
  } = useAppStore();

  // Component references
  const audioPanelRef = useRef<any>(null);

  // Local state
  const [backendInitialized, setBackendInitialized] = useState(false);

  // Placeholder for frequency change handler
  const handleFrequencyChange = useCallback((event: any) => {
    // TODO: Implement frequency change logic
    console.log('Frequency change:', event);
  }, []);

  // Initialize backend on mount
  useEffect(() => {
    const initializeBackend = async () => {
      try {
        await init();
        setBackendInitialized(true);

        // Enable UI elements after connection established
        const elements = [
          ...document.getElementsByTagName("button"),
          ...document.getElementsByTagName("input"),
        ];
        elements.forEach((element: any) => {
          element.disabled = false;
        });

        console.log('Backend initialized successfully');
      } catch (error) {
        console.error('Failed to initialize backend:', error);
      }
    };

    initializeBackend();
  }, []);

  if (!backendInitialized) {
    return (
      <div className="loading-screen">
        <div className="loading-content">
          <div className="spinner"></div>
          <h2>Initializing SDR Application...</h2>
          <p>Connecting to backend services...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <main className="main-container">
        <div className="h-screen overflow-hidden flex flex-col min-h-screen">
          <div className="w-full flex-grow overflow-y-auto">
            <div className="app-content">
              <div className="content-inner">
                {/* Audio Panel */}
                <div className="main-panels" id="middle-column">
                  <LazyWrapper>
                    <AudioPanel
                      ref={audioPanelRef}
                      currentFrequency={parseFloat(frequency) * 1e3}
                      onStateChange={(state) => {
                        setAudioPanelState(state);
                      }}
                    />
                  </LazyWrapper>

                  {/* Center Panel with Frequency Display */}
                  <div className="center-panel">
                    <div className="frequency-display" id="smeter-tut">
                      <div className="frequency-panel">
                        <div className="frequency-section">
                          <input
                            className="frequency-input"
                            type="text"
                            value={frequency}
                            onChange={(e) => setFrequency(e.target.value)}
                            size={3}
                            name="frequency"
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                const freqHz = parseFloat(frequency) * 1e3;
                                handleFrequencyChange({ detail: freqHz });
                              }
                            }}
                          />
                          <div className="frequency-info">
                            <span
                              className="vfo-badge clickable"
                              onClick={() => {/* TODO: Implement VFO switch */}}
                              title="Click to switch VFO (or press V)"
                            >
                              VFO {currentVFO}
                            </span>
                            <span className="separator">•</span>
                            <span className="mode-text">{demodulation}</span>
                            <span className="separator">•</span>
                            <span className="bandwidth-text">{bandwidth} kHz</span>
                          </div>
                        </div>

                        <div className="status-section">
                          <div className="status-indicators">
                            {[
                              { label: "MUTED", enabled: mute, color: "red" },
                              { label: "NR", enabled: NREnabled, color: "green" },
                              { label: "NB", enabled: NBEnabled, color: "green" },
                              { label: "AN", enabled: ANEnabled, color: "green" }
                            ].map((indicator) => (
                              <div
                                key={indicator.label}
                                className={`status-indicator ${indicator.enabled ? `active ${indicator.color}` : ''}`}
                              >
                                <span>{indicator.label}</span>
                              </div>
                            ))}
                          </div>
                          {/* SMeter */}
                          <canvas
                            id="sMeter"
                            width="250"
                            height="40"
                          ></canvas>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* VFO Switch Notification */}
                {vfoSwitchNotification && (
                  <div className="vfo-notification">
                    <div className="notification-content">
                      <div className="vfo-icon-wrapper">
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className="vfo-icon"
                        >
                          <path
                            d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                            fill="currentColor"
                          />
                        </svg>
                      </div>
                      <span className="notification-text">{vfoSwitchNotification}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </ErrorBoundary>
  );
}

export default App;
