import { TConvolverNodeRendererFactory } from './convolver-node-renderer-factory';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeConvolverNodeFactory } from './native-convolver-node-factory';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';

export type TConvolverNodeRendererFactoryFactory = (
    createNativeConvolverNode: TNativeConvolverNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction
) => TConvolverNodeRendererFactory;
