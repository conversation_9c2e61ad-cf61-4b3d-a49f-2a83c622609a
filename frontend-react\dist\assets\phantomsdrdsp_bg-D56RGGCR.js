import{_ as F,b as G,c as I,d as K,e as M,f as N,g as O,h as Q,i as V,k as X,l as Y,m as Z,n as $,o as __,p as e_,__tla as s_}from"./index-DGS8NYvs.js";let i,m,c,w,b,l,f,g,u,h,p,z,y,k,x,W,A,B,L,U,j,C,R,q,v,S,T,E,H,J,P,a_=Promise.all([(()=>{try{return s_}catch(o){}})()]).then(async()=>{const o="/assets/phantomsdrdsp_bg-Bh04HLJe.wasm",D=async(r={},n)=>{let t;if(n.startsWith("data:")){const e=n.replace(/^data:.*?base64,/,"");let s;if(typeof Buffer=="function"&&typeof Buffer.from=="function")s=Buffer.from(e,"base64");else if(typeof atob=="function"){const a=atob(e);s=new Uint8Array(a.length);for(let d=0;d<a.length;d++)s[d]=a.charCodeAt(d)}else throw new Error("Cannot decode base64-encoded data URL");t=await WebAssembly.instantiate(s,r)}else{const e=await fetch(n),s=e.headers.get("Content-Type")||"";if("instantiateStreaming"in WebAssembly&&s.startsWith("application/wasm"))t=await WebAssembly.instantiateStreaming(e,r);else{const a=await e.arrayBuffer();t=await WebAssembly.instantiate(a,r)}}return t.instance.exports};URL=globalThis.URL;let _;_=await D({"./phantomsdrdsp_bg.js":{__wbindgen_object_drop_ref:e_,__wbindgen_object_clone_ref:__,__wbg_alert_379870a8a3d45260:$,__wbg_new_abda76e883ba8a5f:Z,__wbg_stack_658279fe44541cf6:Y,__wbg_error_f851667af71bcfc6:X,__wbg_call_b3ca7c6051f9bec1:V,__wbg_buffer_12d079cc21e14bdb:Q,__wbg_newwithbyteoffsetandlength_aa4a17c33a06e5cb:O,__wbg_new_63b92bc8671ed464:N,__wbg_newwithbyteoffsetandlength_4a659d079a1650e0:M,__wbg_new_9efabd6b6d2ce46d:K,__wbg_newwithlength_1e8b839a06de01c5:I,__wbindgen_throw:G,__wbindgen_memory:F}},o),U=_.memory,m=_.__wbg_foxenflacdecoder_free,A=_.foxenflacdecoder_new,i=_.__wbg_audio_free,p=_.audio_new,h=_.audio_decode,x=_.audio_set_nr,k=_.audio_set_nb,z=_.audio_set_an,y=_.audio_set_decoded_callback,c=_.__wbg_zstdstreamdecoder_free,P=_.zstdstreamdecoder_new,H=_.zstdstreamdecoder_clear,J=_.zstdstreamdecoder_decode,B=_.greet,L=_.main,W=_.firdes_kaiser_lowpass,C=_.rust_zstd_wasm_shim_free,R=_.rust_zstd_wasm_shim_malloc,E=_.rust_zstd_wasm_shim_qsort,q=_.rust_zstd_wasm_shim_memcmp,j=_.rust_zstd_wasm_shim_calloc,v=_.rust_zstd_wasm_shim_memcpy,S=_.rust_zstd_wasm_shim_memmove,T=_.rust_zstd_wasm_shim_memset,f=_.__wbindgen_malloc,w=_.__wbindgen_add_to_stack_pointer,l=_.__wbindgen_free,g=_.__wbindgen_realloc,b=_.__wbindgen_exn_store,u=_.__wbindgen_start});export{a_ as __tla,i as __wbg_audio_free,m as __wbg_foxenflacdecoder_free,c as __wbg_zstdstreamdecoder_free,w as __wbindgen_add_to_stack_pointer,b as __wbindgen_exn_store,l as __wbindgen_free,f as __wbindgen_malloc,g as __wbindgen_realloc,u as __wbindgen_start,h as audio_decode,p as audio_new,z as audio_set_an,y as audio_set_decoded_callback,k as audio_set_nb,x as audio_set_nr,W as firdes_kaiser_lowpass,A as foxenflacdecoder_new,B as greet,L as main,U as memory,j as rust_zstd_wasm_shim_calloc,C as rust_zstd_wasm_shim_free,R as rust_zstd_wasm_shim_malloc,q as rust_zstd_wasm_shim_memcmp,v as rust_zstd_wasm_shim_memcpy,S as rust_zstd_wasm_shim_memmove,T as rust_zstd_wasm_shim_memset,E as rust_zstd_wasm_shim_qsort,H as zstdstreamdecoder_clear,J as zstdstreamdecoder_decode,P as zstdstreamdecoder_new};
