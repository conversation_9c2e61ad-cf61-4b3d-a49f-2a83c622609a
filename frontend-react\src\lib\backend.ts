// @ts-ignore
import SpectrumAudio from "../audio";
// @ts-ignore
import SpectrumWaterfall from "../waterfall";
// @ts-ignore
import SpectrumEvents from "../events";
// @ts-ignore
import initWrappers from "./wrappers";

import { nanoid } from "nanoid";

interface Settings {
  fft_result_size: number;
  total_bandwidth: number;
  basefreq: number;
}

let settings: Settings;

const location = window.location;
const baseUri = `${location.protocol.replace("http", "ws")}//${location.host}`;

export const waterfall = new SpectrumWaterfall(baseUri + "/waterfall");
export const audio = new SpectrumAudio(baseUri + "/audio");
export const events = new SpectrumEvents(baseUri + "/events");

export async function init(): Promise<void> {
  await initWrappers();
  await Promise.all([waterfall.init(), audio.init(), events.init()]);
  settings = audio.settings;
  const id = nanoid();
  [waterfall, audio, events].forEach((s) => {
    s.setUserID(id);
  });
}

export function frequencyToWaterfallOffset(frequency: number): number {
  const [waterfallL, waterfallR] = waterfall.getWaterfallRange();
  const frequencyOffset = frequency - FFTOffsetToFrequency(waterfallL);
  return (
    frequencyOffset /
    (((waterfallR - waterfallL) / settings.fft_result_size) *
      settings.total_bandwidth)
  );
}

export function waterfallOffsetToFrequency(offset: number): number {
  const [waterfallL, waterfallR] = waterfall.getWaterfallRange();
  const frequencyOffset =
    offset *
    ((waterfallR - waterfallL) / settings.fft_result_size) *
    settings.total_bandwidth;
  return frequencyOffset + FFTOffsetToFrequency(waterfallL);
}

export function frequencyToFFTOffset(frequency: number): number {
  const offset = (frequency - settings.basefreq) / settings.total_bandwidth;
  return offset * settings.fft_result_size;
}

export function FFTOffsetToFrequency(offset: number): number {
  const frequency =
    (offset / settings.fft_result_size) * settings.total_bandwidth;
  return frequency + settings.basefreq;
}

// Export types for use in components
export type { Settings };
export type AudioInstance = typeof audio;
export type WaterfallInstance = typeof waterfall;
export type EventsInstance = typeof events;
