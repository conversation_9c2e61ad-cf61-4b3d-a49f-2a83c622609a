{"version": 3, "sources": ["../../efficient-rolling-stats/index.js"], "sourcesContent": ["//if(require.main === module) { var  repl = require(\"repl\");repl.start({ useGlobal:true,  useColors:true, }); }\r\n\r\n\r\n//rolling min max in probably fast javascript, if you treat javascript as c it is fast as c, i.e. no object arrays\r\n/*\r\n\r\nThis is the algorithm from http://stackoverflow.com/a/12195098/466363:\r\n\r\n        at every step:\r\n\r\n          if (!Deque.Empty) and (Deque.Head.Index <= CurrentIndex - T) then \r\n             Deque.ExtractHead;\r\n          //Head is too old, it is leaving the window\r\n\r\n          while (!Deque.Empty) and (Deque.Tail.Value > CurrentValue) do\r\n             Deque.ExtractTail;\r\n          //remove elements that have no chance to become minimum in the window\r\n\r\n          Deque.AddTail(CurrentValue, CurrentIndex); \r\n          CurrentMin = Deque.Head.Value\r\n          //Head value is minimum in the current window\r\n          \r\n  \r\n*/\r\n// https://gist.github.com/shimondoodkin/f274d6e17c66a8b72779\r\n \r\nfunction RollingMin(WindowSize)// generator\r\n{\r\n    var DequeIndex=[],DequeValue=[],CurrentIndex=0,T=WindowSize;\r\n    function atEveryStepDo(CurrentValue)\r\n    {\r\n      if ( DequeIndex.length!==0 && DequeIndex[0] <= CurrentIndex - T ) \r\n      {\r\n         DequeIndex.shift();\r\n         DequeValue.shift();\r\n      }\r\n      //Head is too old, it is leaving the window\r\n \r\n      while ( DequeValue.length!==0 && DequeValue[DequeValue.length-1] > CurrentValue )\r\n      {\r\n         DequeIndex.pop();\r\n         DequeValue.pop();\r\n      }\r\n      //remove elements that have no chance to become minimum in the window\r\n \r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n      CurrentIndex++;\r\n      return DequeValue[0] //Head value is minimum in the current window\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequeValue.splice(0,DequeValue.length);CurrentIndex=0;};\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction RollingMax(WindowSize)// generator\r\n{\r\n    var DequeIndex=[],DequeValue=[],CurrentIndex=0,T=WindowSize;\r\n    function atEveryStepDo(CurrentValue)\r\n    {\r\n      if ( DequeIndex.length!==0 && DequeIndex[0] <= CurrentIndex - T ) \r\n      {\r\n         DequeIndex.shift();\r\n         DequeValue.shift();\r\n      }\r\n      //Head is too old, it is leaving the window\r\n \r\n      while ( DequeValue.length!==0 && DequeValue[DequeValue.length-1] < CurrentValue )\r\n      {\r\n         DequeIndex.pop();\r\n         DequeValue.pop();\r\n      }\r\n      //remove elements that have no chance to become maxbimum in the window\r\n \r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n      CurrentIndex++;\r\n      return DequeValue[0] //Head value is maximum in the current window\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequeValue.splice(0,DequeValue.length);CurrentIndex=0;};\r\n    return atEveryStepDo;\r\n}\r\n\r\n\r\nfunction RollingAvg(WindowSize)// generator\r\n{\r\n    var DequeValue=[],T=WindowSize,Sum=0,prev;\r\n    function atEveryStepDo(CurrentValue)\r\n    {\r\n      if ( DequeValue.length >= T ) \r\n      {\r\n         Sum-=DequeValue.shift();\r\n      }\r\n      //Head is too old, it is leaving the window\r\n      if(CurrentValue||CurrentValue===0) //don't break the sum on junk\r\n      {\r\n      DequeValue.push(CurrentValue); \r\n      Sum+=CurrentValue;\r\n      }\r\n\t  else return prev;\r\n      return prev=(DequeValue.length==0?0:Sum/DequeValue.length) //Head value is maximum in the current window\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){Sum=0;DequeValue.splice(0,DequeValue.length);};\r\n    return atEveryStepDo;\r\n}\r\n \r\n\r\nfunction RollingMinIndex(WindowSize)// generator\r\n{\r\n    var DequeIndex=[],DequeValue=[],T=WindowSize;\r\n    function atEveryStepDo(CurrentValue,CurrentIndex)\r\n    {\r\n      while ( DequeIndex.length!==0 && DequeIndex[0] <= CurrentIndex - T ) \r\n      {\r\n         DequeIndex.shift();\r\n         DequeValue.shift();\r\n      }\r\n      //Head is too old, it is leaving the window\r\n \r\n      while ( DequeValue.length!==0 && DequeValue[DequeValue.length-1] > CurrentValue )\r\n      {\r\n         DequeIndex.pop();\r\n         DequeValue.pop();\r\n      }\r\n      //remove elements that have no chance to become minimum in the window\r\n \r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n      return DequeValue[0] //Head value is minimum in the current window\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequeValue.splice(0,DequeValue.length);};\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction RollingMaxIndex(WindowSize)// generator\r\n{\r\n    var DequeIndex=[],DequeValue=[],T=WindowSize;\r\n    function atEveryStepDo(CurrentValue,CurrentIndex)\r\n    {\r\n      while ( DequeIndex.length!==0 && DequeIndex[0] <= CurrentIndex - T ) \r\n      {\r\n         DequeIndex.shift();\r\n         DequeValue.shift();\r\n      }\r\n      //Head is too old, it is leaving the window\r\n \r\n      while ( DequeValue.length!==0 && DequeValue[DequeValue.length-1] < CurrentValue )\r\n      {\r\n         DequeIndex.pop();\r\n         DequeValue.pop();\r\n      }\r\n      //remove elements that have no chance to become maxbimum in the window\r\n \r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n      return DequeValue[0] //Head value is maximum in the current window\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequeValue.splice(0,DequeValue.length);};\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction RollingAvgIndex(WindowSize)// generator\r\n{\r\n    var DequeIndex=[],DequeValue=[],T=WindowSize,Sum=0;\r\n    function atEveryStepDo(CurrentValue,CurrentIndex)\r\n    {\r\n      while ( DequeIndex.length!==0 && (DequeIndex[0] <= CurrentIndex - T) )\r\n      {\r\n         DequeIndex.shift();\r\n         Sum-=DequeValue.shift();\r\n      }\r\n      \r\n      //Head is too old, it is leaving the window\r\n      if(CurrentValue||CurrentValue===0)\r\n\t  {\r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n\r\n      Sum+=CurrentValue;\r\n\t  }\r\n  \t  else return prev;\r\n      return prev=(DequeValue.length==0?0:Sum/DequeValue.length) //Head value is maximum in the current window\r\n\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequeValue.splice(0,DequeValue.length);Sum=0;};\r\n    return atEveryStepDo;\r\n}\r\n\r\n\r\n\r\n\r\n//simple binary sorted array\r\n//add item: array.splice(sortedIndex(array, value),0,value);\r\n//remove item: var x=sortedIndex(array, value); if(array[x]==value)array.splice(x,1);\r\nfunction sortedIndex(array, value) {\r\n var low = 0,\r\n  high = array.length;\r\n\r\n while (low < high) {\r\n  var mid = low + high >>> 1;\r\n  if (array[mid] < value) low = mid + 1;\r\n  else high = mid;\r\n }\r\n return low;\r\n}\r\n \r\n\r\n// a simple idea to make an efficient algorithm for median\r\n// is not to realy on old index to remove values, \r\n// but add by value and remove by value, and keep values in order,\r\n// anyway it won't remove more values than inserted.\r\n//\r\n// complexity: log(n) add, log(n) remove, performance= 2*log(n)\r\nfunction RollingMedian(WindowSize)// generator , RollingMedian(WindowSize,DivideEven)\r\n{\r\n    var DequeValue=[], T=WindowSize, SortedValues=[], LsortedIndex=sortedIndex;\r\n\tvar prevmedian,findcenter=null,Sum=0,Sum2=0,Sum3=0,Sum4=0,findmoments=null//,DevideE=!!DivideEven; \r\n    function atEveryStepDo(CurrentValue)\r\n    {\r\n      if ( DequeValue.length >= T ) \r\n      {\r\n\t     //Head is too old, it is leaving the window\r\n         var value=DequeValue.shift();\r\n\t\t \r\n\t      var v=value,\r\n          vv=v*v,\r\n\t\t  vvv=vv*v,\r\n\t\t  vvvv=vvv*v;\r\n\t\t  \r\n\t\t Sum-=v;\r\n\t\t Sum2-=vv;\r\n\t\t Sum3-=vvv;\r\n\t\t Sum4-=vvvv;\r\n\t\t \r\n\t     var x=LsortedIndex(SortedValues, value); if(SortedValues[x]==value)SortedValues.splice(x,1);\r\n      }\r\n\t  \r\n\t  if(CurrentValue||CurrentValue===0)\r\n\t  {\r\n      DequeValue.push(CurrentValue); \r\n\t  SortedValues.splice(LsortedIndex(SortedValues, CurrentValue),0,CurrentValue);\r\n\t  \r\n\t  findcenter=null;\r\n\t  findmoments=null;\r\n\r\n\r\n\t  var v=CurrentValue,\r\n          vv=v*v,\r\n\t\t  vvv=vv*v,\r\n\t\t  vvvv=vvv*v;\r\n\t  \r\n\t  \r\n\t  Sum+=v;\r\n\t  Sum2+=vv;\r\n\t  Sum3+=vvv;\r\n\t  Sum4+=vvvv;\r\n\t  }\r\n\t  else\r\n\t  return prevmedian;\r\n\t  if(SortedValues.length ==0)return prevmedian;\r\n\t  \r\n\t  if(SortedValues.length & 1) // if even\r\n \t   return prevmedian=SortedValues[((SortedValues.length-1)>>> 1)] // index=((SortedValues.length -1 for devide by two))/2)+1 add one back -1 for 0 based index, >>> 1 is faster devide by two by bit shifting\r\n\t  else\r\n\t  {\r\n\t   //if odd\r\n\t   var half=(SortedValues.length>>> 1)-1;// index = (SortedValues.length>>> 1) -1 for zero based index\r\n\t   //if(DevideE)\r\n\t    return prevmedian=(SortedValues[half]+SortedValues[half+1])/2; // correct implementation\r\n\t   //else    return SortedValues[half]; //i don't care,same same for my usage\r\n\t  }\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n\r\n\t//atEveryStepDo.setDivideEven=function(DivideEven){DevideE=DivideEven};\r\n\r\n    atEveryStepDo.reset=function(){\r\n       DequeValue.splice(0,DequeValue.length);\r\n       SortedValues.splice(0,SortedValues.length);findcenter=null;Sum=0;Sum2=0;Sum3=0;Sum4=0;findmoments=null;\r\n\t};\r\n\t\r\n\tatEveryStepDo.avg=function(){   return Sum/DequeValue.length };\r\n\tatEveryStepDo.sum=function(){   return Sum };\r\n\t\r\n\tatEveryStepDo.min=function(){   return SortedValues[0] };\r\n\r\n\tatEveryStepDo.q1=function(){\r\n\r\n\t  if(SortedValues.length==1)return SortedValues[0] \r\n\t  if(SortedValues.length==2)return SortedValues[0]*0.75+SortedValues[1]*0.25\r\n\t  if(SortedValues.length==3)return SortedValues[0]*0.5+SortedValues[1]*0.5\r\n\t  \r\n\t  if((SortedValues.length-1)%4==0)\r\n\t  {\r\n\t   var n=(SortedValues.length-1)>>2\r\n\t   return   SortedValues[n-1  ]*0.25+SortedValues[n    ]*0.75 \r\n\t   //return SortedValues[n+0-1]*0.25+SortedValues[n+1-1]*0.75 \r\n\t  }\r\n\t  \r\n\t  if((SortedValues.length-3)%4==0)\r\n\t  {\r\n\t   var n=(SortedValues.length-3)>>2\r\n\t   return   SortedValues[n    ]*0.75+SortedValues[n+1  ]*0.25 \r\n\t   //return SortedValues[n+1-1]*0.75+SortedValues[n+2-1]*0.25 \r\n\t  }\r\n\t};\r\n\t\r\n\t\r\n\tatEveryStepDo.moments_avg=function() //m1 - not useful\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.average;\r\n\t}\r\n\t\r\n\tatEveryStepDo.variance=function()//m2\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.variance;\r\n\t}\r\n\t\r\n\tatEveryStepDo.standardDeviation=function() //sqrt(m2)\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.standardDeviation;\r\n\t}\r\n\t\r\n\tatEveryStepDo.skew=function() // using m2 , m3\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.skew;\r\n\t}\r\n\t\r\n\tatEveryStepDo.kurtosis=function() // using m2, m3 ,m4\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.kurtosis;\r\n\t}\r\n\t\r\n\tatEveryStepDo.exkurtosis=function() // using kurtosis - 3\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.exkurtosis;\r\n\t}\r\n\t\r\n\t\r\n\tatEveryStepDo.moments=function()\r\n\t{\r\n\t\tvar o={};\r\n\t\tif(this.c>0)\r\n\t\t{\r\n\t\t\t//e(x), e(x*x), e(x*x*x), and e(x*x*x*x)\r\n\t\t\tvar c=DequeValue.length,\r\n\t\t\tex = Sum/c,\r\n\t\t\texx = Sum2/c,\r\n\t\t\texxx = Sum3/c,\r\n\t\t\texxxx = Sum4/c,\r\n\t\t\t//central moments:\r\n\t\t\tm1 = ex,\r\n\t\t\tm2 = Sum2/c - ex;\r\n\t\t\tm3 = exxx - 3*exx*ex + 2 *ex*ex*ex;\r\n\t\t\tm4 = exxxx - 3*exxx*ex + 6*exx*ex*ex - -3*ex*ex*ex*ex ;\r\n\t\t\to.average = m1;\r\n\t\t\to.variance = m2;\r\n\t\t\to.standardDeviation= Math.pow(o.variance,.5);\r\n\t\t\tif(c>2){\r\n\t\t\t\t//http://www.amstat.org/publications/jse/v19n2/doane.pdf\r\n\t\t\t\t//http://en.wikipedia.org/wiki/Skewness\r\n\t\t\t\to.skew = Math.pow(c*(c-1),.5)/(c-2) * m3 / Math.pow(m2,1.5);\r\n\t\t\t}\r\n\t\t\tif(m2>0){\r\n\t\t\t\t//http://en.wikipedia.org/wiki/Kurtosis\r\n\t\t\t\to.exkurtosis = m4 / m2 / m2 - 3\r\n\t\t\t\to.kurtosis = m4 / m2 / m2 \r\n\t\t\t}\r\n\t\t}\r\n\t\treturn o;\r\n\t}\r\n\t\r\n\t\r\n\tatEveryStepDo.median=function(){   return prevmedian }; //q2\r\n\t\r\n\tatEveryStepDo.q3=function(){\r\n \t  if(SortedValues.length==1)return SortedValues[0]\r\n\t  if(SortedValues.length==2)return SortedValues[0]*0.25+SortedValues[1]*0.75\r\n\t  if(SortedValues.length==3)return SortedValues[1]*0.5+SortedValues[2]*0.5\r\n\t  \r\n\t  if((SortedValues.length-1)%4==0)\r\n\t  {\r\n\t   var n3=((SortedValues.length-1)>>2)*3\r\n\t   return   SortedValues[n3    ]*0.75+SortedValues[n3+1  ]*0.25 \r\n\t   //return SortedValues[n3+1-1]*0.75+SortedValues[n3+2-1]*0.25 \r\n\t  }\r\n\t  \r\n\t  if((SortedValues.length-3)%4==0)\r\n\t  {\r\n\t   var n3=((SortedValues.length-3)>>2)*3\r\n\t   return   SortedValues[n3+1  ]*0.25+SortedValues[n3+2  ]*0.75 \r\n\t   //return SortedValues[n3+2-1]*0.25+SortedValues[n3+3-1]*0.75 \r\n\t  }\r\n\t};\r\n\t\r\n\tatEveryStepDo.max=function(){   return SortedValues[SortedValues.length-1] };\r\n\tatEveryStepDo.center=function(){   return (SortedValues[0]+SortedValues[SortedValues.length-1])/2 };\r\n\t\r\n\t//atEveryStepDo.nabovecenter=function(){\r\n\t// if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t// return SortedValues.length-findcenter-1;\r\n\t//}\r\n\t//atEveryStepDo.nbelowcenter=function(){\r\n\t// if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t// return findcenter-1;\r\n\t//}\r\n\t\r\n\t//http://books.google.co.il/books?id=4HrJs2o9C5YC&pg=PA32&lpg=PA32&dq=q3+q2+q2+q1+skewness&source=bl&ots=eD24ehhNoz&sig=xxhMOFVL5JngB5JPi5WieIRTCaI&hl=en&sa=X&ei=xfVQVIPzFOLnywPM9YLIAw&ved=0CEoQ6AEwBw#v=onepage&q=Q.D.&f=false\r\n\t\r\n\tatEveryStepDo.medianskew=function(){ //medianskew=(max - median)-(median - min)\r\n     return  (SortedValues[SortedValues.length-1]-prevmedian)-(prevmedian-SortedValues[0]);\r\n\t}\r\n\t\r\n\tatEveryStepDo.medianskew_bowleys_coef=function(){ //medianskew=(max - median)-(median - min)\r\n     return  ((SortedValues[SortedValues.length-1]-prevmedian)*(prevmedian-SortedValues[0]))/(SortedValues[SortedValues.length-1]-SortedValues[0]);\r\n\t}\r\n\t\r\n\tatEveryStepDo.mediankurt=function(){ //q.d=quartile deviatin=(q3-q1)/2, mediankurt=q.d/(p90 - p10), mediankurt=((q3-q1)/2\t)/(p90 - p10) \t\r\n\t var p90=Math.round((SortedValues.length-1)*0.9);\r\n\t var p10=Math.round((SortedValues.length-1)*0.1);\r\n     return  ((SortedValues[SortedValues.length-1]-prevmedian)*(prevmedian-SortedValues[0]))/(SortedValues[p90]-SortedValues[p10]);\r\n\t}\r\n\t\r\n\tatEveryStepDo.pabovecenter=function(){\r\n\t if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t return (SortedValues.length-1-findcenter)/(SortedValues.length-1);\r\n\t}\r\n\tatEveryStepDo.pbelowcenter=function(){\r\n\t if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t return findcenter/(SortedValues.length-1);\r\n\t}\r\n\t\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction RollingMedianIndex(WindowSize)// generator\r\n{\r\n    var DequeValue=[], DequeIndex=[], T=WindowSize, SortedValues=[], LsortedIndex=sortedIndex;\r\n\tvar prevmedian,findcenter=null,Sum=0,Sum2=0,Sum3=0,Sum4=0,findmoments=null//,DevideE=!!DivideEven; \r\n    function atEveryStepDo(CurrentValue,CurrentIndex)\r\n    {\r\n      while ( DequeIndex.length!==0 && (DequeIndex[0] <= CurrentIndex - T) )  \r\n      {\r\n\t     //Head is too old, it is leaving the window\r\n         var index=DequeIndex.shift();\r\n\t\t var value=DequeValue.shift();\r\n\t\t \r\n\t      var v=value,\r\n          vv=v*v,\r\n\t\t  vvv=vv*v,\r\n\t\t  vvvv=vvv*v;\r\n\t\t  \r\n\t\t Sum-=v;\r\n\t\t Sum2-=vv;\r\n\t\t Sum3-=vvv;\r\n\t\t Sum4-=vvvv;\r\n\t\t \r\n\t     var x=LsortedIndex(SortedValues, value); if(SortedValues[x]==value)SortedValues.splice(x,1);\r\n      }\r\n\t  \r\n\t  \r\n\t  if(CurrentValue||CurrentValue===0)\r\n\t  {\r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n\t  SortedValues.splice(LsortedIndex(SortedValues, CurrentValue),0,CurrentValue);\r\n\t  \r\n\t  findcenter=null;\r\n\t  findmoments=null;\r\n\r\n\r\n\t  var v=CurrentValue,\r\n          vv=v*v,\r\n\t\t  vvv=vv*v,\r\n\t\t  vvvv=vvv*v;\r\n\t  \r\n\t  \r\n\t  Sum+=v;\r\n\t  Sum2+=vv;\r\n\t  Sum3+=vvv;\r\n\t  Sum4+=vvvv;\r\n\t  }\r\n\t  else\r\n\t  return prevmedian;\r\n\t  if(SortedValues.length ==0)return prevmedian;\r\n\t  \r\n\t  if(SortedValues.length & 1) // if even\r\n \t   return prevmedian=SortedValues[((SortedValues.length-1)>>> 1)] // index=((SortedValues.length -1 for devide by two))/2)+1 add one back -1 for 0 based index, >>> 1 is faster devide by two by bit shifting\r\n\t  else\r\n\t  {\r\n\t   //if odd\r\n\t   var half=(SortedValues.length>>> 1)-1;// index = (SortedValues.length>>> 1) -1 for zero based index\r\n\t   //if(DevideE)\r\n\t    return prevmedian=(SortedValues[half]+SortedValues[half+1])/2; // correct implementation\r\n\t   //else    return SortedValues[half]; //i don't care,same same for my usage\r\n\t  }\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n\r\n\t//atEveryStepDo.setDivideEven=function(DivideEven){DevideE=DivideEven};\r\n\r\n    atEveryStepDo.reset=function(){\r\n       DequeValue.splice(0,DequeValue.length);DequeIndex.splice(0,DequeIndex.length);\r\n       SortedValues.splice(0,SortedValues.length);findcenter=null;Sum=0;Sum2=0;Sum3=0;Sum4=0;findmoments=null;\r\n\t};\r\n\t\r\n\tatEveryStepDo.avg=function(){   return Sum/DequeValue.length };\r\n\tatEveryStepDo.sum=function(){   return Sum };\r\n\t\r\n\tatEveryStepDo.min=function(){   return SortedValues[0] };\r\n\r\n\tatEveryStepDo.q1=function(){\r\n\r\n\t  if(SortedValues.length==1)return SortedValues[0] \r\n\t  if(SortedValues.length==2)return SortedValues[0]*0.75+SortedValues[1]*0.25\r\n\t  if(SortedValues.length==3)return SortedValues[0]*0.5+SortedValues[1]*0.5\r\n\t  \r\n\t  if((SortedValues.length-1)%4==0)\r\n\t  {\r\n\t   var n=(SortedValues.length-1)>>2\r\n\t   return   SortedValues[n-1  ]*0.25+SortedValues[n    ]*0.75 \r\n\t   //return SortedValues[n+0-1]*0.25+SortedValues[n+1-1]*0.75 \r\n\t  }\r\n\t  \r\n\t  if((SortedValues.length-3)%4==0)\r\n\t  {\r\n\t   var n=(SortedValues.length-3)>>2\r\n\t   return   SortedValues[n    ]*0.75+SortedValues[n+1  ]*0.25 \r\n\t   //return SortedValues[n+1-1]*0.75+SortedValues[n+2-1]*0.25 \r\n\t  }\r\n\t};\r\n\t\r\n\t\r\n\tatEveryStepDo.moments_avg=function() //m1 - not useful\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.average;\r\n\t}\r\n\t\r\n\tatEveryStepDo.variance=function()//m2\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.variance;\r\n\t}\r\n\t\r\n\tatEveryStepDo.standardDeviation=function() //sqrt(m2)\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.standardDeviation;\r\n\t}\r\n\t\r\n\tatEveryStepDo.skew=function() // using m2 , m3\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.skew;\r\n\t}\r\n\t\r\n\tatEveryStepDo.kurtosis=function() // using m2, m3 ,m4\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.kurtosis;\r\n\t}\r\n\t\r\n\tatEveryStepDo.exkurtosis=function() // using kurtosis - 3\r\n\t{\r\n\t  if(findmoments===null) findmoments=atEveryStepDo.moments();\r\n\t  return findmoments.exkurtosis;\r\n\t}\r\n\t\r\n\t\r\n\tatEveryStepDo.moments=function()\r\n\t{\r\n\t\tvar o={};\r\n\t\tif(this.c>0)\r\n\t\t{\r\n\t\t\t//e(x), e(x*x), e(x*x*x), and e(x*x*x*x)\r\n\t\t\tvar c=DequeValue.length,\r\n\t\t\tex = Sum/c,\r\n\t\t\texx = Sum2/c,\r\n\t\t\texxx = Sum3/c,\r\n\t\t\texxxx = Sum4/c,\r\n\t\t\t//central moments:\r\n\t\t\tm1 = ex,\r\n\t\t\tm2 = Sum2/c - ex;\r\n\t\t\tm3 = exxx - 3*exx*ex + 2 *ex*ex*ex;\r\n\t\t\tm4 = exxxx - 3*exxx*ex + 6*exx*ex*ex - -3*ex*ex*ex*ex ;\r\n\t\t\to.average = m1;\r\n\t\t\to.variance = m2;\r\n\t\t\to.standardDeviation= Math.pow(o.variance,.5);\r\n\t\t\tif(c>2){\r\n\t\t\t\t//http://www.amstat.org/publications/jse/v19n2/doane.pdf\r\n\t\t\t\t//http://en.wikipedia.org/wiki/Skewness\r\n\t\t\t\to.skew = Math.pow(c*(c-1),.5)/(c-2) * m3 / Math.pow(m2,1.5);\r\n\t\t\t}\r\n\t\t\tif(m2>0){\r\n\t\t\t\t//http://en.wikipedia.org/wiki/Kurtosis\r\n\t\t\t\to.exkurtosis = m4 / m2 / m2 - 3\r\n\t\t\t\to.kurtosis = m4 / m2 / m2 \r\n\t\t\t}\r\n\t\t}\r\n\t\treturn o;\r\n\t}\r\n\t\r\n\t\r\n\tatEveryStepDo.median=function(){   return prevmedian }; //q2\r\n\t\r\n\tatEveryStepDo.q3=function(){\r\n \t  if(SortedValues.length==1)return SortedValues[0]\r\n\t  if(SortedValues.length==2)return SortedValues[0]*0.25+SortedValues[1]*0.75\r\n\t  if(SortedValues.length==3)return SortedValues[1]*0.5+SortedValues[2]*0.5\r\n\t  \r\n\t  if((SortedValues.length-1)%4==0)\r\n\t  {\r\n\t   var n3=((SortedValues.length-1)>>2)*3\r\n\t   return   SortedValues[n3    ]*0.75+SortedValues[n3+1  ]*0.25 \r\n\t   //return SortedValues[n3+1-1]*0.75+SortedValues[n3+2-1]*0.25 \r\n\t  }\r\n\t  \r\n\t  if((SortedValues.length-3)%4==0)\r\n\t  {\r\n\t   var n3=((SortedValues.length-3)>>2)*3\r\n\t   return   SortedValues[n3+1  ]*0.25+SortedValues[n3+2  ]*0.75 \r\n\t   //return SortedValues[n3+2-1]*0.25+SortedValues[n3+3-1]*0.75 \r\n\t  }\r\n\t};\r\n\t\r\n\tatEveryStepDo.max=function(){   return SortedValues[SortedValues.length-1] };\r\n\tatEveryStepDo.center=function(){   return (SortedValues[0]+SortedValues[SortedValues.length-1])/2 };\r\n\t\r\n\t//atEveryStepDo.nabovecenter=function(){\r\n\t// if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t// return SortedValues.length-findcenter-1;\r\n\t//}\r\n\t//atEveryStepDo.nbelowcenter=function(){\r\n\t// if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t// return findcenter-1;\r\n\t//}\r\n\t\r\n\t//http://books.google.co.il/books?id=4HrJs2o9C5YC&pg=PA32&lpg=PA32&dq=q3+q2+q2+q1+skewness&source=bl&ots=eD24ehhNoz&sig=xxhMOFVL5JngB5JPi5WieIRTCaI&hl=en&sa=X&ei=xfVQVIPzFOLnywPM9YLIAw&ved=0CEoQ6AEwBw#v=onepage&q=Q.D.&f=false\r\n\t\r\n\tatEveryStepDo.medianskew=function(){ //medianskew=(max - median)-(median - min)\r\n     return  (SortedValues[SortedValues.length-1]-prevmedian)-(prevmedian-SortedValues[0]);\r\n\t}\r\n\t\r\n\tatEveryStepDo.medianskew_bowleys_coef=function(){ //medianskew=(max - median)-(median - min)\r\n     return  ((SortedValues[SortedValues.length-1]-prevmedian)*(prevmedian-SortedValues[0]))/(SortedValues[SortedValues.length-1]-SortedValues[0]);\r\n\t}\r\n\t\r\n\tatEveryStepDo.mediankurt=function(){ //q.d=quartile deviatin=(q3-q1)/2, mediankurt=q.d/(p90 - p10), mediankurt=((q3-q1)/2\t)/(p90 - p10) \t\r\n\t var p90=Math.round((SortedValues.length-1)*0.9);\r\n\t var p10=Math.round((SortedValues.length-1)*0.1);\r\n     return  ((SortedValues[SortedValues.length-1]-prevmedian)*(prevmedian-SortedValues[0]))/(SortedValues[p90]-SortedValues[p10]);\r\n\t}\r\n\t\r\n\tatEveryStepDo.pabovecenter=function(){\r\n\t if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t return (SortedValues.length-1-findcenter)/(SortedValues.length-1);\r\n\t}\r\n\tatEveryStepDo.pbelowcenter=function(){\r\n\t if(findcenter===null) findcenter=LsortedIndex(SortedValues, (SortedValues[0]+SortedValues[SortedValues.length-1])/2 );\r\n\t return findcenter/(SortedValues.length-1);\r\n\t}\r\n\t\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction RollingSumPerIndex(WindowSize,UsualIndexSkipBetweenOccations)// generator\r\n{\r\n    var DequeIndex=[],DequeValue=[],T=WindowSize,Sum=0,PrevIndex=false,U=UsualIndexSkipBetweenOccations;\r\n    function atEveryStepDo(CurrentValue,CurrentIndex)\r\n    {\r\n      while ( DequeIndex.length!==0 && (DequeIndex[0] <= CurrentIndex - T) && DequeIndex[0]!=CurrentIndex)  // do not remove current index so you will be able to make an avarage to not devide by zero, because current-current =zero assumes raising order in the index and window size of at last two\r\n      {\r\n         PrevIndex=DequeIndex.shift();\r\n         Sum-=DequeValue.shift();\r\n      }\r\n      if(PrevIndex===false)PrevIndex=CurrentIndex-U;\r\n      \r\n      //Head is too old, it is leaving the window\r\n      \r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n\r\n      Sum+=CurrentValue;\r\n      var Div=CurrentIndex-PrevIndex;\r\n\r\n      if(Div==0)Div=U;\r\n      return Sum/Div //Head value is minimum in the current window\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.setUsualIndexSkipBetweenOccations=function(UsualIndexSkipBetweenOccations){U=UsualIndexSkipBetweenOccations};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequeValue.splice(0,DequeValue.length);Sum=0;PrevIndex=false;};\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction Delay(WindowSize,UndefinedValue)\r\n{\r\n    var DequeValue=[],T=WindowSize,U=UndefinedValue;\r\n    function atEveryStepDo(CurrentValue)\r\n    {\r\n      var ret=U;\r\n      if( DequeValue.length== T ) \r\n      {\r\n          ret=DequeValue.shift();\r\n      }\r\n      DequeValue.push(CurrentValue); \r\n      return ret;\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.setUndefinedValue=function(WindowSize){U=UndefinedValue};\r\n    atEveryStepDo.reset=function(WindowSize){DequeValue.splice(0,DequeValue.length);};\r\n    return atEveryStepDo;\r\n}\r\nfunction HideFirst(WindowSize,UndefinedValue)\r\n{\r\n    var DequeValue=1,T=WindowSize+1,U=UndefinedValue;\r\n    function atEveryStepDo(CurrentValue)\r\n    {\r\n      if( DequeValue== T ) \r\n      {\r\n        return  CurrentValue;\r\n      }\r\n      DequeValue++;\r\n      return U;\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize+1};\r\n    atEveryStepDo.setUndefinedValue=function(WindowSize){U=UndefinedValue};\r\n    atEveryStepDo.reset=function(){DequeValue=0;};\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction DelayIndex(WindowSize,UsualIndexSkipBetweenOccations,UndefinedValue)// generator, expects some high frequency of time inserts because if there will be a delay or no inserts it will stop working;\r\n{\r\n    var DequeIndex=[],DequeValue=[],T=WindowSize,PrevIndex=false,PrevValue=UndefinedValue,U=UsualIndexSkipBetweenOccations;\r\n    function atEveryStepDo(CurrentValue,CurrentIndex)\r\n    {\r\n      DequeIndex.push(CurrentIndex); \r\n      DequeValue.push(CurrentValue); \r\n      if(PrevIndex===false)PrevIndex=CurrentIndex-U;\r\n      //take first to fall off and throw away the rest\r\n      if ( DequeIndex.length!==0 && (DequeIndex[0] <= CurrentIndex - T) )  \r\n      {\r\n         PrevIndex=DequeIndex.shift();\r\n         PrevValue=DequeValue.shift();\r\n         while ( DequeIndex.length!==0 && (DequeIndex[0] <= CurrentIndex - T) )  \r\n         {\r\n           DequeIndex.shift();\r\n           DequeValue.shift();\r\n         }\r\n      }\r\n      return PrevValue;\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.setUsualIndexSkipBetweenOccations=function(UsualIndexSkipBetweenOccations){U=UsualIndexSkipBetweenOccations};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequeValue.splice(0,DequeValue.length);PrevIndex=false;PrevValue=UndefinedValue};\r\n    return atEveryStepDo;\r\n}\r\n\r\n\r\nfunction PositiveLately(WindowSize)\r\n{\r\n    var T=WindowSize,PositiveCount=-1;\r\n    function atEveryStepDo(CurrentValue)\r\n    {\r\n      //if(CurrentValue<0)PositiveCount=-1; // should not reset on negative\r\n\t  if(CurrentValue>0)PositiveCount=0;// should reset each time\r\n\t  if(PositiveCount>=0)PositiveCount++;\r\n\t  if(PositiveCount>WindowSize)PositiveCount=-1;\r\n      return PositiveCount>0;\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){PositiveCount=-1};\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction PositiveLatelyIndex(WindowSize)// generator, expects some high frequency of time inserts because if there will be a delay or no inserts it will stop working;\r\n{\r\n    var T=WindowSize,PrevIndex=false,PositiveCount=-1;\r\n    function atEveryStepDo(CurrentValue,CurrentIndex)\r\n    {\r\n      //if(CurrentValue<0)PositiveCount=-1;\r\n\t  if(CurrentValue>0){PrevIndex=CurrentIndex;PositiveCount=0;}// should reset each time\r\n\t  if(PositiveCount>=0)PositiveCount++;\r\n\t  if( PrevIndex!=false && (PrevIndex <= CurrentIndex - T) ) PositiveCount=-1;\r\n      return PositiveCount>0;\r\n    }\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){PrevIndex=false;PositiveCount=-1};\r\n    return atEveryStepDo;\r\n}\r\n\r\n\r\n// choosing PreRoundingMultiplier, if PreRoundingMultiplier=30 than bins will be like = 0.1,0.2,0.3 .  can be 100 for 0.01 bins,  can be  1000 for 0.001 bins\r\n//\r\n\r\n// this histogram do not remove anything just accamulates all, until you do hist.reset()\r\nfunction Histogram(PreRoundingMultiplier)// generator\r\n{\r\n    var RD=PreRoundingMultiplier,hist= new Map();;\r\n    function atEveryStepDo(CurrentPosition,CurrentAmount=1)\r\n    {\r\n      //Head is too old, it is leaving the window\r\n\t  \r\n\t  var CurrentPositionRound=parseFloat((  Math.round( CurrentPosition*RD )/RD  ).toFixed(12));\r\n\t  \t  \r\n\t  if(hist.has(CurrentPositionRound))\r\n\t    hist.set( CurrentPositionRound, parseFloat((  hist.get(CurrentPositionRound)+CurrentAmount  ).toFixed(12)) ); \r\n\t  else\r\n\t    hist.set( CurrentPositionRound, CurrentAmount );\r\n\t  \r\n      return hist \r\n    }\r\n\tatEveryStepDo.hist=hist;\r\n    atEveryStepDo.reset=function(){ hist.clear() };\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction RollingHistogram(WindowSize,PreRoundingMultiplier)// generator\r\n{\r\n    var DequePosition=[],DequeAmount=[],T=WindowSize,RD=PreRoundingMultiplier,hist= new Map();;\r\n    function atEveryStepDo(CurrentPosition,CurrentAmount=1)\r\n    {\r\n      if ( DequePosition.length >= T ) \r\n      {\r\n        var prevpos=DequePosition.shift();\r\n        var prevamount=DequeAmount.shift();\r\n\t\t\r\n\t\tvar newamount=parseFloat((   hist.get(prevpos)-prevamount   ).toFixed(12));\r\n        if(newamount!==0)\r\n\t\t\thist.set(prevpos , newamount); \r\n\t\telse\r\n\t\t\thist.delete(prevpos);\r\n      }\r\n      //Head is too old, it is leaving the window\r\n\t  \r\n\t  var CurrentPositionRound=parseFloat((  Math.round( CurrentPosition*RD )/RD  ).toFixed(12));\r\n\t  \r\n\t  DequePosition.push(CurrentPositionRound);\r\n\t  DequeAmount.push(CurrentAmount);\r\n\t  \r\n\t  if(hist.has(CurrentPositionRound))\r\n\t    hist.set( CurrentPositionRound, parseFloat((  hist.get(CurrentPositionRound)+CurrentAmount  ).toFixed(12)) ); \r\n\t  else\r\n\t    hist.set( CurrentPositionRound, CurrentAmount );\r\n\t  \r\n      return hist \r\n    }\r\n\tatEveryStepDo.hist=hist;\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){Sum=0;DequePosition.splice(0,DequePosition.length);};\r\n    return atEveryStepDo;\r\n}\r\n\r\nfunction RollingHistogramIndex(WindowSize,PreRoundingMultiplier)// generator\r\n{\r\n    var DequeIndex=[],DequePosition=[],DequeAmount=[],T=WindowSize,RD=PreRoundingMultiplier,hist= new Map();\r\n    function atEveryStepDo(CurrentPosition,CurrentIndex,CurrentAmount=1)\r\n    {\r\n\t  if( (!CurrentAmount&&CurrentAmount!==0) || (!CurrentPosition&&CurrentPosition!==0) ) return hist;\r\n      while ( DequeIndex.length!==0 && (DequeIndex[0] <= CurrentIndex - T) )\r\n      {\r\n            DequeIndex.shift();\r\n        var prevpos=DequePosition.shift();\r\n        var prevamount=DequeAmount.shift();\r\n\t\t\r\n\t\tif(!prevpos && prevpos!==0)console.log('nana1',{prevamount,prevpos})\r\n\t\tif(!prevamount && prevamount!==0)console.log('nana2',{prevamount,prevpos})\r\n\t\t\t\r\n\t\tif(hist.has(prevpos))\r\n\t\t{\r\n\t\t\tvar newamount=parseFloat((   (hist.get(prevpos))-prevamount   ).toFixed(12));\r\n\t\t\tif(!newamount && newamount!==0)console.log('nana3',{newamount,\tprevamount,prevpos})\r\n\r\n\t\t\tif(newamount!==0)\r\n\t\t\t\thist.set(prevpos , newamount); \r\n\t\t\telse\r\n\t\t\t\thist.delete(prevpos);\r\n\t\t}\r\n      }\r\n      \r\n      //Head is too old, it is leaving the window\r\n      \r\n\t  var CurrentPositionRound=parseFloat((  Math.round( CurrentPosition*RD )/RD  ).toFixed(12));\r\n\t  \r\n\t  DequePosition.push(CurrentPositionRound);\r\n\t  DequeAmount.push(CurrentAmount);\r\n\t  DequeIndex.push(CurrentIndex);\r\n\t  \r\n\t  if(hist.has(CurrentPositionRound))\r\n\t  {\r\n\t\tlet newamount=parseFloat((  hist.get(CurrentPositionRound)+CurrentAmount  ).toFixed(12))\r\n\t    hist.set( CurrentPositionRound, newamount   ); \r\n\t  }\r\n\t  else\r\n\t    hist.set( CurrentPositionRound, CurrentAmount );\r\n\t  \r\n      return hist \r\n    }\r\n\tatEveryStepDo.hist=hist;\r\n    atEveryStepDo.setWindowSize=function(WindowSize){T=WindowSize};\r\n    atEveryStepDo.reset=function(){DequeIndex.splice(0,DequeIndex.length);DequePosition.splice(0,DequePosition.length);Sum=0;};\r\n    return atEveryStepDo;\r\n}\r\n\r\n\r\n\r\n//example1: // you can compose all sorts of functions like this, this is a simple one\r\n/*\r\nvar Stats=require('efficient-rolling-stats');\r\n//useful: \r\n// var stats=Stats.AllStats(101,50,15,0.25,7.5) // sample for 15 minutes and usually the input is every 15 seconds so the first input will be more or less not outlier,delay the timed input by 7.5 minutes\r\n//for testing:\r\nvar stats=Stats.AllStats(11,5,1,0.25,0.5) // sample for 15 minutes and usually the input is every 15 seconds so the first input will be more or less not outlier,delay the timed input by 7.5 minutes\r\nsetInterval(function(){stats(Math.random()*100,new Date().getTime()/60000)},1500) // input in minutes,  than also the configuratin arguments can be in minutes\r\nstats(Math.random()*100,new Date().getTime()/60000)\r\n*/    \r\n\r\n\r\nvar Stats=exports;\r\nfunction AllStats(size,delay,timesize,usualtime,timedelay)\r\n{\r\n   var list=[],add=function(v) { list.push(v); return v; };\r\n\r\n    var avgtime    = add(  Stats.RollingAvg(size)    )\r\n   ,stdev          = add(  Stats.RollingAvg(size)    )\r\n   ,zavg           = add(  Stats.RollingAvg(size)    )\r\n   ,median         = add(  Stats.RollingMedian(size) )\r\n   ,mstdev         = add(  Stats.RollingAvg(size)    )\r\n   ,mzavg          = add(  Stats.RollingAvg(size)    )\r\n   ,tzavg          = add(  Stats.RollingAvgIndex(timesize)    )\r\n   ,tstdev         = add(  Stats.RollingAvgIndex(timesize)    )\r\n   ,tmedian        = add(  Stats.RollingMedianIndex(timesize) )\r\n   ,tmzavg         = add(  Stats.RollingAvgIndex(timesize)    )\r\n   ,tmstdev        = add(  Stats.RollingAvgIndex(timesize)    )\r\n   ,tsum           = add(  Stats.RollingSumPerIndex(timesize,usualtime)  ) // may be its called momentum or speed\r\n   ,value_delay    = add(  Stats.Delay(delay)                            ) // because i can't move the indicators forewared but i can move the number backwards so it will match with the lagging indicators\r\n   ,tvalue_delay   = add(  Stats.DelayIndex(timedelay,usualtime)         )\r\n   ,index_delay    = add(  Stats.Delay(delay)                            ) // add the coresponding index to the delaied value, the other option is not to dalay anything and offset it the presentation\r\n   ,tindex_delay   = add(  Stats.DelayIndex(timedelay,usualtime)         )\r\n   ,prev=false\r\n   ,count=0\r\n   ,tcount=0;\r\n   \r\n    function stats(n,t)\r\n    {\r\n     var o={};\r\n     \r\n     o.median=median(n)\r\n     o.min=median.min()\r\n     o.max=median.max()\r\n\t //q0 is min\r\n     o.q1=median.q1()\r\n\t //q2 is median\r\n\t o.q3=median.q3()\r\n\t //q4 is max\r\n     o.avg=median.avg()\r\n     \r\n     o.stdev=Math.sqrt(stdev(Math.pow(n-o.avg,2)))\r\n     o.z=o.stdev==0?0:(n-o.avg)/o.stdev\r\n     o.zavg=zavg(o.z)\r\n     \r\n     o.mstdev=Math.sqrt(mstdev(Math.pow(n-o.median,2)))\r\n     o.mz=o.mstdev==0?0: 0.6745 *(n-o.median)/o.mstdev // mz> 3.5 = outlier http://www.itl.nist.gov/div898/handbook/eda/section3/eda35h.htm , http://stackoverflow.com/questions/22354094/pythonic-way-of-detecting-outliers-in-one-dimensional-observation-data, http://www.itl.nist.gov/div898/handbook/index.htm , idea from: https://www.npmjs.org/package/stats-analysis , It is possible to Calculate, median absolute deviation, Outlier Detection (using Iglewicz and Hoaglin's method) MADz>3.5, Outlier Filter / Removal\r\n     o.mzavg=mzavg(o.mz)\r\n     \r\n     o.tmedian=tmedian(n,t)\r\n     o.tmin=tmedian.min()\r\n     o.tmax=tmedian.max()\r\n\t o.tavg=tmedian.avg()\r\n     o.tcenter=tmedian.center()\r\n\t //q0 is min\r\n     o.tq1=tmedian.q1()\r\n\t //q2 is median\r\n\t o.tq3=tmedian.q3()\r\n\t //q4 is max\r\n     o.tsum=tsum(n,t)\r\n\t \r\n     o.tstdev=Math.sqrt(tstdev(Math.pow(n-o.tavg,2),t))\r\n     o.tz=o.tstdev==0?0:(n-o.tavg)/o.tstdev\r\n     o.tzavg=tzavg(o.tz,t)\r\n     \r\n\t o.tmstdev=Math.sqrt(tmstdev(Math.pow(n-o.tmedian,2),t))\r\n     o.tmz=o.tmstdev==0?0: 0.6745*(n-o.tmedian)/o.tmstdev // tmz> 3.5 = outlier \r\n     o.tmzavg=tmzavg(o.tmz,t) \r\n     \r\n     if(prev===false) prev=t-usualtime;     var delta=t-prev; prev=t;     o.avgtime=avgtime(delta) // to have result in minutes\r\n\t \r\n\t o.count=count;count++;// it is good ide to have count to skip the initial\r\n\t o.tcount=tcount;tcount+=delta;\r\n\t \r\n     o.value_delay=value_delay(n);\r\n     o.tvalue_delay=tvalue_delay(n,t);\r\n     o.index_delay=index_delay(t);\r\n     o.tindex_delay=tindex_delay(t,t);\r\n       \r\n     return o;\r\n    }\r\n\t\r\n    stats.reset=function()\r\n    {\r\n     for(var i=0;i<list.length;i++){list[i].reset()};\r\n     prev=false;\r\n\t count=0;\r\n    }\r\n    return stats;\r\n}\r\n\r\n//example2:\r\n\r\nfunction SimpleStats(size,delay)\r\n{\r\n    var min=Stats.RollingMin(size)\r\n   ,max=Stats.RollingMax(size)\r\n   ,avg=Stats.RollingAvg(size)\r\n   ,value_delay=Stats.Delay(delay)\r\n   \r\n    function stats(n)\r\n    {\r\n     var o={}\r\n     \r\n     o.min=min(n)\r\n     o.max=max(n)\r\n     o.avg=avg(n)\r\n     \r\n     o.value_delay=value_delay(n)\r\n       \r\n     return o;\r\n    }\r\n    \r\n    stats.reset=function()\r\n    {\r\n     min.reset();\r\n     max.reset();\r\n     avg.reset();\r\n     value_delay.reset();\r\n    }\r\n    return stats;\r\n}\r\n\r\n//example3: \r\nfunction SimpleStatsNoDelay(size)\r\n{\r\n    var min=Stats.RollingMin(size)\r\n   ,max=Stats.RollingMax(size)\r\n   ,avg=Stats.RollingAvg(size)\r\n \r\n\r\n    function stats(n)\r\n    {\r\n     var o={}\r\n     \r\n     o.min=min(n)\r\n     o.max=max(n)\r\n     o.avg=avg(n)\r\n\r\n     return o;\r\n    }\r\n    stats.reset=function()\r\n    {\r\n     min.reset();\r\n     max.reset();\r\n     avg.reset();\r\n    }\r\n    return stats;\r\n}\r\n\r\nexports.RollingMin=RollingMin;\r\nexports.RollingMax=RollingMax;\r\nexports.RollingAvg=RollingAvg;\r\nexports.RollingMedian=RollingMedian;\r\nexports.RollingMinIndex=RollingMinIndex;\r\nexports.RollingMaxIndex=RollingMaxIndex;\r\nexports.RollingAvgIndex=RollingAvgIndex;\r\nexports.RollingMedianIndex=RollingMedianIndex;\r\nexports.RollingSumPerIndex=RollingSumPerIndex;\r\n\r\nexports.Delay=Delay;\r\nexports.DelayIndex=DelayIndex;\r\n\r\nexports.AllStats=AllStats;\r\nexports.SimpleStats=SimpleStats;\r\nexports.SimpleStatsNoDelay=SimpleStatsNoDelay;\r\n\r\nexports.PositiveLately=PositiveLately;\r\nexports.PositiveLatelyIndex=PositiveLatelyIndex;\r\n \r\nexports.Histogram=Histogram;\r\nexports.RollingHistogram=RollingHistogram;\r\nexports.RollingHistogramIndex=RollingHistogramIndex;\r\nexports.HideFirst=HideFirst;\r\n"], "mappings": ";;;;;AAAA;AAAA;AA0BA,aAAS,WAAW,YACpB;AACI,UAAI,aAAW,CAAC,GAAE,aAAW,CAAC,GAAE,eAAa,GAAE,IAAE;AACjD,eAAS,cAAc,cACvB;AACE,YAAK,WAAW,WAAS,KAAK,WAAW,CAAC,KAAK,eAAe,GAC9D;AACG,qBAAW,MAAM;AACjB,qBAAW,MAAM;AAAA,QACpB;AAGA,eAAQ,WAAW,WAAS,KAAK,WAAW,WAAW,SAAO,CAAC,IAAI,cACnE;AACG,qBAAW,IAAI;AACf,qBAAW,IAAI;AAAA,QAClB;AAGA,mBAAW,KAAK,YAAY;AAC5B,mBAAW,KAAK,YAAY;AAC5B;AACA,eAAO,WAAW,CAAC;AAAA,MACrB;AACA,oBAAc,gBAAc,SAASA,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,uBAAa;AAAA,MAAE;AAC5H,aAAO;AAAA,IACX;AAEA,aAAS,WAAW,YACpB;AACI,UAAI,aAAW,CAAC,GAAE,aAAW,CAAC,GAAE,eAAa,GAAE,IAAE;AACjD,eAAS,cAAc,cACvB;AACE,YAAK,WAAW,WAAS,KAAK,WAAW,CAAC,KAAK,eAAe,GAC9D;AACG,qBAAW,MAAM;AACjB,qBAAW,MAAM;AAAA,QACpB;AAGA,eAAQ,WAAW,WAAS,KAAK,WAAW,WAAW,SAAO,CAAC,IAAI,cACnE;AACG,qBAAW,IAAI;AACf,qBAAW,IAAI;AAAA,QAClB;AAGA,mBAAW,KAAK,YAAY;AAC5B,mBAAW,KAAK,YAAY;AAC5B;AACA,eAAO,WAAW,CAAC;AAAA,MACrB;AACA,oBAAc,gBAAc,SAASA,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,uBAAa;AAAA,MAAE;AAC5H,aAAO;AAAA,IACX;AAGA,aAAS,WAAW,YACpB;AACI,UAAI,aAAW,CAAC,GAAE,IAAE,YAAWC,OAAI,GAAEC;AACrC,eAAS,cAAc,cACvB;AACE,YAAK,WAAW,UAAU,GAC1B;AACG,UAAAD,QAAK,WAAW,MAAM;AAAA,QACzB;AAEA,YAAG,gBAAc,iBAAe,GAChC;AACA,qBAAW,KAAK,YAAY;AAC5B,UAAAA,QAAK;AAAA,QACL,MACE,QAAOC;AACT,eAAOA,QAAM,WAAW,UAAQ,IAAE,IAAED,OAAI,WAAW;AAAA,MACrD;AACA,oBAAc,gBAAc,SAASD,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,QAAAC,OAAI;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAA,MAAE;AAC5E,aAAO;AAAA,IACX;AAGA,aAAS,gBAAgB,YACzB;AACI,UAAI,aAAW,CAAC,GAAE,aAAW,CAAC,GAAE,IAAE;AAClC,eAAS,cAAc,cAAa,cACpC;AACE,eAAQ,WAAW,WAAS,KAAK,WAAW,CAAC,KAAK,eAAe,GACjE;AACG,qBAAW,MAAM;AACjB,qBAAW,MAAM;AAAA,QACpB;AAGA,eAAQ,WAAW,WAAS,KAAK,WAAW,WAAW,SAAO,CAAC,IAAI,cACnE;AACG,qBAAW,IAAI;AACf,qBAAW,IAAI;AAAA,QAClB;AAGA,mBAAW,KAAK,YAAY;AAC5B,mBAAW,KAAK,YAAY;AAC5B,eAAO,WAAW,CAAC;AAAA,MACrB;AACA,oBAAc,gBAAc,SAASD,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAA,MAAE;AAC7G,aAAO;AAAA,IACX;AAEA,aAAS,gBAAgB,YACzB;AACI,UAAI,aAAW,CAAC,GAAE,aAAW,CAAC,GAAE,IAAE;AAClC,eAAS,cAAc,cAAa,cACpC;AACE,eAAQ,WAAW,WAAS,KAAK,WAAW,CAAC,KAAK,eAAe,GACjE;AACG,qBAAW,MAAM;AACjB,qBAAW,MAAM;AAAA,QACpB;AAGA,eAAQ,WAAW,WAAS,KAAK,WAAW,WAAW,SAAO,CAAC,IAAI,cACnE;AACG,qBAAW,IAAI;AACf,qBAAW,IAAI;AAAA,QAClB;AAGA,mBAAW,KAAK,YAAY;AAC5B,mBAAW,KAAK,YAAY;AAC5B,eAAO,WAAW,CAAC;AAAA,MACrB;AACA,oBAAc,gBAAc,SAASA,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAA,MAAE;AAC7G,aAAO;AAAA,IACX;AAEA,aAAS,gBAAgB,YACzB;AACI,UAAI,aAAW,CAAC,GAAE,aAAW,CAAC,GAAE,IAAE,YAAWC,OAAI;AACjD,eAAS,cAAc,cAAa,cACpC;AACE,eAAQ,WAAW,WAAS,KAAM,WAAW,CAAC,KAAK,eAAe,GAClE;AACG,qBAAW,MAAM;AACjB,UAAAA,QAAK,WAAW,MAAM;AAAA,QACzB;AAGA,YAAG,gBAAc,iBAAe,GACnC;AACG,qBAAW,KAAK,YAAY;AAC5B,qBAAW,KAAK,YAAY;AAE5B,UAAAA,QAAK;AAAA,QACR,MACO,QAAO;AACX,eAAO,OAAM,WAAW,UAAQ,IAAE,IAAEA,OAAI,WAAW;AAAA,MAErD;AACA,oBAAc,gBAAc,SAASD,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,QAAAC,OAAI;AAAA,MAAE;AACnH,aAAO;AAAA,IACX;AAQA,aAAS,YAAY,OAAO,OAAO;AAClC,UAAI,MAAM,GACT,OAAO,MAAM;AAEd,aAAO,MAAM,MAAM;AAClB,YAAI,MAAM,MAAM,SAAS;AACzB,YAAI,MAAM,GAAG,IAAI,MAAO,OAAM,MAAM;AAAA,YAC/B,QAAO;AAAA,MACb;AACA,aAAO;AAAA,IACR;AASA,aAAS,cAAc,YACvB;AACI,UAAI,aAAW,CAAC,GAAG,IAAE,YAAY,eAAa,CAAC,GAAG,eAAa;AAClE,UAAI,YAAW,aAAW,MAAKA,OAAI,GAAEE,QAAK,GAAE,OAAK,GAAE,OAAK,GAAE,cAAY;AACnE,eAAS,cAAc,cACvB;AACE,YAAK,WAAW,UAAU,GAC1B;AAEG,cAAI,QAAM,WAAW,MAAM;AAE7B,cAAI,IAAE,OACH,KAAG,IAAE,GACX,MAAI,KAAG,GACP,OAAK,MAAI;AAEV,UAAAF,QAAK;AACL,UAAAE,SAAM;AACN,kBAAM;AACN,kBAAM;AAEH,cAAI,IAAE,aAAa,cAAc,KAAK;AAAG,cAAG,aAAa,CAAC,KAAG,MAAM,cAAa,OAAO,GAAE,CAAC;AAAA,QAC1F;AAEH,YAAG,gBAAc,iBAAe,GAChC;AACG,qBAAW,KAAK,YAAY;AAC/B,uBAAa,OAAO,aAAa,cAAc,YAAY,GAAE,GAAE,YAAY;AAE3E,uBAAW;AACX,wBAAY;AAGZ,cAAI,IAAE,cACC,KAAG,IAAE,GACX,MAAI,KAAG,GACP,OAAK,MAAI;AAGV,UAAAF,QAAK;AACL,UAAAE,SAAM;AACN,kBAAM;AACN,kBAAM;AAAA,QACN;AAEA,iBAAO;AACP,YAAG,aAAa,UAAS,EAAE,QAAO;AAElC,YAAG,aAAa,SAAS;AACvB,iBAAO,aAAW,aAAe,aAAa,SAAO,MAAM,CAAE;AAAA,aAE/D;AAEC,cAAI,QAAM,aAAa,WAAU,KAAG;AAEnC,iBAAO,cAAY,aAAa,IAAI,IAAE,aAAa,OAAK,CAAC,KAAG;AAAA,QAE9D;AAAA,MACC;AACA,oBAAc,gBAAc,SAASH,aAAW;AAAC,YAAEA;AAAA,MAAU;AAI7D,oBAAc,QAAM,WAAU;AAC3B,mBAAW,OAAO,GAAE,WAAW,MAAM;AACrC,qBAAa,OAAO,GAAE,aAAa,MAAM;AAAE,qBAAW;AAAK,QAAAC,OAAI;AAAE,QAAAE,QAAK;AAAE,eAAK;AAAE,eAAK;AAAE,sBAAY;AAAA,MACxG;AAEA,oBAAc,MAAI,WAAU;AAAI,eAAOF,OAAI,WAAW;AAAA,MAAO;AAC7D,oBAAc,MAAI,WAAU;AAAI,eAAOA;AAAA,MAAI;AAE3C,oBAAc,MAAI,WAAU;AAAI,eAAO,aAAa,CAAC;AAAA,MAAE;AAEvD,oBAAc,KAAG,WAAU;AAEzB,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC;AAC/C,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,OAAK,aAAa,CAAC,IAAE;AACtE,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,MAAI,aAAa,CAAC,IAAE;AAErE,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,IAAG,aAAa,SAAO,KAAI;AAC/B,iBAAS,aAAa,IAAE,CAAG,IAAE,OAAK,aAAa,CAAK,IAAE;AAAA,QAEvD;AAEA,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,IAAG,aAAa,SAAO,KAAI;AAC/B,iBAAS,aAAa,CAAK,IAAE,OAAK,aAAa,IAAE,CAAG,IAAE;AAAA,QAEvD;AAAA,MACF;AAGA,oBAAc,cAAY,WAC1B;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,WAAS,WACvB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,oBAAkB,WAChC;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,OAAK,WACnB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,WAAS,WACvB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,aAAW,WACzB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAGA,oBAAc,UAAQ,WACtB;AACC,YAAI,IAAE,CAAC;AACP,YAAG,KAAK,IAAE,GACV;AAEC,cAAI,IAAE,WAAW,QACjB,KAAKA,OAAI,GACT,MAAME,QAAK,GACX,OAAO,OAAK,GACZ,QAAQ,OAAK,GAEb,KAAK,IACL,KAAKA,QAAK,IAAI;AACd,eAAK,OAAO,IAAE,MAAI,KAAK,IAAG,KAAG,KAAG;AAChC,eAAK,QAAQ,IAAE,OAAK,KAAK,IAAE,MAAI,KAAG,KAAK,KAAG,KAAG,KAAG,KAAG;AACnD,YAAE,UAAU;AACZ,YAAE,WAAW;AACb,YAAE,oBAAmB,KAAK,IAAI,EAAE,UAAS,GAAE;AAC3C,cAAG,IAAE,GAAE;AAGN,cAAE,OAAO,KAAK,IAAI,KAAG,IAAE,IAAG,GAAE,KAAG,IAAE,KAAK,KAAK,KAAK,IAAI,IAAG,GAAG;AAAA,UAC3D;AACA,cAAG,KAAG,GAAE;AAEP,cAAE,aAAa,KAAK,KAAK,KAAK;AAC9B,cAAE,WAAW,KAAK,KAAK;AAAA,UACxB;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAGA,oBAAc,SAAO,WAAU;AAAI,eAAO;AAAA,MAAW;AAErD,oBAAc,KAAG,WAAU;AACxB,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC;AAChD,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,OAAK,aAAa,CAAC,IAAE;AACtE,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,MAAI,aAAa,CAAC,IAAE;AAErE,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,MAAK,aAAa,SAAO,KAAI,KAAG;AACpC,iBAAS,aAAa,EAAM,IAAE,OAAK,aAAa,KAAG,CAAG,IAAE;AAAA,QAEzD;AAEA,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,MAAK,aAAa,SAAO,KAAI,KAAG;AACpC,iBAAS,aAAa,KAAG,CAAG,IAAE,OAAK,aAAa,KAAG,CAAG,IAAE;AAAA,QAEzD;AAAA,MACF;AAEA,oBAAc,MAAI,WAAU;AAAI,eAAO,aAAa,aAAa,SAAO,CAAC;AAAA,MAAE;AAC3E,oBAAc,SAAO,WAAU;AAAI,gBAAQ,aAAa,CAAC,IAAE,aAAa,aAAa,SAAO,CAAC,KAAG;AAAA,MAAE;AAalG,oBAAc,aAAW,WAAU;AAC/B,eAAS,aAAa,aAAa,SAAO,CAAC,IAAE,cAAa,aAAW,aAAa,CAAC;AAAA,MACvF;AAEA,oBAAc,0BAAwB,WAAU;AAC5C,gBAAU,aAAa,aAAa,SAAO,CAAC,IAAE,eAAa,aAAW,aAAa,CAAC,MAAK,aAAa,aAAa,SAAO,CAAC,IAAE,aAAa,CAAC;AAAA,MAC/I;AAEA,oBAAc,aAAW,WAAU;AAClC,YAAI,MAAI,KAAK,OAAO,aAAa,SAAO,KAAG,GAAG;AAC9C,YAAI,MAAI,KAAK,OAAO,aAAa,SAAO,KAAG,GAAG;AAC3C,gBAAU,aAAa,aAAa,SAAO,CAAC,IAAE,eAAa,aAAW,aAAa,CAAC,MAAK,aAAa,GAAG,IAAE,aAAa,GAAG;AAAA,MAC/H;AAEA,oBAAc,eAAa,WAAU;AACpC,YAAG,eAAa,KAAM,cAAW,aAAa,eAAe,aAAa,CAAC,IAAE,aAAa,aAAa,SAAO,CAAC,KAAG,CAAE;AACpH,gBAAQ,aAAa,SAAO,IAAE,eAAa,aAAa,SAAO;AAAA,MAChE;AACA,oBAAc,eAAa,WAAU;AACpC,YAAG,eAAa,KAAM,cAAW,aAAa,eAAe,aAAa,CAAC,IAAE,aAAa,aAAa,SAAO,CAAC,KAAG,CAAE;AACpH,eAAO,cAAY,aAAa,SAAO;AAAA,MACxC;AAEG,aAAO;AAAA,IACX;AAEA,aAAS,mBAAmB,YAC5B;AACI,UAAI,aAAW,CAAC,GAAG,aAAW,CAAC,GAAG,IAAE,YAAY,eAAa,CAAC,GAAG,eAAa;AACjF,UAAI,YAAW,aAAW,MAAKF,OAAI,GAAEE,QAAK,GAAE,OAAK,GAAE,OAAK,GAAE,cAAY;AACnE,eAAS,cAAc,cAAa,cACpC;AACE,eAAQ,WAAW,WAAS,KAAM,WAAW,CAAC,KAAK,eAAe,GAClE;AAEG,cAAI,QAAM,WAAW,MAAM;AACjC,cAAI,QAAM,WAAW,MAAM;AAEvB,cAAI,IAAE,OACH,KAAG,IAAE,GACX,MAAI,KAAG,GACP,OAAK,MAAI;AAEV,UAAAF,QAAK;AACL,UAAAE,SAAM;AACN,kBAAM;AACN,kBAAM;AAEH,cAAI,IAAE,aAAa,cAAc,KAAK;AAAG,cAAG,aAAa,CAAC,KAAG,MAAM,cAAa,OAAO,GAAE,CAAC;AAAA,QAC1F;AAGH,YAAG,gBAAc,iBAAe,GAChC;AACG,qBAAW,KAAK,YAAY;AAC5B,qBAAW,KAAK,YAAY;AAC/B,uBAAa,OAAO,aAAa,cAAc,YAAY,GAAE,GAAE,YAAY;AAE3E,uBAAW;AACX,wBAAY;AAGZ,cAAI,IAAE,cACC,KAAG,IAAE,GACX,MAAI,KAAG,GACP,OAAK,MAAI;AAGV,UAAAF,QAAK;AACL,UAAAE,SAAM;AACN,kBAAM;AACN,kBAAM;AAAA,QACN;AAEA,iBAAO;AACP,YAAG,aAAa,UAAS,EAAE,QAAO;AAElC,YAAG,aAAa,SAAS;AACvB,iBAAO,aAAW,aAAe,aAAa,SAAO,MAAM,CAAE;AAAA,aAE/D;AAEC,cAAI,QAAM,aAAa,WAAU,KAAG;AAEnC,iBAAO,cAAY,aAAa,IAAI,IAAE,aAAa,OAAK,CAAC,KAAG;AAAA,QAE9D;AAAA,MACC;AACA,oBAAc,gBAAc,SAASH,aAAW;AAAC,YAAEA;AAAA,MAAU;AAI7D,oBAAc,QAAM,WAAU;AAC3B,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAC5E,qBAAa,OAAO,GAAE,aAAa,MAAM;AAAE,qBAAW;AAAK,QAAAC,OAAI;AAAE,QAAAE,QAAK;AAAE,eAAK;AAAE,eAAK;AAAE,sBAAY;AAAA,MACxG;AAEA,oBAAc,MAAI,WAAU;AAAI,eAAOF,OAAI,WAAW;AAAA,MAAO;AAC7D,oBAAc,MAAI,WAAU;AAAI,eAAOA;AAAA,MAAI;AAE3C,oBAAc,MAAI,WAAU;AAAI,eAAO,aAAa,CAAC;AAAA,MAAE;AAEvD,oBAAc,KAAG,WAAU;AAEzB,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC;AAC/C,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,OAAK,aAAa,CAAC,IAAE;AACtE,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,MAAI,aAAa,CAAC,IAAE;AAErE,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,IAAG,aAAa,SAAO,KAAI;AAC/B,iBAAS,aAAa,IAAE,CAAG,IAAE,OAAK,aAAa,CAAK,IAAE;AAAA,QAEvD;AAEA,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,IAAG,aAAa,SAAO,KAAI;AAC/B,iBAAS,aAAa,CAAK,IAAE,OAAK,aAAa,IAAE,CAAG,IAAE;AAAA,QAEvD;AAAA,MACF;AAGA,oBAAc,cAAY,WAC1B;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,WAAS,WACvB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,oBAAkB,WAChC;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,OAAK,WACnB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,WAAS,WACvB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAEA,oBAAc,aAAW,WACzB;AACE,YAAG,gBAAc,KAAM,eAAY,cAAc,QAAQ;AACzD,eAAO,YAAY;AAAA,MACrB;AAGA,oBAAc,UAAQ,WACtB;AACC,YAAI,IAAE,CAAC;AACP,YAAG,KAAK,IAAE,GACV;AAEC,cAAI,IAAE,WAAW,QACjB,KAAKA,OAAI,GACT,MAAME,QAAK,GACX,OAAO,OAAK,GACZ,QAAQ,OAAK,GAEb,KAAK,IACL,KAAKA,QAAK,IAAI;AACd,eAAK,OAAO,IAAE,MAAI,KAAK,IAAG,KAAG,KAAG;AAChC,eAAK,QAAQ,IAAE,OAAK,KAAK,IAAE,MAAI,KAAG,KAAK,KAAG,KAAG,KAAG,KAAG;AACnD,YAAE,UAAU;AACZ,YAAE,WAAW;AACb,YAAE,oBAAmB,KAAK,IAAI,EAAE,UAAS,GAAE;AAC3C,cAAG,IAAE,GAAE;AAGN,cAAE,OAAO,KAAK,IAAI,KAAG,IAAE,IAAG,GAAE,KAAG,IAAE,KAAK,KAAK,KAAK,IAAI,IAAG,GAAG;AAAA,UAC3D;AACA,cAAG,KAAG,GAAE;AAEP,cAAE,aAAa,KAAK,KAAK,KAAK;AAC9B,cAAE,WAAW,KAAK,KAAK;AAAA,UACxB;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAGA,oBAAc,SAAO,WAAU;AAAI,eAAO;AAAA,MAAW;AAErD,oBAAc,KAAG,WAAU;AACxB,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC;AAChD,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,OAAK,aAAa,CAAC,IAAE;AACtE,YAAG,aAAa,UAAQ,EAAE,QAAO,aAAa,CAAC,IAAE,MAAI,aAAa,CAAC,IAAE;AAErE,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,MAAK,aAAa,SAAO,KAAI,KAAG;AACpC,iBAAS,aAAa,EAAM,IAAE,OAAK,aAAa,KAAG,CAAG,IAAE;AAAA,QAEzD;AAEA,aAAI,aAAa,SAAO,KAAG,KAAG,GAC9B;AACC,cAAI,MAAK,aAAa,SAAO,KAAI,KAAG;AACpC,iBAAS,aAAa,KAAG,CAAG,IAAE,OAAK,aAAa,KAAG,CAAG,IAAE;AAAA,QAEzD;AAAA,MACF;AAEA,oBAAc,MAAI,WAAU;AAAI,eAAO,aAAa,aAAa,SAAO,CAAC;AAAA,MAAE;AAC3E,oBAAc,SAAO,WAAU;AAAI,gBAAQ,aAAa,CAAC,IAAE,aAAa,aAAa,SAAO,CAAC,KAAG;AAAA,MAAE;AAalG,oBAAc,aAAW,WAAU;AAC/B,eAAS,aAAa,aAAa,SAAO,CAAC,IAAE,cAAa,aAAW,aAAa,CAAC;AAAA,MACvF;AAEA,oBAAc,0BAAwB,WAAU;AAC5C,gBAAU,aAAa,aAAa,SAAO,CAAC,IAAE,eAAa,aAAW,aAAa,CAAC,MAAK,aAAa,aAAa,SAAO,CAAC,IAAE,aAAa,CAAC;AAAA,MAC/I;AAEA,oBAAc,aAAW,WAAU;AAClC,YAAI,MAAI,KAAK,OAAO,aAAa,SAAO,KAAG,GAAG;AAC9C,YAAI,MAAI,KAAK,OAAO,aAAa,SAAO,KAAG,GAAG;AAC3C,gBAAU,aAAa,aAAa,SAAO,CAAC,IAAE,eAAa,aAAW,aAAa,CAAC,MAAK,aAAa,GAAG,IAAE,aAAa,GAAG;AAAA,MAC/H;AAEA,oBAAc,eAAa,WAAU;AACpC,YAAG,eAAa,KAAM,cAAW,aAAa,eAAe,aAAa,CAAC,IAAE,aAAa,aAAa,SAAO,CAAC,KAAG,CAAE;AACpH,gBAAQ,aAAa,SAAO,IAAE,eAAa,aAAa,SAAO;AAAA,MAChE;AACA,oBAAc,eAAa,WAAU;AACpC,YAAG,eAAa,KAAM,cAAW,aAAa,eAAe,aAAa,CAAC,IAAE,aAAa,aAAa,SAAO,CAAC,KAAG,CAAE;AACpH,eAAO,cAAY,aAAa,SAAO;AAAA,MACxC;AAEG,aAAO;AAAA,IACX;AAEA,aAAS,mBAAmB,YAAW,gCACvC;AACI,UAAI,aAAW,CAAC,GAAE,aAAW,CAAC,GAAE,IAAE,YAAWF,OAAI,GAAE,YAAU,OAAM,IAAE;AACrE,eAAS,cAAc,cAAa,cACpC;AACE,eAAQ,WAAW,WAAS,KAAM,WAAW,CAAC,KAAK,eAAe,KAAM,WAAW,CAAC,KAAG,cACvF;AACG,sBAAU,WAAW,MAAM;AAC3B,UAAAA,QAAK,WAAW,MAAM;AAAA,QACzB;AACA,YAAG,cAAY,MAAM,aAAU,eAAa;AAI5C,mBAAW,KAAK,YAAY;AAC5B,mBAAW,KAAK,YAAY;AAE5B,QAAAA,QAAK;AACL,YAAI,MAAI,eAAa;AAErB,YAAG,OAAK,EAAE,OAAI;AACd,eAAOA,OAAI;AAAA,MACb;AACA,oBAAc,gBAAc,SAASD,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,oCAAkC,SAASI,iCAA+B;AAAC,YAAEA;AAAA,MAA8B;AACzH,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,QAAAH,OAAI;AAAE,oBAAU;AAAA,MAAM;AACnI,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,YAAW,gBAC1B;AACI,UAAI,aAAW,CAAC,GAAE,IAAE,YAAW,IAAE;AACjC,eAAS,cAAc,cACvB;AACE,YAAI,MAAI;AACR,YAAI,WAAW,UAAS,GACxB;AACI,gBAAI,WAAW,MAAM;AAAA,QACzB;AACA,mBAAW,KAAK,YAAY;AAC5B,eAAO;AAAA,MACT;AACA,oBAAc,gBAAc,SAASD,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,oBAAkB,SAASA,aAAW;AAAC,YAAE;AAAA,MAAc;AACrE,oBAAc,QAAM,SAASA,aAAW;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAA,MAAE;AAChF,aAAO;AAAA,IACX;AACA,aAAS,UAAU,YAAW,gBAC9B;AACI,UAAI,aAAW,GAAE,IAAE,aAAW,GAAE,IAAE;AAClC,eAAS,cAAc,cACvB;AACE,YAAI,cAAa,GACjB;AACE,iBAAQ;AAAA,QACV;AACA;AACA,eAAO;AAAA,MACT;AACA,oBAAc,gBAAc,SAASA,aAAW;AAAC,YAAEA,cAAW;AAAA,MAAC;AAC/D,oBAAc,oBAAkB,SAASA,aAAW;AAAC,YAAE;AAAA,MAAc;AACrE,oBAAc,QAAM,WAAU;AAAC,qBAAW;AAAA,MAAE;AAC5C,aAAO;AAAA,IACX;AAEA,aAAS,WAAW,YAAW,gCAA+B,gBAC9D;AACI,UAAI,aAAW,CAAC,GAAE,aAAW,CAAC,GAAE,IAAE,YAAW,YAAU,OAAM,YAAU,gBAAe,IAAE;AACxF,eAAS,cAAc,cAAa,cACpC;AACE,mBAAW,KAAK,YAAY;AAC5B,mBAAW,KAAK,YAAY;AAC5B,YAAG,cAAY,MAAM,aAAU,eAAa;AAE5C,YAAK,WAAW,WAAS,KAAM,WAAW,CAAC,KAAK,eAAe,GAC/D;AACG,sBAAU,WAAW,MAAM;AAC3B,sBAAU,WAAW,MAAM;AAC3B,iBAAQ,WAAW,WAAS,KAAM,WAAW,CAAC,KAAK,eAAe,GAClE;AACE,uBAAW,MAAM;AACjB,uBAAW,MAAM;AAAA,UACnB;AAAA,QACH;AACA,eAAO;AAAA,MACT;AACA,oBAAc,gBAAc,SAASA,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,oCAAkC,SAASI,iCAA+B;AAAC,YAAEA;AAAA,MAA8B;AACzH,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,oBAAU;AAAM,oBAAU;AAAA,MAAc;AACrJ,aAAO;AAAA,IACX;AAGA,aAAS,eAAe,YACxB;AACI,UAAI,IAAE,YAAW,gBAAc;AAC/B,eAAS,cAAc,cACvB;AAED,YAAG,eAAa,EAAE,iBAAc;AAChC,YAAG,iBAAe,EAAE;AACpB,YAAG,gBAAc,WAAW,iBAAc;AACvC,eAAO,gBAAc;AAAA,MACvB;AACA,oBAAc,gBAAc,SAASJ,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,wBAAc;AAAA,MAAE;AAC/C,aAAO;AAAA,IACX;AAEA,aAAS,oBAAoB,YAC7B;AACI,UAAI,IAAE,YAAW,YAAU,OAAM,gBAAc;AAC/C,eAAS,cAAc,cAAa,cACpC;AAED,YAAG,eAAa,GAAE;AAAC,sBAAU;AAAa,0BAAc;AAAA,QAAE;AAC1D,YAAG,iBAAe,EAAE;AACpB,YAAI,aAAW,SAAU,aAAa,eAAe,EAAK,iBAAc;AACrE,eAAO,gBAAc;AAAA,MACvB;AACA,oBAAc,gBAAc,SAASA,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,oBAAU;AAAM,wBAAc;AAAA,MAAE;AAC/D,aAAO;AAAA,IACX;AAOA,aAAS,UAAU,uBACnB;AACI,UAAI,KAAG,uBAAsB,OAAM,oBAAI,IAAI;AAAE;AAC7C,eAAS,cAAc,iBAAgB,gBAAc,GACrD;AAGD,YAAI,uBAAqB,YAAc,KAAK,MAAO,kBAAgB,EAAG,IAAE,IAAM,QAAQ,EAAE,CAAC;AAEzF,YAAG,KAAK,IAAI,oBAAoB;AAC9B,eAAK,IAAK,sBAAsB,YAAc,KAAK,IAAI,oBAAoB,IAAE,eAAiB,QAAQ,EAAE,CAAC,CAAE;AAAA;AAE3G,eAAK,IAAK,sBAAsB,aAAc;AAE7C,eAAO;AAAA,MACT;AACH,oBAAc,OAAK;AAChB,oBAAc,QAAM,WAAU;AAAE,aAAK,MAAM;AAAA,MAAE;AAC7C,aAAO;AAAA,IACX;AAEA,aAAS,iBAAiB,YAAW,uBACrC;AACI,UAAI,gBAAc,CAAC,GAAE,cAAY,CAAC,GAAE,IAAE,YAAW,KAAG,uBAAsB,OAAM,oBAAI,IAAI;AAAE;AAC1F,eAAS,cAAc,iBAAgB,gBAAc,GACrD;AACE,YAAK,cAAc,UAAU,GAC7B;AACE,cAAI,UAAQ,cAAc,MAAM;AAChC,cAAI,aAAW,YAAY,MAAM;AAEvC,cAAI,YAAU,YAAe,KAAK,IAAI,OAAO,IAAE,YAAe,QAAQ,EAAE,CAAC;AACnE,cAAG,cAAY;AACpB,iBAAK,IAAI,SAAU,SAAS;AAAA;AAE5B,iBAAK,OAAO,OAAO;AAAA,QAChB;AAGH,YAAI,uBAAqB,YAAc,KAAK,MAAO,kBAAgB,EAAG,IAAE,IAAM,QAAQ,EAAE,CAAC;AAEzF,sBAAc,KAAK,oBAAoB;AACvC,oBAAY,KAAK,aAAa;AAE9B,YAAG,KAAK,IAAI,oBAAoB;AAC9B,eAAK,IAAK,sBAAsB,YAAc,KAAK,IAAI,oBAAoB,IAAE,eAAiB,QAAQ,EAAE,CAAC,CAAE;AAAA;AAE3G,eAAK,IAAK,sBAAsB,aAAc;AAE7C,eAAO;AAAA,MACT;AACH,oBAAc,OAAK;AAChB,oBAAc,gBAAc,SAASA,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,cAAI;AAAE,sBAAc,OAAO,GAAE,cAAc,MAAM;AAAA,MAAE;AAClF,aAAO;AAAA,IACX;AAEA,aAAS,sBAAsB,YAAW,uBAC1C;AACI,UAAI,aAAW,CAAC,GAAE,gBAAc,CAAC,GAAE,cAAY,CAAC,GAAE,IAAE,YAAW,KAAG,uBAAsB,OAAM,oBAAI,IAAI;AACtG,eAAS,cAAc,iBAAgB,cAAa,gBAAc,GAClE;AACD,YAAK,CAAC,iBAAe,kBAAgB,KAAO,CAAC,mBAAiB,oBAAkB,EAAK,QAAO;AACzF,eAAQ,WAAW,WAAS,KAAM,WAAW,CAAC,KAAK,eAAe,GAClE;AACM,qBAAW,MAAM;AACrB,cAAI,UAAQ,cAAc,MAAM;AAChC,cAAI,aAAW,YAAY,MAAM;AAEvC,cAAG,CAAC,WAAW,YAAU,EAAE,SAAQ,IAAI,SAAQ,EAAC,YAAW,QAAO,CAAC;AACnE,cAAG,CAAC,cAAc,eAAa,EAAE,SAAQ,IAAI,SAAQ,EAAC,YAAW,QAAO,CAAC;AAEzE,cAAG,KAAK,IAAI,OAAO,GACnB;AACC,gBAAI,YAAU,YAAgB,KAAK,IAAI,OAAO,IAAG,YAAe,QAAQ,EAAE,CAAC;AAC3E,gBAAG,CAAC,aAAa,cAAY,EAAE,SAAQ,IAAI,SAAQ,EAAC,WAAW,YAAW,QAAO,CAAC;AAElF,gBAAG,cAAY;AACd,mBAAK,IAAI,SAAU,SAAS;AAAA;AAE5B,mBAAK,OAAO,OAAO;AAAA,UACrB;AAAA,QACI;AAIH,YAAI,uBAAqB,YAAc,KAAK,MAAO,kBAAgB,EAAG,IAAE,IAAM,QAAQ,EAAE,CAAC;AAEzF,sBAAc,KAAK,oBAAoB;AACvC,oBAAY,KAAK,aAAa;AAC9B,mBAAW,KAAK,YAAY;AAE5B,YAAG,KAAK,IAAI,oBAAoB,GAChC;AACD,cAAIK,aAAU,YAAc,KAAK,IAAI,oBAAoB,IAAE,eAAiB,QAAQ,EAAE,CAAC;AACpF,eAAK,IAAK,sBAAsBA,UAAY;AAAA,QAC9C;AAEE,eAAK,IAAK,sBAAsB,aAAc;AAE7C,eAAO;AAAA,MACT;AACH,oBAAc,OAAK;AAChB,oBAAc,gBAAc,SAASL,aAAW;AAAC,YAAEA;AAAA,MAAU;AAC7D,oBAAc,QAAM,WAAU;AAAC,mBAAW,OAAO,GAAE,WAAW,MAAM;AAAE,sBAAc,OAAO,GAAE,cAAc,MAAM;AAAE,cAAI;AAAA,MAAE;AACzH,aAAO;AAAA,IACX;AAgBA,QAAI,QAAM;AACV,aAAS,SAAS,MAAK,OAAM,UAAS,WAAU,WAChD;AACG,UAAI,OAAK,CAAC,GAAE,MAAI,SAAS,GAAG;AAAE,aAAK,KAAK,CAAC;AAAG,eAAO;AAAA,MAAG;AAErD,UAAI,UAAa,IAAM,MAAM,WAAW,IAAI,CAAK,GACjD,QAAiB,IAAM,MAAM,WAAW,IAAI,CAAK,GACjD,OAAiB,IAAM,MAAM,WAAW,IAAI,CAAK,GACjD,SAAiB,IAAM,MAAM,cAAc,IAAI,CAAE,GACjD,SAAiB,IAAM,MAAM,WAAW,IAAI,CAAK,GACjD,QAAiB,IAAM,MAAM,WAAW,IAAI,CAAK,GACjD,QAAiB,IAAM,MAAM,gBAAgB,QAAQ,CAAK,GAC1D,SAAiB,IAAM,MAAM,gBAAgB,QAAQ,CAAK,GAC1D,UAAiB,IAAM,MAAM,mBAAmB,QAAQ,CAAE,GAC1D,SAAiB,IAAM,MAAM,gBAAgB,QAAQ,CAAK,GAC1D,UAAiB,IAAM,MAAM,gBAAgB,QAAQ,CAAK,GAC1D,OAAiB,IAAM,MAAM,mBAAmB,UAAS,SAAS,CAAG,GACrE,cAAiB,IAAM,MAAM,MAAM,KAAK,CAA6B,GACrE,eAAiB,IAAM,MAAM,WAAW,WAAU,SAAS,CAAU,GACrE,cAAiB,IAAM,MAAM,MAAM,KAAK,CAA6B,GACrE,eAAiB,IAAM,MAAM,WAAW,WAAU,SAAS,CAAU,GACrEE,QAAK,OACL,QAAM,GACN,SAAO;AAEP,eAAS,MAAM,GAAE,GACjB;AACC,YAAI,IAAE,CAAC;AAEP,UAAE,SAAO,OAAO,CAAC;AACjB,UAAE,MAAI,OAAO,IAAI;AACjB,UAAE,MAAI,OAAO,IAAI;AAEjB,UAAE,KAAG,OAAO,GAAG;AAElB,UAAE,KAAG,OAAO,GAAG;AAEZ,UAAE,MAAI,OAAO,IAAI;AAEjB,UAAE,QAAM,KAAK,KAAK,MAAM,KAAK,IAAI,IAAE,EAAE,KAAI,CAAC,CAAC,CAAC;AAC5C,UAAE,IAAE,EAAE,SAAO,IAAE,KAAG,IAAE,EAAE,OAAK,EAAE;AAC7B,UAAE,OAAK,KAAK,EAAE,CAAC;AAEf,UAAE,SAAO,KAAK,KAAK,OAAO,KAAK,IAAI,IAAE,EAAE,QAAO,CAAC,CAAC,CAAC;AACjD,UAAE,KAAG,EAAE,UAAQ,IAAE,IAAG,UAAS,IAAE,EAAE,UAAQ,EAAE;AAC3C,UAAE,QAAM,MAAM,EAAE,EAAE;AAElB,UAAE,UAAQ,QAAQ,GAAE,CAAC;AACrB,UAAE,OAAK,QAAQ,IAAI;AACnB,UAAE,OAAK,QAAQ,IAAI;AACtB,UAAE,OAAK,QAAQ,IAAI;AAChB,UAAE,UAAQ,QAAQ,OAAO;AAEzB,UAAE,MAAI,QAAQ,GAAG;AAEpB,UAAE,MAAI,QAAQ,GAAG;AAEd,UAAE,OAAK,KAAK,GAAE,CAAC;AAEf,UAAE,SAAO,KAAK,KAAK,OAAO,KAAK,IAAI,IAAE,EAAE,MAAK,CAAC,GAAE,CAAC,CAAC;AACjD,UAAE,KAAG,EAAE,UAAQ,IAAE,KAAG,IAAE,EAAE,QAAM,EAAE;AAChC,UAAE,QAAM,MAAM,EAAE,IAAG,CAAC;AAEvB,UAAE,UAAQ,KAAK,KAAK,QAAQ,KAAK,IAAI,IAAE,EAAE,SAAQ,CAAC,GAAE,CAAC,CAAC;AACnD,UAAE,MAAI,EAAE,WAAS,IAAE,IAAG,UAAQ,IAAE,EAAE,WAAS,EAAE;AAC7C,UAAE,SAAO,OAAO,EAAE,KAAI,CAAC;AAEvB,YAAGA,UAAO,MAAO,CAAAA,QAAK,IAAE;AAAe,YAAI,QAAM,IAAEA;AAAM,QAAAA,QAAK;AAAO,UAAE,UAAQ,QAAQ,KAAK;AAE/F,UAAE,QAAM;AAAM;AACd,UAAE,SAAO;AAAO,kBAAQ;AAErB,UAAE,cAAY,YAAY,CAAC;AAC3B,UAAE,eAAa,aAAa,GAAE,CAAC;AAC/B,UAAE,cAAY,YAAY,CAAC;AAC3B,UAAE,eAAa,aAAa,GAAE,CAAC;AAE/B,eAAO;AAAA,MACR;AAEA,YAAM,QAAM,WACZ;AACC,iBAAQ,IAAE,GAAE,IAAE,KAAK,QAAO,KAAI;AAAC,eAAK,CAAC,EAAE,MAAM;AAAA,QAAC;AAAC;AAC/C,QAAAA,QAAK;AACR,gBAAM;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAIA,aAAS,YAAY,MAAK,OAC1B;AACI,UAAI,MAAI,MAAM,WAAW,IAAI,GAC7B,MAAI,MAAM,WAAW,IAAI,GACzB,MAAI,MAAM,WAAW,IAAI,GACzB,cAAY,MAAM,MAAM,KAAK;AAE7B,eAAS,MAAM,GACf;AACC,YAAI,IAAE,CAAC;AAEP,UAAE,MAAI,IAAI,CAAC;AACX,UAAE,MAAI,IAAI,CAAC;AACX,UAAE,MAAI,IAAI,CAAC;AAEX,UAAE,cAAY,YAAY,CAAC;AAE3B,eAAO;AAAA,MACR;AAEA,YAAM,QAAM,WACZ;AACC,YAAI,MAAM;AACV,YAAI,MAAM;AACV,YAAI,MAAM;AACV,oBAAY,MAAM;AAAA,MACnB;AACA,aAAO;AAAA,IACX;AAGA,aAAS,mBAAmB,MAC5B;AACI,UAAI,MAAI,MAAM,WAAW,IAAI,GAC7B,MAAI,MAAM,WAAW,IAAI,GACzB,MAAI,MAAM,WAAW,IAAI;AAGzB,eAAS,MAAM,GACf;AACC,YAAI,IAAE,CAAC;AAEP,UAAE,MAAI,IAAI,CAAC;AACX,UAAE,MAAI,IAAI,CAAC;AACX,UAAE,MAAI,IAAI,CAAC;AAEX,eAAO;AAAA,MACR;AACA,YAAM,QAAM,WACZ;AACC,YAAI,MAAM;AACV,YAAI,MAAM;AACV,YAAI,MAAM;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAEA,YAAQ,aAAW;AACnB,YAAQ,aAAW;AACnB,YAAQ,aAAW;AACnB,YAAQ,gBAAc;AACtB,YAAQ,kBAAgB;AACxB,YAAQ,kBAAgB;AACxB,YAAQ,kBAAgB;AACxB,YAAQ,qBAAmB;AAC3B,YAAQ,qBAAmB;AAE3B,YAAQ,QAAM;AACd,YAAQ,aAAW;AAEnB,YAAQ,WAAS;AACjB,YAAQ,cAAY;AACpB,YAAQ,qBAAmB;AAE3B,YAAQ,iBAAe;AACvB,YAAQ,sBAAoB;AAE5B,YAAQ,YAAU;AAClB,YAAQ,mBAAiB;AACzB,YAAQ,wBAAsB;AAC9B,YAAQ,YAAU;AAAA;AAAA;", "names": ["WindowSize", "Sum", "prev", "Sum2", "UsualIndexSkipBetweenOccations", "newamount"]}