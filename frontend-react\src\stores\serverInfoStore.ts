import { create } from 'zustand';

interface FrequencyLookup {
  name: string;
  url: string;
}

interface ServerInfo {
  serverName: string;
  location: string;
  operators: string[];
  email: string;
  sdrListUrl: string;
  frequencyLookup: FrequencyLookup[];
  callsignLookupUrl: string;
  chatEnabled: boolean;
}

interface ServerInfoState {
  serverInfo: ServerInfo;
  updateServerInfo: (info: Partial<ServerInfo>) => void;
  fetchServerInfo: () => Promise<void>;
}

// Default server info structure
const defaultServerInfo: ServerInfo = {
  serverName: "Loading...",
  location: "-",
  operators: [],
  email: "",
  sdrListUrl: "https://sdr-list.xyz",
  frequencyLookup: [
    {
      name: "MW List",
      url: "https://www.mwlist.org/mwlist_quick_and_easy.php?area=1&kHz=",
    },
    {
      name: "Short-wave.info",
      url: "https://www.short-wave.info/index.php?timbus=NOW&ip=179&porm=4&freq=",
    },
  ],
  callsignLookupUrl: "https://www.qrz.com/db/",
  chatEnabled: true,
};

export const useServerInfoStore = create<ServerInfoState>((set, get) => ({
  serverInfo: defaultServerInfo,
  
  updateServerInfo: (info: Partial<ServerInfo>) => {
    set((state) => ({
      serverInfo: { ...state.serverInfo, ...info }
    }));
  },

  fetchServerInfo: async () => {
    try {
      const response = await fetch("/server-info.json");
      if (response.ok) {
        const data = await response.json();
        get().updateServerInfo(data);
      } else {
        console.error("Failed to fetch server info:", response.status);
      }
    } catch (error) {
      console.error("Error fetching server info:", error);
    }
  },
}));

// Auto-fetch server info on store creation
useServerInfoStore.getState().fetchServerInfo();
