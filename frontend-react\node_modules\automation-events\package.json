{"author": "<PERSON>", "browser": "build/es5/bundle.js", "bugs": {"url": "https://github.com/chrisguttandin/automation-events/issues"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@babel/runtime": "^7.27.6", "tslib": "^2.8.1"}, "description": "A module which provides an implementation of an automation event list.", "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.4", "@babel/plugin-external-helpers": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/register": "^7.27.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-angular": "^19.8.1", "@rollup/plugin-babel": "^6.0.4", "chai": "^4.3.10", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-holy-grail": "^60.0.35", "husky": "^9.1.7", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.3", "karma-mocha": "^2.0.1", "karma-sauce-launcher": "^4.3.6", "karma-sinon-chai": "^2.0.2", "karma-webkit-launcher": "^2.6.0", "karma-webpack": "^5.0.1", "lint-staged": "^15.5.0", "mocha": "^11.6.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "rollup": "^4.43.0", "sinon": "^17.0.2", "sinon-chai": "^3.7.0", "ts-loader": "^9.5.2", "tsconfig-holy-grail": "^15.0.2", "tslint": "^6.1.3", "tslint-config-holy-grail": "^56.0.6", "typescript": "^5.8.3", "webpack": "^5.99.9"}, "engines": {"node": ">=18.2.0"}, "files": ["build/es2019/", "build/es5/", "build/node/", "src/"], "homepage": "https://github.com/chrisguttandin/automation-events", "keywords": ["AudioParam", "Web Audio API"], "license": "MIT", "main": "build/node/module.js", "module": "build/es2019/module.js", "name": "automation-events", "repository": {"type": "git", "url": "https://github.com/chrisguttandin/automation-events.git"}, "scripts": {"build": "rimraf build/* && tsc --project src/tsconfig.json && rollup --config config/rollup/bundle.mjs && babel ./build/es2019 --config-file ./config/babel/build.json --out-dir ./build/node", "lint": "npm run lint:config && npm run lint:src && npm run lint:test", "lint:config": "eslint --config config/eslint/config.json --ext .cjs --ext .js --ext .mjs --report-unused-disable-directives config/", "lint:src": "tslint --config config/tslint/src.json --project src/tsconfig.json src/*.ts src/**/*.ts", "lint:test": "eslint --config config/eslint/test.json --ext .js --report-unused-disable-directives test/", "prepare": "husky", "prepublishOnly": "npm run build", "test": "npm run lint && npm run build && npm run test:unit-browser && npm run test:unit-node", "test:unit-browser": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"unit\" ] && [ \"$TARGET\" = \"\" -o \"$TARGET\" = \"chrome\" -o \"$TARGET\" = \"firefox\" -o \"$TARGET\" = \"safari\" ]; then karma start config/karma/config-unit.js --single-run; fi", "test:unit-node": "if [ \"$TYPE\" = \"\" -o \"$TYPE\" = \"unit\" ] && [ \"$TARGET\" = \"\" -o \"$TARGET\" = \"node\" ]; then mocha --bail --parallel --recursive --require config/mocha/config-unit.js test/unit; fi"}, "types": "build/es2019/module.d.ts", "version": "7.1.11"}