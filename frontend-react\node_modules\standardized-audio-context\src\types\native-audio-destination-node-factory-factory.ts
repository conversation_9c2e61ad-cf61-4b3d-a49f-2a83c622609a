import { TNativeAudioDestinationNodeFactory } from './native-audio-destination-node-factory';
import { TNativeGainNodeFactory } from './native-gain-node-factory';
import { TOverwriteAccessorsFunction } from './overwrite-accessors-function';

export type TNativeAudioDestinationNodeFactoryFactory = (
    createNativeGainNode: TNativeGainNodeFactory,
    overwriteAccessors: TOverwriteAccessorsFunction
) => TNativeAudioDestinationNodeFactory;
