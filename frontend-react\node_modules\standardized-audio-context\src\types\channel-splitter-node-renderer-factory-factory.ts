import { TChannelSplitterNodeRendererFactory } from './channel-splitter-node-renderer-factory';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeChannelSplitterNodeFactory } from './native-channel-splitter-node-factory';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';

export type TChannelSplitterNodeRendererFactoryFactory = (
    createNativeChannelSplitterNode: TNativeChannelSplitterNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction
) => TChannelSplitterNodeRendererFactory;
