import { IMediaStreamTrackAudioSourceOptions } from '../interfaces';
import { TNativeAudioContext } from './native-audio-context';
import { TNativeMediaStreamTrackAudioSourceNode } from './native-media-stream-track-audio-source-node';

export type TNativeMediaStreamTrackAudioSourceNodeFactory = (
    nativeAudioContext: TNativeAudioContext,
    options: IMediaStreamTrackAudioSourceOptions
) => TNativeMediaStreamTrackAudioSourceNode;
