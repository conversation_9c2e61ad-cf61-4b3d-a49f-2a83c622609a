{"name": "phantomsdr-react", "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"cbor-js": "^0.1.0", "nanoid": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "fzstd": "^0.1.1", "efficient-rolling-stats": "^0.1.1", "cbor-x": "^1.5.9", "denque": "^2.1.0", "binary-search-bounds": "^2.0.5", "buffer": "^6.0.3", "colormap": "^2.3.2", "copy-to-clipboard": "^3.3.3", "core-js": "^3.36.1", "is-number": "^7.0.0", "live-moving-average": "^1.1.0", "qs": "^6.12.0", "standardized-audio-context": "^25.3.68", "typedarray-to-buffer": "^4.0.0", "@egjs/hammerjs": "^2.0.17", "@popperjs/core": "^2.11.8"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-legacy": "^4.1.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.21", "cssnano": "^7.1.0", "postcss": "^8.5.6", "rollup-plugin-visualizer": "^5.9.2", "tailwindcss": "^3.4.17", "vite": "^4.4.0", "vite-plugin-top-level-await": "^1.3.1", "vite-plugin-wasm": "^3.2.2", "esbuild": "^0.20.2", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "resolve": "^1.22.8", "source-map": "^0.7.4", "postcss-load-config": "^5.0.3"}}