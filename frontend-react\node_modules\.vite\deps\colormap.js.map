{"version": 3, "sources": ["../../lerp/index.js", "../../colormap/index.js"], "sourcesContent": ["function lerp(v0, v1, t) {\n    return v0*(1-t)+v1*t\n}\nmodule.exports = lerp", "/*\n * <PERSON>\n * January 2013\n * License MIT\n */\n'use strict';\n\nvar colorScale = require('./colorScale');\nvar lerp = require('lerp')\n\nmodule.exports = createColormap;\n\nfunction createColormap (spec) {\n    /*\n     * Default Options\n     */\n    var indicies, fromrgba, torgba,\n        nsteps, cmap, colormap, format,\n        nshades, colors, alpha, i;\n\n    if ( !spec ) spec = {};\n\n    nshades = (spec.nshades || 72) - 1;\n    format = spec.format || 'hex';\n\n    colormap = spec.colormap;\n    if (!colormap) colormap = 'jet';\n\n    if (typeof colormap === 'string') {\n        colormap = colormap.toLowerCase();\n\n        if (!colorScale[colormap]) {\n            throw Error(colormap + ' not a supported colorscale');\n        }\n\n        cmap = colorScale[colormap];\n\n    } else if (Array.isArray(colormap)) {\n        cmap = colormap.slice();\n\n    } else {\n        throw Error('unsupported colormap option', colormap);\n    }\n\n    if (cmap.length > nshades + 1) {\n        throw new Error(\n            colormap+' map requires nshades to be at least size '+cmap.length\n        );\n    }\n\n    if (!Array.isArray(spec.alpha)) {\n\n        if (typeof spec.alpha === 'number') {\n            alpha = [spec.alpha, spec.alpha];\n\n        } else {\n            alpha = [1, 1];\n        }\n\n    } else if (spec.alpha.length !== 2) {\n        alpha = [1, 1];\n\n    } else {\n        alpha = spec.alpha.slice();\n    }\n\n    // map index points from 0..1 to 0..n-1\n    indicies = cmap.map(function(c) {\n        return Math.round(c.index * nshades);\n    });\n\n    // Add alpha channel to the map\n    alpha[0] = Math.min(Math.max(alpha[0], 0), 1);\n    alpha[1] = Math.min(Math.max(alpha[1], 0), 1);\n\n    var steps = cmap.map(function(c, i) {\n        var index = cmap[i].index\n\n        var rgba = cmap[i].rgb.slice();\n\n        // if user supplies their own map use it\n        if (rgba.length === 4 && rgba[3] >= 0 && rgba[3] <= 1) {\n            return rgba\n        }\n        rgba[3] = alpha[0] + (alpha[1] - alpha[0])*index;\n\n        return rgba\n    })\n\n\n    /*\n     * map increasing linear values between indicies to\n     * linear steps in colorvalues\n     */\n    var colors = []\n    for (i = 0; i < indicies.length-1; ++i) {\n        nsteps = indicies[i+1] - indicies[i];\n        fromrgba = steps[i];\n        torgba = steps[i+1];\n\n        for (var j = 0; j < nsteps; j++) {\n            var amt = j / nsteps\n            colors.push([\n                Math.round(lerp(fromrgba[0], torgba[0], amt)),\n                Math.round(lerp(fromrgba[1], torgba[1], amt)),\n                Math.round(lerp(fromrgba[2], torgba[2], amt)),\n                lerp(fromrgba[3], torgba[3], amt)\n            ])\n        }\n    }\n\n    //add 1 step as last value\n    colors.push(cmap[cmap.length - 1].rgb.concat(alpha[1]))\n\n    if (format === 'hex') colors = colors.map( rgb2hex );\n    else if (format === 'rgbaString') colors = colors.map( rgbaStr );\n    else if (format === 'float') colors = colors.map( rgb2float );\n\n    return colors;\n};\n\nfunction rgb2float (rgba) {\n    return [\n        rgba[0] / 255,\n        rgba[1] / 255,\n        rgba[2] / 255,\n        rgba[3]\n    ]\n}\n\nfunction rgb2hex (rgba) {\n    var dig, hex = '#';\n    for (var i = 0; i < 3; ++i) {\n        dig = rgba[i];\n        dig = dig.toString(16);\n        hex += ('00' + dig).substr( dig.length );\n    }\n    return hex;\n}\n\nfunction rgbaStr (rgba) {\n    return 'rgba(' + rgba.join(',') + ')';\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,aAAS,KAAK,IAAI,IAAI,GAAG;AACrB,aAAO,MAAI,IAAE,KAAG,KAAG;AAAA,IACvB;AACA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAOA,QAAI,aAAa;AACjB,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,aAAS,eAAgB,MAAM;AAI3B,UAAI,UAAU,UAAU,QACpB,QAAQ,MAAM,UAAU,QACxB,SAAS,QAAQ,OAAO;AAE5B,UAAK,CAAC,KAAO,QAAO,CAAC;AAErB,iBAAW,KAAK,WAAW,MAAM;AACjC,eAAS,KAAK,UAAU;AAExB,iBAAW,KAAK;AAChB,UAAI,CAAC,SAAU,YAAW;AAE1B,UAAI,OAAO,aAAa,UAAU;AAC9B,mBAAW,SAAS,YAAY;AAEhC,YAAI,CAAC,WAAW,QAAQ,GAAG;AACvB,gBAAM,MAAM,WAAW,6BAA6B;AAAA,QACxD;AAEA,eAAO,WAAW,QAAQ;AAAA,MAE9B,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAChC,eAAO,SAAS,MAAM;AAAA,MAE1B,OAAO;AACH,cAAM,MAAM,+BAA+B,QAAQ;AAAA,MACvD;AAEA,UAAI,KAAK,SAAS,UAAU,GAAG;AAC3B,cAAM,IAAI;AAAA,UACN,WAAS,+CAA6C,KAAK;AAAA,QAC/D;AAAA,MACJ;AAEA,UAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,GAAG;AAE5B,YAAI,OAAO,KAAK,UAAU,UAAU;AAChC,kBAAQ,CAAC,KAAK,OAAO,KAAK,KAAK;AAAA,QAEnC,OAAO;AACH,kBAAQ,CAAC,GAAG,CAAC;AAAA,QACjB;AAAA,MAEJ,WAAW,KAAK,MAAM,WAAW,GAAG;AAChC,gBAAQ,CAAC,GAAG,CAAC;AAAA,MAEjB,OAAO;AACH,gBAAQ,KAAK,MAAM,MAAM;AAAA,MAC7B;AAGA,iBAAW,KAAK,IAAI,SAAS,GAAG;AAC5B,eAAO,KAAK,MAAM,EAAE,QAAQ,OAAO;AAAA,MACvC,CAAC;AAGD,YAAM,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AAC5C,YAAM,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AAE5C,UAAI,QAAQ,KAAK,IAAI,SAAS,GAAGA,IAAG;AAChC,YAAI,QAAQ,KAAKA,EAAC,EAAE;AAEpB,YAAI,OAAO,KAAKA,EAAC,EAAE,IAAI,MAAM;AAG7B,YAAI,KAAK,WAAW,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG;AACnD,iBAAO;AAAA,QACX;AACA,aAAK,CAAC,IAAI,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,KAAG;AAE3C,eAAO;AAAA,MACX,CAAC;AAOD,UAAI,SAAS,CAAC;AACd,WAAK,IAAI,GAAG,IAAI,SAAS,SAAO,GAAG,EAAE,GAAG;AACpC,iBAAS,SAAS,IAAE,CAAC,IAAI,SAAS,CAAC;AACnC,mBAAW,MAAM,CAAC;AAClB,iBAAS,MAAM,IAAE,CAAC;AAElB,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,cAAI,MAAM,IAAI;AACd,iBAAO,KAAK;AAAA,YACR,KAAK,MAAM,KAAK,SAAS,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;AAAA,YAC5C,KAAK,MAAM,KAAK,SAAS,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;AAAA,YAC5C,KAAK,MAAM,KAAK,SAAS,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC;AAAA,YAC5C,KAAK,SAAS,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG;AAAA,UACpC,CAAC;AAAA,QACL;AAAA,MACJ;AAGA,aAAO,KAAK,KAAK,KAAK,SAAS,CAAC,EAAE,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC;AAEtD,UAAI,WAAW,MAAO,UAAS,OAAO,IAAK,OAAQ;AAAA,eAC1C,WAAW,aAAc,UAAS,OAAO,IAAK,OAAQ;AAAA,eACtD,WAAW,QAAS,UAAS,OAAO,IAAK,SAAU;AAE5D,aAAO;AAAA,IACX;AAEA,aAAS,UAAW,MAAM;AACtB,aAAO;AAAA,QACH,KAAK,CAAC,IAAI;AAAA,QACV,KAAK,CAAC,IAAI;AAAA,QACV,KAAK,CAAC,IAAI;AAAA,QACV,KAAK,CAAC;AAAA,MACV;AAAA,IACJ;AAEA,aAAS,QAAS,MAAM;AACpB,UAAI,KAAK,MAAM;AACf,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,cAAM,KAAK,CAAC;AACZ,cAAM,IAAI,SAAS,EAAE;AACrB,gBAAQ,OAAO,KAAK,OAAQ,IAAI,MAAO;AAAA,MAC3C;AACA,aAAO;AAAA,IACX;AAEA,aAAS,QAAS,MAAM;AACpB,aAAO,UAAU,KAAK,KAAK,GAAG,IAAI;AAAA,IACtC;AAAA;AAAA;", "names": ["i"]}