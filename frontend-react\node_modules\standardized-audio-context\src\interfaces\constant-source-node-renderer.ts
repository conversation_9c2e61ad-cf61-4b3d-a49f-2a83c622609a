import { IAudioNodeRenderer } from './audio-node-renderer';
import { IConstantSourceNode } from './constant-source-node';
import { IMinimalOfflineAudioContext } from './minimal-offline-audio-context';
import { IOfflineAudioContext } from './offline-audio-context';

export interface IConstantSourceNodeRenderer<T extends IMinimalOfflineAudioContext | IOfflineAudioContext>
    extends IAudioNodeRenderer<T, IConstantSourceNode<T>> {
    start: number;

    stop: number;
}
