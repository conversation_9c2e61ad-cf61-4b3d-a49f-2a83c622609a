let Gb,<PERSON>h,Qb,<PERSON>s,Nh,Dh,zh,Uh,qh,jh,Wh,Vh,Me,Hh,Bh,$h,Yh,Xh,Jh,Kn,Kh,zS=(async()=>{Qb=function(){import.meta.url,import("_").then(async r=>(await r.__tla,r)).catch(()=>1),async function*(){}().next()},function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))o(s);new MutationObserver(s=>{for(const u of s)if(u.type==="childList")for(const d of u.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&o(d)}).observe(document,{childList:!0,subtree:!0});function i(s){const u={};return s.integrity&&(u.integrity=s.integrity),s.referrerPolicy&&(u.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?u.credentials="include":s.crossOrigin==="anonymous"?u.credentials="omit":u.credentials="same-origin",u}function o(s){if(s.ep)return;s.ep=!0;const u=i(s);fetch(s.href,u)}}();var Gh=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Fs(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Qh={exports:{}},lo={},ep;function n2(){if(ep)return lo;ep=1;var r=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function o(s,u,d){var f=null;if(d!==void 0&&(f=""+d),u.key!==void 0&&(f=""+u.key),"key"in u){d={};for(var h in u)h!=="key"&&(d[h]=u[h])}else d=u;return u=d.ref,{$$typeof:r,type:s,key:f,ref:u!==void 0?u:null,props:d}}return lo.Fragment=i,lo.jsx=o,lo.jsxs=o,lo}var tp;function r2(){return tp||(tp=1,Qh.exports=n2()),Qh.exports}let hu,Le;Me=r2(),hu={exports:{}},Le={};var np;function i2(){if(np)return Le;np=1;var r=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),v=Symbol.iterator;function x(w){return w===null||typeof w!="object"?null:(w=v&&w[v]||w["@@iterator"],typeof w=="function"?w:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,R={};function j(w,D,te){this.props=w,this.context=D,this.refs=R,this.updater=te||E}j.prototype.isReactComponent={},j.prototype.setState=function(w,D){if(typeof w!="object"&&typeof w!="function"&&w!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,w,D,"setState")},j.prototype.forceUpdate=function(w){this.updater.enqueueForceUpdate(this,w,"forceUpdate")};function ie(){}ie.prototype=j.prototype;function B(w,D,te){this.props=w,this.context=D,this.refs=R,this.updater=te||E}var $=B.prototype=new ie;$.constructor=B,M($,j.prototype),$.isPureReactComponent=!0;var ae=Array.isArray,V={H:null,A:null,T:null,S:null,V:null},Y=Object.prototype.hasOwnProperty;function N(w,D,te,de,me,ge){return te=ge.ref,{$$typeof:r,type:w,key:D,ref:te!==void 0?te:null,props:ge}}function I(w,D){return N(w.type,D,void 0,void 0,void 0,w.props)}function z(w){return typeof w=="object"&&w!==null&&w.$$typeof===r}function F(w){var D={"=":"=0",":":"=2"};return"$"+w.replace(/[=:]/g,function(te){return D[te]})}var b=/\/+/g;function S(w,D){return typeof w=="object"&&w!==null&&w.key!=null?F(""+w.key):D.toString(36)}function T(){}function O(w){switch(w.status){case"fulfilled":return w.value;case"rejected":throw w.reason;default:switch(typeof w.status=="string"?w.then(T,T):(w.status="pending",w.then(function(D){w.status==="pending"&&(w.status="fulfilled",w.value=D)},function(D){w.status==="pending"&&(w.status="rejected",w.reason=D)})),w.status){case"fulfilled":return w.value;case"rejected":throw w.reason}}throw w}function U(w,D,te,de,me){var ge=typeof w;(ge==="undefined"||ge==="boolean")&&(w=null);var _e=!1;if(w===null)_e=!0;else switch(ge){case"bigint":case"string":case"number":_e=!0;break;case"object":switch(w.$$typeof){case r:case i:_e=!0;break;case y:return _e=w._init,U(_e(w._payload),D,te,de,me)}}if(_e)return me=me(w),_e=de===""?"."+S(w,0):de,ae(me)?(te="",_e!=null&&(te=_e.replace(b,"$&/")+"/"),U(me,D,te,"",function(Ye){return Ye})):me!=null&&(z(me)&&(me=I(me,te+(me.key==null||w&&w.key===me.key?"":(""+me.key).replace(b,"$&/")+"/")+_e)),D.push(me)),1;_e=0;var ze=de===""?".":de+":";if(ae(w))for(var he=0;he<w.length;he++)de=w[he],ge=ze+S(de,he),_e+=U(de,D,te,ge,me);else if(he=x(w),typeof he=="function")for(w=he.call(w),he=0;!(de=w.next()).done;)de=de.value,ge=ze+S(de,he++),_e+=U(de,D,te,ge,me);else if(ge==="object"){if(typeof w.then=="function")return U(O(w),D,te,de,me);throw D=String(w),Error("Objects are not valid as a React child (found: "+(D==="[object Object]"?"object with keys {"+Object.keys(w).join(", ")+"}":D)+"). If you meant to render a collection of children, use an array instead.")}return _e}function k(w,D,te){if(w==null)return w;var de=[],me=0;return U(w,de,"","",function(ge){return D.call(te,ge,me++)}),de}function K(w){if(w._status===-1){var D=w._result;D=D(),D.then(function(te){(w._status===0||w._status===-1)&&(w._status=1,w._result=te)},function(te){(w._status===0||w._status===-1)&&(w._status=2,w._result=te)}),w._status===-1&&(w._status=0,w._result=D)}if(w._status===1)return w._result.default;throw w._result}var G=typeof reportError=="function"?reportError:function(w){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var D=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof w=="object"&&w!==null&&typeof w.message=="string"?String(w.message):String(w),error:w});if(!window.dispatchEvent(D))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",w);return}console.error(w)};function X(){}return Le.Children={map:k,forEach:function(w,D,te){k(w,function(){D.apply(this,arguments)},te)},count:function(w){var D=0;return k(w,function(){D++}),D},toArray:function(w){return k(w,function(D){return D})||[]},only:function(w){if(!z(w))throw Error("React.Children.only expected to receive a single React element child.");return w}},Le.Component=j,Le.Fragment=o,Le.Profiler=u,Le.PureComponent=B,Le.StrictMode=s,Le.Suspense=p,Le.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=V,Le.__COMPILER_RUNTIME={__proto__:null,c:function(w){return V.H.useMemoCache(w)}},Le.cache=function(w){return function(){return w.apply(null,arguments)}},Le.cloneElement=function(w,D,te){if(w==null)throw Error("The argument must be a React element, but you passed "+w+".");var de=M({},w.props),me=w.key,ge=void 0;if(D!=null)for(_e in D.ref!==void 0&&(ge=void 0),D.key!==void 0&&(me=""+D.key),D)!Y.call(D,_e)||_e==="key"||_e==="__self"||_e==="__source"||_e==="ref"&&D.ref===void 0||(de[_e]=D[_e]);var _e=arguments.length-2;if(_e===1)de.children=te;else if(1<_e){for(var ze=Array(_e),he=0;he<_e;he++)ze[he]=arguments[he+2];de.children=ze}return N(w.type,me,void 0,void 0,ge,de)},Le.createContext=function(w){return w={$$typeof:f,_currentValue:w,_currentValue2:w,_threadCount:0,Provider:null,Consumer:null},w.Provider=w,w.Consumer={$$typeof:d,_context:w},w},Le.createElement=function(w,D,te){var de,me={},ge=null;if(D!=null)for(de in D.key!==void 0&&(ge=""+D.key),D)Y.call(D,de)&&de!=="key"&&de!=="__self"&&de!=="__source"&&(me[de]=D[de]);var _e=arguments.length-2;if(_e===1)me.children=te;else if(1<_e){for(var ze=Array(_e),he=0;he<_e;he++)ze[he]=arguments[he+2];me.children=ze}if(w&&w.defaultProps)for(de in _e=w.defaultProps,_e)me[de]===void 0&&(me[de]=_e[de]);return N(w,ge,void 0,void 0,null,me)},Le.createRef=function(){return{current:null}},Le.forwardRef=function(w){return{$$typeof:h,render:w}},Le.isValidElement=z,Le.lazy=function(w){return{$$typeof:y,_payload:{_status:-1,_result:w},_init:K}},Le.memo=function(w,D){return{$$typeof:m,type:w,compare:D===void 0?null:D}},Le.startTransition=function(w){var D=V.T,te={};V.T=te;try{var de=w(),me=V.S;me!==null&&me(te,de),typeof de=="object"&&de!==null&&typeof de.then=="function"&&de.then(X,G)}catch(ge){G(ge)}finally{V.T=D}},Le.unstable_useCacheRefresh=function(){return V.H.useCacheRefresh()},Le.use=function(w){return V.H.use(w)},Le.useActionState=function(w,D,te){return V.H.useActionState(w,D,te)},Le.useCallback=function(w,D){return V.H.useCallback(w,D)},Le.useContext=function(w){return V.H.useContext(w)},Le.useDebugValue=function(){},Le.useDeferredValue=function(w,D){return V.H.useDeferredValue(w,D)},Le.useEffect=function(w,D,te){var de=V.H;if(typeof te=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return de.useEffect(w,D)},Le.useId=function(){return V.H.useId()},Le.useImperativeHandle=function(w,D,te){return V.H.useImperativeHandle(w,D,te)},Le.useInsertionEffect=function(w,D){return V.H.useInsertionEffect(w,D)},Le.useLayoutEffect=function(w,D){return V.H.useLayoutEffect(w,D)},Le.useMemo=function(w,D){return V.H.useMemo(w,D)},Le.useOptimistic=function(w,D){return V.H.useOptimistic(w,D)},Le.useReducer=function(w,D,te){return V.H.useReducer(w,D,te)},Le.useRef=function(w){return V.H.useRef(w)},Le.useState=function(w){return V.H.useState(w)},Le.useSyncExternalStore=function(w,D,te){return V.H.useSyncExternalStore(w,D,te)},Le.useTransition=function(){return V.H.useTransition()},Le.version="19.1.0",Le}var rp;function pu(){return rp||(rp=1,hu.exports=i2()),hu.exports}Kn=pu();const ip=Fs(Kn);var mu={exports:{}},uo={},ap={exports:{}},op={},sp;function a2(){return sp||(sp=1,function(r){function i(k,K){var G=k.length;k.push(K);e:for(;0<G;){var X=G-1>>>1,w=k[X];if(0<u(w,K))k[X]=K,k[G]=w,G=X;else break e}}function o(k){return k.length===0?null:k[0]}function s(k){if(k.length===0)return null;var K=k[0],G=k.pop();if(G!==K){k[0]=G;e:for(var X=0,w=k.length,D=w>>>1;X<D;){var te=2*(X+1)-1,de=k[te],me=te+1,ge=k[me];if(0>u(de,G))me<w&&0>u(ge,de)?(k[X]=ge,k[me]=G,X=me):(k[X]=de,k[te]=G,X=te);else if(me<w&&0>u(ge,G))k[X]=ge,k[me]=G,X=me;else break e}}return K}function u(k,K){var G=k.sortIndex-K.sortIndex;return G!==0?G:k.id-K.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;r.unstable_now=function(){return d.now()}}else{var f=Date,h=f.now();r.unstable_now=function(){return f.now()-h}}var p=[],m=[],y=1,v=null,x=3,E=!1,M=!1,R=!1,j=!1,ie=typeof setTimeout=="function"?setTimeout:null,B=typeof clearTimeout=="function"?clearTimeout:null,$=typeof setImmediate<"u"?setImmediate:null;function ae(k){for(var K=o(m);K!==null;){if(K.callback===null)s(m);else if(K.startTime<=k)s(m),K.sortIndex=K.expirationTime,i(p,K);else break;K=o(m)}}function V(k){if(R=!1,ae(k),!M)if(o(p)!==null)M=!0,Y||(Y=!0,S());else{var K=o(m);K!==null&&U(V,K.startTime-k)}}var Y=!1,N=-1,I=5,z=-1;function F(){return j?!0:!(r.unstable_now()-z<I)}function b(){if(j=!1,Y){var k=r.unstable_now();z=k;var K=!0;try{e:{M=!1,R&&(R=!1,B(N),N=-1),E=!0;var G=x;try{t:{for(ae(k),v=o(p);v!==null&&!(v.expirationTime>k&&F());){var X=v.callback;if(typeof X=="function"){v.callback=null,x=v.priorityLevel;var w=X(v.expirationTime<=k);if(k=r.unstable_now(),typeof w=="function"){v.callback=w,ae(k),K=!0;break t}v===o(p)&&s(p),ae(k)}else s(p);v=o(p)}if(v!==null)K=!0;else{var D=o(m);D!==null&&U(V,D.startTime-k),K=!1}}break e}finally{v=null,x=G,E=!1}K=void 0}}finally{K?S():Y=!1}}}var S;if(typeof $=="function")S=function(){$(b)};else if(typeof MessageChannel<"u"){var T=new MessageChannel,O=T.port2;T.port1.onmessage=b,S=function(){O.postMessage(null)}}else S=function(){ie(b,0)};function U(k,K){N=ie(function(){k(r.unstable_now())},K)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(k){k.callback=null},r.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<k?Math.floor(1e3/k):5},r.unstable_getCurrentPriorityLevel=function(){return x},r.unstable_next=function(k){switch(x){case 1:case 2:case 3:var K=3;break;default:K=x}var G=x;x=K;try{return k()}finally{x=G}},r.unstable_requestPaint=function(){j=!0},r.unstable_runWithPriority=function(k,K){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var G=x;x=k;try{return K()}finally{x=G}},r.unstable_scheduleCallback=function(k,K,G){var X=r.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?X+G:X):G=X,k){case 1:var w=-1;break;case 2:w=250;break;case 5:w=1073741823;break;case 4:w=1e4;break;default:w=5e3}return w=G+w,k={id:y++,callback:K,priorityLevel:k,startTime:G,expirationTime:w,sortIndex:-1},G>X?(k.sortIndex=G,i(m,k),o(p)===null&&k===o(m)&&(R?(B(N),N=-1):R=!0,U(V,G-X))):(k.sortIndex=w,i(p,k),M||E||(M=!0,Y||(Y=!0,S()))),k},r.unstable_shouldYield=F,r.unstable_wrapCallback=function(k){var K=x;return function(){var G=x;x=K;try{return k.apply(this,arguments)}finally{x=G}}}}(op)),op}var lp;function o2(){return lp||(lp=1,ap.exports=a2()),ap.exports}var gu={exports:{}},Qt={},up;function s2(){if(up)return Qt;up=1;var r=pu();function i(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)m+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var s={d:{f:o,r:function(){throw Error(i(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},u=Symbol.for("react.portal");function d(p,m,y){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:v==null?null:""+v,children:p,containerInfo:m,implementation:y}}var f=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return Qt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Qt.createPortal=function(p,m){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(i(299));return d(p,m,null,y)},Qt.flushSync=function(p){var m=f.T,y=s.p;try{if(f.T=null,s.p=2,p)return p()}finally{f.T=m,s.p=y,s.d.f()}},Qt.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,s.d.C(p,m))},Qt.prefetchDNS=function(p){typeof p=="string"&&s.d.D(p)},Qt.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var y=m.as,v=h(y,m.crossOrigin),x=typeof m.integrity=="string"?m.integrity:void 0,E=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;y==="style"?s.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:v,integrity:x,fetchPriority:E}):y==="script"&&s.d.X(p,{crossOrigin:v,integrity:x,fetchPriority:E,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},Qt.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var y=h(m.as,m.crossOrigin);s.d.M(p,{crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&s.d.M(p)},Qt.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var y=m.as,v=h(y,m.crossOrigin);s.d.L(p,y,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},Qt.preloadModule=function(p,m){if(typeof p=="string")if(m){var y=h(m.as,m.crossOrigin);s.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else s.d.m(p)},Qt.requestFormReset=function(p){s.d.r(p)},Qt.unstable_batchedUpdates=function(p,m){return p(m)},Qt.useFormState=function(p,m,y){return f.H.useFormState(p,m,y)},Qt.useFormStatus=function(){return f.H.useHostTransitionStatus()},Qt.version="19.1.0",Qt}var cp;function l2(){if(cp)return gu.exports;cp=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(i){console.error(i)}}return r(),gu.exports=s2(),gu.exports}var dp;function u2(){if(dp)return uo;dp=1;var r=o2(),i=pu(),o=l2();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function f(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(d(e)!==e)throw Error(s(188))}function p(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,a=t;;){var l=n.return;if(l===null)break;var c=l.alternate;if(c===null){if(a=l.return,a!==null){n=a;continue}break}if(l.child===c.child){for(c=l.child;c;){if(c===n)return h(l),e;if(c===a)return h(l),t;c=c.sibling}throw Error(s(188))}if(n.return!==a.return)n=l,a=c;else{for(var g=!1,_=l.child;_;){if(_===n){g=!0,n=l,a=c;break}if(_===a){g=!0,a=l,n=c;break}_=_.sibling}if(!g){for(_=c.child;_;){if(_===n){g=!0,n=c,a=l;break}if(_===a){g=!0,a=c,n=l;break}_=_.sibling}if(!g)throw Error(s(189))}}if(n.alternate!==a)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,v=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),E=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),R=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),ie=Symbol.for("react.provider"),B=Symbol.for("react.consumer"),$=Symbol.for("react.context"),ae=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),Y=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),I=Symbol.for("react.lazy"),z=Symbol.for("react.activity"),F=Symbol.for("react.memo_cache_sentinel"),b=Symbol.iterator;function S(e){return e===null||typeof e!="object"?null:(e=b&&e[b]||e["@@iterator"],typeof e=="function"?e:null)}var T=Symbol.for("react.client.reference");function O(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===T?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case M:return"Fragment";case j:return"Profiler";case R:return"StrictMode";case V:return"Suspense";case Y:return"SuspenseList";case z:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case E:return"Portal";case $:return(e.displayName||"Context")+".Provider";case B:return(e._context.displayName||"Context")+".Consumer";case ae:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case N:return t=e.displayName||null,t!==null?t:O(e.type)||"Memo";case I:t=e._payload,e=e._init;try{return O(e(t))}catch(n){}}return null}var U=Array.isArray,k=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G={pending:!1,data:null,method:null,action:null},X=[],w=-1;function D(e){return{current:e}}function te(e){0>w||(e.current=X[w],X[w]=null,w--)}function de(e,t){w++,X[w]=e.current,e.current=t}var me=D(null),ge=D(null),_e=D(null),ze=D(null);function he(e,t){switch(de(_e,t),de(ge,e),de(me,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Eb(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Eb(t),e=Mb(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}te(me),de(me,e)}function Ye(){te(me),te(ge),te(_e)}function Ze(e){e.memoizedState!==null&&de(ze,e);var t=me.current,n=Mb(t,e.type);t!==n&&(de(ge,e),de(me,n))}function dt(e){ge.current===e&&(te(me),te(ge)),ze.current===e&&(te(ze),ks._currentValue=G)}var bt=Object.prototype.hasOwnProperty,tr=r.unstable_scheduleCallback,et=r.unstable_cancelCallback,Pe=r.unstable_shouldYield,ve=r.unstable_requestPaint,Xt=r.unstable_now,Ri=r.unstable_getCurrentPriorityLevel,dn=r.unstable_ImmediatePriority,Ue=r.unstable_UserBlockingPriority,ft=r.unstable_NormalPriority,Ii=r.unstable_LowPriority,ya=r.unstable_IdlePriority,Li=r.log,Po=r.unstable_setDisableYieldValue,wr=null,Xe=null;function Rn(e){if(typeof Li=="function"&&Po(e),Xe&&typeof Xe.setStrictMode=="function")try{Xe.setStrictMode(wr,e)}catch(t){}}var Nt=Math.clz32?Math.clz32:Di,Ni=Math.log,Oo=Math.LN2;function Di(e){return e>>>=0,e===0?32:31-(Ni(e)/Oo|0)|0}var on=256,In=4194304;function Dt(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ht(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var l=0,c=e.suspendedLanes,g=e.pingedLanes;e=e.warmLanes;var _=a&134217727;return _!==0?(a=_&~c,a!==0?l=Dt(a):(g&=_,g!==0?l=Dt(g):n||(n=_&~e,n!==0&&(l=Dt(n))))):(_=a&~c,_!==0?l=Dt(_):g!==0?l=Dt(g):n||(n=a&~e,n!==0&&(l=Dt(n)))),l===0?0:t!==0&&t!==l&&(t&c)===0&&(c=l&-l,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:l}function _r(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Ro(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ba(){var e=on;return on<<=1,(on&4194048)===0&&(on=256),e}function wa(){var e=In;return In<<=1,(In&62914560)===0&&(In=4194304),e}function Bn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function $n(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function _a(e,t,n,a,l,c){var g=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var _=e.entanglements,L=e.expirationTimes,Q=e.hiddenUpdates;for(n=g&~n;0<n;){var se=31-Nt(n),fe=1<<se;_[se]=0,L[se]=-1;var ne=Q[se];if(ne!==null)for(Q[se]=null,se=0;se<ne.length;se++){var re=ne[se];re!==null&&(re.lane&=-536870913)}n&=~fe}a!==0&&xa(e,a,0),c!==0&&l===0&&e.tag!==0&&(e.suspendedLanes|=c&~(g&~t))}function xa(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-Nt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function Sa(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-Nt(n),l=1<<a;l&t|e[a]&t&&(e[a]|=t),n&=~l}}function zi(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ui(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function ka(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:$b(e.type))}function Io(e,t){var n=K.p;try{return K.p=e,t()}finally{K.p=n}}var sn=Math.random().toString(36).slice(2),wt="__reactFiber$"+sn,_t="__reactProps$"+sn,nr="__reactContainer$"+sn,qi="__reactEvents$"+sn,Yr="__reactListeners$"+sn,Lo="__reactHandles$"+sn,we="__reactResources$"+sn,xr="__reactMarker$"+sn;function ji(e){delete e[wt],delete e[_t],delete e[qi],delete e[Yr],delete e[Lo]}function rr(e){var t=e[wt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[nr]||n[wt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Pb(e);e!==null;){if(n=e[wt])return n;e=Pb(e)}return t}e=n,n=e.parentNode}return null}function be(e){if(e=e[wt]||e[nr]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Sr(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function ir(e){var t=e[we];return t||(t=e[we]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function xt(e){e[xr]=!0}var Ca=new Set,Ea={};function wn(e,t){Ft(e,t),Ft(e+"Capture",t)}function Ft(e,t){for(Ea[e]=t,e=0;e<t.length;e++)Ca.add(t[e])}var No=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ma={},kr={};function Jt(e){return bt.call(kr,e)?!0:bt.call(Ma,e)?!1:No.test(e)?kr[e]=!0:(Ma[e]=!0,!1)}function Xr(e,t,n){if(Jt(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Jr(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function fn(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+a)}}var Cr,Kr;function ar(e){if(Cr===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Cr=t&&t[1]||"",Kr=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Cr+e+Kr}var Wi=!1;function Zr(e,t){if(!e||Wi)return"";Wi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var fe=function(){throw Error()};if(Object.defineProperty(fe.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(fe,[])}catch(re){var ne=re}Reflect.construct(e,[],fe)}else{try{fe.call()}catch(re){ne=re}e.call(fe.prototype)}}else{try{throw Error()}catch(re){ne=re}(fe=e())&&typeof fe.catch=="function"&&fe.catch(function(){})}}catch(re){if(re&&ne&&typeof re.stack=="string")return[re.stack,ne.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var l=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");l&&l.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=a.DetermineComponentFrameRoot(),g=c[0],_=c[1];if(g&&_){var L=g.split("\n"),Q=_.split("\n");for(l=a=0;a<L.length&&!L[a].includes("DetermineComponentFrameRoot");)a++;for(;l<Q.length&&!Q[l].includes("DetermineComponentFrameRoot");)l++;if(a===L.length||l===Q.length)for(a=L.length-1,l=Q.length-1;1<=a&&0<=l&&L[a]!==Q[l];)l--;for(;1<=a&&0<=l;a--,l--)if(L[a]!==Q[l]){if(a!==1||l!==1)do if(a--,l--,0>l||L[a]!==Q[l]){var se="\n"+L[a].replace(" at new "," at ");return e.displayName&&se.includes("<anonymous>")&&(se=se.replace("<anonymous>",e.displayName)),se}while(1<=a&&0<=l);break}}}finally{Wi=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ar(n):""}function Aa(e){switch(e.tag){case 26:case 27:case 5:return ar(e.type);case 16:return ar("Lazy");case 13:return ar("Suspense");case 19:return ar("SuspenseList");case 0:case 15:return Zr(e.type,!1);case 11:return Zr(e.type.render,!1);case 1:return Zr(e.type,!0);case 31:return ar("Activity");default:return""}}function Ta(e){try{var t="";do t+=Aa(e),e=e.return;while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function zt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Fa(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Pa(e){var t=Fa(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(g){a=""+g,c.call(this,g)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(g){a=""+g},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function A(e){e._valueTracker||(e._valueTracker=Pa(e))}function W(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=Fa(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function ce(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch(t){return e.body}}var ye=/[\n"\\]/g;function ke(e){return e.replace(ye,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ne(e,t,n,a,l,c,g,_){e.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.type=g:e.removeAttribute("type"),t!=null?g==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+zt(t)):e.value!==""+zt(t)&&(e.value=""+zt(t)):g!=="submit"&&g!=="reset"||e.removeAttribute("value"),t!=null?P(e,g,zt(t)):n!=null?P(e,g,zt(n)):a!=null&&e.removeAttribute("value"),l==null&&c!=null&&(e.defaultChecked=!!c),l!=null&&(e.checked=l&&typeof l!="function"&&typeof l!="symbol"),_!=null&&typeof _!="function"&&typeof _!="symbol"&&typeof _!="boolean"?e.name=""+zt(_):e.removeAttribute("name")}function C(e,t,n,a,l,c,g,_){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+zt(n):"",t=t!=null?""+zt(t):n,_||t===e.value||(e.value=t),e.defaultValue=t}a=a!=null?a:l,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=_?e.checked:!!a,e.defaultChecked=!!a,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.name=g)}function P(e,t,n){t==="number"&&ce(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function oe(e,t,n,a){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&a&&(e[n].defaultSelected=!0)}else{for(n=""+zt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,a&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function pe(e,t,n){if(t!=null&&(t=""+zt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+zt(n):""}function Ee(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error(s(92));if(U(a)){if(1<a.length)throw Error(s(93));a=a[0]}n=a}n==null&&(n=""),t=n}n=zt(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function Re(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ie=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Oe(e,t,n){var a=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||Ie.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function ut(e,t,n){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var l in t)a=t[l],t.hasOwnProperty(l)&&n[l]!==a&&Oe(e,l,a)}else for(var c in t)t.hasOwnProperty(c)&&Oe(e,c,t[c])}function Vt(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var St=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),or=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Gr(e){return or.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Qr=null;function Oa(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Er=null,Ut=null;function ei(e){var t=be(e);if(t&&(e=t.stateNode)){var n=e[_t]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ne(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ke(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var l=a[_t]||null;if(!l)throw Error(s(90));Ne(a,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&W(a)}break e;case"textarea":pe(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&oe(e,!!n.multiple,t,!1)}}}var Nd=!1;function Tg(e,t,n){if(Nd)return e(t,n);Nd=!0;try{var a=e(t);return a}finally{if(Nd=!1,(Er!==null||Ut!==null)&&($l(),Er&&(t=Er,e=Ut,Ut=Er=null,ei(t),e)))for(t=0;t<e.length;t++)ei(e[t])}}function Do(e,t){var n=e.stateNode;if(n===null)return null;var a=n[_t]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var Mr=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Dd=!1;if(Mr)try{var zo={};Object.defineProperty(zo,"passive",{get:function(){Dd=!0}}),window.addEventListener("test",zo,zo),window.removeEventListener("test",zo,zo)}catch(e){Dd=!1}var ti=null,zd=null,ll=null;function Fg(){if(ll)return ll;var e,t=zd,n=t.length,a,l="value"in ti?ti.value:ti.textContent,c=l.length;for(e=0;e<n&&t[e]===l[e];e++);var g=n-e;for(a=1;a<=g&&t[n-a]===l[c-a];a++);return ll=l.slice(e,1<a?1-a:void 0)}function ul(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function cl(){return!0}function Pg(){return!1}function hn(e){function t(n,a,l,c,g){this._reactName=n,this._targetInst=l,this.type=a,this.nativeEvent=c,this.target=g,this.currentTarget=null;for(var _ in e)e.hasOwnProperty(_)&&(n=e[_],this[_]=n?n(c):c[_]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?cl:Pg,this.isPropagationStopped=Pg,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=cl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=cl)},persist:function(){},isPersistent:cl}),t}var Vi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},dl=hn(Vi),Uo=y({},Vi,{view:0,detail:0}),z6=hn(Uo),Ud,qd,qo,fl=y({},Uo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Wd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==qo&&(qo&&e.type==="mousemove"?(Ud=e.screenX-qo.screenX,qd=e.screenY-qo.screenY):qd=Ud=0,qo=e),Ud)},movementY:function(e){return"movementY"in e?e.movementY:qd}}),Og=hn(fl),U6=y({},fl,{dataTransfer:0}),q6=hn(U6),j6=y({},Uo,{relatedTarget:0}),jd=hn(j6),W6=y({},Vi,{animationName:0,elapsedTime:0,pseudoElement:0}),V6=hn(W6),H6=y({},Vi,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),B6=hn(H6),$6=y({},Vi,{data:0}),Rg=hn($6),Y6={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},X6={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},J6={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function K6(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=J6[e])?!!t[e]:!1}function Wd(){return K6}var Z6=y({},Uo,{key:function(e){if(e.key){var t=Y6[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ul(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?X6[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Wd,charCode:function(e){return e.type==="keypress"?ul(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ul(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),G6=hn(Z6),Q6=y({},fl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ig=hn(Q6),ex=y({},Uo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Wd}),tx=hn(ex),nx=y({},Vi,{propertyName:0,elapsedTime:0,pseudoElement:0}),rx=hn(nx),ix=y({},fl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ax=hn(ix),ox=y({},Vi,{newState:0,oldState:0}),sx=hn(ox),lx=[9,13,27,32],Vd=Mr&&"CompositionEvent"in window,jo=null;Mr&&"documentMode"in document&&(jo=document.documentMode);var ux=Mr&&"TextEvent"in window&&!jo,Lg=Mr&&(!Vd||jo&&8<jo&&11>=jo),Ng=" ",Dg=!1;function zg(e,t){switch(e){case"keyup":return lx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ug(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ra=!1;function cx(e,t){switch(e){case"compositionend":return Ug(t);case"keypress":return t.which!==32?null:(Dg=!0,Ng);case"textInput":return e=t.data,e===Ng&&Dg?null:e;default:return null}}function dx(e,t){if(Ra)return e==="compositionend"||!Vd&&zg(e,t)?(e=Fg(),ll=zd=ti=null,Ra=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Lg&&t.locale!=="ko"?null:t.data;default:return null}}var fx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function qg(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!fx[e.type]:t==="textarea"}function jg(e,t,n,a){Er?Ut?Ut.push(a):Ut=[a]:Er=a,t=Gl(t,"onChange"),0<t.length&&(n=new dl("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var Wo=null,Vo=null;function hx(e){_b(e,0)}function hl(e){var t=Sr(e);if(W(t))return e}function Wg(e,t){if(e==="change")return t}var Vg=!1;if(Mr){var Hd;if(Mr){var Bd="oninput"in document;if(!Bd){var Hg=document.createElement("div");Hg.setAttribute("oninput","return;"),Bd=typeof Hg.oninput=="function"}Hd=Bd}else Hd=!1;Vg=Hd&&(!document.documentMode||9<document.documentMode)}function Bg(){Wo&&(Wo.detachEvent("onpropertychange",$g),Vo=Wo=null)}function $g(e){if(e.propertyName==="value"&&hl(Vo)){var t=[];jg(t,Vo,e,Oa(e)),Tg(hx,t)}}function px(e,t,n){e==="focusin"?(Bg(),Wo=t,Vo=n,Wo.attachEvent("onpropertychange",$g)):e==="focusout"&&Bg()}function mx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return hl(Vo)}function gx(e,t){if(e==="click")return hl(t)}function vx(e,t){if(e==="input"||e==="change")return hl(t)}function yx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var _n=typeof Object.is=="function"?Object.is:yx;function Ho(e,t){if(_n(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var l=n[a];if(!bt.call(t,l)||!_n(e[l],t[l]))return!1}return!0}function Yg(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xg(e,t){var n=Yg(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Yg(n)}}function Jg(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Jg(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Kg(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ce(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch(a){n=!1}if(n)e=t.contentWindow;else break;t=ce(e.document)}return t}function $d(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var bx=Mr&&"documentMode"in document&&11>=document.documentMode,Ia=null,Yd=null,Bo=null,Xd=!1;function Zg(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Xd||Ia==null||Ia!==ce(a)||(a=Ia,"selectionStart"in a&&$d(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Bo&&Ho(Bo,a)||(Bo=a,a=Gl(Yd,"onSelect"),0<a.length&&(t=new dl("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=Ia)))}function Hi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var La={animationend:Hi("Animation","AnimationEnd"),animationiteration:Hi("Animation","AnimationIteration"),animationstart:Hi("Animation","AnimationStart"),transitionrun:Hi("Transition","TransitionRun"),transitionstart:Hi("Transition","TransitionStart"),transitioncancel:Hi("Transition","TransitionCancel"),transitionend:Hi("Transition","TransitionEnd")},Jd={},Gg={};Mr&&(Gg=document.createElement("div").style,"AnimationEvent"in window||(delete La.animationend.animation,delete La.animationiteration.animation,delete La.animationstart.animation),"TransitionEvent"in window||delete La.transitionend.transition);function Bi(e){if(Jd[e])return Jd[e];if(!La[e])return e;var t=La[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Gg)return Jd[e]=t[n];return e}var Qg=Bi("animationend"),ev=Bi("animationiteration"),tv=Bi("animationstart"),wx=Bi("transitionrun"),_x=Bi("transitionstart"),xx=Bi("transitioncancel"),nv=Bi("transitionend"),rv=new Map,Kd="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Kd.push("scrollEnd");function Yn(e,t){rv.set(e,t),wn(t,[e])}var iv=new WeakMap;function Ln(e,t){if(typeof e=="object"&&e!==null){var n=iv.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Ta(t)},iv.set(e,t),t)}return{value:e,source:t,stack:Ta(t)}}var Nn=[],Na=0,Zd=0;function pl(){for(var e=Na,t=Zd=Na=0;t<e;){var n=Nn[t];Nn[t++]=null;var a=Nn[t];Nn[t++]=null;var l=Nn[t];Nn[t++]=null;var c=Nn[t];if(Nn[t++]=null,a!==null&&l!==null){var g=a.pending;g===null?l.next=l:(l.next=g.next,g.next=l),a.pending=l}c!==0&&av(n,l,c)}}function ml(e,t,n,a){Nn[Na++]=e,Nn[Na++]=t,Nn[Na++]=n,Nn[Na++]=a,Zd|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Gd(e,t,n,a){return ml(e,t,n,a),gl(e)}function Da(e,t){return ml(e,null,null,t),gl(e)}function av(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var l=!1,c=e.return;c!==null;)c.childLanes|=n,a=c.alternate,a!==null&&(a.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(l=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,l&&t!==null&&(l=31-Nt(n),e=c.hiddenUpdates,a=e[l],a===null?e[l]=[t]:a.push(t),t.lane=n|536870912),c):null}function gl(e){if(50<gs)throw gs=0,ah=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var za={};function Sx(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function xn(e,t,n,a){return new Sx(e,t,n,a)}function Qd(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ar(e,t){var n=e.alternate;return n===null?(n=xn(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function ov(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function vl(e,t,n,a,l,c){var g=0;if(a=e,typeof e=="function")Qd(e)&&(g=1);else if(typeof e=="string")g=CS(e,n,me.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case z:return e=xn(31,n,t,l),e.elementType=z,e.lanes=c,e;case M:return $i(n.children,l,c,t);case R:g=8,l|=24;break;case j:return e=xn(12,n,t,l|2),e.elementType=j,e.lanes=c,e;case V:return e=xn(13,n,t,l),e.elementType=V,e.lanes=c,e;case Y:return e=xn(19,n,t,l),e.elementType=Y,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ie:case $:g=10;break e;case B:g=9;break e;case ae:g=11;break e;case N:g=14;break e;case I:g=16,a=null;break e}g=29,n=Error(s(130,e===null?"null":typeof e,"")),a=null}return t=xn(g,n,t,l),t.elementType=e,t.type=a,t.lanes=c,t}function $i(e,t,n,a){return e=xn(7,e,a,t),e.lanes=n,e}function ef(e,t,n){return e=xn(6,e,null,t),e.lanes=n,e}function tf(e,t,n){return t=xn(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ua=[],qa=0,yl=null,bl=0,Dn=[],zn=0,Yi=null,Tr=1,Fr="";function Xi(e,t){Ua[qa++]=bl,Ua[qa++]=yl,yl=e,bl=t}function sv(e,t,n){Dn[zn++]=Tr,Dn[zn++]=Fr,Dn[zn++]=Yi,Yi=e;var a=Tr;e=Fr;var l=32-Nt(a)-1;a&=~(1<<l),n+=1;var c=32-Nt(t)+l;if(30<c){var g=l-l%5;c=(a&(1<<g)-1).toString(32),a>>=g,l-=g,Tr=1<<32-Nt(t)+l|n<<l|a,Fr=c+e}else Tr=1<<c|n<<l|a,Fr=e}function nf(e){e.return!==null&&(Xi(e,1),sv(e,1,0))}function rf(e){for(;e===yl;)yl=Ua[--qa],Ua[qa]=null,bl=Ua[--qa],Ua[qa]=null;for(;e===Yi;)Yi=Dn[--zn],Dn[zn]=null,Fr=Dn[--zn],Dn[zn]=null,Tr=Dn[--zn],Dn[zn]=null}var ln=null,kt=null,Ke=!1,Ji=null,sr=!1,af=Error(s(519));function Ki(e){var t=Error(s(418,""));throw Xo(Ln(t,e)),af}function lv(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[wt]=e,t[_t]=a,n){case"dialog":We("cancel",t),We("close",t);break;case"iframe":case"object":case"embed":We("load",t);break;case"video":case"audio":for(n=0;n<ys.length;n++)We(ys[n],t);break;case"source":We("error",t);break;case"img":case"image":case"link":We("error",t),We("load",t);break;case"details":We("toggle",t);break;case"input":We("invalid",t),C(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),A(t);break;case"select":We("invalid",t);break;case"textarea":We("invalid",t),Ee(t,a.value,a.defaultValue,a.children),A(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||Cb(t.textContent,n)?(a.popover!=null&&(We("beforetoggle",t),We("toggle",t)),a.onScroll!=null&&We("scroll",t),a.onScrollEnd!=null&&We("scrollend",t),a.onClick!=null&&(t.onclick=Ql),t=!0):t=!1,t||Ki(e)}function uv(e){for(ln=e.return;ln;)switch(ln.tag){case 5:case 13:sr=!1;return;case 27:case 3:sr=!0;return;default:ln=ln.return}}function $o(e){if(e!==ln)return!1;if(!Ke)return uv(e),Ke=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||_h(e.type,e.memoizedProps)),n=!n),n&&kt&&Ki(e),uv(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){kt=Jn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}kt=null}}else t===27?(t=kt,vi(e.type)?(e=Ch,Ch=null,kt=e):kt=t):kt=ln?Jn(e.stateNode.nextSibling):null;return!0}function Yo(){kt=ln=null,Ke=!1}function cv(){var e=Ji;return e!==null&&(gn===null?gn=e:gn.push.apply(gn,e),Ji=null),e}function Xo(e){Ji===null?Ji=[e]:Ji.push(e)}var of=D(null),Zi=null,Pr=null;function ni(e,t,n){de(of,t._currentValue),t._currentValue=n}function Or(e){e._currentValue=of.current,te(of)}function sf(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function lf(e,t,n,a){var l=e.child;for(l!==null&&(l.return=e);l!==null;){var c=l.dependencies;if(c!==null){var g=l.child;c=c.firstContext;e:for(;c!==null;){var _=c;c=l;for(var L=0;L<t.length;L++)if(_.context===t[L]){c.lanes|=n,_=c.alternate,_!==null&&(_.lanes|=n),sf(c.return,n,e),a||(g=null);break e}c=_.next}}else if(l.tag===18){if(g=l.return,g===null)throw Error(s(341));g.lanes|=n,c=g.alternate,c!==null&&(c.lanes|=n),sf(g,n,e),g=null}else g=l.child;if(g!==null)g.return=l;else for(g=l;g!==null;){if(g===e){g=null;break}if(l=g.sibling,l!==null){l.return=g.return,g=l;break}g=g.return}l=g}}function Jo(e,t,n,a){e=null;for(var l=t,c=!1;l!==null;){if(!c){if((l.flags&524288)!==0)c=!0;else if((l.flags&262144)!==0)break}if(l.tag===10){var g=l.alternate;if(g===null)throw Error(s(387));if(g=g.memoizedProps,g!==null){var _=l.type;_n(l.pendingProps.value,g.value)||(e!==null?e.push(_):e=[_])}}else if(l===ze.current){if(g=l.alternate,g===null)throw Error(s(387));g.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(e!==null?e.push(ks):e=[ks])}l=l.return}e!==null&&lf(t,e,n,a),t.flags|=262144}function wl(e){for(e=e.firstContext;e!==null;){if(!_n(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Gi(e){Zi=e,Pr=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function tn(e){return dv(Zi,e)}function _l(e,t){return Zi===null&&Gi(e),dv(e,t)}function dv(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Pr===null){if(e===null)throw Error(s(308));Pr=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Pr=Pr.next=t;return n}var kx=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},Cx=r.unstable_scheduleCallback,Ex=r.unstable_NormalPriority,qt={$$typeof:$,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function uf(){return{controller:new kx,data:new Map,refCount:0}}function Ko(e){e.refCount--,e.refCount===0&&Cx(Ex,function(){e.controller.abort()})}var Zo=null,cf=0,ja=0,Wa=null;function Mx(e,t){if(Zo===null){var n=Zo=[];cf=0,ja=fh(),Wa={status:"pending",value:void 0,then:function(a){n.push(a)}}}return cf++,t.then(fv,fv),t}function fv(){if(--cf===0&&Zo!==null){Wa!==null&&(Wa.status="fulfilled");var e=Zo;Zo=null,ja=0,Wa=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Ax(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(l){n.push(l)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var l=0;l<n.length;l++)(0,n[l])(t)},function(l){for(a.status="rejected",a.reason=l,l=0;l<n.length;l++)(0,n[l])(void 0)}),a}var hv=k.S;k.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Mx(e,t),hv!==null&&hv(e,t)};var Qi=D(null);function df(){var e=Qi.current;return e!==null?e:ot.pooledCache}function xl(e,t){t===null?de(Qi,Qi.current):de(Qi,t.pool)}function pv(){var e=df();return e===null?null:{parent:qt._currentValue,pool:e}}var Go=Error(s(460)),mv=Error(s(474)),Sl=Error(s(542)),ff={then:function(){}};function gv(e){return e=e.status,e==="fulfilled"||e==="rejected"}function kl(){}function vv(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(kl,kl),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,bv(e),e;default:if(typeof t.status=="string")t.then(kl,kl);else{if(e=ot,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var l=t;l.status="fulfilled",l.value=a}},function(a){if(t.status==="pending"){var l=t;l.status="rejected",l.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,bv(e),e}throw Qo=t,Go}}var Qo=null;function yv(){if(Qo===null)throw Error(s(459));var e=Qo;return Qo=null,e}function bv(e){if(e===Go||e===Sl)throw Error(s(483))}var ri=!1;function hf(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function pf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ii(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ai(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(Ge&2)!==0){var l=a.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),a.pending=t,t=gl(e),av(e,null,n),t}return ml(e,a,t,n),gl(e)}function es(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,Sa(e,n)}}function mf(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var l=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var g={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?l=c=g:c=c.next=g,n=n.next}while(n!==null);c===null?l=c=t:c=c.next=t}else l=c=t;n={baseState:a.baseState,firstBaseUpdate:l,lastBaseUpdate:c,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var gf=!1;function ts(){if(gf){var e=Wa;if(e!==null)throw e}}function ns(e,t,n,a){gf=!1;var l=e.updateQueue;ri=!1;var c=l.firstBaseUpdate,g=l.lastBaseUpdate,_=l.shared.pending;if(_!==null){l.shared.pending=null;var L=_,Q=L.next;L.next=null,g===null?c=Q:g.next=Q,g=L;var se=e.alternate;se!==null&&(se=se.updateQueue,_=se.lastBaseUpdate,_!==g&&(_===null?se.firstBaseUpdate=Q:_.next=Q,se.lastBaseUpdate=L))}if(c!==null){var fe=l.baseState;g=0,se=Q=L=null,_=c;do{var ne=_.lane&-536870913,re=ne!==_.lane;if(re?(Be&ne)===ne:(a&ne)===ne){ne!==0&&ne===ja&&(gf=!0),se!==null&&(se=se.next={lane:0,tag:_.tag,payload:_.payload,callback:null,next:null});e:{var Fe=e,Ae=_;ne=t;var rt=n;switch(Ae.tag){case 1:if(Fe=Ae.payload,typeof Fe=="function"){fe=Fe.call(rt,fe,ne);break e}fe=Fe;break e;case 3:Fe.flags=Fe.flags&-65537|128;case 0:if(Fe=Ae.payload,ne=typeof Fe=="function"?Fe.call(rt,fe,ne):Fe,ne==null)break e;fe=y({},fe,ne);break e;case 2:ri=!0}}ne=_.callback,ne!==null&&(e.flags|=64,re&&(e.flags|=8192),re=l.callbacks,re===null?l.callbacks=[ne]:re.push(ne))}else re={lane:ne,tag:_.tag,payload:_.payload,callback:_.callback,next:null},se===null?(Q=se=re,L=fe):se=se.next=re,g|=ne;if(_=_.next,_===null){if(_=l.shared.pending,_===null)break;re=_,_=re.next,re.next=null,l.lastBaseUpdate=re,l.shared.pending=null}}while(!0);se===null&&(L=fe),l.baseState=L,l.firstBaseUpdate=Q,l.lastBaseUpdate=se,c===null&&(l.shared.lanes=0),hi|=g,e.lanes=g,e.memoizedState=fe}}function wv(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function _v(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)wv(n[e],t)}var Va=D(null),Cl=D(0);function xv(e,t){e=qr,de(Cl,e),de(Va,t),qr=e|t.baseLanes}function vf(){de(Cl,qr),de(Va,Va.current)}function yf(){qr=Cl.current,te(Va),te(Cl)}var oi=0,De=null,tt=null,Pt=null,El=!1,Ha=!1,ea=!1,Ml=0,rs=0,Ba=null,Tx=0;function Et(){throw Error(s(321))}function bf(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!_n(e[n],t[n]))return!1;return!0}function wf(e,t,n,a,l,c){return oi=c,De=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,k.H=e===null||e.memoizedState===null?ay:oy,ea=!1,c=n(a,l),ea=!1,Ha&&(c=kv(t,n,a,l)),Sv(e),c}function Sv(e){k.H=Rl;var t=tt!==null&&tt.next!==null;if(oi=0,Pt=tt=De=null,El=!1,rs=0,Ba=null,t)throw Error(s(300));e===null||Ht||(e=e.dependencies,e!==null&&wl(e)&&(Ht=!0))}function kv(e,t,n,a){De=e;var l=0;do{if(Ha&&(Ba=null),rs=0,Ha=!1,25<=l)throw Error(s(301));if(l+=1,Pt=tt=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}k.H=Nx,c=t(n,a)}while(Ha);return c}function Fx(){var e=k.H,t=e.useState()[0];return t=typeof t.then=="function"?is(t):t,e=e.useState()[0],(tt!==null?tt.memoizedState:null)!==e&&(De.flags|=1024),t}function _f(){var e=Ml!==0;return Ml=0,e}function xf(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Sf(e){if(El){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}El=!1}oi=0,Pt=tt=De=null,Ha=!1,rs=Ml=0,Ba=null}function pn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Pt===null?De.memoizedState=Pt=e:Pt=Pt.next=e,Pt}function Ot(){if(tt===null){var e=De.alternate;e=e!==null?e.memoizedState:null}else e=tt.next;var t=Pt===null?De.memoizedState:Pt.next;if(t!==null)Pt=t,tt=e;else{if(e===null)throw De.alternate===null?Error(s(467)):Error(s(310));tt=e,e={memoizedState:tt.memoizedState,baseState:tt.baseState,baseQueue:tt.baseQueue,queue:tt.queue,next:null},Pt===null?De.memoizedState=Pt=e:Pt=Pt.next=e}return Pt}function kf(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function is(e){var t=rs;return rs+=1,Ba===null&&(Ba=[]),e=vv(Ba,e,t),t=De,(Pt===null?t.memoizedState:Pt.next)===null&&(t=t.alternate,k.H=t===null||t.memoizedState===null?ay:oy),e}function Al(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return is(e);if(e.$$typeof===$)return tn(e)}throw Error(s(438,String(e)))}function Cf(e){var t=null,n=De.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=De.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(l){return l.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=kf(),De.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=F;return t.index++,n}function Rr(e,t){return typeof t=="function"?t(e):t}function Tl(e){var t=Ot();return Ef(t,tt,e)}function Ef(e,t,n){var a=e.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=n;var l=e.baseQueue,c=a.pending;if(c!==null){if(l!==null){var g=l.next;l.next=c.next,c.next=g}t.baseQueue=l=c,a.pending=null}if(c=e.baseState,l===null)e.memoizedState=c;else{t=l.next;var _=g=null,L=null,Q=t,se=!1;do{var fe=Q.lane&-536870913;if(fe!==Q.lane?(Be&fe)===fe:(oi&fe)===fe){var ne=Q.revertLane;if(ne===0)L!==null&&(L=L.next={lane:0,revertLane:0,action:Q.action,hasEagerState:Q.hasEagerState,eagerState:Q.eagerState,next:null}),fe===ja&&(se=!0);else if((oi&ne)===ne){Q=Q.next,ne===ja&&(se=!0);continue}else fe={lane:0,revertLane:Q.revertLane,action:Q.action,hasEagerState:Q.hasEagerState,eagerState:Q.eagerState,next:null},L===null?(_=L=fe,g=c):L=L.next=fe,De.lanes|=ne,hi|=ne;fe=Q.action,ea&&n(c,fe),c=Q.hasEagerState?Q.eagerState:n(c,fe)}else ne={lane:fe,revertLane:Q.revertLane,action:Q.action,hasEagerState:Q.hasEagerState,eagerState:Q.eagerState,next:null},L===null?(_=L=ne,g=c):L=L.next=ne,De.lanes|=fe,hi|=fe;Q=Q.next}while(Q!==null&&Q!==t);if(L===null?g=c:L.next=_,!_n(c,e.memoizedState)&&(Ht=!0,se&&(n=Wa,n!==null)))throw n;e.memoizedState=c,e.baseState=g,e.baseQueue=L,a.lastRenderedState=c}return l===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Mf(e){var t=Ot(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var a=n.dispatch,l=n.pending,c=t.memoizedState;if(l!==null){n.pending=null;var g=l=l.next;do c=e(c,g.action),g=g.next;while(g!==l);_n(c,t.memoizedState)||(Ht=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,a]}function Cv(e,t,n){var a=De,l=Ot(),c=Ke;if(c){if(n===void 0)throw Error(s(407));n=n()}else n=t();var g=!_n((tt||l).memoizedState,n);g&&(l.memoizedState=n,Ht=!0),l=l.queue;var _=Av.bind(null,a,l,e);if(as(2048,8,_,[e]),l.getSnapshot!==t||g||Pt!==null&&Pt.memoizedState.tag&1){if(a.flags|=2048,$a(9,Fl(),Mv.bind(null,a,l,n,t),null),ot===null)throw Error(s(349));c||(oi&124)!==0||Ev(a,t,n)}return n}function Ev(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=De.updateQueue,t===null?(t=kf(),De.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Mv(e,t,n,a){t.value=n,t.getSnapshot=a,Tv(t)&&Fv(e)}function Av(e,t,n){return n(function(){Tv(t)&&Fv(e)})}function Tv(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!_n(e,n)}catch(a){return!0}}function Fv(e){var t=Da(e,2);t!==null&&Mn(t,e,2)}function Af(e){var t=pn();if(typeof e=="function"){var n=e;if(e=n(),ea){Rn(!0);try{n()}finally{Rn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rr,lastRenderedState:e},t}function Pv(e,t,n,a){return e.baseState=n,Ef(e,tt,typeof a=="function"?a:Rr)}function Px(e,t,n,a,l){if(Ol(e))throw Error(s(485));if(e=t.action,e!==null){var c={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){c.listeners.push(g)}};k.T!==null?n(!0):c.isTransition=!1,a(c),n=t.pending,n===null?(c.next=t.pending=c,Ov(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Ov(e,t){var n=t.action,a=t.payload,l=e.state;if(t.isTransition){var c=k.T,g={};k.T=g;try{var _=n(l,a),L=k.S;L!==null&&L(g,_),Rv(e,t,_)}catch(Q){Tf(e,t,Q)}finally{k.T=c}}else try{c=n(l,a),Rv(e,t,c)}catch(Q){Tf(e,t,Q)}}function Rv(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){Iv(e,t,a)},function(a){return Tf(e,t,a)}):Iv(e,t,n)}function Iv(e,t,n){t.status="fulfilled",t.value=n,Lv(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Ov(e,n)))}function Tf(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,Lv(t),t=t.next;while(t!==a)}e.action=null}function Lv(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Nv(e,t){return t}function Dv(e,t){if(Ke){var n=ot.formState;if(n!==null){e:{var a=De;if(Ke){if(kt){t:{for(var l=kt,c=sr;l.nodeType!==8;){if(!c){l=null;break t}if(l=Jn(l.nextSibling),l===null){l=null;break t}}c=l.data,l=c==="F!"||c==="F"?l:null}if(l){kt=Jn(l.nextSibling),a=l.data==="F!";break e}}Ki(a)}a=!1}a&&(t=n[0])}}return n=pn(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Nv,lastRenderedState:t},n.queue=a,n=ny.bind(null,De,a),a.dispatch=n,a=Af(!1),c=If.bind(null,De,!1,a.queue),a=pn(),l={state:t,dispatch:null,action:e,pending:null},a.queue=l,n=Px.bind(null,De,l,c,n),l.dispatch=n,a.memoizedState=e,[t,n,!1]}function zv(e){var t=Ot();return Uv(t,tt,e)}function Uv(e,t,n){if(t=Ef(e,t,Nv)[0],e=Tl(Rr)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=is(t)}catch(g){throw g===Go?Sl:g}else a=t;t=Ot();var l=t.queue,c=l.dispatch;return n!==t.memoizedState&&(De.flags|=2048,$a(9,Fl(),Ox.bind(null,l,n),null)),[a,c,e]}function Ox(e,t){e.action=t}function qv(e){var t=Ot(),n=tt;if(n!==null)return Uv(t,n,e);Ot(),t=t.memoizedState,n=Ot();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function $a(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=De.updateQueue,t===null&&(t=kf(),De.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Fl(){return{destroy:void 0,resource:void 0}}function jv(){return Ot().memoizedState}function Pl(e,t,n,a){var l=pn();a=a===void 0?null:a,De.flags|=e,l.memoizedState=$a(1|t,Fl(),n,a)}function as(e,t,n,a){var l=Ot();a=a===void 0?null:a;var c=l.memoizedState.inst;tt!==null&&a!==null&&bf(a,tt.memoizedState.deps)?l.memoizedState=$a(t,c,n,a):(De.flags|=e,l.memoizedState=$a(1|t,c,n,a))}function Wv(e,t){Pl(8390656,8,e,t)}function Vv(e,t){as(2048,8,e,t)}function Hv(e,t){return as(4,2,e,t)}function Bv(e,t){return as(4,4,e,t)}function $v(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Yv(e,t,n){n=n!=null?n.concat([e]):null,as(4,4,$v.bind(null,t,e),n)}function Ff(){}function Xv(e,t){var n=Ot();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&bf(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function Jv(e,t){var n=Ot();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&bf(t,a[1]))return a[0];if(a=e(),ea){Rn(!0);try{e()}finally{Rn(!1)}}return n.memoizedState=[a,t],a}function Pf(e,t,n){return n===void 0||(oi&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Gy(),De.lanes|=e,hi|=e,n)}function Kv(e,t,n,a){return _n(n,t)?n:Va.current!==null?(e=Pf(e,n,a),_n(e,t)||(Ht=!0),e):(oi&42)===0?(Ht=!0,e.memoizedState=n):(e=Gy(),De.lanes|=e,hi|=e,t)}function Zv(e,t,n,a,l){var c=K.p;K.p=c!==0&&8>c?c:8;var g=k.T,_={};k.T=_,If(e,!1,t,n);try{var L=l(),Q=k.S;if(Q!==null&&Q(_,L),L!==null&&typeof L=="object"&&typeof L.then=="function"){var se=Ax(L,a);os(e,t,se,En(e))}else os(e,t,a,En(e))}catch(fe){os(e,t,{then:function(){},status:"rejected",reason:fe},En())}finally{K.p=c,k.T=g}}function Rx(){}function Of(e,t,n,a){if(e.tag!==5)throw Error(s(476));var l=Gv(e).queue;Zv(e,l,t,G,n===null?Rx:function(){return Qv(e),n(a)})}function Gv(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:G,baseState:G,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rr,lastRenderedState:G},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rr,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Qv(e){var t=Gv(e).next.queue;os(e,t,{},En())}function Rf(){return tn(ks)}function ey(){return Ot().memoizedState}function ty(){return Ot().memoizedState}function Ix(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=En();e=ii(n);var a=ai(t,e,n);a!==null&&(Mn(a,t,n),es(a,t,n)),t={cache:uf()},e.payload=t;return}t=t.return}}function Lx(e,t,n){var a=En();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ol(e)?ry(t,n):(n=Gd(e,t,n,a),n!==null&&(Mn(n,e,a),iy(n,t,a)))}function ny(e,t,n){var a=En();os(e,t,n,a)}function os(e,t,n,a){var l={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ol(e))ry(t,l);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var g=t.lastRenderedState,_=c(g,n);if(l.hasEagerState=!0,l.eagerState=_,_n(_,g))return ml(e,t,l,0),ot===null&&pl(),!1}catch(L){}finally{}if(n=Gd(e,t,l,a),n!==null)return Mn(n,e,a),iy(n,t,a),!0}return!1}function If(e,t,n,a){if(a={lane:2,revertLane:fh(),action:a,hasEagerState:!1,eagerState:null,next:null},Ol(e)){if(t)throw Error(s(479))}else t=Gd(e,n,a,2),t!==null&&Mn(t,e,2)}function Ol(e){var t=e.alternate;return e===De||t!==null&&t===De}function ry(e,t){Ha=El=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function iy(e,t,n){if((n&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,Sa(e,n)}}var Rl={readContext:tn,use:Al,useCallback:Et,useContext:Et,useEffect:Et,useImperativeHandle:Et,useLayoutEffect:Et,useInsertionEffect:Et,useMemo:Et,useReducer:Et,useRef:Et,useState:Et,useDebugValue:Et,useDeferredValue:Et,useTransition:Et,useSyncExternalStore:Et,useId:Et,useHostTransitionStatus:Et,useFormState:Et,useActionState:Et,useOptimistic:Et,useMemoCache:Et,useCacheRefresh:Et},ay={readContext:tn,use:Al,useCallback:function(e,t){return pn().memoizedState=[e,t===void 0?null:t],e},useContext:tn,useEffect:Wv,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Pl(4194308,4,$v.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Pl(4194308,4,e,t)},useInsertionEffect:function(e,t){Pl(4,2,e,t)},useMemo:function(e,t){var n=pn();t=t===void 0?null:t;var a=e();if(ea){Rn(!0);try{e()}finally{Rn(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=pn();if(n!==void 0){var l=n(t);if(ea){Rn(!0);try{n(t)}finally{Rn(!1)}}}else l=t;return a.memoizedState=a.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},a.queue=e,e=e.dispatch=Lx.bind(null,De,e),[a.memoizedState,e]},useRef:function(e){var t=pn();return e={current:e},t.memoizedState=e},useState:function(e){e=Af(e);var t=e.queue,n=ny.bind(null,De,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ff,useDeferredValue:function(e,t){var n=pn();return Pf(n,e,t)},useTransition:function(){var e=Af(!1);return e=Zv.bind(null,De,e.queue,!0,!1),pn().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=De,l=pn();if(Ke){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),ot===null)throw Error(s(349));(Be&124)!==0||Ev(a,t,n)}l.memoizedState=n;var c={value:n,getSnapshot:t};return l.queue=c,Wv(Av.bind(null,a,c,e),[e]),a.flags|=2048,$a(9,Fl(),Mv.bind(null,a,c,n,t),null),n},useId:function(){var e=pn(),t=ot.identifierPrefix;if(Ke){var n=Fr,a=Tr;n=(a&~(1<<32-Nt(a)-1)).toString(32)+n,t="\xAB"+t+"R"+n,n=Ml++,0<n&&(t+="H"+n.toString(32)),t+="\xBB"}else n=Tx++,t="\xAB"+t+"r"+n.toString(32)+"\xBB";return e.memoizedState=t},useHostTransitionStatus:Rf,useFormState:Dv,useActionState:Dv,useOptimistic:function(e){var t=pn();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=If.bind(null,De,!0,n),n.dispatch=t,[e,t]},useMemoCache:Cf,useCacheRefresh:function(){return pn().memoizedState=Ix.bind(null,De)}},oy={readContext:tn,use:Al,useCallback:Xv,useContext:tn,useEffect:Vv,useImperativeHandle:Yv,useInsertionEffect:Hv,useLayoutEffect:Bv,useMemo:Jv,useReducer:Tl,useRef:jv,useState:function(){return Tl(Rr)},useDebugValue:Ff,useDeferredValue:function(e,t){var n=Ot();return Kv(n,tt.memoizedState,e,t)},useTransition:function(){var e=Tl(Rr)[0],t=Ot().memoizedState;return[typeof e=="boolean"?e:is(e),t]},useSyncExternalStore:Cv,useId:ey,useHostTransitionStatus:Rf,useFormState:zv,useActionState:zv,useOptimistic:function(e,t){var n=Ot();return Pv(n,tt,e,t)},useMemoCache:Cf,useCacheRefresh:ty},Nx={readContext:tn,use:Al,useCallback:Xv,useContext:tn,useEffect:Vv,useImperativeHandle:Yv,useInsertionEffect:Hv,useLayoutEffect:Bv,useMemo:Jv,useReducer:Mf,useRef:jv,useState:function(){return Mf(Rr)},useDebugValue:Ff,useDeferredValue:function(e,t){var n=Ot();return tt===null?Pf(n,e,t):Kv(n,tt.memoizedState,e,t)},useTransition:function(){var e=Mf(Rr)[0],t=Ot().memoizedState;return[typeof e=="boolean"?e:is(e),t]},useSyncExternalStore:Cv,useId:ey,useHostTransitionStatus:Rf,useFormState:qv,useActionState:qv,useOptimistic:function(e,t){var n=Ot();return tt!==null?Pv(n,tt,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Cf,useCacheRefresh:ty},Ya=null,ss=0;function Il(e){var t=ss;return ss+=1,Ya===null&&(Ya=[]),vv(Ya,e,t)}function ls(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ll(e,t){throw t.$$typeof===v?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function sy(e){var t=e._init;return t(e._payload)}function ly(e){function t(J,H){if(e){var Z=J.deletions;Z===null?(J.deletions=[H],J.flags|=16):Z.push(H)}}function n(J,H){if(!e)return null;for(;H!==null;)t(J,H),H=H.sibling;return null}function a(J){for(var H=new Map;J!==null;)J.key!==null?H.set(J.key,J):H.set(J.index,J),J=J.sibling;return H}function l(J,H){return J=Ar(J,H),J.index=0,J.sibling=null,J}function c(J,H,Z){return J.index=Z,e?(Z=J.alternate,Z!==null?(Z=Z.index,Z<H?(J.flags|=67108866,H):Z):(J.flags|=67108866,H)):(J.flags|=1048576,H)}function g(J){return e&&J.alternate===null&&(J.flags|=67108866),J}function _(J,H,Z,ue){return H===null||H.tag!==6?(H=ef(Z,J.mode,ue),H.return=J,H):(H=l(H,Z),H.return=J,H)}function L(J,H,Z,ue){var Se=Z.type;return Se===M?se(J,H,Z.props.children,ue,Z.key):H!==null&&(H.elementType===Se||typeof Se=="object"&&Se!==null&&Se.$$typeof===I&&sy(Se)===H.type)?(H=l(H,Z.props),ls(H,Z),H.return=J,H):(H=vl(Z.type,Z.key,Z.props,null,J.mode,ue),ls(H,Z),H.return=J,H)}function Q(J,H,Z,ue){return H===null||H.tag!==4||H.stateNode.containerInfo!==Z.containerInfo||H.stateNode.implementation!==Z.implementation?(H=tf(Z,J.mode,ue),H.return=J,H):(H=l(H,Z.children||[]),H.return=J,H)}function se(J,H,Z,ue,Se){return H===null||H.tag!==7?(H=$i(Z,J.mode,ue,Se),H.return=J,H):(H=l(H,Z),H.return=J,H)}function fe(J,H,Z){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return H=ef(""+H,J.mode,Z),H.return=J,H;if(typeof H=="object"&&H!==null){switch(H.$$typeof){case x:return Z=vl(H.type,H.key,H.props,null,J.mode,Z),ls(Z,H),Z.return=J,Z;case E:return H=tf(H,J.mode,Z),H.return=J,H;case I:var ue=H._init;return H=ue(H._payload),fe(J,H,Z)}if(U(H)||S(H))return H=$i(H,J.mode,Z,null),H.return=J,H;if(typeof H.then=="function")return fe(J,Il(H),Z);if(H.$$typeof===$)return fe(J,_l(J,H),Z);Ll(J,H)}return null}function ne(J,H,Z,ue){var Se=H!==null?H.key:null;if(typeof Z=="string"&&Z!==""||typeof Z=="number"||typeof Z=="bigint")return Se!==null?null:_(J,H,""+Z,ue);if(typeof Z=="object"&&Z!==null){switch(Z.$$typeof){case x:return Z.key===Se?L(J,H,Z,ue):null;case E:return Z.key===Se?Q(J,H,Z,ue):null;case I:return Se=Z._init,Z=Se(Z._payload),ne(J,H,Z,ue)}if(U(Z)||S(Z))return Se!==null?null:se(J,H,Z,ue,null);if(typeof Z.then=="function")return ne(J,H,Il(Z),ue);if(Z.$$typeof===$)return ne(J,H,_l(J,Z),ue);Ll(J,Z)}return null}function re(J,H,Z,ue,Se){if(typeof ue=="string"&&ue!==""||typeof ue=="number"||typeof ue=="bigint")return J=J.get(Z)||null,_(H,J,""+ue,Se);if(typeof ue=="object"&&ue!==null){switch(ue.$$typeof){case x:return J=J.get(ue.key===null?Z:ue.key)||null,L(H,J,ue,Se);case E:return J=J.get(ue.key===null?Z:ue.key)||null,Q(H,J,ue,Se);case I:var qe=ue._init;return ue=qe(ue._payload),re(J,H,Z,ue,Se)}if(U(ue)||S(ue))return J=J.get(Z)||null,se(H,J,ue,Se,null);if(typeof ue.then=="function")return re(J,H,Z,Il(ue),Se);if(ue.$$typeof===$)return re(J,H,Z,_l(H,ue),Se);Ll(H,ue)}return null}function Fe(J,H,Z,ue){for(var Se=null,qe=null,Ce=H,Te=H=0,$t=null;Ce!==null&&Te<Z.length;Te++){Ce.index>Te?($t=Ce,Ce=null):$t=Ce.sibling;var Je=ne(J,Ce,Z[Te],ue);if(Je===null){Ce===null&&(Ce=$t);break}e&&Ce&&Je.alternate===null&&t(J,Ce),H=c(Je,H,Te),qe===null?Se=Je:qe.sibling=Je,qe=Je,Ce=$t}if(Te===Z.length)return n(J,Ce),Ke&&Xi(J,Te),Se;if(Ce===null){for(;Te<Z.length;Te++)Ce=fe(J,Z[Te],ue),Ce!==null&&(H=c(Ce,H,Te),qe===null?Se=Ce:qe.sibling=Ce,qe=Ce);return Ke&&Xi(J,Te),Se}for(Ce=a(Ce);Te<Z.length;Te++)$t=re(Ce,J,Te,Z[Te],ue),$t!==null&&(e&&$t.alternate!==null&&Ce.delete($t.key===null?Te:$t.key),H=c($t,H,Te),qe===null?Se=$t:qe.sibling=$t,qe=$t);return e&&Ce.forEach(function(xi){return t(J,xi)}),Ke&&Xi(J,Te),Se}function Ae(J,H,Z,ue){if(Z==null)throw Error(s(151));for(var Se=null,qe=null,Ce=H,Te=H=0,$t=null,Je=Z.next();Ce!==null&&!Je.done;Te++,Je=Z.next()){Ce.index>Te?($t=Ce,Ce=null):$t=Ce.sibling;var xi=ne(J,Ce,Je.value,ue);if(xi===null){Ce===null&&(Ce=$t);break}e&&Ce&&xi.alternate===null&&t(J,Ce),H=c(xi,H,Te),qe===null?Se=xi:qe.sibling=xi,qe=xi,Ce=$t}if(Je.done)return n(J,Ce),Ke&&Xi(J,Te),Se;if(Ce===null){for(;!Je.done;Te++,Je=Z.next())Je=fe(J,Je.value,ue),Je!==null&&(H=c(Je,H,Te),qe===null?Se=Je:qe.sibling=Je,qe=Je);return Ke&&Xi(J,Te),Se}for(Ce=a(Ce);!Je.done;Te++,Je=Z.next())Je=re(Ce,J,Te,Je.value,ue),Je!==null&&(e&&Je.alternate!==null&&Ce.delete(Je.key===null?Te:Je.key),H=c(Je,H,Te),qe===null?Se=Je:qe.sibling=Je,qe=Je);return e&&Ce.forEach(function(DS){return t(J,DS)}),Ke&&Xi(J,Te),Se}function rt(J,H,Z,ue){if(typeof Z=="object"&&Z!==null&&Z.type===M&&Z.key===null&&(Z=Z.props.children),typeof Z=="object"&&Z!==null){switch(Z.$$typeof){case x:e:{for(var Se=Z.key;H!==null;){if(H.key===Se){if(Se=Z.type,Se===M){if(H.tag===7){n(J,H.sibling),ue=l(H,Z.props.children),ue.return=J,J=ue;break e}}else if(H.elementType===Se||typeof Se=="object"&&Se!==null&&Se.$$typeof===I&&sy(Se)===H.type){n(J,H.sibling),ue=l(H,Z.props),ls(ue,Z),ue.return=J,J=ue;break e}n(J,H);break}else t(J,H);H=H.sibling}Z.type===M?(ue=$i(Z.props.children,J.mode,ue,Z.key),ue.return=J,J=ue):(ue=vl(Z.type,Z.key,Z.props,null,J.mode,ue),ls(ue,Z),ue.return=J,J=ue)}return g(J);case E:e:{for(Se=Z.key;H!==null;){if(H.key===Se)if(H.tag===4&&H.stateNode.containerInfo===Z.containerInfo&&H.stateNode.implementation===Z.implementation){n(J,H.sibling),ue=l(H,Z.children||[]),ue.return=J,J=ue;break e}else{n(J,H);break}else t(J,H);H=H.sibling}ue=tf(Z,J.mode,ue),ue.return=J,J=ue}return g(J);case I:return Se=Z._init,Z=Se(Z._payload),rt(J,H,Z,ue)}if(U(Z))return Fe(J,H,Z,ue);if(S(Z)){if(Se=S(Z),typeof Se!="function")throw Error(s(150));return Z=Se.call(Z),Ae(J,H,Z,ue)}if(typeof Z.then=="function")return rt(J,H,Il(Z),ue);if(Z.$$typeof===$)return rt(J,H,_l(J,Z),ue);Ll(J,Z)}return typeof Z=="string"&&Z!==""||typeof Z=="number"||typeof Z=="bigint"?(Z=""+Z,H!==null&&H.tag===6?(n(J,H.sibling),ue=l(H,Z),ue.return=J,J=ue):(n(J,H),ue=ef(Z,J.mode,ue),ue.return=J,J=ue),g(J)):n(J,H)}return function(J,H,Z,ue){try{ss=0;var Se=rt(J,H,Z,ue);return Ya=null,Se}catch(Ce){if(Ce===Go||Ce===Sl)throw Ce;var qe=xn(29,Ce,null,J.mode);return qe.lanes=ue,qe.return=J,qe}finally{}}}var Xa=ly(!0),uy=ly(!1),Un=D(null),Ir=null;function si(e){var t=e.alternate;de(jt,jt.current&1),de(Un,e),Ir===null&&(t===null||Va.current!==null||t.memoizedState!==null)&&(Ir=e)}function cy(e){if(e.tag===22){if(de(jt,jt.current),de(Un,e),Ir===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ir=e)}}else li()}function li(){de(jt,jt.current),de(Un,Un.current)}function Lr(e){te(Un),Ir===e&&(Ir=null),te(jt)}var jt=D(0);function Nl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||kh(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Lf(e,t,n,a){t=e.memoizedState,n=n(a,t),n=n==null?t:y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Nf={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=En(),l=ii(a);l.payload=t,n!=null&&(l.callback=n),t=ai(e,l,a),t!==null&&(Mn(t,e,a),es(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=En(),l=ii(a);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=ai(e,l,a),t!==null&&(Mn(t,e,a),es(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=En(),a=ii(n);a.tag=2,t!=null&&(a.callback=t),t=ai(e,a,n),t!==null&&(Mn(t,e,n),es(t,e,n))}};function dy(e,t,n,a,l,c,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,c,g):t.prototype&&t.prototype.isPureReactComponent?!Ho(n,a)||!Ho(l,c):!0}function fy(e,t,n,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&Nf.enqueueReplaceState(t,t.state,null)}function ta(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=y({},n));for(var l in e)n[l]===void 0&&(n[l]=e[l])}return n}var Dl=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function hy(e){Dl(e)}function py(e){console.error(e)}function my(e){Dl(e)}function zl(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function gy(e,t,n){try{var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(l){setTimeout(function(){throw l})}}function Df(e,t,n){return n=ii(n),n.tag=3,n.payload={element:null},n.callback=function(){zl(e,t)},n}function vy(e){return e=ii(e),e.tag=3,e}function yy(e,t,n,a){var l=n.type.getDerivedStateFromError;if(typeof l=="function"){var c=a.value;e.payload=function(){return l(c)},e.callback=function(){gy(t,n,a)}}var g=n.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(e.callback=function(){gy(t,n,a),typeof l!="function"&&(pi===null?pi=new Set([this]):pi.add(this));var _=a.stack;this.componentDidCatch(a.value,{componentStack:_!==null?_:""})})}function Dx(e,t,n,a,l){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&Jo(t,n,l,!0),n=Un.current,n!==null){switch(n.tag){case 13:return Ir===null?sh():n.alternate===null&&Ct===0&&(Ct=3),n.flags&=-257,n.flags|=65536,n.lanes=l,a===ff?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),uh(e,a,l)),!1;case 22:return n.flags|=65536,a===ff?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),uh(e,a,l)),!1}throw Error(s(435,n.tag))}return uh(e,a,l),sh(),!1}if(Ke)return t=Un.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=l,a!==af&&(e=Error(s(422),{cause:a}),Xo(Ln(e,n)))):(a!==af&&(t=Error(s(423),{cause:a}),Xo(Ln(t,n))),e=e.current.alternate,e.flags|=65536,l&=-l,e.lanes|=l,a=Ln(a,n),l=Df(e.stateNode,a,l),mf(e,l),Ct!==4&&(Ct=2)),!1;var c=Error(s(520),{cause:a});if(c=Ln(c,n),ms===null?ms=[c]:ms.push(c),Ct!==4&&(Ct=2),t===null)return!0;a=Ln(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=l&-l,n.lanes|=e,e=Df(n.stateNode,a,e),mf(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(pi===null||!pi.has(c))))return n.flags|=65536,l&=-l,n.lanes|=l,l=vy(l),yy(l,e,n,a),mf(n,l),!1}n=n.return}while(n!==null);return!1}var by=Error(s(461)),Ht=!1;function Kt(e,t,n,a){t.child=e===null?uy(t,null,n,a):Xa(t,e.child,n,a)}function wy(e,t,n,a,l){n=n.render;var c=t.ref;if("ref"in a){var g={};for(var _ in a)_!=="ref"&&(g[_]=a[_])}else g=a;return Gi(t),a=wf(e,t,n,g,c,l),_=_f(),e!==null&&!Ht?(xf(e,t,l),Nr(e,t,l)):(Ke&&_&&nf(t),t.flags|=1,Kt(e,t,a,l),t.child)}function _y(e,t,n,a,l){if(e===null){var c=n.type;return typeof c=="function"&&!Qd(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,xy(e,t,c,a,l)):(e=vl(n.type,null,a,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!Bf(e,l)){var g=c.memoizedProps;if(n=n.compare,n=n!==null?n:Ho,n(g,a)&&e.ref===t.ref)return Nr(e,t,l)}return t.flags|=1,e=Ar(c,a),e.ref=t.ref,e.return=t,t.child=e}function xy(e,t,n,a,l){if(e!==null){var c=e.memoizedProps;if(Ho(c,a)&&e.ref===t.ref)if(Ht=!1,t.pendingProps=a=c,Bf(e,l))(e.flags&131072)!==0&&(Ht=!0);else return t.lanes=e.lanes,Nr(e,t,l)}return zf(e,t,n,a,l)}function Sy(e,t,n){var a=t.pendingProps,l=a.children,c=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=c!==null?c.baseLanes|n:n,e!==null){for(l=t.child=e.child,c=0;l!==null;)c=c|l.lanes|l.childLanes,l=l.sibling;t.childLanes=c&~a}else t.childLanes=0,t.child=null;return ky(e,t,a,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&xl(t,c!==null?c.cachePool:null),c!==null?xv(t,c):vf(),cy(t);else return t.lanes=t.childLanes=536870912,ky(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(xl(t,c.cachePool),xv(t,c),li(),t.memoizedState=null):(e!==null&&xl(t,null),vf(),li());return Kt(e,t,l,n),t.child}function ky(e,t,n,a){var l=df();return l=l===null?null:{parent:qt._currentValue,pool:l},t.memoizedState={baseLanes:n,cachePool:l},e!==null&&xl(t,null),vf(),cy(t),e!==null&&Jo(e,t,a,!0),null}function Ul(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(s(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function zf(e,t,n,a,l){return Gi(t),n=wf(e,t,n,a,void 0,l),a=_f(),e!==null&&!Ht?(xf(e,t,l),Nr(e,t,l)):(Ke&&a&&nf(t),t.flags|=1,Kt(e,t,n,l),t.child)}function Cy(e,t,n,a,l,c){return Gi(t),t.updateQueue=null,n=kv(t,a,n,l),Sv(e),a=_f(),e!==null&&!Ht?(xf(e,t,c),Nr(e,t,c)):(Ke&&a&&nf(t),t.flags|=1,Kt(e,t,n,c),t.child)}function Ey(e,t,n,a,l){if(Gi(t),t.stateNode===null){var c=za,g=n.contextType;typeof g=="object"&&g!==null&&(c=tn(g)),c=new n(a,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=Nf,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=a,c.state=t.memoizedState,c.refs={},hf(t),g=n.contextType,c.context=typeof g=="object"&&g!==null?tn(g):za,c.state=t.memoizedState,g=n.getDerivedStateFromProps,typeof g=="function"&&(Lf(t,n,g,a),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(g=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),g!==c.state&&Nf.enqueueReplaceState(c,c.state,null),ns(t,a,c,l),ts(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){c=t.stateNode;var _=t.memoizedProps,L=ta(n,_);c.props=L;var Q=c.context,se=n.contextType;g=za,typeof se=="object"&&se!==null&&(g=tn(se));var fe=n.getDerivedStateFromProps;se=typeof fe=="function"||typeof c.getSnapshotBeforeUpdate=="function",_=t.pendingProps!==_,se||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(_||Q!==g)&&fy(t,c,a,g),ri=!1;var ne=t.memoizedState;c.state=ne,ns(t,a,c,l),ts(),Q=t.memoizedState,_||ne!==Q||ri?(typeof fe=="function"&&(Lf(t,n,fe,a),Q=t.memoizedState),(L=ri||dy(t,n,L,a,ne,Q,g))?(se||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=Q),c.props=a,c.state=Q,c.context=g,a=L):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{c=t.stateNode,pf(e,t),g=t.memoizedProps,se=ta(n,g),c.props=se,fe=t.pendingProps,ne=c.context,Q=n.contextType,L=za,typeof Q=="object"&&Q!==null&&(L=tn(Q)),_=n.getDerivedStateFromProps,(Q=typeof _=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(g!==fe||ne!==L)&&fy(t,c,a,L),ri=!1,ne=t.memoizedState,c.state=ne,ns(t,a,c,l),ts();var re=t.memoizedState;g!==fe||ne!==re||ri||e!==null&&e.dependencies!==null&&wl(e.dependencies)?(typeof _=="function"&&(Lf(t,n,_,a),re=t.memoizedState),(se=ri||dy(t,n,se,a,ne,re,L)||e!==null&&e.dependencies!==null&&wl(e.dependencies))?(Q||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(a,re,L),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(a,re,L)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||g===e.memoizedProps&&ne===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&ne===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=re),c.props=a,c.state=re,c.context=L,a=se):(typeof c.componentDidUpdate!="function"||g===e.memoizedProps&&ne===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||g===e.memoizedProps&&ne===e.memoizedState||(t.flags|=1024),a=!1)}return c=a,Ul(e,t),a=(t.flags&128)!==0,c||a?(c=t.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&a?(t.child=Xa(t,e.child,null,l),t.child=Xa(t,null,n,l)):Kt(e,t,n,l),t.memoizedState=c.state,e=t.child):e=Nr(e,t,l),e}function My(e,t,n,a){return Yo(),t.flags|=256,Kt(e,t,n,a),t.child}var Uf={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function qf(e){return{baseLanes:e,cachePool:pv()}}function jf(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=qn),e}function Ay(e,t,n){var a=t.pendingProps,l=!1,c=(t.flags&128)!==0,g;if((g=c)||(g=e!==null&&e.memoizedState===null?!1:(jt.current&2)!==0),g&&(l=!0,t.flags&=-129),g=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ke){if(l?si(t):li(),Ke){var _=kt,L;if(L=_){e:{for(L=_,_=sr;L.nodeType!==8;){if(!_){_=null;break e}if(L=Jn(L.nextSibling),L===null){_=null;break e}}_=L}_!==null?(t.memoizedState={dehydrated:_,treeContext:Yi!==null?{id:Tr,overflow:Fr}:null,retryLane:536870912,hydrationErrors:null},L=xn(18,null,null,0),L.stateNode=_,L.return=t,t.child=L,ln=t,kt=null,L=!0):L=!1}L||Ki(t)}if(_=t.memoizedState,_!==null&&(_=_.dehydrated,_!==null))return kh(_)?t.lanes=32:t.lanes=536870912,null;Lr(t)}return _=a.children,a=a.fallback,l?(li(),l=t.mode,_=ql({mode:"hidden",children:_},l),a=$i(a,l,n,null),_.return=t,a.return=t,_.sibling=a,t.child=_,l=t.child,l.memoizedState=qf(n),l.childLanes=jf(e,g,n),t.memoizedState=Uf,a):(si(t),Wf(t,_))}if(L=e.memoizedState,L!==null&&(_=L.dehydrated,_!==null)){if(c)t.flags&256?(si(t),t.flags&=-257,t=Vf(e,t,n)):t.memoizedState!==null?(li(),t.child=e.child,t.flags|=128,t=null):(li(),l=a.fallback,_=t.mode,a=ql({mode:"visible",children:a.children},_),l=$i(l,_,n,null),l.flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,Xa(t,e.child,null,n),a=t.child,a.memoizedState=qf(n),a.childLanes=jf(e,g,n),t.memoizedState=Uf,t=l);else if(si(t),kh(_)){if(g=_.nextSibling&&_.nextSibling.dataset,g)var Q=g.dgst;g=Q,a=Error(s(419)),a.stack="",a.digest=g,Xo({value:a,source:null,stack:null}),t=Vf(e,t,n)}else if(Ht||Jo(e,t,n,!1),g=(n&e.childLanes)!==0,Ht||g){if(g=ot,g!==null&&(a=n&-n,a=(a&42)!==0?1:zi(a),a=(a&(g.suspendedLanes|n))!==0?0:a,a!==0&&a!==L.retryLane))throw L.retryLane=a,Da(e,a),Mn(g,e,a),by;_.data==="$?"||sh(),t=Vf(e,t,n)}else _.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=L.treeContext,kt=Jn(_.nextSibling),ln=t,Ke=!0,Ji=null,sr=!1,e!==null&&(Dn[zn++]=Tr,Dn[zn++]=Fr,Dn[zn++]=Yi,Tr=e.id,Fr=e.overflow,Yi=t),t=Wf(t,a.children),t.flags|=4096);return t}return l?(li(),l=a.fallback,_=t.mode,L=e.child,Q=L.sibling,a=Ar(L,{mode:"hidden",children:a.children}),a.subtreeFlags=L.subtreeFlags&65011712,Q!==null?l=Ar(Q,l):(l=$i(l,_,n,null),l.flags|=2),l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,_=e.child.memoizedState,_===null?_=qf(n):(L=_.cachePool,L!==null?(Q=qt._currentValue,L=L.parent!==Q?{parent:Q,pool:Q}:L):L=pv(),_={baseLanes:_.baseLanes|n,cachePool:L}),l.memoizedState=_,l.childLanes=jf(e,g,n),t.memoizedState=Uf,a):(si(t),n=e.child,e=n.sibling,n=Ar(n,{mode:"visible",children:a.children}),n.return=t,n.sibling=null,e!==null&&(g=t.deletions,g===null?(t.deletions=[e],t.flags|=16):g.push(e)),t.child=n,t.memoizedState=null,n)}function Wf(e,t){return t=ql({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ql(e,t){return e=xn(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vf(e,t,n){return Xa(t,e.child,null,n),e=Wf(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ty(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),sf(e.return,t,n)}function Hf(e,t,n,a,l){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:l}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=a,c.tail=n,c.tailMode=l)}function Fy(e,t,n){var a=t.pendingProps,l=a.revealOrder,c=a.tail;if(Kt(e,t,a.children,n),a=jt.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ty(e,n,t);else if(e.tag===19)Ty(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(de(jt,a),l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Nl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Hf(t,!1,l,n,c);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Nl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Hf(t,!0,n,null,c);break;case"together":Hf(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Nr(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),hi|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Jo(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=Ar(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ar(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Bf(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&wl(e)))}function zx(e,t,n){switch(t.tag){case 3:he(t,t.stateNode.containerInfo),ni(t,qt,e.memoizedState.cache),Yo();break;case 27:case 5:Ze(t);break;case 4:he(t,t.stateNode.containerInfo);break;case 10:ni(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(si(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Ay(e,t,n):(si(t),e=Nr(e,t,n),e!==null?e.sibling:null);si(t);break;case 19:var l=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(Jo(e,t,n,!1),a=(n&t.childLanes)!==0),l){if(a)return Fy(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),de(jt,jt.current),a)break;return null;case 22:case 23:return t.lanes=0,Sy(e,t,n);case 24:ni(t,qt,e.memoizedState.cache)}return Nr(e,t,n)}function Py(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ht=!0;else{if(!Bf(e,n)&&(t.flags&128)===0)return Ht=!1,zx(e,t,n);Ht=(e.flags&131072)!==0}else Ht=!1,Ke&&(t.flags&1048576)!==0&&sv(t,bl,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,l=a._init;if(a=l(a._payload),t.type=a,typeof a=="function")Qd(a)?(e=ta(a,e),t.tag=1,t=Ey(null,t,a,e,n)):(t.tag=0,t=zf(null,t,a,e,n));else{if(a!=null){if(l=a.$$typeof,l===ae){t.tag=11,t=wy(null,t,a,e,n);break e}else if(l===N){t.tag=14,t=_y(null,t,a,e,n);break e}}throw t=O(a)||a,Error(s(306,t,""))}}return t;case 0:return zf(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,l=ta(a,t.pendingProps),Ey(e,t,a,l,n);case 3:e:{if(he(t,t.stateNode.containerInfo),e===null)throw Error(s(387));a=t.pendingProps;var c=t.memoizedState;l=c.element,pf(e,t),ns(t,a,null,n);var g=t.memoizedState;if(a=g.cache,ni(t,qt,a),a!==c.cache&&lf(t,[qt],n,!0),ts(),a=g.element,c.isDehydrated)if(c={element:a,isDehydrated:!1,cache:g.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=My(e,t,a,n);break e}else if(a!==l){l=Ln(Error(s(424)),t),Xo(l),t=My(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(kt=Jn(e.firstChild),ln=t,Ke=!0,Ji=null,sr=!0,n=uy(t,null,a,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Yo(),a===l){t=Nr(e,t,n);break e}Kt(e,t,a,n)}t=t.child}return t;case 26:return Ul(e,t),e===null?(n=Lb(t.type,null,t.pendingProps,null))?t.memoizedState=n:Ke||(n=t.type,e=t.pendingProps,a=eu(_e.current).createElement(n),a[wt]=t,a[_t]=e,Gt(a,n,e),xt(a),t.stateNode=a):t.memoizedState=Lb(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ze(t),e===null&&Ke&&(a=t.stateNode=Ob(t.type,t.pendingProps,_e.current),ln=t,sr=!0,l=kt,vi(t.type)?(Ch=l,kt=Jn(a.firstChild)):kt=l),Kt(e,t,t.pendingProps.children,n),Ul(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ke&&((l=a=kt)&&(a=fS(a,t.type,t.pendingProps,sr),a!==null?(t.stateNode=a,ln=t,kt=Jn(a.firstChild),sr=!1,l=!0):l=!1),l||Ki(t)),Ze(t),l=t.type,c=t.pendingProps,g=e!==null?e.memoizedProps:null,a=c.children,_h(l,c)?a=null:g!==null&&_h(l,g)&&(t.flags|=32),t.memoizedState!==null&&(l=wf(e,t,Fx,null,null,n),ks._currentValue=l),Ul(e,t),Kt(e,t,a,n),t.child;case 6:return e===null&&Ke&&((e=n=kt)&&(n=hS(n,t.pendingProps,sr),n!==null?(t.stateNode=n,ln=t,kt=null,e=!0):e=!1),e||Ki(t)),null;case 13:return Ay(e,t,n);case 4:return he(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Xa(t,null,a,n):Kt(e,t,a,n),t.child;case 11:return wy(e,t,t.type,t.pendingProps,n);case 7:return Kt(e,t,t.pendingProps,n),t.child;case 8:return Kt(e,t,t.pendingProps.children,n),t.child;case 12:return Kt(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,ni(t,t.type,a.value),Kt(e,t,a.children,n),t.child;case 9:return l=t.type._context,a=t.pendingProps.children,Gi(t),l=tn(l),a=a(l),t.flags|=1,Kt(e,t,a,n),t.child;case 14:return _y(e,t,t.type,t.pendingProps,n);case 15:return xy(e,t,t.type,t.pendingProps,n);case 19:return Fy(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(n=ql(a,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Ar(e.child,a),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Sy(e,t,n);case 24:return Gi(t),a=tn(qt),e===null?(l=df(),l===null&&(l=ot,c=uf(),l.pooledCache=c,c.refCount++,c!==null&&(l.pooledCacheLanes|=n),l=c),t.memoizedState={parent:a,cache:l},hf(t),ni(t,qt,l)):((e.lanes&n)!==0&&(pf(e,t),ns(t,null,null,n),ts()),l=e.memoizedState,c=t.memoizedState,l.parent!==a?(l={parent:a,cache:a},t.memoizedState=l,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=l),ni(t,qt,a)):(a=c.cache,ni(t,qt,a),a!==l.cache&&lf(t,[qt],n,!0))),Kt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function Dr(e){e.flags|=4}function Oy(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!qb(t)){if(t=Un.current,t!==null&&((Be&4194048)===Be?Ir!==null:(Be&62914560)!==Be&&(Be&536870912)===0||t!==Ir))throw Qo=ff,mv;e.flags|=8192}}function jl(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?wa():536870912,e.lanes|=t,Ga|=t)}function us(e,t){if(!Ke)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function gt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags&65011712,a|=l.flags&65011712,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags,a|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function Ux(e,t,n){var a=t.pendingProps;switch(rf(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return gt(t),null;case 1:return gt(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Or(qt),Ye(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&($o(t)?Dr(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,cv())),gt(t),null;case 26:return n=t.memoizedState,e===null?(Dr(t),n!==null?(gt(t),Oy(t,n)):(gt(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Dr(t),gt(t),Oy(t,n)):(gt(t),t.flags&=-16777217):(e.memoizedProps!==a&&Dr(t),gt(t),t.flags&=-16777217),null;case 27:dt(t),n=_e.current;var l=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Dr(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return gt(t),null}e=me.current,$o(t)?lv(t):(e=Ob(l,a,n),t.stateNode=e,Dr(t))}return gt(t),null;case 5:if(dt(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Dr(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return gt(t),null}if(e=me.current,$o(t))lv(t);else{switch(l=eu(_e.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?l.createElement("select",{is:a.is}):l.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?l.createElement(n,{is:a.is}):l.createElement(n)}}e[wt]=t,e[_t]=a;e:for(l=t.child;l!==null;){if(l.tag===5||l.tag===6)e.appendChild(l.stateNode);else if(l.tag!==4&&l.tag!==27&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;l.sibling===null;){if(l.return===null||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(Gt(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Dr(t)}}return gt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Dr(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(s(166));if(e=_e.current,$o(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,l=ln,l!==null)switch(l.tag){case 27:case 5:a=l.memoizedProps}e[wt]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||Cb(e.nodeValue,n)),e||Ki(t)}else e=eu(e).createTextNode(a),e[wt]=t,t.stateNode=e}return gt(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(l=$o(t),a!==null&&a.dehydrated!==null){if(e===null){if(!l)throw Error(s(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(s(317));l[wt]=t}else Yo(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;gt(t),l=!1}else l=cv(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return t.flags&256?(Lr(t),t):(Lr(t),null)}if(Lr(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=a!==null,e=e!==null&&e.memoizedState!==null,n){a=t.child,l=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(l=a.alternate.memoizedState.cachePool.pool);var c=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(c=a.memoizedState.cachePool.pool),c!==l&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),jl(t,t.updateQueue),gt(t),null;case 4:return Ye(),e===null&&gh(t.stateNode.containerInfo),gt(t),null;case 10:return Or(t.type),gt(t),null;case 19:if(te(jt),l=t.memoizedState,l===null)return gt(t),null;if(a=(t.flags&128)!==0,c=l.rendering,c===null)if(a)us(l,!1);else{if(Ct!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Nl(e),c!==null){for(t.flags|=128,us(l,!1),e=c.updateQueue,t.updateQueue=e,jl(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)ov(n,e),n=n.sibling;return de(jt,jt.current&1|2),t.child}e=e.sibling}l.tail!==null&&Xt()>Hl&&(t.flags|=128,a=!0,us(l,!1),t.lanes=4194304)}else{if(!a)if(e=Nl(c),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,jl(t,e),us(l,!0),l.tail===null&&l.tailMode==="hidden"&&!c.alternate&&!Ke)return gt(t),null}else 2*Xt()-l.renderingStartTime>Hl&&n!==536870912&&(t.flags|=128,a=!0,us(l,!1),t.lanes=4194304);l.isBackwards?(c.sibling=t.child,t.child=c):(e=l.last,e!==null?e.sibling=c:t.child=c,l.last=c)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Xt(),t.sibling=null,e=jt.current,de(jt,a?e&1|2:e&1),t):(gt(t),null);case 22:case 23:return Lr(t),yf(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(n&536870912)!==0&&(t.flags&128)===0&&(gt(t),t.subtreeFlags&6&&(t.flags|=8192)):gt(t),n=t.updateQueue,n!==null&&jl(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&te(Qi),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Or(qt),gt(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function qx(e,t){switch(rf(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Or(qt),Ye(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return dt(t),null;case 13:if(Lr(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Yo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return te(jt),null;case 4:return Ye(),null;case 10:return Or(t.type),null;case 22:case 23:return Lr(t),yf(),e!==null&&te(Qi),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Or(qt),null;case 25:return null;default:return null}}function Ry(e,t){switch(rf(t),t.tag){case 3:Or(qt),Ye();break;case 26:case 27:case 5:dt(t);break;case 4:Ye();break;case 13:Lr(t);break;case 19:te(jt);break;case 10:Or(t.type);break;case 22:case 23:Lr(t),yf(),e!==null&&te(Qi);break;case 24:Or(qt)}}function cs(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var l=a.next;n=l;do{if((n.tag&e)===e){a=void 0;var c=n.create,g=n.inst;a=c(),g.destroy=a}n=n.next}while(n!==l)}}catch(_){it(t,t.return,_)}}function ui(e,t,n){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var c=l.next;a=c;do{if((a.tag&e)===e){var g=a.inst,_=g.destroy;if(_!==void 0){g.destroy=void 0,l=t;var L=n,Q=_;try{Q()}catch(se){it(l,L,se)}}}a=a.next}while(a!==c)}}catch(se){it(t,t.return,se)}}function Iy(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{_v(t,n)}catch(a){it(e,e.return,a)}}}function Ly(e,t,n){n.props=ta(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){it(e,t,a)}}function ds(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof n=="function"?e.refCleanup=n(a):n.current=a}}catch(l){it(e,t,l)}}function lr(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(l){it(e,t,l)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(l){it(e,t,l)}else n.current=null}function Ny(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(l){it(e,e.return,l)}}function $f(e,t,n){try{var a=e.stateNode;sS(a,e.type,n,t),a[_t]=t}catch(l){it(e,e.return,l)}}function Dy(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&vi(e.type)||e.tag===4}function Yf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Dy(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&vi(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Xf(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ql));else if(a!==4&&(a===27&&vi(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Xf(e,t,n),e=e.sibling;e!==null;)Xf(e,t,n),e=e.sibling}function Wl(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&vi(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Wl(e,t,n),e=e.sibling;e!==null;)Wl(e,t,n),e=e.sibling}function zy(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,l=t.attributes;l.length;)t.removeAttributeNode(l[0]);Gt(t,a,n),t[wt]=e,t[_t]=n}catch(c){it(e,e.return,c)}}var zr=!1,Mt=!1,Jf=!1,Uy=typeof WeakSet=="function"?WeakSet:Set,Bt=null;function jx(e,t){if(e=e.containerInfo,bh=ou,e=Kg(e),$d(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var l=a.anchorOffset,c=a.focusNode;a=a.focusOffset;try{n.nodeType,c.nodeType}catch(Ae){n=null;break e}var g=0,_=-1,L=-1,Q=0,se=0,fe=e,ne=null;t:for(;;){for(var re;fe!==n||l!==0&&fe.nodeType!==3||(_=g+l),fe!==c||a!==0&&fe.nodeType!==3||(L=g+a),fe.nodeType===3&&(g+=fe.nodeValue.length),(re=fe.firstChild)!==null;)ne=fe,fe=re;for(;;){if(fe===e)break t;if(ne===n&&++Q===l&&(_=g),ne===c&&++se===a&&(L=g),(re=fe.nextSibling)!==null)break;fe=ne,ne=fe.parentNode}fe=re}n=_===-1||L===-1?null:{start:_,end:L}}else n=null}n=n||{start:0,end:0}}else n=null;for(wh={focusedElem:e,selectionRange:n},ou=!1,Bt=t;Bt!==null;)if(t=Bt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Bt=e;else for(;Bt!==null;){switch(t=Bt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,l=c.memoizedProps,c=c.memoizedState,a=n.stateNode;try{var Fe=ta(n.type,l,n.elementType===n.type);e=a.getSnapshotBeforeUpdate(Fe,c),a.__reactInternalSnapshotBeforeUpdate=e}catch(Ae){it(n,n.return,Ae)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Sh(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Sh(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,Bt=e;break}Bt=t.return}}function qy(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:ci(e,n),a&4&&cs(5,n);break;case 1:if(ci(e,n),a&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(g){it(n,n.return,g)}else{var l=ta(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(l,t,e.__reactInternalSnapshotBeforeUpdate)}catch(g){it(n,n.return,g)}}a&64&&Iy(n),a&512&&ds(n,n.return);break;case 3:if(ci(e,n),a&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{_v(e,t)}catch(g){it(n,n.return,g)}}break;case 27:t===null&&a&4&&zy(n);case 26:case 5:ci(e,n),t===null&&a&4&&Ny(n),a&512&&ds(n,n.return);break;case 12:ci(e,n);break;case 13:ci(e,n),a&4&&Vy(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Kx.bind(null,n),pS(e,n))));break;case 22:if(a=n.memoizedState!==null||zr,!a){t=t!==null&&t.memoizedState!==null||Mt,l=zr;var c=Mt;zr=a,(Mt=t)&&!c?di(e,n,(n.subtreeFlags&8772)!==0):ci(e,n),zr=l,Mt=c}break;case 30:break;default:ci(e,n)}}function jy(e){var t=e.alternate;t!==null&&(e.alternate=null,jy(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&ji(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var pt=null,mn=!1;function Ur(e,t,n){for(n=n.child;n!==null;)Wy(e,t,n),n=n.sibling}function Wy(e,t,n){if(Xe&&typeof Xe.onCommitFiberUnmount=="function")try{Xe.onCommitFiberUnmount(wr,n)}catch(c){}switch(n.tag){case 26:Mt||lr(n,t),Ur(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Mt||lr(n,t);var a=pt,l=mn;vi(n.type)&&(pt=n.stateNode,mn=!1),Ur(e,t,n),ws(n.stateNode),pt=a,mn=l;break;case 5:Mt||lr(n,t);case 6:if(a=pt,l=mn,pt=null,Ur(e,t,n),pt=a,mn=l,pt!==null)if(mn)try{(pt.nodeType===9?pt.body:pt.nodeName==="HTML"?pt.ownerDocument.body:pt).removeChild(n.stateNode)}catch(c){it(n,t,c)}else try{pt.removeChild(n.stateNode)}catch(c){it(n,t,c)}break;case 18:pt!==null&&(mn?(e=pt,Fb(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),As(e)):Fb(pt,n.stateNode));break;case 4:a=pt,l=mn,pt=n.stateNode.containerInfo,mn=!0,Ur(e,t,n),pt=a,mn=l;break;case 0:case 11:case 14:case 15:Mt||ui(2,n,t),Mt||ui(4,n,t),Ur(e,t,n);break;case 1:Mt||(lr(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&Ly(n,t,a)),Ur(e,t,n);break;case 21:Ur(e,t,n);break;case 22:Mt=(a=Mt)||n.memoizedState!==null,Ur(e,t,n),Mt=a;break;default:Ur(e,t,n)}}function Vy(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{As(e)}catch(n){it(t,t.return,n)}}function Wx(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Uy),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Uy),t;default:throw Error(s(435,e.tag))}}function Kf(e,t){var n=Wx(e);t.forEach(function(a){var l=Zx.bind(null,e,a);n.has(a)||(n.add(a),a.then(l,l))})}function Sn(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var l=n[a],c=e,g=t,_=g;e:for(;_!==null;){switch(_.tag){case 27:if(vi(_.type)){pt=_.stateNode,mn=!1;break e}break;case 5:pt=_.stateNode,mn=!1;break e;case 3:case 4:pt=_.stateNode.containerInfo,mn=!0;break e}_=_.return}if(pt===null)throw Error(s(160));Wy(c,g,l),pt=null,mn=!1,c=l.alternate,c!==null&&(c.return=null),l.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Hy(t,e),t=t.sibling}var Xn=null;function Hy(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Sn(t,e),kn(e),a&4&&(ui(3,e,e.return),cs(3,e),ui(5,e,e.return));break;case 1:Sn(t,e),kn(e),a&512&&(Mt||n===null||lr(n,n.return)),a&64&&zr&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var l=Xn;if(Sn(t,e),kn(e),a&512&&(Mt||n===null||lr(n,n.return)),a&4){var c=n!==null?n.memoizedState:null;if(a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,l=l.ownerDocument||l;t:switch(a){case"title":c=l.getElementsByTagName("title")[0],(!c||c[xr]||c[wt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=l.createElement(a),l.head.insertBefore(c,l.querySelector("head > title"))),Gt(c,a,n),c[wt]=e,xt(c),a=c;break e;case"link":var g=zb("link","href",l).get(a+(n.href||""));if(g){for(var _=0;_<g.length;_++)if(c=g[_],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){g.splice(_,1);break t}}c=l.createElement(a),Gt(c,a,n),l.head.appendChild(c);break;case"meta":if(g=zb("meta","content",l).get(a+(n.content||""))){for(_=0;_<g.length;_++)if(c=g[_],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){g.splice(_,1);break t}}c=l.createElement(a),Gt(c,a,n),l.head.appendChild(c);break;default:throw Error(s(468,a))}c[wt]=e,xt(c),a=c}e.stateNode=a}else Ub(l,e.type,e.stateNode);else e.stateNode=Db(l,a,e.memoizedProps);else c!==a?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,a===null?Ub(l,e.type,e.stateNode):Db(l,a,e.memoizedProps)):a===null&&e.stateNode!==null&&$f(e,e.memoizedProps,n.memoizedProps)}break;case 27:Sn(t,e),kn(e),a&512&&(Mt||n===null||lr(n,n.return)),n!==null&&a&4&&$f(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Sn(t,e),kn(e),a&512&&(Mt||n===null||lr(n,n.return)),e.flags&32){l=e.stateNode;try{Re(l,"")}catch(re){it(e,e.return,re)}}a&4&&e.stateNode!=null&&(l=e.memoizedProps,$f(e,l,n!==null?n.memoizedProps:l)),a&1024&&(Jf=!0);break;case 6:if(Sn(t,e),kn(e),a&4){if(e.stateNode===null)throw Error(s(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(re){it(e,e.return,re)}}break;case 3:if(ru=null,l=Xn,Xn=tu(t.containerInfo),Sn(t,e),Xn=l,kn(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{As(t.containerInfo)}catch(re){it(e,e.return,re)}Jf&&(Jf=!1,By(e));break;case 4:a=Xn,Xn=tu(e.stateNode.containerInfo),Sn(t,e),kn(e),Xn=a;break;case 12:Sn(t,e),kn(e);break;case 13:Sn(t,e),kn(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(nh=Xt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Kf(e,a)));break;case 22:l=e.memoizedState!==null;var L=n!==null&&n.memoizedState!==null,Q=zr,se=Mt;if(zr=Q||l,Mt=se||L,Sn(t,e),Mt=se,zr=Q,kn(e),a&8192)e:for(t=e.stateNode,t._visibility=l?t._visibility&-2:t._visibility|1,l&&(n===null||L||zr||Mt||na(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){L=n=t;try{if(c=L.stateNode,l)g=c.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{_=L.stateNode;var fe=L.memoizedProps.style,ne=fe!=null&&fe.hasOwnProperty("display")?fe.display:null;_.style.display=ne==null||typeof ne=="boolean"?"":(""+ne).trim()}}catch(re){it(L,L.return,re)}}}else if(t.tag===6){if(n===null){L=t;try{L.stateNode.nodeValue=l?"":L.memoizedProps}catch(re){it(L,L.return,re)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,Kf(e,n))));break;case 19:Sn(t,e),kn(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Kf(e,a)));break;case 30:break;case 21:break;default:Sn(t,e),kn(e)}}function kn(e){var t=e.flags;if(t&2){try{for(var n,a=e.return;a!==null;){if(Dy(a)){n=a;break}a=a.return}if(n==null)throw Error(s(160));switch(n.tag){case 27:var l=n.stateNode,c=Yf(e);Wl(e,c,l);break;case 5:var g=n.stateNode;n.flags&32&&(Re(g,""),n.flags&=-33);var _=Yf(e);Wl(e,_,g);break;case 3:case 4:var L=n.stateNode.containerInfo,Q=Yf(e);Xf(e,Q,L);break;default:throw Error(s(161))}}catch(se){it(e,e.return,se)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function By(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;By(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ci(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)qy(e,t.alternate,t),t=t.sibling}function na(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ui(4,t,t.return),na(t);break;case 1:lr(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Ly(t,t.return,n),na(t);break;case 27:ws(t.stateNode);case 26:case 5:lr(t,t.return),na(t);break;case 22:t.memoizedState===null&&na(t);break;case 30:na(t);break;default:na(t)}e=e.sibling}}function di(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,l=e,c=t,g=c.flags;switch(c.tag){case 0:case 11:case 15:di(l,c,n),cs(4,c);break;case 1:if(di(l,c,n),a=c,l=a.stateNode,typeof l.componentDidMount=="function")try{l.componentDidMount()}catch(Q){it(a,a.return,Q)}if(a=c,l=a.updateQueue,l!==null){var _=a.stateNode;try{var L=l.shared.hiddenCallbacks;if(L!==null)for(l.shared.hiddenCallbacks=null,l=0;l<L.length;l++)wv(L[l],_)}catch(Q){it(a,a.return,Q)}}n&&g&64&&Iy(c),ds(c,c.return);break;case 27:zy(c);case 26:case 5:di(l,c,n),n&&a===null&&g&4&&Ny(c),ds(c,c.return);break;case 12:di(l,c,n);break;case 13:di(l,c,n),n&&g&4&&Vy(l,c);break;case 22:c.memoizedState===null&&di(l,c,n),ds(c,c.return);break;case 30:break;default:di(l,c,n)}t=t.sibling}}function Zf(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Ko(n))}function Gf(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ko(e))}function ur(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)$y(e,t,n,a),t=t.sibling}function $y(e,t,n,a){var l=t.flags;switch(t.tag){case 0:case 11:case 15:ur(e,t,n,a),l&2048&&cs(9,t);break;case 1:ur(e,t,n,a);break;case 3:ur(e,t,n,a),l&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ko(e)));break;case 12:if(l&2048){ur(e,t,n,a),e=t.stateNode;try{var c=t.memoizedProps,g=c.id,_=c.onPostCommit;typeof _=="function"&&_(g,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(L){it(t,t.return,L)}}else ur(e,t,n,a);break;case 13:ur(e,t,n,a);break;case 23:break;case 22:c=t.stateNode,g=t.alternate,t.memoizedState!==null?c._visibility&2?ur(e,t,n,a):fs(e,t):c._visibility&2?ur(e,t,n,a):(c._visibility|=2,Ja(e,t,n,a,(t.subtreeFlags&10256)!==0)),l&2048&&Zf(g,t);break;case 24:ur(e,t,n,a),l&2048&&Gf(t.alternate,t);break;default:ur(e,t,n,a)}}function Ja(e,t,n,a,l){for(l=l&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,g=t,_=n,L=a,Q=g.flags;switch(g.tag){case 0:case 11:case 15:Ja(c,g,_,L,l),cs(8,g);break;case 23:break;case 22:var se=g.stateNode;g.memoizedState!==null?se._visibility&2?Ja(c,g,_,L,l):fs(c,g):(se._visibility|=2,Ja(c,g,_,L,l)),l&&Q&2048&&Zf(g.alternate,g);break;case 24:Ja(c,g,_,L,l),l&&Q&2048&&Gf(g.alternate,g);break;default:Ja(c,g,_,L,l)}t=t.sibling}}function fs(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,l=a.flags;switch(a.tag){case 22:fs(n,a),l&2048&&Zf(a.alternate,a);break;case 24:fs(n,a),l&2048&&Gf(a.alternate,a);break;default:fs(n,a)}t=t.sibling}}var hs=8192;function Ka(e){if(e.subtreeFlags&hs)for(e=e.child;e!==null;)Yy(e),e=e.sibling}function Yy(e){switch(e.tag){case 26:Ka(e),e.flags&hs&&e.memoizedState!==null&&MS(Xn,e.memoizedState,e.memoizedProps);break;case 5:Ka(e);break;case 3:case 4:var t=Xn;Xn=tu(e.stateNode.containerInfo),Ka(e),Xn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=hs,hs=16777216,Ka(e),hs=t):Ka(e));break;default:Ka(e)}}function Xy(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ps(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];Bt=a,Ky(a,e)}Xy(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Jy(e),e=e.sibling}function Jy(e){switch(e.tag){case 0:case 11:case 15:ps(e),e.flags&2048&&ui(9,e,e.return);break;case 3:ps(e);break;case 12:ps(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Vl(e)):ps(e);break;default:ps(e)}}function Vl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];Bt=a,Ky(a,e)}Xy(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:ui(8,t,t.return),Vl(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Vl(t));break;default:Vl(t)}e=e.sibling}}function Ky(e,t){for(;Bt!==null;){var n=Bt;switch(n.tag){case 0:case 11:case 15:ui(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Ko(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,Bt=a;else e:for(n=e;Bt!==null;){a=Bt;var l=a.sibling,c=a.return;if(jy(a),a===n){Bt=null;break e}if(l!==null){l.return=c,Bt=l;break e}Bt=c}}}var Vx={getCacheForType:function(e){var t=tn(qt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},Hx=typeof WeakMap=="function"?WeakMap:Map,Ge=0,ot=null,je=null,Be=0,Qe=0,Cn=null,fi=!1,Za=!1,Qf=!1,qr=0,Ct=0,hi=0,ra=0,eh=0,qn=0,Ga=0,ms=null,gn=null,th=!1,nh=0,Hl=1/0,Bl=null,pi=null,Zt=0,mi=null,Qa=null,eo=0,rh=0,ih=null,Zy=null,gs=0,ah=null;function En(){if((Ge&2)!==0&&Be!==0)return Be&-Be;if(k.T!==null){var e=ja;return e!==0?e:fh()}return ka()}function Gy(){qn===0&&(qn=(Be&536870912)===0||Ke?ba():536870912);var e=Un.current;return e!==null&&(e.flags|=32),qn}function Mn(e,t,n){(e===ot&&(Qe===2||Qe===9)||e.cancelPendingCommit!==null)&&(to(e,0),gi(e,Be,qn,!1)),$n(e,n),((Ge&2)===0||e!==ot)&&(e===ot&&((Ge&2)===0&&(ra|=n),Ct===4&&gi(e,Be,qn,!1)),cr(e))}function Qy(e,t,n){if((Ge&6)!==0)throw Error(s(327));var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||_r(e,t),l=a?Yx(e,t):lh(e,t,!0),c=a;do{if(l===0){Za&&!a&&gi(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!Bx(n)){l=lh(e,t,!1),c=!1;continue}if(l===2){if(c=t,e.errorRecoveryDisabledLanes&c)var g=0;else g=e.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){t=g;e:{var _=e;l=ms;var L=_.current.memoizedState.isDehydrated;if(L&&(to(_,g).flags|=256),g=lh(_,g,!1),g!==2){if(Qf&&!L){_.errorRecoveryDisabledLanes|=c,ra|=c,l=4;break e}c=gn,gn=l,c!==null&&(gn===null?gn=c:gn.push.apply(gn,c))}l=g}if(c=!1,l!==2)continue}}if(l===1){to(e,0),gi(e,t,0,!0);break}e:{switch(a=e,c=l,c){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:gi(a,t,qn,!fi);break e;case 2:gn=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(l=nh+300-Xt(),10<l)){if(gi(a,t,qn,!fi),ht(a,0,!0)!==0)break e;a.timeoutHandle=Ab(eb.bind(null,a,n,gn,Bl,th,t,qn,ra,Ga,fi,c,2,-0,0),l);break e}eb(a,n,gn,Bl,th,t,qn,ra,Ga,fi,c,0,-0,0)}}break}while(!0);cr(e)}function eb(e,t,n,a,l,c,g,_,L,Q,se,fe,ne,re){if(e.timeoutHandle=-1,fe=t.subtreeFlags,(fe&8192||(fe&16785408)===16785408)&&(Ss={stylesheets:null,count:0,unsuspend:ES},Yy(t),fe=AS(),fe!==null)){e.cancelPendingCommit=fe(sb.bind(null,e,t,c,n,a,l,g,_,L,se,1,ne,re)),gi(e,c,g,!Q);return}sb(e,t,c,n,a,l,g,_,L)}function Bx(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var l=n[a],c=l.getSnapshot;l=l.value;try{if(!_n(c(),l))return!1}catch(g){return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gi(e,t,n,a){t&=~eh,t&=~ra,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var l=t;0<l;){var c=31-Nt(l),g=1<<c;a[c]=-1,l&=~g}n!==0&&xa(e,n,t)}function $l(){return(Ge&6)===0?(vs(0),!1):!0}function oh(){if(je!==null){if(Qe===0)var e=je.return;else e=je,Pr=Zi=null,Sf(e),Ya=null,ss=0,e=je;for(;e!==null;)Ry(e.alternate,e),e=e.return;je=null}}function to(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,uS(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),oh(),ot=e,je=n=Ar(e.current,null),Be=t,Qe=0,Cn=null,fi=!1,Za=_r(e,t),Qf=!1,Ga=qn=eh=ra=hi=Ct=0,gn=ms=null,th=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var l=31-Nt(a),c=1<<l;t|=e[l],a&=~c}return qr=t,pl(),n}function tb(e,t){De=null,k.H=Rl,t===Go||t===Sl?(t=yv(),Qe=3):t===mv?(t=yv(),Qe=4):Qe=t===by?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Cn=t,je===null&&(Ct=1,zl(e,Ln(t,e.current)))}function nb(){var e=k.H;return k.H=Rl,e===null?Rl:e}function rb(){var e=k.A;return k.A=Vx,e}function sh(){Ct=4,fi||(Be&4194048)!==Be&&Un.current!==null||(Za=!0),(hi&134217727)===0&&(ra&134217727)===0||ot===null||gi(ot,Be,qn,!1)}function lh(e,t,n){var a=Ge;Ge|=2;var l=nb(),c=rb();(ot!==e||Be!==t)&&(Bl=null,to(e,t)),t=!1;var g=Ct;e:do try{if(Qe!==0&&je!==null){var _=je,L=Cn;switch(Qe){case 8:oh(),g=6;break e;case 3:case 2:case 9:case 6:Un.current===null&&(t=!0);var Q=Qe;if(Qe=0,Cn=null,no(e,_,L,Q),n&&Za){g=0;break e}break;default:Q=Qe,Qe=0,Cn=null,no(e,_,L,Q)}}$x(),g=Ct;break}catch(se){tb(e,se)}while(!0);return t&&e.shellSuspendCounter++,Pr=Zi=null,Ge=a,k.H=l,k.A=c,je===null&&(ot=null,Be=0,pl()),g}function $x(){for(;je!==null;)ib(je)}function Yx(e,t){var n=Ge;Ge|=2;var a=nb(),l=rb();ot!==e||Be!==t?(Bl=null,Hl=Xt()+500,to(e,t)):Za=_r(e,t);e:do try{if(Qe!==0&&je!==null){t=je;var c=Cn;t:switch(Qe){case 1:Qe=0,Cn=null,no(e,t,c,1);break;case 2:case 9:if(gv(c)){Qe=0,Cn=null,ab(t);break}t=function(){Qe!==2&&Qe!==9||ot!==e||(Qe=7),cr(e)},c.then(t,t);break e;case 3:Qe=7;break e;case 4:Qe=5;break e;case 7:gv(c)?(Qe=0,Cn=null,ab(t)):(Qe=0,Cn=null,no(e,t,c,7));break;case 5:var g=null;switch(je.tag){case 26:g=je.memoizedState;case 5:case 27:var _=je;if(!g||qb(g)){Qe=0,Cn=null;var L=_.sibling;if(L!==null)je=L;else{var Q=_.return;Q!==null?(je=Q,Yl(Q)):je=null}break t}}Qe=0,Cn=null,no(e,t,c,5);break;case 6:Qe=0,Cn=null,no(e,t,c,6);break;case 8:oh(),Ct=6;break e;default:throw Error(s(462))}}Xx();break}catch(se){tb(e,se)}while(!0);return Pr=Zi=null,k.H=a,k.A=l,Ge=n,je!==null?0:(ot=null,Be=0,pl(),Ct)}function Xx(){for(;je!==null&&!Pe();)ib(je)}function ib(e){var t=Py(e.alternate,e,qr);e.memoizedProps=e.pendingProps,t===null?Yl(e):je=t}function ab(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Cy(n,t,t.pendingProps,t.type,void 0,Be);break;case 11:t=Cy(n,t,t.pendingProps,t.type.render,t.ref,Be);break;case 5:Sf(t);default:Ry(n,t),t=je=ov(t,qr),t=Py(n,t,qr)}e.memoizedProps=e.pendingProps,t===null?Yl(e):je=t}function no(e,t,n,a){Pr=Zi=null,Sf(t),Ya=null,ss=0;var l=t.return;try{if(Dx(e,l,t,n,Be)){Ct=1,zl(e,Ln(n,e.current)),je=null;return}}catch(c){if(l!==null)throw je=l,c;Ct=1,zl(e,Ln(n,e.current)),je=null;return}t.flags&32768?(Ke||a===1?e=!0:Za||(Be&536870912)!==0?e=!1:(fi=e=!0,(a===2||a===9||a===3||a===6)&&(a=Un.current,a!==null&&a.tag===13&&(a.flags|=16384))),ob(t,e)):Yl(t)}function Yl(e){var t=e;do{if((t.flags&32768)!==0){ob(t,fi);return}e=t.return;var n=Ux(t.alternate,t,qr);if(n!==null){je=n;return}if(t=t.sibling,t!==null){je=t;return}je=t=e}while(t!==null);Ct===0&&(Ct=5)}function ob(e,t){do{var n=qx(e.alternate,e);if(n!==null){n.flags&=32767,je=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){je=e;return}je=e=n}while(e!==null);Ct=6,je=null}function sb(e,t,n,a,l,c,g,_,L){e.cancelPendingCommit=null;do Xl();while(Zt!==0);if((Ge&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(c=t.lanes|t.childLanes,c|=Zd,_a(e,n,c,g,_,L),e===ot&&(je=ot=null,Be=0),Qa=t,mi=e,eo=n,rh=c,ih=l,Zy=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Gx(ft,function(){return fb(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=k.T,k.T=null,l=K.p,K.p=2,g=Ge,Ge|=4;try{jx(e,t,n)}finally{Ge=g,K.p=l,k.T=a}}Zt=1,lb(),ub(),cb()}}function lb(){if(Zt===1){Zt=0;var e=mi,t=Qa,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=k.T,k.T=null;var a=K.p;K.p=2;var l=Ge;Ge|=4;try{Hy(t,e);var c=wh,g=Kg(e.containerInfo),_=c.focusedElem,L=c.selectionRange;if(g!==_&&_&&_.ownerDocument&&Jg(_.ownerDocument.documentElement,_)){if(L!==null&&$d(_)){var Q=L.start,se=L.end;if(se===void 0&&(se=Q),"selectionStart"in _)_.selectionStart=Q,_.selectionEnd=Math.min(se,_.value.length);else{var fe=_.ownerDocument||document,ne=fe&&fe.defaultView||window;if(ne.getSelection){var re=ne.getSelection(),Fe=_.textContent.length,Ae=Math.min(L.start,Fe),rt=L.end===void 0?Ae:Math.min(L.end,Fe);!re.extend&&Ae>rt&&(g=rt,rt=Ae,Ae=g);var J=Xg(_,Ae),H=Xg(_,rt);if(J&&H&&(re.rangeCount!==1||re.anchorNode!==J.node||re.anchorOffset!==J.offset||re.focusNode!==H.node||re.focusOffset!==H.offset)){var Z=fe.createRange();Z.setStart(J.node,J.offset),re.removeAllRanges(),Ae>rt?(re.addRange(Z),re.extend(H.node,H.offset)):(Z.setEnd(H.node,H.offset),re.addRange(Z))}}}}for(fe=[],re=_;re=re.parentNode;)re.nodeType===1&&fe.push({element:re,left:re.scrollLeft,top:re.scrollTop});for(typeof _.focus=="function"&&_.focus(),_=0;_<fe.length;_++){var ue=fe[_];ue.element.scrollLeft=ue.left,ue.element.scrollTop=ue.top}}ou=!!bh,wh=bh=null}finally{Ge=l,K.p=a,k.T=n}}e.current=t,Zt=2}}function ub(){if(Zt===2){Zt=0;var e=mi,t=Qa,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=k.T,k.T=null;var a=K.p;K.p=2;var l=Ge;Ge|=4;try{qy(e,t.alternate,t)}finally{Ge=l,K.p=a,k.T=n}}Zt=3}}function cb(){if(Zt===4||Zt===3){Zt=0,ve();var e=mi,t=Qa,n=eo,a=Zy;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Zt=5:(Zt=0,Qa=mi=null,db(e,e.pendingLanes));var l=e.pendingLanes;if(l===0&&(pi=null),Ui(n),t=t.stateNode,Xe&&typeof Xe.onCommitFiberRoot=="function")try{Xe.onCommitFiberRoot(wr,t,void 0,(t.current.flags&128)===128)}catch(L){}if(a!==null){t=k.T,l=K.p,K.p=2,k.T=null;try{for(var c=e.onRecoverableError,g=0;g<a.length;g++){var _=a[g];c(_.value,{componentStack:_.stack})}}finally{k.T=t,K.p=l}}(eo&3)!==0&&Xl(),cr(e),l=e.pendingLanes,(n&4194090)!==0&&(l&42)!==0?e===ah?gs++:(gs=0,ah=e):gs=0,vs(0)}}function db(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ko(t)))}function Xl(e){return lb(),ub(),cb(),fb()}function fb(){if(Zt!==5)return!1;var e=mi,t=rh;rh=0;var n=Ui(eo),a=k.T,l=K.p;try{K.p=32>n?32:n,k.T=null,n=ih,ih=null;var c=mi,g=eo;if(Zt=0,Qa=mi=null,eo=0,(Ge&6)!==0)throw Error(s(331));var _=Ge;if(Ge|=4,Jy(c.current),$y(c,c.current,g,n),Ge=_,vs(0,!1),Xe&&typeof Xe.onPostCommitFiberRoot=="function")try{Xe.onPostCommitFiberRoot(wr,c)}catch(L){}return!0}finally{K.p=l,k.T=a,db(e,t)}}function hb(e,t,n){t=Ln(n,t),t=Df(e.stateNode,t,2),e=ai(e,t,2),e!==null&&($n(e,2),cr(e))}function it(e,t,n){if(e.tag===3)hb(e,e,n);else for(;t!==null;){if(t.tag===3){hb(t,e,n);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(pi===null||!pi.has(a))){e=Ln(n,e),n=vy(2),a=ai(t,n,2),a!==null&&(yy(n,a,t,e),$n(a,2),cr(a));break}}t=t.return}}function uh(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new Hx;var l=new Set;a.set(t,l)}else l=a.get(t),l===void 0&&(l=new Set,a.set(t,l));l.has(n)||(Qf=!0,l.add(n),e=Jx.bind(null,e,t,n),t.then(e,e))}function Jx(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ot===e&&(Be&n)===n&&(Ct===4||Ct===3&&(Be&62914560)===Be&&300>Xt()-nh?(Ge&2)===0&&to(e,0):eh|=n,Ga===Be&&(Ga=0)),cr(e)}function pb(e,t){t===0&&(t=wa()),e=Da(e,t),e!==null&&($n(e,t),cr(e))}function Kx(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),pb(e,n)}function Zx(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(t),pb(e,n)}function Gx(e,t){return tr(e,t)}var Jl=null,ro=null,ch=!1,Kl=!1,dh=!1,ia=0;function cr(e){e!==ro&&e.next===null&&(ro===null?Jl=ro=e:ro=ro.next=e),Kl=!0,ch||(ch=!0,eS())}function vs(e,t){if(!dh&&Kl){dh=!0;do for(var n=!1,a=Jl;a!==null;){if(e!==0){var l=a.pendingLanes;if(l===0)var c=0;else{var g=a.suspendedLanes,_=a.pingedLanes;c=(1<<31-Nt(42|e)+1)-1,c&=l&~(g&~_),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,yb(a,c))}else c=Be,c=ht(a,a===ot?c:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(c&3)===0||_r(a,c)||(n=!0,yb(a,c));a=a.next}while(n);dh=!1}}function Qx(){mb()}function mb(){Kl=ch=!1;var e=0;ia!==0&&(lS()&&(e=ia),ia=0);for(var t=Xt(),n=null,a=Jl;a!==null;){var l=a.next,c=gb(a,t);c===0?(a.next=null,n===null?Jl=l:n.next=l,l===null&&(ro=n)):(n=a,(e!==0||(c&3)!==0)&&(Kl=!0)),a=l}vs(e)}function gb(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,l=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var g=31-Nt(c),_=1<<g,L=l[g];L===-1?((_&n)===0||(_&a)!==0)&&(l[g]=Ro(_,t)):L<=t&&(e.expiredLanes|=_),c&=~_}if(t=ot,n=Be,n=ht(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,n===0||e===t&&(Qe===2||Qe===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&et(a),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||_r(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(a!==null&&et(a),Ui(n)){case 2:case 8:n=Ue;break;case 32:n=ft;break;case 268435456:n=ya;break;default:n=ft}return a=vb.bind(null,e),n=tr(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&a!==null&&et(a),e.callbackPriority=2,e.callbackNode=null,2}function vb(e,t){if(Zt!==0&&Zt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Xl()&&e.callbackNode!==n)return null;var a=Be;return a=ht(e,e===ot?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Qy(e,a,t),gb(e,Xt()),e.callbackNode!=null&&e.callbackNode===n?vb.bind(null,e):null)}function yb(e,t){if(Xl())return null;Qy(e,t,!0)}function eS(){cS(function(){(Ge&6)!==0?tr(dn,Qx):mb()})}function fh(){return ia===0&&(ia=ba()),ia}function bb(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Gr(""+e)}function wb(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function tS(e,t,n,a,l){if(t==="submit"&&n&&n.stateNode===l){var c=bb((l[_t]||null).action),g=a.submitter;g&&(t=(t=g[_t]||null)?bb(t.formAction):g.getAttribute("formAction"),t!==null&&(c=t,g=null));var _=new dl("action","action",null,a,l);e.push({event:_,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ia!==0){var L=g?wb(l,g):new FormData(l);Of(n,{pending:!0,data:L,method:l.method,action:c},null,L)}}else typeof c=="function"&&(_.preventDefault(),L=g?wb(l,g):new FormData(l),Of(n,{pending:!0,data:L,method:l.method,action:c},c,L))},currentTarget:l}]})}}for(var hh=0;hh<Kd.length;hh++){var ph=Kd[hh],nS=ph.toLowerCase(),rS=ph[0].toUpperCase()+ph.slice(1);Yn(nS,"on"+rS)}Yn(Qg,"onAnimationEnd"),Yn(ev,"onAnimationIteration"),Yn(tv,"onAnimationStart"),Yn("dblclick","onDoubleClick"),Yn("focusin","onFocus"),Yn("focusout","onBlur"),Yn(wx,"onTransitionRun"),Yn(_x,"onTransitionStart"),Yn(xx,"onTransitionCancel"),Yn(nv,"onTransitionEnd"),Ft("onMouseEnter",["mouseout","mouseover"]),Ft("onMouseLeave",["mouseout","mouseover"]),Ft("onPointerEnter",["pointerout","pointerover"]),Ft("onPointerLeave",["pointerout","pointerover"]),wn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),wn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),wn("onBeforeInput",["compositionend","keypress","textInput","paste"]),wn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),wn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),wn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ys="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),iS=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ys));function _b(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n],l=a.event;a=a.listeners;e:{var c=void 0;if(t)for(var g=a.length-1;0<=g;g--){var _=a[g],L=_.instance,Q=_.currentTarget;if(_=_.listener,L!==c&&l.isPropagationStopped())break e;c=_,l.currentTarget=Q;try{c(l)}catch(se){Dl(se)}l.currentTarget=null,c=L}else for(g=0;g<a.length;g++){if(_=a[g],L=_.instance,Q=_.currentTarget,_=_.listener,L!==c&&l.isPropagationStopped())break e;c=_,l.currentTarget=Q;try{c(l)}catch(se){Dl(se)}l.currentTarget=null,c=L}}}}function We(e,t){var n=t[qi];n===void 0&&(n=t[qi]=new Set);var a=e+"__bubble";n.has(a)||(xb(t,e,2,!1),n.add(a))}function mh(e,t,n){var a=0;t&&(a|=4),xb(n,e,a,t)}var Zl="_reactListening"+Math.random().toString(36).slice(2);function gh(e){if(!e[Zl]){e[Zl]=!0,Ca.forEach(function(n){n!=="selectionchange"&&(iS.has(n)||mh(n,!1,e),mh(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Zl]||(t[Zl]=!0,mh("selectionchange",!1,t))}}function xb(e,t,n,a){switch($b(t)){case 2:var l=PS;break;case 8:l=OS;break;default:l=Fh}n=l.bind(null,t,n,e),l=void 0,!Dd||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),a?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function vh(e,t,n,a,l){var c=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var g=a.tag;if(g===3||g===4){var _=a.stateNode.containerInfo;if(_===l)break;if(g===4)for(g=a.return;g!==null;){var L=g.tag;if((L===3||L===4)&&g.stateNode.containerInfo===l)return;g=g.return}for(;_!==null;){if(g=rr(_),g===null)return;if(L=g.tag,L===5||L===6||L===26||L===27){a=c=g;continue e}_=_.parentNode}}a=a.return}Tg(function(){var Q=c,se=Oa(n),fe=[];e:{var ne=rv.get(e);if(ne!==void 0){var re=dl,Fe=e;switch(e){case"keypress":if(ul(n)===0)break e;case"keydown":case"keyup":re=G6;break;case"focusin":Fe="focus",re=jd;break;case"focusout":Fe="blur",re=jd;break;case"beforeblur":case"afterblur":re=jd;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":re=Og;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":re=q6;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":re=tx;break;case Qg:case ev:case tv:re=V6;break;case nv:re=rx;break;case"scroll":case"scrollend":re=z6;break;case"wheel":re=ax;break;case"copy":case"cut":case"paste":re=B6;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":re=Ig;break;case"toggle":case"beforetoggle":re=sx}var Ae=(t&4)!==0,rt=!Ae&&(e==="scroll"||e==="scrollend"),J=Ae?ne!==null?ne+"Capture":null:ne;Ae=[];for(var H=Q,Z;H!==null;){var ue=H;if(Z=ue.stateNode,ue=ue.tag,ue!==5&&ue!==26&&ue!==27||Z===null||J===null||(ue=Do(H,J),ue!=null&&Ae.push(bs(H,ue,Z))),rt)break;H=H.return}0<Ae.length&&(ne=new re(ne,Fe,null,n,se),fe.push({event:ne,listeners:Ae}))}}if((t&7)===0){e:{if(ne=e==="mouseover"||e==="pointerover",re=e==="mouseout"||e==="pointerout",ne&&n!==Qr&&(Fe=n.relatedTarget||n.fromElement)&&(rr(Fe)||Fe[nr]))break e;if((re||ne)&&(ne=se.window===se?se:(ne=se.ownerDocument)?ne.defaultView||ne.parentWindow:window,re?(Fe=n.relatedTarget||n.toElement,re=Q,Fe=Fe?rr(Fe):null,Fe!==null&&(rt=d(Fe),Ae=Fe.tag,Fe!==rt||Ae!==5&&Ae!==27&&Ae!==6)&&(Fe=null)):(re=null,Fe=Q),re!==Fe)){if(Ae=Og,ue="onMouseLeave",J="onMouseEnter",H="mouse",(e==="pointerout"||e==="pointerover")&&(Ae=Ig,ue="onPointerLeave",J="onPointerEnter",H="pointer"),rt=re==null?ne:Sr(re),Z=Fe==null?ne:Sr(Fe),ne=new Ae(ue,H+"leave",re,n,se),ne.target=rt,ne.relatedTarget=Z,ue=null,rr(se)===Q&&(Ae=new Ae(J,H+"enter",Fe,n,se),Ae.target=Z,Ae.relatedTarget=rt,ue=Ae),rt=ue,re&&Fe)t:{for(Ae=re,J=Fe,H=0,Z=Ae;Z;Z=io(Z))H++;for(Z=0,ue=J;ue;ue=io(ue))Z++;for(;0<H-Z;)Ae=io(Ae),H--;for(;0<Z-H;)J=io(J),Z--;for(;H--;){if(Ae===J||J!==null&&Ae===J.alternate)break t;Ae=io(Ae),J=io(J)}Ae=null}else Ae=null;re!==null&&Sb(fe,ne,re,Ae,!1),Fe!==null&&rt!==null&&Sb(fe,rt,Fe,Ae,!0)}}e:{if(ne=Q?Sr(Q):window,re=ne.nodeName&&ne.nodeName.toLowerCase(),re==="select"||re==="input"&&ne.type==="file")var Se=Wg;else if(qg(ne))if(Vg)Se=vx;else{Se=mx;var qe=px}else re=ne.nodeName,!re||re.toLowerCase()!=="input"||ne.type!=="checkbox"&&ne.type!=="radio"?Q&&Vt(Q.elementType)&&(Se=Wg):Se=gx;if(Se&&(Se=Se(e,Q))){jg(fe,Se,n,se);break e}qe&&qe(e,ne,Q),e==="focusout"&&Q&&ne.type==="number"&&Q.memoizedProps.value!=null&&P(ne,"number",ne.value)}switch(qe=Q?Sr(Q):window,e){case"focusin":(qg(qe)||qe.contentEditable==="true")&&(Ia=qe,Yd=Q,Bo=null);break;case"focusout":Bo=Yd=Ia=null;break;case"mousedown":Xd=!0;break;case"contextmenu":case"mouseup":case"dragend":Xd=!1,Zg(fe,n,se);break;case"selectionchange":if(bx)break;case"keydown":case"keyup":Zg(fe,n,se)}var Ce;if(Vd)e:{switch(e){case"compositionstart":var Te="onCompositionStart";break e;case"compositionend":Te="onCompositionEnd";break e;case"compositionupdate":Te="onCompositionUpdate";break e}Te=void 0}else Ra?zg(e,n)&&(Te="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Te="onCompositionStart");Te&&(Lg&&n.locale!=="ko"&&(Ra||Te!=="onCompositionStart"?Te==="onCompositionEnd"&&Ra&&(Ce=Fg()):(ti=se,zd="value"in ti?ti.value:ti.textContent,Ra=!0)),qe=Gl(Q,Te),0<qe.length&&(Te=new Rg(Te,e,null,n,se),fe.push({event:Te,listeners:qe}),Ce?Te.data=Ce:(Ce=Ug(n),Ce!==null&&(Te.data=Ce)))),(Ce=ux?cx(e,n):dx(e,n))&&(Te=Gl(Q,"onBeforeInput"),0<Te.length&&(qe=new Rg("onBeforeInput","beforeinput",null,n,se),fe.push({event:qe,listeners:Te}),qe.data=Ce)),tS(fe,e,Q,n,se)}_b(fe,t)})}function bs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gl(e,t){for(var n=t+"Capture",a=[];e!==null;){var l=e,c=l.stateNode;if(l=l.tag,l!==5&&l!==26&&l!==27||c===null||(l=Do(e,n),l!=null&&a.unshift(bs(e,l,c)),l=Do(e,t),l!=null&&a.push(bs(e,l,c))),e.tag===3)return a;e=e.return}return[]}function io(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Sb(e,t,n,a,l){for(var c=t._reactName,g=[];n!==null&&n!==a;){var _=n,L=_.alternate,Q=_.stateNode;if(_=_.tag,L!==null&&L===a)break;_!==5&&_!==26&&_!==27||Q===null||(L=Q,l?(Q=Do(n,c),Q!=null&&g.unshift(bs(n,Q,L))):l||(Q=Do(n,c),Q!=null&&g.push(bs(n,Q,L)))),n=n.return}g.length!==0&&e.push({event:t,listeners:g})}var aS=/\r\n?/g,oS=/\u0000|\uFFFD/g;function kb(e){return(typeof e=="string"?e:""+e).replace(aS,"\n").replace(oS,"")}function Cb(e,t){return t=kb(t),kb(e)===t}function Ql(){}function nt(e,t,n,a,l,c){switch(n){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Re(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Re(e,""+a);break;case"className":Jr(e,"class",a);break;case"tabIndex":Jr(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Jr(e,n,a);break;case"style":ut(e,a,c);break;case"data":if(t!=="object"){Jr(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=Gr(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&nt(e,t,"name",l.name,l,null),nt(e,t,"formEncType",l.formEncType,l,null),nt(e,t,"formMethod",l.formMethod,l,null),nt(e,t,"formTarget",l.formTarget,l,null)):(nt(e,t,"encType",l.encType,l,null),nt(e,t,"method",l.method,l,null),nt(e,t,"target",l.target,l,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=Gr(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(e.onclick=Ql);break;case"onScroll":a!=null&&We("scroll",e);break;case"onScrollEnd":a!=null&&We("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(n=a.__html,n!=null){if(l.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}n=Gr(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":We("beforetoggle",e),We("toggle",e),Xr(e,"popover",a);break;case"xlinkActuate":fn(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":fn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":fn(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":fn(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":fn(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":fn(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":fn(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":fn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":fn(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Xr(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=St.get(n)||n,Xr(e,n,a))}}function yh(e,t,n,a,l,c){switch(n){case"style":ut(e,a,c);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(n=a.__html,n!=null){if(l.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"children":typeof a=="string"?Re(e,a):(typeof a=="number"||typeof a=="bigint")&&Re(e,""+a);break;case"onScroll":a!=null&&We("scroll",e);break;case"onScrollEnd":a!=null&&We("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Ql);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ea.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(l=n.endsWith("Capture"),t=n.slice(2,l?n.length-7:void 0),c=e[_t]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,l),typeof a=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,l);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):Xr(e,n,a)}}}function Gt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":We("error",e),We("load",e);var a=!1,l=!1,c;for(c in n)if(n.hasOwnProperty(c)){var g=n[c];if(g!=null)switch(c){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:nt(e,t,c,g,n,null)}}l&&nt(e,t,"srcSet",n.srcSet,n,null),a&&nt(e,t,"src",n.src,n,null);return;case"input":We("invalid",e);var _=c=g=l=null,L=null,Q=null;for(a in n)if(n.hasOwnProperty(a)){var se=n[a];if(se!=null)switch(a){case"name":l=se;break;case"type":g=se;break;case"checked":L=se;break;case"defaultChecked":Q=se;break;case"value":c=se;break;case"defaultValue":_=se;break;case"children":case"dangerouslySetInnerHTML":if(se!=null)throw Error(s(137,t));break;default:nt(e,t,a,se,n,null)}}C(e,c,_,L,Q,g,l,!1),A(e);return;case"select":We("invalid",e),a=g=c=null;for(l in n)if(n.hasOwnProperty(l)&&(_=n[l],_!=null))switch(l){case"value":c=_;break;case"defaultValue":g=_;break;case"multiple":a=_;default:nt(e,t,l,_,n,null)}t=c,n=g,e.multiple=!!a,t!=null?oe(e,!!a,t,!1):n!=null&&oe(e,!!a,n,!0);return;case"textarea":We("invalid",e),c=l=a=null;for(g in n)if(n.hasOwnProperty(g)&&(_=n[g],_!=null))switch(g){case"value":a=_;break;case"defaultValue":l=_;break;case"children":c=_;break;case"dangerouslySetInnerHTML":if(_!=null)throw Error(s(91));break;default:nt(e,t,g,_,n,null)}Ee(e,a,l,c),A(e);return;case"option":for(L in n)if(n.hasOwnProperty(L)&&(a=n[L],a!=null))switch(L){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:nt(e,t,L,a,n,null)}return;case"dialog":We("beforetoggle",e),We("toggle",e),We("cancel",e),We("close",e);break;case"iframe":case"object":We("load",e);break;case"video":case"audio":for(a=0;a<ys.length;a++)We(ys[a],e);break;case"image":We("error",e),We("load",e);break;case"details":We("toggle",e);break;case"embed":case"source":case"link":We("error",e),We("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(Q in n)if(n.hasOwnProperty(Q)&&(a=n[Q],a!=null))switch(Q){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:nt(e,t,Q,a,n,null)}return;default:if(Vt(t)){for(se in n)n.hasOwnProperty(se)&&(a=n[se],a!==void 0&&yh(e,t,se,a,n,void 0));return}}for(_ in n)n.hasOwnProperty(_)&&(a=n[_],a!=null&&nt(e,t,_,a,n,null))}function sS(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,c=null,g=null,_=null,L=null,Q=null,se=null;for(re in n){var fe=n[re];if(n.hasOwnProperty(re)&&fe!=null)switch(re){case"checked":break;case"value":break;case"defaultValue":L=fe;default:a.hasOwnProperty(re)||nt(e,t,re,null,a,fe)}}for(var ne in a){var re=a[ne];if(fe=n[ne],a.hasOwnProperty(ne)&&(re!=null||fe!=null))switch(ne){case"type":c=re;break;case"name":l=re;break;case"checked":Q=re;break;case"defaultChecked":se=re;break;case"value":g=re;break;case"defaultValue":_=re;break;case"children":case"dangerouslySetInnerHTML":if(re!=null)throw Error(s(137,t));break;default:re!==fe&&nt(e,t,ne,re,a,fe)}}Ne(e,g,_,L,Q,se,c,l);return;case"select":re=g=_=ne=null;for(c in n)if(L=n[c],n.hasOwnProperty(c)&&L!=null)switch(c){case"value":break;case"multiple":re=L;default:a.hasOwnProperty(c)||nt(e,t,c,null,a,L)}for(l in a)if(c=a[l],L=n[l],a.hasOwnProperty(l)&&(c!=null||L!=null))switch(l){case"value":ne=c;break;case"defaultValue":_=c;break;case"multiple":g=c;default:c!==L&&nt(e,t,l,c,a,L)}t=_,n=g,a=re,ne!=null?oe(e,!!n,ne,!1):!!a!=!!n&&(t!=null?oe(e,!!n,t,!0):oe(e,!!n,n?[]:"",!1));return;case"textarea":re=ne=null;for(_ in n)if(l=n[_],n.hasOwnProperty(_)&&l!=null&&!a.hasOwnProperty(_))switch(_){case"value":break;case"children":break;default:nt(e,t,_,null,a,l)}for(g in a)if(l=a[g],c=n[g],a.hasOwnProperty(g)&&(l!=null||c!=null))switch(g){case"value":ne=l;break;case"defaultValue":re=l;break;case"children":break;case"dangerouslySetInnerHTML":if(l!=null)throw Error(s(91));break;default:l!==c&&nt(e,t,g,l,a,c)}pe(e,ne,re);return;case"option":for(var Fe in n)if(ne=n[Fe],n.hasOwnProperty(Fe)&&ne!=null&&!a.hasOwnProperty(Fe))switch(Fe){case"selected":e.selected=!1;break;default:nt(e,t,Fe,null,a,ne)}for(L in a)if(ne=a[L],re=n[L],a.hasOwnProperty(L)&&ne!==re&&(ne!=null||re!=null))switch(L){case"selected":e.selected=ne&&typeof ne!="function"&&typeof ne!="symbol";break;default:nt(e,t,L,ne,a,re)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var Ae in n)ne=n[Ae],n.hasOwnProperty(Ae)&&ne!=null&&!a.hasOwnProperty(Ae)&&nt(e,t,Ae,null,a,ne);for(Q in a)if(ne=a[Q],re=n[Q],a.hasOwnProperty(Q)&&ne!==re&&(ne!=null||re!=null))switch(Q){case"children":case"dangerouslySetInnerHTML":if(ne!=null)throw Error(s(137,t));break;default:nt(e,t,Q,ne,a,re)}return;default:if(Vt(t)){for(var rt in n)ne=n[rt],n.hasOwnProperty(rt)&&ne!==void 0&&!a.hasOwnProperty(rt)&&yh(e,t,rt,void 0,a,ne);for(se in a)ne=a[se],re=n[se],!a.hasOwnProperty(se)||ne===re||ne===void 0&&re===void 0||yh(e,t,se,ne,a,re);return}}for(var J in n)ne=n[J],n.hasOwnProperty(J)&&ne!=null&&!a.hasOwnProperty(J)&&nt(e,t,J,null,a,ne);for(fe in a)ne=a[fe],re=n[fe],!a.hasOwnProperty(fe)||ne===re||ne==null&&re==null||nt(e,t,fe,ne,a,re)}var bh=null,wh=null;function eu(e){return e.nodeType===9?e:e.ownerDocument}function Eb(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Mb(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function _h(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var xh=null;function lS(){var e=window.event;return e&&e.type==="popstate"?e===xh?!1:(xh=e,!0):(xh=null,!1)}var Ab=typeof setTimeout=="function"?setTimeout:void 0,uS=typeof clearTimeout=="function"?clearTimeout:void 0,Tb=typeof Promise=="function"?Promise:void 0,cS=typeof queueMicrotask=="function"?queueMicrotask:typeof Tb<"u"?function(e){return Tb.resolve(null).then(e).catch(dS)}:Ab;function dS(e){setTimeout(function(){throw e})}function vi(e){return e==="head"}function Fb(e,t){var n=t,a=0,l=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<a&&8>a){n=a;var g=e.ownerDocument;if(n&1&&ws(g.documentElement),n&2&&ws(g.body),n&4)for(n=g.head,ws(n),g=n.firstChild;g;){var _=g.nextSibling,L=g.nodeName;g[xr]||L==="SCRIPT"||L==="STYLE"||L==="LINK"&&g.rel.toLowerCase()==="stylesheet"||n.removeChild(g),g=_}}if(l===0){e.removeChild(c),As(t);return}l--}else n==="$"||n==="$?"||n==="$!"?l++:a=n.charCodeAt(0)-48;else a=0;n=c}while(n);As(t)}function Sh(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Sh(n),ji(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function fS(e,t,n,a){for(;e.nodeType===1;){var l=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[xr])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence")||c!==l.rel||e.getAttribute("href")!==(l.href==null||l.href===""?null:l.href)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin)||e.getAttribute("title")!==(l.title==null?null:l.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(l.src==null?null:l.src)||e.getAttribute("type")!==(l.type==null?null:l.type)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=l.name==null?null:""+l.name;if(l.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Jn(e.nextSibling),e===null)break}return null}function hS(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Jn(e.nextSibling),e===null))return null;return e}function kh(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function pS(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Jn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Ch=null;function Pb(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Ob(e,t,n){switch(t=eu(n),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function ws(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);ji(e)}var jn=new Map,Rb=new Set;function tu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var jr=K.d;K.d={f:mS,r:gS,D:vS,C:yS,L:bS,m:wS,X:xS,S:_S,M:SS};function mS(){var e=jr.f(),t=$l();return e||t}function gS(e){var t=be(e);t!==null&&t.tag===5&&t.type==="form"?Qv(t):jr.r(e)}var ao=typeof document>"u"?null:document;function Ib(e,t,n){var a=ao;if(a&&typeof t=="string"&&t){var l=ke(t);l='link[rel="'+e+'"][href="'+l+'"]',typeof n=="string"&&(l+='[crossorigin="'+n+'"]'),Rb.has(l)||(Rb.add(l),e={rel:e,crossOrigin:n,href:t},a.querySelector(l)===null&&(t=a.createElement("link"),Gt(t,"link",e),xt(t),a.head.appendChild(t)))}}function vS(e){jr.D(e),Ib("dns-prefetch",e,null)}function yS(e,t){jr.C(e,t),Ib("preconnect",e,t)}function bS(e,t,n){jr.L(e,t,n);var a=ao;if(a&&e&&t){var l='link[rel="preload"][as="'+ke(t)+'"]';t==="image"&&n&&n.imageSrcSet?(l+='[imagesrcset="'+ke(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(l+='[imagesizes="'+ke(n.imageSizes)+'"]')):l+='[href="'+ke(e)+'"]';var c=l;switch(t){case"style":c=oo(e);break;case"script":c=so(e)}jn.has(c)||(e=y({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),jn.set(c,e),a.querySelector(l)!==null||t==="style"&&a.querySelector(_s(c))||t==="script"&&a.querySelector(xs(c))||(t=a.createElement("link"),Gt(t,"link",e),xt(t),a.head.appendChild(t)))}}function wS(e,t){jr.m(e,t);var n=ao;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",l='link[rel="modulepreload"][as="'+ke(a)+'"][href="'+ke(e)+'"]',c=l;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=so(e)}if(!jn.has(c)&&(e=y({rel:"modulepreload",href:e},t),jn.set(c,e),n.querySelector(l)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(xs(c)))return}a=n.createElement("link"),Gt(a,"link",e),xt(a),n.head.appendChild(a)}}}function _S(e,t,n){jr.S(e,t,n);var a=ao;if(a&&e){var l=ir(a).hoistableStyles,c=oo(e);t=t||"default";var g=l.get(c);if(!g){var _={loading:0,preload:null};if(g=a.querySelector(_s(c)))_.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},n),(n=jn.get(c))&&Eh(e,n);var L=g=a.createElement("link");xt(L),Gt(L,"link",e),L._p=new Promise(function(Q,se){L.onload=Q,L.onerror=se}),L.addEventListener("load",function(){_.loading|=1}),L.addEventListener("error",function(){_.loading|=2}),_.loading|=4,nu(g,t,a)}g={type:"stylesheet",instance:g,count:1,state:_},l.set(c,g)}}}function xS(e,t){jr.X(e,t);var n=ao;if(n&&e){var a=ir(n).hoistableScripts,l=so(e),c=a.get(l);c||(c=n.querySelector(xs(l)),c||(e=y({src:e,async:!0},t),(t=jn.get(l))&&Mh(e,t),c=n.createElement("script"),xt(c),Gt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(l,c))}}function SS(e,t){jr.M(e,t);var n=ao;if(n&&e){var a=ir(n).hoistableScripts,l=so(e),c=a.get(l);c||(c=n.querySelector(xs(l)),c||(e=y({src:e,async:!0,type:"module"},t),(t=jn.get(l))&&Mh(e,t),c=n.createElement("script"),xt(c),Gt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(l,c))}}function Lb(e,t,n,a){var l=(l=_e.current)?tu(l):null;if(!l)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=oo(n.href),n=ir(l).hoistableStyles,a=n.get(t),a||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=oo(n.href);var c=ir(l).hoistableStyles,g=c.get(e);if(g||(l=l.ownerDocument||l,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,g),(c=l.querySelector(_s(e)))&&!c._p&&(g.instance=c,g.state.loading=5),jn.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},jn.set(e,n),c||kS(l,e,n,g.state))),t&&a===null)throw Error(s(528,""));return g}if(t&&a!==null)throw Error(s(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=so(n),n=ir(l).hoistableScripts,a=n.get(t),a||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function oo(e){return'href="'+ke(e)+'"'}function _s(e){return'link[rel="stylesheet"]['+e+"]"}function Nb(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function kS(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Gt(t,"link",n),xt(t),e.head.appendChild(t))}function so(e){return'[src="'+ke(e)+'"]'}function xs(e){return"script[async]"+e}function Db(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+ke(n.href)+'"]');if(a)return t.instance=a,xt(a),a;var l=y({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),xt(a),Gt(a,"style",l),nu(a,n.precedence,e),t.instance=a;case"stylesheet":l=oo(n.href);var c=e.querySelector(_s(l));if(c)return t.state.loading|=4,t.instance=c,xt(c),c;a=Nb(n),(l=jn.get(l))&&Eh(a,l),c=(e.ownerDocument||e).createElement("link"),xt(c);var g=c;return g._p=new Promise(function(_,L){g.onload=_,g.onerror=L}),Gt(c,"link",a),t.state.loading|=4,nu(c,n.precedence,e),t.instance=c;case"script":return c=so(n.src),(l=e.querySelector(xs(c)))?(t.instance=l,xt(l),l):(a=n,(l=jn.get(c))&&(a=y({},n),Mh(a,l)),e=e.ownerDocument||e,l=e.createElement("script"),xt(l),Gt(l,"link",a),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,nu(a,n.precedence,e));return t.instance}function nu(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),l=a.length?a[a.length-1]:null,c=l,g=0;g<a.length;g++){var _=a[g];if(_.dataset.precedence===t)c=_;else if(c!==l)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Eh(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Mh(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var ru=null;function zb(e,t,n){if(ru===null){var a=new Map,l=ru=new Map;l.set(n,a)}else l=ru,a=l.get(n),a||(a=new Map,l.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),l=0;l<n.length;l++){var c=n[l];if(!(c[xr]||c[wt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var g=c.getAttribute(t)||"";g=e+g;var _=a.get(g);_?_.push(c):a.set(g,[c])}}return a}function Ub(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function CS(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function qb(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ss=null;function ES(){}function MS(e,t,n){if(Ss===null)throw Error(s(475));var a=Ss;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var l=oo(n.href),c=e.querySelector(_s(l));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=iu.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=c,xt(c);return}c=e.ownerDocument||e,n=Nb(n),(l=jn.get(l))&&Eh(n,l),c=c.createElement("link"),xt(c);var g=c;g._p=new Promise(function(_,L){g.onload=_,g.onerror=L}),Gt(c,"link",n),t.instance=c}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=iu.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function AS(){if(Ss===null)throw Error(s(475));var e=Ss;return e.stylesheets&&e.count===0&&Ah(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Ah(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function iu(){if(this.count--,this.count===0){if(this.stylesheets)Ah(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var au=null;function Ah(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,au=new Map,t.forEach(TS,e),au=null,iu.call(e))}function TS(e,t){if(!(t.state.loading&4)){var n=au.get(e);if(n)var a=n.get(null);else{n=new Map,au.set(e,n);for(var l=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<l.length;c++){var g=l[c];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(n.set(g.dataset.precedence,g),a=g)}a&&n.set(null,a)}l=t.instance,g=l.getAttribute("data-precedence"),c=n.get(g)||a,c===a&&n.set(null,l),n.set(g,l),this.count++,a=iu.bind(this),l.addEventListener("load",a),l.addEventListener("error",a),c?c.parentNode.insertBefore(l,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(l,e.firstChild)),t.state.loading|=4}}var ks={$$typeof:$,Provider:null,Consumer:null,_currentValue:G,_currentValue2:G,_threadCount:0};function FS(e,t,n,a,l,c,g,_){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Bn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Bn(0),this.hiddenUpdates=Bn(null),this.identifierPrefix=a,this.onUncaughtError=l,this.onCaughtError=c,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=_,this.incompleteTransitions=new Map}function jb(e,t,n,a,l,c,g,_,L,Q,se,fe){return e=new FS(e,t,n,g,_,L,Q,fe),t=1,c===!0&&(t|=24),c=xn(3,null,null,t),e.current=c,c.stateNode=e,t=uf(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:a,isDehydrated:n,cache:t},hf(c),e}function Wb(e){return e?(e=za,e):za}function Vb(e,t,n,a,l,c){l=Wb(l),a.context===null?a.context=l:a.pendingContext=l,a=ii(t),a.payload={element:n},c=c===void 0?null:c,c!==null&&(a.callback=c),n=ai(e,a,t),n!==null&&(Mn(n,e,t),es(n,e,t))}function Hb(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Th(e,t){Hb(e,t),(e=e.alternate)&&Hb(e,t)}function Bb(e){if(e.tag===13){var t=Da(e,67108864);t!==null&&Mn(t,e,67108864),Th(e,67108864)}}var ou=!0;function PS(e,t,n,a){var l=k.T;k.T=null;var c=K.p;try{K.p=2,Fh(e,t,n,a)}finally{K.p=c,k.T=l}}function OS(e,t,n,a){var l=k.T;k.T=null;var c=K.p;try{K.p=8,Fh(e,t,n,a)}finally{K.p=c,k.T=l}}function Fh(e,t,n,a){if(ou){var l=Ph(a);if(l===null)vh(e,t,a,su,n),Yb(e,a);else if(IS(l,e,t,n,a))a.stopPropagation();else if(Yb(e,a),t&4&&-1<RS.indexOf(e)){for(;l!==null;){var c=be(l);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var g=Dt(c.pendingLanes);if(g!==0){var _=c;for(_.pendingLanes|=2,_.entangledLanes|=2;g;){var L=1<<31-Nt(g);_.entanglements[1]|=L,g&=~L}cr(c),(Ge&6)===0&&(Hl=Xt()+500,vs(0))}}break;case 13:_=Da(c,2),_!==null&&Mn(_,c,2),$l(),Th(c,2)}if(c=Ph(a),c===null&&vh(e,t,a,su,n),c===l)break;l=c}l!==null&&a.stopPropagation()}else vh(e,t,a,null,n)}}function Ph(e){return e=Oa(e),Oh(e)}var su=null;function Oh(e){if(su=null,e=rr(e),e!==null){var t=d(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=f(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return su=e,null}function $b(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ri()){case dn:return 2;case Ue:return 8;case ft:case Ii:return 32;case ya:return 268435456;default:return 32}default:return 32}}var Rh=!1,yi=null,bi=null,wi=null,Cs=new Map,Es=new Map,_i=[],RS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Yb(e,t){switch(e){case"focusin":case"focusout":yi=null;break;case"dragenter":case"dragleave":bi=null;break;case"mouseover":case"mouseout":wi=null;break;case"pointerover":case"pointerout":Cs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Es.delete(t.pointerId)}}function Ms(e,t,n,a,l,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:c,targetContainers:[l]},t!==null&&(t=be(t),t!==null&&Bb(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function IS(e,t,n,a,l){switch(t){case"focusin":return yi=Ms(yi,e,t,n,a,l),!0;case"dragenter":return bi=Ms(bi,e,t,n,a,l),!0;case"mouseover":return wi=Ms(wi,e,t,n,a,l),!0;case"pointerover":var c=l.pointerId;return Cs.set(c,Ms(Cs.get(c)||null,e,t,n,a,l)),!0;case"gotpointercapture":return c=l.pointerId,Es.set(c,Ms(Es.get(c)||null,e,t,n,a,l)),!0}return!1}function Xb(e){var t=rr(e.target);if(t!==null){var n=d(t);if(n!==null){if(t=n.tag,t===13){if(t=f(n),t!==null){e.blockedOn=t,Io(e.priority,function(){if(n.tag===13){var a=En();a=zi(a);var l=Da(n,a);l!==null&&Mn(l,n,a),Th(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function lu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ph(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n);Qr=a,n.target.dispatchEvent(a),Qr=null}else return t=be(n),t!==null&&Bb(t),e.blockedOn=n,!1;t.shift()}return!0}function Jb(e,t,n){lu(e)&&n.delete(t)}function LS(){Rh=!1,yi!==null&&lu(yi)&&(yi=null),bi!==null&&lu(bi)&&(bi=null),wi!==null&&lu(wi)&&(wi=null),Cs.forEach(Jb),Es.forEach(Jb)}function uu(e,t){e.blockedOn===t&&(e.blockedOn=null,Rh||(Rh=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,LS)))}var cu=null;function Kb(e){cu!==e&&(cu=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){cu===e&&(cu=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],l=e[t+2];if(typeof a!="function"){if(Oh(a||n)===null)continue;break}var c=be(n);c!==null&&(e.splice(t,3),t-=3,Of(c,{pending:!0,data:l,method:n.method,action:a},a,l))}}))}function As(e){function t(L){return uu(L,e)}yi!==null&&uu(yi,e),bi!==null&&uu(bi,e),wi!==null&&uu(wi,e),Cs.forEach(t),Es.forEach(t);for(var n=0;n<_i.length;n++){var a=_i[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<_i.length&&(n=_i[0],n.blockedOn===null);)Xb(n),n.blockedOn===null&&_i.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var l=n[a],c=n[a+1],g=l[_t]||null;if(typeof c=="function")g||Kb(n);else if(g){var _=null;if(c&&c.hasAttribute("formAction")){if(l=c,g=c[_t]||null)_=g.formAction;else if(Oh(l)!==null)continue}else _=g.action;typeof _=="function"?n[a+1]=_:(n.splice(a,3),a-=3),Kb(n)}}}function Ih(e){this._internalRoot=e}du.prototype.render=Ih.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var n=t.current,a=En();Vb(n,a,e,t,null,null)},du.prototype.unmount=Ih.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Vb(e.current,2,null,e,null,null),$l(),t[nr]=null}};function du(e){this._internalRoot=e}du.prototype.unstable_scheduleHydration=function(e){if(e){var t=ka();e={blockedOn:null,target:e,priority:t};for(var n=0;n<_i.length&&t!==0&&t<_i[n].priority;n++);_i.splice(n,0,e),n===0&&Xb(e)}};var Zb=i.version;if(Zb!=="19.1.0")throw Error(s(527,Zb,"19.1.0"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=p(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var NS={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:k,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var fu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!fu.isDisabled&&fu.supportsFiber)try{wr=fu.inject(NS),Xe=fu}catch(e){}}return uo.createRoot=function(e,t){if(!u(e))throw Error(s(299));var n=!1,a="",l=hy,c=py,g=my,_=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(l=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(g=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(_=t.unstable_transitionCallbacks)),t=jb(e,1,!1,null,null,n,a,l,c,g,_,null),e[nr]=t.current,gh(e),new Ih(t)},uo.hydrateRoot=function(e,t,n){if(!u(e))throw Error(s(299));var a=!1,l="",c=hy,g=py,_=my,L=null,Q=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(g=n.onCaughtError),n.onRecoverableError!==void 0&&(_=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(L=n.unstable_transitionCallbacks),n.formState!==void 0&&(Q=n.formState)),t=jb(e,1,!0,t,n!=null?n:null,a,l,c,g,_,L,Q),t.context=Wb(null),n=t.current,a=En(),a=zi(a),l=ii(a),l.callback=null,ai(n,l,a),n=a,t.current.lanes=n,$n(t,n),cr(t),e[nr]=t.current,gh(e),new du(t)},uo.version="19.1.0",uo}var fp;function c2(){if(fp)return mu.exports;fp=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(i){console.error(i)}}return r(),mu.exports=u2(),mu.exports}var d2=c2();const f2="modulepreload",h2=function(r){return"/"+r},hp={},co=function(r,i,o){let s=Promise.resolve();if(i&&i.length>0){let d=function(p){return Promise.all(p.map(m=>Promise.resolve(m).then(y=>({status:"fulfilled",value:y}),y=>({status:"rejected",reason:y}))))};document.getElementsByTagName("link");const f=document.querySelector("meta[property=csp-nonce]"),h=(f==null?void 0:f.nonce)||(f==null?void 0:f.getAttribute("nonce"));s=d(i.map(p=>{if(p=h2(p),p in hp)return;hp[p]=!0;const m=p.endsWith(".css"),y=m?'[rel="stylesheet"]':"";if(document.querySelector('link[href="'.concat(p,'"]').concat(y)))return;const v=document.createElement("link");if(v.rel=m?"stylesheet":f2,m||(v.as="script"),v.crossOrigin="",v.href=p,h&&v.setAttribute("nonce",h),document.head.appendChild(v),m)return new Promise((x,E)=>{v.addEventListener("load",x),v.addEventListener("error",()=>E(new Error("Unable to preload CSS for ".concat(p))))})}))}function u(d){const f=new Event("vite:preloadError",{cancelable:!0});if(f.payload=d,window.dispatchEvent(f),!f.defaultPrevented)throw d}return s.then(d=>{for(const f of d||[])f.status==="rejected"&&u(f.reason);return r().catch(u)})},pp=r=>{let i;const o=new Set,s=(h,p)=>{const m=typeof h=="function"?h(i):h;if(!Object.is(m,i)){const y=i;i=(p!=null?p:typeof m!="object"||m===null)?m:Object.assign({},i,m),o.forEach(v=>v(i,y))}},u=()=>i,d={setState:s,getState:u,getInitialState:()=>f,subscribe:h=>(o.add(h),()=>o.delete(h))},f=i=r(s,u,d);return d},p2=r=>r?pp(r):pp,m2=r=>r;function g2(r,i=m2){const o=ip.useSyncExternalStore(r.subscribe,()=>i(r.getState()),()=>i(r.getInitialState()));return ip.useDebugValue(o),o}let vu,mp,yu;vu=r=>{const i=p2(r),o=s=>g2(i,s);return Object.assign(o,i),o},mp=r=>r?vu(r):vu,yu={frequency:null,demodulation:null,bandwidth:null,audioRange:null,waterfallRange:null,zoom:null},Kh=mp(r=>({frequency:"14074.00",demodulation:"USB",bandwidth:"2.8",link:"",showBookmarkDialog:!1,showBandsMenu:!1,audioGateVisible:!0,showTutorial:!1,currentVFO:"A",vfoA:{...yu},vfoB:{...yu},vfoSwitchNotification:"",NREnabled:!1,NBEnabled:!1,ANEnabled:!1,CTCSSSupressEnabled:!1,mute:!1,setFrequency:i=>r({frequency:i}),setDemodulation:i=>r({demodulation:i}),setBandwidth:i=>r({bandwidth:i}),setLink:i=>r({link:i}),setShowBookmarkDialog:i=>r({showBookmarkDialog:i}),setShowBandsMenu:i=>r({showBandsMenu:i}),setAudioGateVisible:i=>r({audioGateVisible:i}),setShowTutorial:i=>r({showTutorial:i}),setCurrentVFO:i=>r({currentVFO:i}),setVFOSwitchNotification:i=>r({vfoSwitchNotification:i}),updateVFO:(i,o)=>r(s=>({[i==="A"?"vfoA":"vfoB"]:{...s[i==="A"?"vfoA":"vfoB"],...o}})),setAudioPanelState:i=>r(o=>({...o,...i}))}));let $e;function gp(r){$e=r}const dr=new Array(128).fill(void 0);dr.push(void 0,null,!0,!1);function Wn(r){return dr[r]}let fo=dr.length;function v2(r){r<132||(dr[r]=fo,fo=r)}function Ps(r){const i=Wn(r);return v2(r),i}function An(r){fo===dr.length&&dr.push(dr.length+1);const i=fo;return fo=dr[i],dr[i]=r,i}const y2=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let vp=new y2("utf-8",{ignoreBOM:!0,fatal:!0});vp.decode();let Os=null;function ho(){return(Os===null||Os.byteLength===0)&&(Os=new Uint8Array($e.memory.buffer)),Os}function bu(r,i){return r=r>>>0,vp.decode(ho().subarray(r,r+i))}let aa=0;function yp(r,i){const o=i(r.length*1,1)>>>0;return ho().set(r,o/1),aa=r.length,o}function b2(r){return r==null}let Rs=null;function Is(){return(Rs===null||Rs.byteLength===0)&&(Rs=new Int32Array($e.memory.buffer)),Rs}let Ls=null;function w2(){return(Ls===null||Ls.byteLength===0)&&(Ls=new Uint32Array($e.memory.buffer)),Ls}function _2(r,i){r=r>>>0;const o=w2().subarray(r/4,r/4+i),s=[];for(let u=0;u<o.length;u++)s.push(Ps(o[u]));return s}function x2(){$e.greet()}function S2(){$e.main()}function bp(r,i,o){const s=$e.firdes_kaiser_lowpass(r,i,o);return Ps(s)}const k2=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let Ns=new k2("utf-8");const C2=typeof Ns.encodeInto=="function"?function(r,i){return Ns.encodeInto(r,i)}:function(r,i){const o=Ns.encode(r);return i.set(o),{read:r.length,written:o.length}};function E2(r,i,o){if(o===void 0){const h=Ns.encode(r),p=i(h.length,1)>>>0;return ho().subarray(p,p+h.length).set(h),aa=h.length,p}let s=r.length,u=i(s,1)>>>0;const d=ho();let f=0;for(;f<s;f++){const h=r.charCodeAt(f);if(h>127)break;d[u+f]=h}if(f!==s){f!==0&&(r=r.slice(f)),u=o(u,s,s=f+r.length*3,1)>>>0;const h=ho().subarray(u+f,u+s),p=C2(r,h);f+=p.written,u=o(u,s,f,1)>>>0}return aa=f,u}function M2(r,i){try{return r.apply(this,i)}catch(o){$e.__wbindgen_exn_store(An(o))}}const wu=Object.freeze({Flac:0,0:"Flac",Opus:1,1:"Opus"}),A2=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(r=>$e.__wbg_audio_free(r>>>0));class _u{__destroy_into_raw(){const i=this.__wbg_ptr;return this.__wbg_ptr=0,A2.unregister(this),i}free(){const i=this.__destroy_into_raw();$e.__wbg_audio_free(i)}constructor(i,o,s,u){const d=$e.audio_new(i,o,s,u);return this.__wbg_ptr=d>>>0,this}decode(i){const o=yp(i,$e.__wbindgen_malloc),s=aa,u=$e.audio_decode(this.__wbg_ptr,o,s);return Ps(u)}set_nr(i){$e.audio_set_nr(this.__wbg_ptr,i)}set_nb(i){$e.audio_set_nb(this.__wbg_ptr,i)}set_an(i){$e.audio_set_an(this.__wbg_ptr,i)}set_decoded_callback(i){$e.audio_set_decoded_callback(this.__wbg_ptr,b2(i)?0:An(i))}}const wp=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(r=>$e.__wbg_foxenflacdecoder_free(r>>>0));class Ds{static __wrap(i){i=i>>>0;const o=Object.create(Ds.prototype);return o.__wbg_ptr=i,wp.register(o,o.__wbg_ptr,o),o}__destroy_into_raw(){const i=this.__wbg_ptr;return this.__wbg_ptr=0,wp.unregister(this),i}free(){const i=this.__destroy_into_raw();$e.__wbg_foxenflacdecoder_free(i)}static new(){const i=$e.foxenflacdecoder_new();return Ds.__wrap(i)}}const T2=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(r=>$e.__wbg_zstdstreamdecoder_free(r>>>0));class _p{__destroy_into_raw(){const i=this.__wbg_ptr;return this.__wbg_ptr=0,T2.unregister(this),i}free(){const i=this.__destroy_into_raw();$e.__wbg_zstdstreamdecoder_free(i)}constructor(){const i=$e.zstdstreamdecoder_new();return this.__wbg_ptr=i>>>0,this}clear(){$e.zstdstreamdecoder_clear(this.__wbg_ptr)}decode(i){try{const d=$e.__wbindgen_add_to_stack_pointer(-16),f=yp(i,$e.__wbindgen_malloc),h=aa;$e.zstdstreamdecoder_decode(d,this.__wbg_ptr,f,h);var o=Is()[d/4+0],s=Is()[d/4+1],u=_2(o,s).slice();return $e.__wbindgen_free(o,s*4,4),u}finally{$e.__wbindgen_add_to_stack_pointer(16)}}}Jh=function(r){Ps(r)},Xh=function(r){const i=Wn(r);return An(i)},Yh=function(r,i){alert(bu(r,i))},$h=function(){const r=new Error;return An(r)},Bh=function(r,i){const o=Wn(i).stack,s=E2(o,$e.__wbindgen_malloc,$e.__wbindgen_realloc),u=aa;Is()[r/4+1]=u,Is()[r/4+0]=s},Hh=function(r,i){let o,s;try{o=r,s=i,console.error(bu(r,i))}finally{$e.__wbindgen_free(o,s,1)}},Vh=function(){return M2(function(r,i,o){const s=Wn(r).call(Wn(i),Wn(o));return An(s)},arguments)},Wh=function(r){const i=Wn(r).buffer;return An(i)},jh=function(r,i,o){const s=new Uint8Array(Wn(r),i>>>0,o>>>0);return An(s)},qh=function(r){const i=new Uint8Array(Wn(r));return An(i)},Uh=function(r,i,o){const s=new Float32Array(Wn(r),i>>>0,o>>>0);return An(s)},zh=function(r){const i=new Float32Array(Wn(r));return An(i)},Dh=function(r){const i=new Float32Array(r>>>0);return An(i)},Nh=function(r,i){throw new Error(bu(r,i))},Lh=function(){const r=$e.memory;return An(r)},Gb=Object.freeze(Object.defineProperty({__proto__:null,Audio:_u,AudioCodec:wu,FoxenFlacDecoder:Ds,ZstdStreamDecoder:_p,__wbg_alert_379870a8a3d45260:Yh,__wbg_buffer_12d079cc21e14bdb:Wh,__wbg_call_b3ca7c6051f9bec1:Vh,__wbg_error_f851667af71bcfc6:Hh,__wbg_new_63b92bc8671ed464:qh,__wbg_new_9efabd6b6d2ce46d:zh,__wbg_new_abda76e883ba8a5f:$h,__wbg_newwithbyteoffsetandlength_4a659d079a1650e0:Uh,__wbg_newwithbyteoffsetandlength_aa4a17c33a06e5cb:jh,__wbg_newwithlength_1e8b839a06de01c5:Dh,__wbg_set_wasm:gp,__wbg_stack_658279fe44541cf6:Bh,__wbindgen_memory:Lh,__wbindgen_object_clone_ref:Xh,__wbindgen_object_drop_ref:Jh,__wbindgen_throw:Nh,firdes_kaiser_lowpass:bp,greet:x2,main:S2},Symbol.toStringTag,{value:"Module"}));var xp={},Sp;function F2(){return Sp||(Sp=1,function(r){function i(N){var I=[],z=[],F=0,b=N;function S(T){for(I.length!==0&&I[0]<=F-b&&(I.shift(),z.shift());z.length!==0&&z[z.length-1]>T;)I.pop(),z.pop();return I.push(F),z.push(T),F++,z[0]}return S.setWindowSize=function(T){b=T},S.reset=function(){I.splice(0,I.length),z.splice(0,z.length),F=0},S}function o(N){var I=[],z=[],F=0,b=N;function S(T){for(I.length!==0&&I[0]<=F-b&&(I.shift(),z.shift());z.length!==0&&z[z.length-1]<T;)I.pop(),z.pop();return I.push(F),z.push(T),F++,z[0]}return S.setWindowSize=function(T){b=T},S.reset=function(){I.splice(0,I.length),z.splice(0,z.length),F=0},S}function s(N){var I=[],z=N,F=0,b;function S(T){if(I.length>=z&&(F-=I.shift()),T||T===0)I.push(T),F+=T;else return b;return b=I.length==0?0:F/I.length}return S.setWindowSize=function(T){z=T},S.reset=function(){F=0,I.splice(0,I.length)},S}function u(N){var I=[],z=[],F=N;function b(S,T){for(;I.length!==0&&I[0]<=T-F;)I.shift(),z.shift();for(;z.length!==0&&z[z.length-1]>S;)I.pop(),z.pop();return I.push(T),z.push(S),z[0]}return b.setWindowSize=function(S){F=S},b.reset=function(){I.splice(0,I.length),z.splice(0,z.length)},b}function d(N){var I=[],z=[],F=N;function b(S,T){for(;I.length!==0&&I[0]<=T-F;)I.shift(),z.shift();for(;z.length!==0&&z[z.length-1]<S;)I.pop(),z.pop();return I.push(T),z.push(S),z[0]}return b.setWindowSize=function(S){F=S},b.reset=function(){I.splice(0,I.length),z.splice(0,z.length)},b}function f(N){var I=[],z=[],F=N,b=0;function S(T,O){for(;I.length!==0&&I[0]<=O-F;)I.shift(),b-=z.shift();if(T||T===0)I.push(O),z.push(T),b+=T;else return prev;return prev=z.length==0?0:b/z.length}return S.setWindowSize=function(T){F=T},S.reset=function(){I.splice(0,I.length),z.splice(0,z.length),b=0},S}function h(N,I){for(var z=0,F=N.length;z<F;){var b=z+F>>>1;N[b]<I?z=b+1:F=b}return z}function p(N){var I=[],z=N,F=[],b=h,S,T=null,O=0,U=0,k=0,K=0,G=null;function X(w){if(I.length>=z){var D=I.shift(),te=D,de=te*te,me=de*te,ge=me*te;O-=te,U-=de,k-=me,K-=ge;var _e=b(F,D);F[_e]==D&&F.splice(_e,1)}if(w||w===0){I.push(w),F.splice(b(F,w),0,w),T=null,G=null;var te=w,de=te*te,me=de*te,ge=me*te;O+=te,U+=de,k+=me,K+=ge}else return S;if(F.length==0)return S;if(F.length&1)return S=F[F.length-1>>>1];var ze=(F.length>>>1)-1;return S=(F[ze]+F[ze+1])/2}return X.setWindowSize=function(w){z=w},X.reset=function(){I.splice(0,I.length),F.splice(0,F.length),T=null,O=0,U=0,k=0,K=0,G=null},X.avg=function(){return O/I.length},X.sum=function(){return O},X.min=function(){return F[0]},X.q1=function(){if(F.length==1)return F[0];if(F.length==2)return F[0]*.75+F[1]*.25;if(F.length==3)return F[0]*.5+F[1]*.5;if((F.length-1)%4==0){var w=F.length-1>>2;return F[w-1]*.25+F[w]*.75}if((F.length-3)%4==0){var w=F.length-3>>2;return F[w]*.75+F[w+1]*.25}},X.moments_avg=function(){return G===null&&(G=X.moments()),G.average},X.variance=function(){return G===null&&(G=X.moments()),G.variance},X.standardDeviation=function(){return G===null&&(G=X.moments()),G.standardDeviation},X.skew=function(){return G===null&&(G=X.moments()),G.skew},X.kurtosis=function(){return G===null&&(G=X.moments()),G.kurtosis},X.exkurtosis=function(){return G===null&&(G=X.moments()),G.exkurtosis},X.moments=function(){var w={};if(this.c>0){var D=I.length,te=O/D,de=U/D,me=k/D,ge=K/D,_e=te,ze=U/D-te;m3=me-3*de*te+2*te*te*te,m4=ge-3*me*te+6*de*te*te- -3*te*te*te*te,w.average=_e,w.variance=ze,w.standardDeviation=Math.pow(w.variance,.5),D>2&&(w.skew=Math.pow(D*(D-1),.5)/(D-2)*m3/Math.pow(ze,1.5)),ze>0&&(w.exkurtosis=m4/ze/ze-3,w.kurtosis=m4/ze/ze)}return w},X.median=function(){return S},X.q3=function(){if(F.length==1)return F[0];if(F.length==2)return F[0]*.25+F[1]*.75;if(F.length==3)return F[1]*.5+F[2]*.5;if((F.length-1)%4==0){var w=(F.length-1>>2)*3;return F[w]*.75+F[w+1]*.25}if((F.length-3)%4==0){var w=(F.length-3>>2)*3;return F[w+1]*.25+F[w+2]*.75}},X.max=function(){return F[F.length-1]},X.center=function(){return(F[0]+F[F.length-1])/2},X.medianskew=function(){return F[F.length-1]-S-(S-F[0])},X.medianskew_bowleys_coef=function(){return(F[F.length-1]-S)*(S-F[0])/(F[F.length-1]-F[0])},X.mediankurt=function(){var w=Math.round((F.length-1)*.9),D=Math.round((F.length-1)*.1);return(F[F.length-1]-S)*(S-F[0])/(F[w]-F[D])},X.pabovecenter=function(){return T===null&&(T=b(F,(F[0]+F[F.length-1])/2)),(F.length-1-T)/(F.length-1)},X.pbelowcenter=function(){return T===null&&(T=b(F,(F[0]+F[F.length-1])/2)),T/(F.length-1)},X}function m(N){var I=[],z=[],F=N,b=[],S=h,T,O=null,U=0,k=0,K=0,G=0,X=null;function w(D,te){for(;z.length!==0&&z[0]<=te-F;){z.shift();var de=I.shift(),me=de,ge=me*me,_e=ge*me,ze=_e*me;U-=me,k-=ge,K-=_e,G-=ze;var he=S(b,de);b[he]==de&&b.splice(he,1)}if(D||D===0){z.push(te),I.push(D),b.splice(S(b,D),0,D),O=null,X=null;var me=D,ge=me*me,_e=ge*me,ze=_e*me;U+=me,k+=ge,K+=_e,G+=ze}else return T;if(b.length==0)return T;if(b.length&1)return T=b[b.length-1>>>1];var Ye=(b.length>>>1)-1;return T=(b[Ye]+b[Ye+1])/2}return w.setWindowSize=function(D){F=D},w.reset=function(){I.splice(0,I.length),z.splice(0,z.length),b.splice(0,b.length),O=null,U=0,k=0,K=0,G=0,X=null},w.avg=function(){return U/I.length},w.sum=function(){return U},w.min=function(){return b[0]},w.q1=function(){if(b.length==1)return b[0];if(b.length==2)return b[0]*.75+b[1]*.25;if(b.length==3)return b[0]*.5+b[1]*.5;if((b.length-1)%4==0){var D=b.length-1>>2;return b[D-1]*.25+b[D]*.75}if((b.length-3)%4==0){var D=b.length-3>>2;return b[D]*.75+b[D+1]*.25}},w.moments_avg=function(){return X===null&&(X=w.moments()),X.average},w.variance=function(){return X===null&&(X=w.moments()),X.variance},w.standardDeviation=function(){return X===null&&(X=w.moments()),X.standardDeviation},w.skew=function(){return X===null&&(X=w.moments()),X.skew},w.kurtosis=function(){return X===null&&(X=w.moments()),X.kurtosis},w.exkurtosis=function(){return X===null&&(X=w.moments()),X.exkurtosis},w.moments=function(){var D={};if(this.c>0){var te=I.length,de=U/te,me=k/te,ge=K/te,_e=G/te,ze=de,he=k/te-de;m3=ge-3*me*de+2*de*de*de,m4=_e-3*ge*de+6*me*de*de- -3*de*de*de*de,D.average=ze,D.variance=he,D.standardDeviation=Math.pow(D.variance,.5),te>2&&(D.skew=Math.pow(te*(te-1),.5)/(te-2)*m3/Math.pow(he,1.5)),he>0&&(D.exkurtosis=m4/he/he-3,D.kurtosis=m4/he/he)}return D},w.median=function(){return T},w.q3=function(){if(b.length==1)return b[0];if(b.length==2)return b[0]*.25+b[1]*.75;if(b.length==3)return b[1]*.5+b[2]*.5;if((b.length-1)%4==0){var D=(b.length-1>>2)*3;return b[D]*.75+b[D+1]*.25}if((b.length-3)%4==0){var D=(b.length-3>>2)*3;return b[D+1]*.25+b[D+2]*.75}},w.max=function(){return b[b.length-1]},w.center=function(){return(b[0]+b[b.length-1])/2},w.medianskew=function(){return b[b.length-1]-T-(T-b[0])},w.medianskew_bowleys_coef=function(){return(b[b.length-1]-T)*(T-b[0])/(b[b.length-1]-b[0])},w.mediankurt=function(){var D=Math.round((b.length-1)*.9),te=Math.round((b.length-1)*.1);return(b[b.length-1]-T)*(T-b[0])/(b[D]-b[te])},w.pabovecenter=function(){return O===null&&(O=S(b,(b[0]+b[b.length-1])/2)),(b.length-1-O)/(b.length-1)},w.pbelowcenter=function(){return O===null&&(O=S(b,(b[0]+b[b.length-1])/2)),O/(b.length-1)},w}function y(N,I){var z=[],F=[],b=N,S=0,T=!1,O=I;function U(k,K){for(;z.length!==0&&z[0]<=K-b&&z[0]!=K;)T=z.shift(),S-=F.shift();T===!1&&(T=K-O),z.push(K),F.push(k),S+=k;var G=K-T;return G==0&&(G=O),S/G}return U.setWindowSize=function(k){b=k},U.setUsualIndexSkipBetweenOccations=function(k){O=k},U.reset=function(){z.splice(0,z.length),F.splice(0,F.length),S=0,T=!1},U}function v(N,I){var z=[],F=N,b=I;function S(T){var O=b;return z.length==F&&(O=z.shift()),z.push(T),O}return S.setWindowSize=function(T){F=T},S.setUndefinedValue=function(T){b=I},S.reset=function(T){z.splice(0,z.length)},S}function x(N,I){var z=1,F=N+1,b=I;function S(T){return z==F?T:(z++,b)}return S.setWindowSize=function(T){F=T+1},S.setUndefinedValue=function(T){b=I},S.reset=function(){z=0},S}function E(N,I,z){var F=[],b=[],S=N,T=z;function O(U,k){if(F.push(k),b.push(U),F.length!==0&&F[0]<=k-S)for(F.shift(),T=b.shift();F.length!==0&&F[0]<=k-S;)F.shift(),b.shift();return T}return O.setWindowSize=function(U){S=U},O.setUsualIndexSkipBetweenOccations=function(U){},O.reset=function(){F.splice(0,F.length),b.splice(0,b.length),T=z},O}function M(N){var I=-1;function z(F){return F>0&&(I=0),I>=0&&I++,I>N&&(I=-1),I>0}return z.setWindowSize=function(F){},z.reset=function(){I=-1},z}function R(N){var I=N,z=!1,F=-1;function b(S,T){return S>0&&(z=T,F=0),F>=0&&F++,z!=!1&&z<=T-I&&(F=-1),F>0}return b.setWindowSize=function(S){I=S},b.reset=function(){z=!1,F=-1},b}function j(N){var I=N,z=new Map;function F(b,S=1){var T=parseFloat((Math.round(b*I)/I).toFixed(12));return z.has(T)?z.set(T,parseFloat((z.get(T)+S).toFixed(12))):z.set(T,S),z}return F.hist=z,F.reset=function(){z.clear()},F}function ie(N,I){var z=[],F=[],b=N,S=I,T=new Map;function O(U,k=1){if(z.length>=b){var K=z.shift(),G=F.shift(),X=parseFloat((T.get(K)-G).toFixed(12));X!==0?T.set(K,X):T.delete(K)}var w=parseFloat((Math.round(U*S)/S).toFixed(12));return z.push(w),F.push(k),T.has(w)?T.set(w,parseFloat((T.get(w)+k).toFixed(12))):T.set(w,k),T}return O.hist=T,O.setWindowSize=function(U){b=U},O.reset=function(){Sum=0,z.splice(0,z.length)},O}function B(N,I){var z=[],F=[],b=[],S=N,T=I,O=new Map;function U(k,K,G=1){if(!G&&G!==0||!k&&k!==0)return O;for(;z.length!==0&&z[0]<=K-S;){z.shift();var X=F.shift(),w=b.shift();if(!X&&X!==0&&console.log("nana1",{prevamount:w,prevpos:X}),!w&&w!==0&&console.log("nana2",{prevamount:w,prevpos:X}),O.has(X)){var D=parseFloat((O.get(X)-w).toFixed(12));!D&&D!==0&&console.log("nana3",{newamount:D,prevamount:w,prevpos:X}),D!==0?O.set(X,D):O.delete(X)}}var te=parseFloat((Math.round(k*T)/T).toFixed(12));if(F.push(te),b.push(G),z.push(K),O.has(te)){let de=parseFloat((O.get(te)+G).toFixed(12));O.set(te,de)}else O.set(te,G);return O}return U.hist=O,U.setWindowSize=function(k){S=k},U.reset=function(){z.splice(0,z.length),F.splice(0,F.length),Sum=0},U}var $=r;function ae(N,I,z,F,b){var S=[],T=function(et){return S.push(et),et},O=T($.RollingAvg(N)),U=T($.RollingAvg(N)),k=T($.RollingAvg(N)),K=T($.RollingMedian(N)),G=T($.RollingAvg(N)),X=T($.RollingAvg(N)),w=T($.RollingAvgIndex(z)),D=T($.RollingAvgIndex(z)),te=T($.RollingMedianIndex(z)),de=T($.RollingAvgIndex(z)),me=T($.RollingAvgIndex(z)),ge=T($.RollingSumPerIndex(z,F)),_e=T($.Delay(I)),ze=T($.DelayIndex(b,F)),he=T($.Delay(I)),Ye=T($.DelayIndex(b,F)),Ze=!1,dt=0,bt=0;function tr(et,Pe){var ve={};ve.median=K(et),ve.min=K.min(),ve.max=K.max(),ve.q1=K.q1(),ve.q3=K.q3(),ve.avg=K.avg(),ve.stdev=Math.sqrt(U(Math.pow(et-ve.avg,2))),ve.z=ve.stdev==0?0:(et-ve.avg)/ve.stdev,ve.zavg=k(ve.z),ve.mstdev=Math.sqrt(G(Math.pow(et-ve.median,2))),ve.mz=ve.mstdev==0?0:.6745*(et-ve.median)/ve.mstdev,ve.mzavg=X(ve.mz),ve.tmedian=te(et,Pe),ve.tmin=te.min(),ve.tmax=te.max(),ve.tavg=te.avg(),ve.tcenter=te.center(),ve.tq1=te.q1(),ve.tq3=te.q3(),ve.tsum=ge(et,Pe),ve.tstdev=Math.sqrt(D(Math.pow(et-ve.tavg,2),Pe)),ve.tz=ve.tstdev==0?0:(et-ve.tavg)/ve.tstdev,ve.tzavg=w(ve.tz,Pe),ve.tmstdev=Math.sqrt(me(Math.pow(et-ve.tmedian,2),Pe)),ve.tmz=ve.tmstdev==0?0:.6745*(et-ve.tmedian)/ve.tmstdev,ve.tmzavg=de(ve.tmz,Pe),Ze===!1&&(Ze=Pe-F);var Xt=Pe-Ze;return Ze=Pe,ve.avgtime=O(Xt),ve.count=dt,dt++,ve.tcount=bt,bt+=Xt,ve.value_delay=_e(et),ve.tvalue_delay=ze(et,Pe),ve.index_delay=he(Pe),ve.tindex_delay=Ye(Pe,Pe),ve}return tr.reset=function(){for(var et=0;et<S.length;et++)S[et].reset();Ze=!1,dt=0},tr}function V(N,I){var z=$.RollingMin(N),F=$.RollingMax(N),b=$.RollingAvg(N),S=$.Delay(I);function T(O){var U={};return U.min=z(O),U.max=F(O),U.avg=b(O),U.value_delay=S(O),U}return T.reset=function(){z.reset(),F.reset(),b.reset(),S.reset()},T}function Y(N){var I=$.RollingMin(N),z=$.RollingMax(N),F=$.RollingAvg(N);function b(S){var T={};return T.min=I(S),T.max=z(S),T.avg=F(S),T}return b.reset=function(){I.reset(),z.reset(),F.reset()},b}r.RollingMin=i,r.RollingMax=o,r.RollingAvg=s,r.RollingMedian=p,r.RollingMinIndex=u,r.RollingMaxIndex=d,r.RollingAvgIndex=f,r.RollingMedianIndex=m,r.RollingSumPerIndex=y,r.Delay=v,r.DelayIndex=E,r.AllStats=ae,r.SimpleStats=V,r.SimpleStatsNoDelay=Y,r.PositiveLately=M,r.PositiveLatelyIndex=R,r.Histogram=j,r.RollingHistogram=ie,r.RollingHistogramIndex=B,r.HideFirst=x}(xp)),xp}F2();let xu;try{xu=new TextDecoder}catch(r){}let xe,Si,le=0;const P2=105,O2=57342,R2=57343,kp=57337,Cp=6,oa={};let po=11281e4,fr=1681e4,Ve={},vt,zs,Us=0,mo=0,Rt,Tn,At=[],Su=[],un,nn,go,Ep={useRecords:!1,mapsAsObjects:!0},vo=!1,Mp=2;try{new Function("")}catch(r){Mp=1/0}let Ap=class Zh{constructor(i){if(i&&((i.keyMap||i._keyMap)&&!i.useRecords&&(i.useRecords=!1,i.mapsAsObjects=!0),i.useRecords===!1&&i.mapsAsObjects===void 0&&(i.mapsAsObjects=!0),i.getStructures&&(i.getShared=i.getStructures),i.getShared&&!i.structures&&((i.structures=[]).uninitialized=!0),i.keyMap)){this.mapKey=new Map;for(let[o,s]of Object.entries(i.keyMap))this.mapKey.set(s,o)}Object.assign(this,i)}decodeKey(i){return this.keyMap&&this.mapKey.get(i)||i}encodeKey(i){return this.keyMap&&this.keyMap.hasOwnProperty(i)?this.keyMap[i]:i}encodeKeys(i){if(!this._keyMap)return i;let o=new Map;for(let[s,u]of Object.entries(i))o.set(this._keyMap.hasOwnProperty(s)?this._keyMap[s]:s,u);return o}decodeKeys(i){if(!this._keyMap||i.constructor.name!="Map")return i;if(!this._mapKey){this._mapKey=new Map;for(let[s,u]of Object.entries(this._keyMap))this._mapKey.set(u,s)}let o={};return i.forEach((s,u)=>o[Fn(this._mapKey.has(u)?this._mapKey.get(u):u)]=s),o}mapDecode(i,o){let s=this.decode(i);if(this._keyMap)switch(s.constructor.name){case"Array":return s.map(u=>this.decodeKeys(u))}return s}decode(i,o){if(xe)return Ip(()=>(Au(),this?this.decode(i,o):Zh.prototype.decode.call(Ep,i,o)));Si=o>-1?o:i.length,le=0,mo=0,zs=null,Rt=null,xe=i;try{nn=i.dataView||(i.dataView=new DataView(i.buffer,i.byteOffset,i.byteLength))}catch(s){throw xe=null,i instanceof Uint8Array?s:new Error("Source must be a Uint8Array or Buffer but was a "+(i&&typeof i=="object"?i.constructor.name:typeof i))}if(this instanceof Zh){if(Ve=this,un=this.sharedValues&&(this.pack?new Array(this.maxPrivatePackedValues||16).concat(this.sharedValues):this.sharedValues),this.structures)return vt=this.structures,qs();(!vt||vt.length>0)&&(vt=[])}else Ve=Ep,(!vt||vt.length>0)&&(vt=[]),un=null;return qs()}decodeMultiple(i,o){let s,u=0;try{let d=i.length;vo=!0;let f=this?this.decode(i,d):Fu.decode(i,d);if(o){if(o(f)===!1)return;for(;le<d;)if(u=le,o(qs())===!1)return}else{for(s=[f];le<d;)u=le,s.push(qs());return s}}catch(d){throw d.lastPosition=u,d.values=s,d}finally{vo=!1,Au()}}};function qs(){try{let r=He();if(Rt){if(le>=Rt.postBundlePosition){let i=new Error("Unexpected bundle position");throw i.incomplete=!0,i}le=Rt.postBundlePosition,Rt=null}if(le==Si)vt=null,xe=null,Tn&&(Tn=null);else if(le>Si){let i=new Error("Unexpected end of CBOR data");throw i.incomplete=!0,i}else if(!vo)throw new Error("Data read, but end of buffer not reached");return r}catch(r){throw Au(),(r instanceof RangeError||r.message.startsWith("Unexpected end of buffer"))&&(r.incomplete=!0),r}}function He(){let r=xe[le++],i=r>>5;if(r=r&31,r>23)switch(r){case 24:r=xe[le++];break;case 25:if(i==7)return D2();r=nn.getUint16(le),le+=2;break;case 26:if(i==7){let o=nn.getFloat32(le);if(Ve.useFloat32>2){let s=Tu[(xe[le]&127)<<1|xe[le+1]>>7];return le+=4,(s*o+(o>0?.5:-.5)>>0)/s}return le+=4,o}r=nn.getUint32(le),le+=4;break;case 27:if(i==7){let o=nn.getFloat64(le);return le+=8,o}if(i>1){if(nn.getUint32(le)>0)throw new Error("JavaScript does not support arrays, maps, or strings with length over 4294967295");r=nn.getUint32(le+4)}else Ve.int64AsNumber?(r=nn.getUint32(le)*4294967296,r+=nn.getUint32(le+4)):r=nn.getBigUint64(le);le+=8;break;case 31:switch(i){case 2:case 3:throw new Error("Indefinite length not supported for byte or text strings");case 4:let o=[],s,u=0;for(;(s=He())!=oa;){if(u>=po)throw new Error("Array length exceeds ".concat(po));o[u++]=s}return i==4?o:i==3?o.join(""):Buffer.concat(o);case 5:let d;if(Ve.mapsAsObjects){let f={},h=0;if(Ve.keyMap)for(;(d=He())!=oa;){if(h++>=fr)throw new Error("Property count exceeds ".concat(fr));f[Fn(Ve.decodeKey(d))]=He()}else for(;(d=He())!=oa;){if(h++>=fr)throw new Error("Property count exceeds ".concat(fr));f[Fn(d)]=He()}return f}else{go&&(Ve.mapsAsObjects=!0,go=!1);let f=new Map;if(Ve.keyMap){let h=0;for(;(d=He())!=oa;){if(h++>=fr)throw new Error("Map size exceeds ".concat(fr));f.set(Ve.decodeKey(d),He())}}else{let h=0;for(;(d=He())!=oa;){if(h++>=fr)throw new Error("Map size exceeds ".concat(fr));f.set(d,He())}}return f}case 7:return oa;default:throw new Error("Invalid major type for indefinite length "+i)}default:throw new Error("Unknown token "+r)}switch(i){case 0:return r;case 1:return~r;case 2:return N2(r);case 3:if(mo>=le)return zs.slice(le-Us,(le+=r)-Us);if(mo==0&&Si<140&&r<32){let u=r<16?Fp(r):L2(r);if(u!=null)return u}return I2(r);case 4:if(r>=po)throw new Error("Array length exceeds ".concat(po));let o=new Array(r);for(let u=0;u<r;u++)o[u]=He();return o;case 5:if(r>=fr)throw new Error("Map size exceeds ".concat(po));if(Ve.mapsAsObjects){let u={};if(Ve.keyMap)for(let d=0;d<r;d++)u[Fn(Ve.decodeKey(He()))]=He();else for(let d=0;d<r;d++)u[Fn(He())]=He();return u}else{go&&(Ve.mapsAsObjects=!0,go=!1);let u=new Map;if(Ve.keyMap)for(let d=0;d<r;d++)u.set(Ve.decodeKey(He()),He());else for(let d=0;d<r;d++)u.set(He(),He());return u}case 6:if(r>=kp){let u=vt[r&8191];if(u)return u.read||(u.read=ku(u)),u.read();if(r<65536){if(r==R2){let d=la(),f=He(),h=He();Eu(f,h);let p={};if(Ve.keyMap)for(let m=2;m<d;m++){let y=Ve.decodeKey(h[m-2]);p[Fn(y)]=He()}else for(let m=2;m<d;m++){let y=h[m-2];p[Fn(y)]=He()}return p}else if(r==O2){let d=la(),f=He();for(let h=2;h<d;h++)Eu(f++,He());return He()}else if(r==kp)return V2();if(Ve.getShared&&(Mu(),u=vt[r&8191],u))return u.read||(u.read=ku(u)),u.read()}}let s=At[r];if(s)return s.handlesRead?s(He):s(He());{let u=He();for(let d=0;d<Su.length;d++){let f=Su[d](r,u);if(f!==void 0)return f}return new ki(u,r)}case 7:switch(r){case 20:return!1;case 21:return!0;case 22:return null;case 23:return;case 31:default:let u=(un||Ci())[r];if(u!==void 0)return u;throw new Error("Unknown token "+r)}default:if(isNaN(r)){let u=new Error("Unexpected end of CBOR data");throw u.incomplete=!0,u}throw new Error("Unknown CBOR token "+r)}}const Tp=/^[a-zA-Z_$][a-zA-Z\d_$]*$/;function ku(r){if(!r)throw new Error("Structure is required in record definition");function i(){let o=xe[le++];if(o=o&31,o>23)switch(o){case 24:o=xe[le++];break;case 25:o=nn.getUint16(le),le+=2;break;case 26:o=nn.getUint32(le),le+=4;break;default:throw new Error("Expected array header, but got "+xe[le-1])}let s=this.compiledReader;for(;s;){if(s.propertyCount===o)return s(He);s=s.next}if(this.slowReads++>=Mp){let d=this.length==o?this:this.slice(0,o);return s=Ve.keyMap?new Function("r","return {"+d.map(f=>Ve.decodeKey(f)).map(f=>Tp.test(f)?Fn(f)+":r()":"["+JSON.stringify(f)+"]:r()").join(",")+"}"):new Function("r","return {"+d.map(f=>Tp.test(f)?Fn(f)+":r()":"["+JSON.stringify(f)+"]:r()").join(",")+"}"),this.compiledReader&&(s.next=this.compiledReader),s.propertyCount=o,this.compiledReader=s,s(He)}let u={};if(Ve.keyMap)for(let d=0;d<o;d++)u[Fn(Ve.decodeKey(this[d]))]=He();else for(let d=0;d<o;d++)u[Fn(this[d])]=He();return u}return r.slowReads=0,i}function Fn(r){if(typeof r=="string")return r==="__proto__"?"__proto_":r;if(typeof r=="number"||typeof r=="boolean"||typeof r=="bigint")return r.toString();if(r==null)return r+"";throw new Error("Invalid property name type "+typeof r)}let I2=Cu;function Cu(r){let i;if(r<16&&(i=Fp(r)))return i;if(r>64&&xu)return xu.decode(xe.subarray(le,le+=r));const o=le+r,s=[];for(i="";le<o;){const u=xe[le++];if((u&128)===0)s.push(u);else if((u&224)===192){const d=xe[le++]&63;s.push((u&31)<<6|d)}else if((u&240)===224){const d=xe[le++]&63,f=xe[le++]&63;s.push((u&31)<<12|d<<6|f)}else if((u&248)===240){const d=xe[le++]&63,f=xe[le++]&63,h=xe[le++]&63;let p=(u&7)<<18|d<<12|f<<6|h;p>65535&&(p-=65536,s.push(p>>>10&1023|55296),p=56320|p&1023),s.push(p)}else s.push(u);s.length>=4096&&(i+=Wt.apply(String,s),s.length=0)}return s.length>0&&(i+=Wt.apply(String,s)),i}let Wt=String.fromCharCode;function L2(r){let i=le,o=new Array(r);for(let s=0;s<r;s++){const u=xe[le++];if((u&128)>0){le=i;return}o[s]=u}return Wt.apply(String,o)}function Fp(r){if(r<4)if(r<2){if(r===0)return"";{let i=xe[le++];if((i&128)>1){le-=1;return}return Wt(i)}}else{let i=xe[le++],o=xe[le++];if((i&128)>0||(o&128)>0){le-=2;return}if(r<3)return Wt(i,o);let s=xe[le++];if((s&128)>0){le-=3;return}return Wt(i,o,s)}else{let i=xe[le++],o=xe[le++],s=xe[le++],u=xe[le++];if((i&128)>0||(o&128)>0||(s&128)>0||(u&128)>0){le-=4;return}if(r<6){if(r===4)return Wt(i,o,s,u);{let d=xe[le++];if((d&128)>0){le-=5;return}return Wt(i,o,s,u,d)}}else if(r<8){let d=xe[le++],f=xe[le++];if((d&128)>0||(f&128)>0){le-=6;return}if(r<7)return Wt(i,o,s,u,d,f);let h=xe[le++];if((h&128)>0){le-=7;return}return Wt(i,o,s,u,d,f,h)}else{let d=xe[le++],f=xe[le++],h=xe[le++],p=xe[le++];if((d&128)>0||(f&128)>0||(h&128)>0||(p&128)>0){le-=8;return}if(r<10){if(r===8)return Wt(i,o,s,u,d,f,h,p);{let m=xe[le++];if((m&128)>0){le-=9;return}return Wt(i,o,s,u,d,f,h,p,m)}}else if(r<12){let m=xe[le++],y=xe[le++];if((m&128)>0||(y&128)>0){le-=10;return}if(r<11)return Wt(i,o,s,u,d,f,h,p,m,y);let v=xe[le++];if((v&128)>0){le-=11;return}return Wt(i,o,s,u,d,f,h,p,m,y,v)}else{let m=xe[le++],y=xe[le++],v=xe[le++],x=xe[le++];if((m&128)>0||(y&128)>0||(v&128)>0||(x&128)>0){le-=12;return}if(r<14){if(r===12)return Wt(i,o,s,u,d,f,h,p,m,y,v,x);{let E=xe[le++];if((E&128)>0){le-=13;return}return Wt(i,o,s,u,d,f,h,p,m,y,v,x,E)}}else{let E=xe[le++],M=xe[le++];if((E&128)>0||(M&128)>0){le-=14;return}if(r<15)return Wt(i,o,s,u,d,f,h,p,m,y,v,x,E,M);let R=xe[le++];if((R&128)>0){le-=15;return}return Wt(i,o,s,u,d,f,h,p,m,y,v,x,E,M,R)}}}}}function N2(r){return Ve.copyBuffers?Uint8Array.prototype.slice.call(xe,le,le+=r):xe.subarray(le,le+=r)}let Pp=new Float32Array(1),js=new Uint8Array(Pp.buffer,0,4);function D2(){let r=xe[le++],i=xe[le++],o=(r&127)>>2;if(o===31)return i||r&3?NaN:r&128?-1/0:1/0;if(o===0){let s=((r&3)<<8|i)/16777216;return r&128?-s:s}return js[3]=r&128|(o>>1)+56,js[2]=(r&7)<<5|i>>3,js[1]=i<<5,js[0]=0,Pp[0]}new Array(4096);class ki{constructor(i,o){this.value=i,this.tag=o}}At[0]=r=>new Date(r),At[1]=r=>new Date(Math.round(r*1e3)),At[2]=r=>{let i=BigInt(0);for(let o=0,s=r.byteLength;o<s;o++)i=BigInt(r[o])+(i<<BigInt(8));return i},At[3]=r=>BigInt(-1)-At[2](r),At[4]=r=>+(r[1]+"e"+r[0]),At[5]=r=>r[1]*Math.exp(r[0]*Math.log(2));const Eu=(r,i)=>{r=r-57344;let o=vt[r];o&&o.isShared&&((vt.restoreStructures||(vt.restoreStructures=[]))[r]=o),vt[r]=i,i.read=ku(i)};At[P2]=r=>{let i=r.length,o=r[1];Eu(r[0],o);let s={};for(let u=2;u<i;u++){let d=o[u-2];s[Fn(d)]=r[u]}return s},At[14]=r=>Rt?Rt[0].slice(Rt.position0,Rt.position0+=r):new ki(r,14),At[15]=r=>Rt?Rt[1].slice(Rt.position1,Rt.position1+=r):new ki(r,15);let z2={Error,RegExp};At[27]=r=>(z2[r[0]]||Error)(r[1],r[2]);const Op=r=>{if(xe[le++]!=132){let o=new Error("Packed values structure must be followed by a 4 element array");throw xe.length<le&&(o.incomplete=!0),o}let i=r();if(!i||!i.length){let o=new Error("Packed values structure must be followed by a 4 element array");throw o.incomplete=!0,o}return un=un?i.concat(un.slice(i.length)):i,un.prefixes=r(),un.suffixes=r(),r()};Op.handlesRead=!0,At[51]=Op,At[Cp]=r=>{if(!un)if(Ve.getShared)Mu();else return new ki(r,Cp);if(typeof r=="number")return un[16+(r>=0?2*r:-2*r-1)];let i=new Error("No support for non-integer packed references yet");throw r===void 0&&(i.incomplete=!0),i},At[28]=r=>{Tn||(Tn=new Map,Tn.id=0);let i=Tn.id++,o=le,s=xe[le],u;s>>5==4?u=[]:u={};let d={target:u};Tn.set(i,d);let f=r();return d.used?(Object.getPrototypeOf(u)!==Object.getPrototypeOf(f)&&(le=o,u=f,Tn.set(i,{target:u}),f=r()),Object.assign(u,f)):(d.target=f,f)},At[28].handlesRead=!0,At[29]=r=>{let i=Tn.get(r);return i.used=!0,i.target},At[258]=r=>new Set(r),(At[259]=r=>(Ve.mapsAsObjects&&(Ve.mapsAsObjects=!1,go=!0),r())).handlesRead=!0;function sa(r,i){return typeof r=="string"?r+i:r instanceof Array?r.concat(i):Object.assign({},r,i)}function Ci(){if(!un)if(Ve.getShared)Mu();else throw new Error("No packed values available");return un}const U2=1399353956;Su.push((r,i)=>{if(r>=225&&r<=255)return sa(Ci().prefixes[r-224],i);if(r>=28704&&r<=32767)return sa(Ci().prefixes[r-28672],i);if(r>=1879052288&&r<=2147483647)return sa(Ci().prefixes[r-1879048192],i);if(r>=216&&r<=223)return sa(i,Ci().suffixes[r-216]);if(r>=27647&&r<=28671)return sa(i,Ci().suffixes[r-27639]);if(r>=1811940352&&r<=1879048191)return sa(i,Ci().suffixes[r-1811939328]);if(r==U2)return{packedValues:un,structures:vt.slice(0),version:i};if(r==55799)return i});const q2=new Uint8Array(new Uint16Array([1]).buffer)[0]==1,Rp=[Uint8Array,Uint8ClampedArray,Uint16Array,Uint32Array,typeof BigUint64Array>"u"?{name:"BigUint64Array"}:BigUint64Array,Int8Array,Int16Array,Int32Array,typeof BigInt64Array>"u"?{name:"BigInt64Array"}:BigInt64Array,Float32Array,Float64Array],j2=[64,68,69,70,71,72,77,78,79,85,86];for(let r=0;r<Rp.length;r++)W2(Rp[r],j2[r]);function W2(r,i){let o="get"+r.name.slice(0,-5),s;typeof r=="function"?s=r.BYTES_PER_ELEMENT:r=null;for(let u=0;u<2;u++){if(!u&&s==1)continue;let d=s==2?1:s==4?2:s==8?3:0;At[u?i:i-4]=s==1||u==q2?f=>{if(!r)throw new Error("Could not find typed array for code "+i);return!Ve.copyBuffers&&(s===1||s===2&&!(f.byteOffset&1)||s===4&&!(f.byteOffset&3)||s===8&&!(f.byteOffset&7))?new r(f.buffer,f.byteOffset,f.byteLength>>d):new r(Uint8Array.prototype.slice.call(f,0).buffer)}:f=>{if(!r)throw new Error("Could not find typed array for code "+i);let h=new DataView(f.buffer,f.byteOffset,f.byteLength),p=f.length>>d,m=new r(p),y=h[o];for(let v=0;v<p;v++)m[v]=y.call(h,v<<d,u);return m}}}function V2(){let r=la(),i=le+He();for(let s=2;s<r;s++){let u=la();le+=u}let o=le;return le=i,Rt=[Cu(la()),Cu(la())],Rt.position0=0,Rt.position1=0,Rt.postBundlePosition=le,le=o,He()}function la(){let r=xe[le++]&31;if(r>23)switch(r){case 24:r=xe[le++];break;case 25:r=nn.getUint16(le),le+=2;break;case 26:r=nn.getUint32(le),le+=4;break}return r}function Mu(){if(Ve.getShared){let r=Ip(()=>(xe=null,Ve.getShared()))||{},i=r.structures||[];Ve.sharedVersion=r.version,un=Ve.sharedValues=r.packedValues,vt===!0?Ve.structures=vt=i:vt.splice.apply(vt,[0,i.length].concat(i))}}function Ip(r){let i=Si,o=le,s=Us,u=mo,d=zs,f=Tn,h=Rt,p=new Uint8Array(xe.slice(0,Si)),m=vt,y=Ve,v=vo,x=r();return Si=i,le=o,Us=s,mo=u,zs=d,Tn=f,Rt=h,xe=p,vo=v,vt=m,Ve=y,nn=new DataView(xe.buffer,xe.byteOffset,xe.byteLength),x}function Au(){xe=null,Tn=null,vt=null}const Tu=new Array(147);for(let r=0;r<256;r++)Tu[r]=+("1e"+Math.floor(45.15-r*.30103));let Fu=new Ap({useRecords:!1});const Lp=Fu.decode;Fu.decodeMultiple;let Ws;try{Ws=new TextEncoder}catch(r){}let Pu,Np;const Vs=typeof globalThis=="object"&&globalThis.Buffer,yo=typeof Vs<"u",Ou=yo?Vs.allocUnsafeSlow:Uint8Array,Dp=yo?Vs:Uint8Array,zp=256,Up=yo?4294967296:2144337920;let Ru,ee,st,q=0,Wr,It=null;const H2=61440,B2=/[\u0080-\uFFFF]/,vn=Symbol("record-id");let $2=class extends Ap{constructor(r){super(r),this.offset=0;let i,o,s,u,d;r=r||{};let f=Dp.prototype.utf8Write?function(S,T,O){return ee.utf8Write(S,T,O)}:Ws&&Ws.encodeInto?function(S,T){return Ws.encodeInto(S,ee.subarray(T)).written}:!1,h=this,p=r.structures||r.saveStructures,m=r.maxSharedStructures;if(m==null&&(m=p?128:0),m>8190)throw new Error("Maximum maxSharedStructure is 8190");let y=r.sequential;y&&(m=0),this.structures||(this.structures=[]),this.saveStructures&&(this.saveShared=this.saveStructures);let v,x,E=r.sharedValues,M;if(E){M=Object.create(null);for(let S=0,T=E.length;S<T;S++)M[E[S]]=S}let R=[],j=0,ie=0;this.mapEncode=function(S,T){if(this._keyMap&&!this._mapped)switch(S.constructor.name){case"Array":S=S.map(O=>this.encodeKeys(O));break}return this.encode(S,T)},this.encode=function(S,T){if(ee||(ee=new Ou(8192),st=new DataView(ee.buffer,0,8192),q=0),Wr=ee.length-10,Wr-q<2048?(ee=new Ou(ee.length),st=new DataView(ee.buffer,0,ee.length),Wr=ee.length-10,q=0):T===Vp&&(q=q+7&2147483640),i=q,h.useSelfDescribedHeader&&(st.setUint32(q,3654940416),q+=3),d=h.structuredClone?new Map:null,h.bundleStrings&&typeof S!="string"?(It=[],It.size=1/0):It=null,o=h.structures,o){if(o.uninitialized){let U=h.getShared()||{};h.structures=o=U.structures||[],h.sharedVersion=U.version;let k=h.sharedValues=U.packedValues;if(k){M={};for(let K=0,G=k.length;K<G;K++)M[k[K]]=K}}let O=o.length;if(O>m&&!y&&(O=m),!o.transitions){o.transitions=Object.create(null);for(let U=0;U<O;U++){let k=o[U];if(!k)continue;let K,G=o.transitions;for(let X=0,w=k.length;X<w;X++){G[vn]===void 0&&(G[vn]=U);let D=k[X];K=G[D],K||(K=G[D]=Object.create(null)),G=K}G[vn]=U|1048576}}y||(o.nextId=O)}if(s&&(s=!1),u=o||[],x=M,r.pack){let O=new Map;if(O.values=[],O.encoder=h,O.maxValues=r.maxPrivatePackedValues||(M?16:1/0),O.objectMap=M||!1,O.samplingPackedValues=v,Hs(S,O),O.values.length>0){ee[q++]=216,ee[q++]=51,Zn(4);let U=O.values;B(U),Zn(0),Zn(0),x=Object.create(M||null);for(let k=0,K=U.length;k<K;k++)x[U[k]]=k}}Ru=T&Du;try{if(Ru)return;if(B(S),It&&Wp(i,B),h.offset=q,d&&d.idsToInsert){q+=d.idsToInsert.length*2,q>Wr&&ae(q),h.offset=q;let O=J2(ee.subarray(i,q),d.idsToInsert);return d=null,O}return T&Vp?(ee.start=i,ee.end=q,ee):ee.subarray(i,q)}finally{if(o){if(ie<10&&ie++,o.length>m&&(o.length=m),j>1e4)o.transitions=null,ie=0,j=0,R.length>0&&(R=[]);else if(R.length>0&&!y){for(let O=0,U=R.length;O<U;O++)R[O][vn]=void 0;R=[]}}if(s&&h.saveShared){h.structures.length>m&&(h.structures=h.structures.slice(0,m));let O=ee.subarray(i,q);return h.updateSharedData()===!1?h.encode(S):O}T&K2&&(q=i)}},this.findCommonStringsToPack=()=>(v=new Map,M||(M=Object.create(null)),S=>{let T=S&&S.threshold||4,O=this.pack?S.maxPrivatePackedValues||16:0;E||(E=this.sharedValues=[]);for(let[U,k]of v)k.count>T&&(M[U]=O++,E.push(U),s=!0);for(;this.saveShared&&this.updateSharedData()===!1;);v=null});const B=S=>{q>Wr&&(ee=ae(q));var T=typeof S,O;if(T==="string"){if(x){let G=x[S];if(G>=0){G<16?ee[q++]=G+224:(ee[q++]=198,G&1?B(15-G>>1):B(G-16>>1));return}else if(v&&!r.pack){let X=v.get(S);X?X.count++:v.set(S,{count:1})}}let U=S.length;if(It&&U>=4&&U<1024){if((It.size+=U)>H2){let X,w=(It[0]?It[0].length*3+It[1].length:0)+10;q+w>Wr&&(ee=ae(q+w)),ee[q++]=217,ee[q++]=223,ee[q++]=249,ee[q++]=It.position?132:130,ee[q++]=26,X=q-i,q+=4,It.position&&Wp(i,B),It=["",""],It.size=0,It.position=X}let G=B2.test(S);It[G?0:1]+=S,ee[q++]=G?206:207,B(U);return}let k;U<32?k=1:U<256?k=2:U<65536?k=3:k=5;let K=U*3;if(q+K>Wr&&(ee=ae(q+K)),U<64||!f){let G,X,w,D=q+k;for(G=0;G<U;G++)X=S.charCodeAt(G),X<128?ee[D++]=X:X<2048?(ee[D++]=X>>6|192,ee[D++]=X&63|128):(X&64512)===55296&&((w=S.charCodeAt(G+1))&64512)===56320?(X=65536+((X&1023)<<10)+(w&1023),G++,ee[D++]=X>>18|240,ee[D++]=X>>12&63|128,ee[D++]=X>>6&63|128,ee[D++]=X&63|128):(ee[D++]=X>>12|224,ee[D++]=X>>6&63|128,ee[D++]=X&63|128);O=D-q-k}else O=f(S,q+k,K);O<24?ee[q++]=96|O:O<256?(k<2&&ee.copyWithin(q+2,q+1,q+1+O),ee[q++]=120,ee[q++]=O):O<65536?(k<3&&ee.copyWithin(q+3,q+2,q+2+O),ee[q++]=121,ee[q++]=O>>8,ee[q++]=O&255):(k<5&&ee.copyWithin(q+5,q+3,q+3+O),ee[q++]=122,st.setUint32(q,O),q+=4),q+=O}else if(T==="number")if(!this.alwaysUseFloat&&S>>>0===S)S<24?ee[q++]=S:S<256?(ee[q++]=24,ee[q++]=S):S<65536?(ee[q++]=25,ee[q++]=S>>8,ee[q++]=S&255):(ee[q++]=26,st.setUint32(q,S),q+=4);else if(!this.alwaysUseFloat&&S>>0===S)S>=-24?ee[q++]=31-S:S>=-256?(ee[q++]=56,ee[q++]=~S):S>=-65536?(ee[q++]=57,st.setUint16(q,~S),q+=2):(ee[q++]=58,st.setUint32(q,~S),q+=4);else{let U;if((U=this.useFloat32)>0&&S<4294967296&&S>=-2147483648){ee[q++]=250,st.setFloat32(q,S);let k;if(U<4||(k=S*Tu[(ee[q]&127)<<1|ee[q+1]>>7])>>0===k){q+=4;return}else q--}ee[q++]=251,st.setFloat64(q,S),q+=8}else if(T==="object")if(!S)ee[q++]=246;else{if(d){let k=d.get(S);if(k){if(ee[q++]=216,ee[q++]=29,ee[q++]=25,!k.references){let K=d.idsToInsert||(d.idsToInsert=[]);k.references=[],K.push(k)}k.references.push(q-i),q+=2;return}else d.set(S,{offset:q-i})}let U=S.constructor;if(U===Object)$(S);else if(U===Array){O=S.length,O<24?ee[q++]=128|O:Zn(O);for(let k=0;k<O;k++)B(S[k])}else if(U===Map)if((this.mapsAsObjects?this.useTag259ForMaps!==!1:this.useTag259ForMaps)&&(ee[q++]=217,ee[q++]=1,ee[q++]=3),O=S.size,O<24?ee[q++]=160|O:O<256?(ee[q++]=184,ee[q++]=O):O<65536?(ee[q++]=185,ee[q++]=O>>8,ee[q++]=O&255):(ee[q++]=186,st.setUint32(q,O),q+=4),h.keyMap)for(let[k,K]of S)B(h.encodeKey(k)),B(K);else for(let[k,K]of S)B(k),B(K);else{for(let k=0,K=Pu.length;k<K;k++){let G=Np[k];if(S instanceof G){let X=Pu[k],w=X.tag;w==null&&(w=X.getTag&&X.getTag.call(this,S)),w<24?ee[q++]=192|w:w<256?(ee[q++]=216,ee[q++]=w):w<65536?(ee[q++]=217,ee[q++]=w>>8,ee[q++]=w&255):w>-1&&(ee[q++]=218,st.setUint32(q,w),q+=4),X.encode.call(this,S,B,ae);return}}if(S[Symbol.iterator]){if(Ru){let k=new Error("Iterable should be serialized as iterator");throw k.iteratorNotHandled=!0,k}ee[q++]=159;for(let k of S)B(k);ee[q++]=255;return}if(S[Symbol.asyncIterator]||Iu(S)){let k=new Error("Iterable/blob should be serialized as iterator");throw k.iteratorNotHandled=!0,k}if(this.useToJSON&&S.toJSON){const k=S.toJSON();if(k!==S)return B(k)}$(S)}}else if(T==="boolean")ee[q++]=S?245:244;else if(T==="bigint"){if(S<BigInt(1)<<BigInt(64)&&S>=0)ee[q++]=27,st.setBigUint64(q,S);else if(S>-(BigInt(1)<<BigInt(64))&&S<0)ee[q++]=59,st.setBigUint64(q,-S-BigInt(1));else if(this.largeBigIntToFloat)ee[q++]=251,st.setFloat64(q,Number(S));else{S>=BigInt(0)?ee[q++]=194:(ee[q++]=195,S=BigInt(-1)-S);let U=[];for(;S;)U.push(Number(S&BigInt(255))),S>>=BigInt(8);Lu(new Uint8Array(U.reverse()),ae);return}q+=8}else if(T==="undefined")ee[q++]=247;else throw new Error("Unknown type: "+T)},$=this.useRecords===!1?this.variableMapSize?S=>{let T=Object.keys(S),O=Object.values(S),U=T.length;if(U<24?ee[q++]=160|U:U<256?(ee[q++]=184,ee[q++]=U):U<65536?(ee[q++]=185,ee[q++]=U>>8,ee[q++]=U&255):(ee[q++]=186,st.setUint32(q,U),q+=4),h.keyMap)for(let k=0;k<U;k++)B(h.encodeKey(T[k])),B(O[k]);else for(let k=0;k<U;k++)B(T[k]),B(O[k])}:S=>{ee[q++]=185;let T=q-i;q+=2;let O=0;if(h.keyMap)for(let U in S)(typeof S.hasOwnProperty!="function"||S.hasOwnProperty(U))&&(B(h.encodeKey(U)),B(S[U]),O++);else for(let U in S)(typeof S.hasOwnProperty!="function"||S.hasOwnProperty(U))&&(B(U),B(S[U]),O++);ee[T+++i]=O>>8,ee[T+i]=O&255}:(S,T)=>{let O,U=u.transitions||(u.transitions=Object.create(null)),k=0,K=0,G,X;if(this.keyMap){X=Object.keys(S).map(D=>this.encodeKey(D)),K=X.length;for(let D=0;D<K;D++){let te=X[D];O=U[te],O||(O=U[te]=Object.create(null),k++),U=O}}else for(let D in S)(typeof S.hasOwnProperty!="function"||S.hasOwnProperty(D))&&(O=U[D],O||(U[vn]&1048576&&(G=U[vn]&65535),O=U[D]=Object.create(null),k++),U=O,K++);let w=U[vn];if(w!==void 0)w&=65535,ee[q++]=217,ee[q++]=w>>8|224,ee[q++]=w&255;else if(X||(X=U.__keys__||(U.__keys__=Object.keys(S))),G===void 0?(w=u.nextId++,w||(w=0,u.nextId=1),w>=zp&&(u.nextId=(w=m)+1)):w=G,u[w]=X,w<m){ee[q++]=217,ee[q++]=w>>8|224,ee[q++]=w&255,U=u.transitions;for(let D=0;D<K;D++)(U[vn]===void 0||U[vn]&1048576)&&(U[vn]=w),U=U[X[D]];U[vn]=w|1048576,s=!0}else{if(U[vn]=w,st.setUint32(q,3655335680),q+=3,k&&(j+=ie*k),R.length>=zp-m&&(R.shift()[vn]=void 0),R.push(U),Zn(K+2),B(57344+w),B(X),T)return;for(let D in S)(typeof S.hasOwnProperty!="function"||S.hasOwnProperty(D))&&B(S[D]);return}if(K<24?ee[q++]=128|K:Zn(K),!T)for(let D in S)(typeof S.hasOwnProperty!="function"||S.hasOwnProperty(D))&&B(S[D])},ae=S=>{let T;if(S>16777216){if(S-i>Up)throw new Error("Encoded buffer would be larger than maximum buffer size");T=Math.min(Up,Math.round(Math.max((S-i)*(S>67108864?1.25:2),4194304)/4096)*4096)}else T=(Math.max(S-i<<2,ee.length-1)>>12)+1<<12;let O=new Ou(T);return st=new DataView(O.buffer,0,T),ee.copy?ee.copy(O,0,i,S):O.set(ee.slice(i,S)),q-=i,i=0,Wr=O.length-10,ee=O};let V=100,Y=1e3;this.encodeAsIterable=function(S,T){return F(S,T,N)},this.encodeAsAsyncIterable=function(S,T){return F(S,T,b)};function*N(S,T,O){let U=S.constructor;if(U===Object){let k=h.useRecords!==!1;k?$(S,!0):qp(Object.keys(S).length,160);for(let K in S){let G=S[K];k||B(K),G&&typeof G=="object"?T[K]?yield*N(G,T[K]):yield*I(G,T,K):B(G)}}else if(U===Array){let k=S.length;Zn(k);for(let K=0;K<k;K++){let G=S[K];G&&(typeof G=="object"||q-i>V)?T.element?yield*N(G,T.element):yield*I(G,T,"element"):B(G)}}else if(S[Symbol.iterator]&&!S.buffer){ee[q++]=159;for(let k of S)k&&(typeof k=="object"||q-i>V)?T.element?yield*N(k,T.element):yield*I(k,T,"element"):B(k);ee[q++]=255}else Iu(S)?(qp(S.size,64),yield ee.subarray(i,q),yield S,z()):S[Symbol.asyncIterator]?(ee[q++]=159,yield ee.subarray(i,q),yield S,z(),ee[q++]=255):B(S);O&&q>i?yield ee.subarray(i,q):q-i>V&&(yield ee.subarray(i,q),z())}function*I(S,T,O){let U=q-i;try{B(S),q-i>V&&(yield ee.subarray(i,q),z())}catch(k){if(k.iteratorNotHandled)T[O]={},q=i+U,yield*N.call(this,S,T[O]);else throw k}}function z(){V=Y,h.encode(null,Du)}function F(S,T,O){return T&&T.chunkThreshold?V=Y=T.chunkThreshold:V=100,S&&typeof S=="object"?(h.encode(null,Du),O(S,h.iterateProperties||(h.iterateProperties={}),!0)):[h.encode(S)]}async function*b(S,T){for(let O of N(S,T,!0)){let U=O.constructor;if(U===Dp||U===Uint8Array)yield O;else if(Iu(O)){let k=O.stream().getReader(),K;for(;!(K=await k.read()).done;)yield K.value}else if(O[Symbol.asyncIterator])for await(let k of O)z(),k?yield*b(k,T.async||(T.async={})):yield h.encode(k);else yield O}}}useBuffer(r){ee=r,st=new DataView(ee.buffer,ee.byteOffset,ee.byteLength),q=0}clearSharedData(){this.structures&&(this.structures=[]),this.sharedValues&&(this.sharedValues=void 0)}updateSharedData(){let r=this.sharedVersion||0;this.sharedVersion=r+1;let i=this.structures.slice(0),o=new jp(i,this.sharedValues,this.sharedVersion),s=this.saveShared(o,u=>(u&&u.version||0)==r);return s===!1?(o=this.getShared()||{},this.structures=o.structures||[],this.sharedValues=o.packedValues,this.sharedVersion=o.version,this.structures.nextId=this.structures.length):i.forEach((u,d)=>this.structures[d]=u),s}};function qp(r,i){r<24?ee[q++]=i|r:r<256?(ee[q++]=i|24,ee[q++]=r):r<65536?(ee[q++]=i|25,ee[q++]=r>>8,ee[q++]=r&255):(ee[q++]=i|26,st.setUint32(q,r),q+=4)}class jp{constructor(i,o,s){this.structures=i,this.packedValues=o,this.version=s}}function Zn(r){r<24?ee[q++]=128|r:r<256?(ee[q++]=152,ee[q++]=r):r<65536?(ee[q++]=153,ee[q++]=r>>8,ee[q++]=r&255):(ee[q++]=154,st.setUint32(q,r),q+=4)}const Y2=typeof Blob>"u"?function(){}:Blob;function Iu(r){if(r instanceof Y2)return!0;let i=r[Symbol.toStringTag];return i==="Blob"||i==="File"}function Hs(r,i){switch(typeof r){case"string":if(r.length>3){if(i.objectMap[r]>-1||i.values.length>=i.maxValues)return;let s=i.get(r);if(s)++s.count==2&&i.values.push(r);else if(i.set(r,{count:1}),i.samplingPackedValues){let u=i.samplingPackedValues.get(r);u?u.count++:i.samplingPackedValues.set(r,{count:1})}}break;case"object":if(r)if(r instanceof Array)for(let s=0,u=r.length;s<u;s++)Hs(r[s],i);else{let s=!i.encoder.useRecords;for(var o in r)r.hasOwnProperty(o)&&(s&&Hs(o,i),Hs(r[o],i))}break;case"function":console.log(r)}}const X2=new Uint8Array(new Uint16Array([1]).buffer)[0]==1;Np=[Date,Set,Error,RegExp,ki,ArrayBuffer,Uint8Array,Uint8ClampedArray,Uint16Array,Uint32Array,typeof BigUint64Array>"u"?function(){}:BigUint64Array,Int8Array,Int16Array,Int32Array,typeof BigInt64Array>"u"?function(){}:BigInt64Array,Float32Array,Float64Array,jp],Pu=[{tag:1,encode(r,i){let o=r.getTime()/1e3;(this.useTimestamp32||r.getMilliseconds()===0)&&o>=0&&o<4294967296?(ee[q++]=26,st.setUint32(q,o),q+=4):(ee[q++]=251,st.setFloat64(q,o),q+=8)}},{tag:258,encode(r,i){let o=Array.from(r);i(o)}},{tag:27,encode(r,i){i([r.name,r.message])}},{tag:27,encode(r,i){i(["RegExp",r.source,r.flags])}},{getTag(r){return r.tag},encode(r,i){i(r.value)}},{encode(r,i,o){Lu(r,o)}},{getTag(r){if(r.constructor===Uint8Array&&(this.tagUint8Array||yo&&this.tagUint8Array!==!1))return 64},encode(r,i,o){Lu(r,o)}},Gn(68,1),Gn(69,2),Gn(70,4),Gn(71,8),Gn(72,1),Gn(77,2),Gn(78,4),Gn(79,8),Gn(85,4),Gn(86,8),{encode(r,i){let o=r.packedValues||[],s=r.structures||[];if(o.values.length>0){ee[q++]=216,ee[q++]=51,Zn(4);let u=o.values;i(u),Zn(0),Zn(0),packedObjectMap=Object.create(sharedPackedObjectMap||null);for(let d=0,f=u.length;d<f;d++)packedObjectMap[u[d]]=d}if(s){st.setUint32(q,3655335424),q+=3;let u=s.slice(0);u.unshift(57344),u.push(new ki(r.version,1399353956)),i(u)}else i(new ki(r.version,1399353956))}}];function Gn(r,i){return!X2&&i>1&&(r-=4),{tag:r,encode:function(o,s){let u=o.byteLength,d=o.byteOffset||0,f=o.buffer||o;s(yo?Vs.from(f,d,u):new Uint8Array(f,d,u))}}}function Lu(r,i){let o=r.byteLength;o<24?ee[q++]=64+o:o<256?(ee[q++]=88,ee[q++]=o):o<65536?(ee[q++]=89,ee[q++]=o>>8,ee[q++]=o&255):(ee[q++]=90,st.setUint32(q,o),q+=4),q+o>=ee.length&&i(q+o),ee.set(r.buffer?r:new Uint8Array(r),q),q+=o}function J2(r,i){let o,s=i.length*2,u=r.length-s;i.sort((d,f)=>d.offset>f.offset?1:-1);for(let d=0;d<i.length;d++){let f=i[d];f.id=d;for(let h of f.references)r[h++]=d>>8,r[h]=d&255}for(;o=i.pop();){let d=o.offset;r.copyWithin(d+s,d,u),s-=2;let f=d+s;r[f++]=216,r[f++]=28,u=d}return r}function Wp(r,i){st.setUint32(It.position+r,q-It.position-r+1);let o=It;It=null,i(o[0]),i(o[1])}let Nu=new $2({useRecords:!1});Nu.encode,Nu.encodeAsIterable,Nu.encodeAsAsyncIterable;const Vp=512,K2=1024,Du=2048;var zu,Hp;function Z2(){if(Hp)return zu;Hp=1;function r(i,s){var s=s||{};this._capacity=s.capacity,this._head=0,this._tail=0,Array.isArray(i)?this._fromArray(i):(this._capacityMask=3,this._list=new Array(4))}return r.prototype.peekAt=function(i){var o=i;if(o===(o|0)){var s=this.size();if(!(o>=s||o<-s))return o<0&&(o+=s),o=this._head+o&this._capacityMask,this._list[o]}},r.prototype.get=function(i){return this.peekAt(i)},r.prototype.peek=function(){if(this._head!==this._tail)return this._list[this._head]},r.prototype.peekFront=function(){return this.peek()},r.prototype.peekBack=function(){return this.peekAt(-1)},Object.defineProperty(r.prototype,"length",{get:function(){return this.size()}}),r.prototype.size=function(){return this._head===this._tail?0:this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},r.prototype.unshift=function(i){if(arguments.length===0)return this.size();var o=this._list.length;return this._head=this._head-1+o&this._capacityMask,this._list[this._head]=i,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.pop(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},r.prototype.shift=function(){var i=this._head;if(i!==this._tail){var o=this._list[i];return this._list[i]=void 0,this._head=i+1&this._capacityMask,i<2&&this._tail>1e4&&this._tail<=this._list.length>>>2&&this._shrinkArray(),o}},r.prototype.push=function(i){if(arguments.length===0)return this.size();var o=this._tail;return this._list[o]=i,this._tail=o+1&this._capacityMask,this._tail===this._head&&this._growArray(),this._capacity&&this.size()>this._capacity&&this.shift(),this._head<this._tail?this._tail-this._head:this._capacityMask+1-(this._head-this._tail)},r.prototype.pop=function(){var i=this._tail;if(i!==this._head){var o=this._list.length;this._tail=i-1+o&this._capacityMask;var s=this._list[this._tail];return this._list[this._tail]=void 0,this._head<2&&i>1e4&&i<=o>>>2&&this._shrinkArray(),s}},r.prototype.removeOne=function(i){var o=i;if(o===(o|0)&&this._head!==this._tail){var s=this.size(),u=this._list.length;if(!(o>=s||o<-s)){o<0&&(o+=s),o=this._head+o&this._capacityMask;var d=this._list[o],f;if(i<s/2){for(f=i;f>0;f--)this._list[o]=this._list[o=o-1+u&this._capacityMask];this._list[o]=void 0,this._head=this._head+1+u&this._capacityMask}else{for(f=s-1-i;f>0;f--)this._list[o]=this._list[o=o+1+u&this._capacityMask];this._list[o]=void 0,this._tail=this._tail-1+u&this._capacityMask}return d}}},r.prototype.remove=function(i,o){var s=i,u,d=o;if(s===(s|0)&&this._head!==this._tail){var f=this.size(),h=this._list.length;if(!(s>=f||s<-f||o<1)){if(s<0&&(s+=f),o===1||!o)return u=new Array(1),u[0]=this.removeOne(s),u;if(s===0&&s+o>=f)return u=this.toArray(),this.clear(),u;s+o>f&&(o=f-s);var p;for(u=new Array(o),p=0;p<o;p++)u[p]=this._list[this._head+s+p&this._capacityMask];if(s=this._head+s&this._capacityMask,i+o===f){for(this._tail=this._tail-o+h&this._capacityMask,p=o;p>0;p--)this._list[s=s+1+h&this._capacityMask]=void 0;return u}if(i===0){for(this._head=this._head+o+h&this._capacityMask,p=o-1;p>0;p--)this._list[s=s+1+h&this._capacityMask]=void 0;return u}if(s<f/2){for(this._head=this._head+i+o+h&this._capacityMask,p=i;p>0;p--)this.unshift(this._list[s=s-1+h&this._capacityMask]);for(s=this._head-1+h&this._capacityMask;d>0;)this._list[s=s-1+h&this._capacityMask]=void 0,d--;i<0&&(this._tail=s)}else{for(this._tail=s,s=s+o+h&this._capacityMask,p=f-(o+i);p>0;p--)this.push(this._list[s++]);for(s=this._tail;d>0;)this._list[s=s+1+h&this._capacityMask]=void 0,d--}return this._head<2&&this._tail>1e4&&this._tail<=h>>>2&&this._shrinkArray(),u}}},r.prototype.splice=function(i,o){var s=i;if(s===(s|0)){var u=this.size();if(s<0&&(s+=u),!(s>u))if(arguments.length>2){var d,f,h,p=arguments.length,m=this._list.length,y=2;if(!u||s<u/2){for(f=new Array(s),d=0;d<s;d++)f[d]=this._list[this._head+d&this._capacityMask];for(o===0?(h=[],s>0&&(this._head=this._head+s+m&this._capacityMask)):(h=this.remove(s,o),this._head=this._head+s+m&this._capacityMask);p>y;)this.unshift(arguments[--p]);for(d=s;d>0;d--)this.unshift(f[d-1])}else{f=new Array(u-(s+o));var v=f.length;for(d=0;d<v;d++)f[d]=this._list[this._head+s+o+d&this._capacityMask];for(o===0?(h=[],s!=u&&(this._tail=this._head+s+m&this._capacityMask)):(h=this.remove(s,o),this._tail=this._tail-v+m&this._capacityMask);y<p;)this.push(arguments[y++]);for(d=0;d<v;d++)this.push(f[d])}return h}else return this.remove(s,o)}},r.prototype.clear=function(){this._list=new Array(this._list.length),this._head=0,this._tail=0},r.prototype.isEmpty=function(){return this._head===this._tail},r.prototype.toArray=function(){return this._copyArray(!1)},r.prototype._fromArray=function(i){var o=i.length,s=this._nextPowerOf2(o);this._list=new Array(s),this._capacityMask=s-1,this._tail=o;for(var u=0;u<o;u++)this._list[u]=i[u]},r.prototype._copyArray=function(i,o){var s=this._list,u=s.length,d=this.length;if(o=o|d,o==d&&this._head<this._tail)return this._list.slice(this._head,this._tail);var f=new Array(o),h=0,p;if(i||this._head>this._tail){for(p=this._head;p<u;p++)f[h++]=s[p];for(p=0;p<this._tail;p++)f[h++]=s[p]}else for(p=this._head;p<this._tail;p++)f[h++]=s[p];return f},r.prototype._growArray=function(){if(this._head!=0){var i=this._copyArray(!0,this._list.length<<1);this._tail=this._list.length,this._head=0,this._list=i}else this._tail=this._list.length,this._list.length<<=1;this._capacityMask=this._capacityMask<<1|1},r.prototype._shrinkArray=function(){this._list.length>>>=1,this._capacityMask>>>=1},r.prototype._nextPowerOf2=function(i){var o=Math.log(i)/Math.log(2),s=1<<o+1;return Math.max(s,4)},zu=r,zu}var G2=Z2();const Bp=Fs(G2),Q2=(()=>{try{if(typeof WebAssembly=="object"&&typeof WebAssembly.instantiate=="function"){const r=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(r instanceof WebAssembly.Module)return new WebAssembly.Instance(r)instanceof WebAssembly.Instance}}catch(r){}return!1})();window.crypto||(window.crypto=window.msCrypto);function ew(r,i,o,s){switch(r){case"flac":return new _u(wu.Flac,i,o,s);case"opus":return new _u(wu.Opus,i,o,s)}}function tw(r){return r=Lp(r),r.data=new Int8Array(r.data),r}class $p{constructor(){this.decoder=new _p}decode(i){return i=new Uint8Array(i),this.decoder.decode(i).map(tw)}destroy(){this.decoder.free()}}function nw(r){switch(r){case"zstd":return new $p;case"av1":return new $p}}async function rw(){let r;Q2?r=await co(()=>import("./phantomsdrdsp_bg-D56RGGCR.js").then(async i=>(await i.__tla,i)),[]):r=await co(()=>import("./phantomsdrdsp_bg_fallback-BPr2VyA0.js").then(async i=>(await i.__tla,i)),[]),gp(r),r.__wbindgen_start()}var Uu,Yp;function iw(){if(Yp)return Uu;Yp=1;const r={push:function(i){if(typeof i!="number")throw new Error("val must be a number.");return this.sum-=this.data[this.dataI],this.sum+=i,this.data[this.dataI]=i,this.dataI=(this.dataI+1)%this.size,this},get:function(){return this.sum/this.size}};return Uu=(i,o=0)=>{if(typeof i!="number")throw new Error("size must be a number.");if(typeof o!="number")throw new Error("fill must be a number.");const s=Object.create(r);return s.sum=i*o,s.size=i,s.data=new Array(i),s.data.fill(o),s.dataI=0,s},Uu}var aw=iw();const Xp=Fs(aw);var ow=(()=>{var r=import.meta.url;return async function(i={}){var o=i,s,u;o.ready=new Promise((C,P)=>{s=C,u=P}),["_init_decode","_exec_decode","_free","_malloc","_memory","_fflush","___indirect_function_table","onRuntimeInitialized"].forEach(C=>{Object.getOwnPropertyDescriptor(o.ready,C)||Object.defineProperty(o.ready,C,{get:()=>Pe("You are getting "+C+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js"),set:()=>Pe("You are setting "+C+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")})});var d=Object.assign({},o),f=(C,P)=>{throw P},h=typeof window=="object",p=typeof importScripts=="function",m=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",y=!h&&!m&&!p;if(o.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -sENVIRONMENT=web or -sENVIRONMENT=node)");var v="";function x(C){return o.locateFile?o.locateFile(C,v):v+C}var E,M,R;if(m){if(typeof process>"u"||!process.release||process.release.name!=="node")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");var j=process.versions.node,ie=j.split(".").slice(0,3);if(ie=ie[0]*1e4+ie[1]*100+ie[2].split("-")[0]*1,ie<16e4)throw new Error("This emscripten-generated code requires node v16.0.0 (detected v"+j+")");const{createRequire:C}=await co(async()=>{const{createRequire:P}=await Promise.resolve().then(()=>Ag);return{createRequire:P}},void 0);var B=C(import.meta.url),$=B("fs"),ae=B("path");p?v=ae.dirname(v)+"/":v=B("url").fileURLToPath(new URL("data:text/javascript;base64,aW1wb3J0ICogYXMgd2FzbSBmcm9tICIuL3BoYW50b21zZHJkc3BfYmcud2FzbSI7CmltcG9ydCB7IF9fd2JnX3NldF93YXNtIH0gZnJvbSAiLi9waGFudG9tc2RyZHNwX2JnLmpzIjsKX193Ymdfc2V0X3dhc20od2FzbSk7CmV4cG9ydCAqIGZyb20gIi4vcGhhbnRvbXNkcmRzcF9iZy5qcyI7Cgp3YXNtLl9fd2JpbmRnZW5fc3RhcnQoKTsK",import.meta.url)),E=(P,oe)=>(P=dn(P)?new URL(P):ae.normalize(P),$.readFileSync(P,oe?void 0:"utf8")),R=P=>{var oe=E(P,!0);return oe.buffer||(oe=new Uint8Array(oe)),b(oe.buffer),oe},M=(P,oe,pe,Ee=!0)=>{P=dn(P)?new URL(P):ae.normalize(P),$.readFile(P,Ee?void 0:"utf8",(Re,Ie)=>{Re?pe(Re):oe(Ee?Ie.buffer:Ie)})},!o.thisProgram&&process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),process.on("uncaughtException",P=>{if(P!=="unwind"&&!(P instanceof on)&&!(P.context instanceof on))throw P}),f=(P,oe)=>{throw process.exitCode=P,oe},o.inspect=()=>"[Emscripten Module object]"}else if(y){if(typeof process=="object"&&typeof B=="function"||typeof window=="object"||typeof importScripts=="function")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");typeof read<"u"&&(E=read),R=C=>{if(typeof readbuffer=="function")return new Uint8Array(readbuffer(C));let P=read(C,"binary");return b(typeof P=="object"),P},M=(C,P,oe)=>{setTimeout(()=>P(R(C)))},typeof clearTimeout>"u"&&(globalThis.clearTimeout=C=>{}),typeof setTimeout>"u"&&(globalThis.setTimeout=C=>typeof C=="function"?C():Pe()),typeof scriptArgs<"u"&&scriptArgs,typeof quit=="function"&&(f=(C,P)=>{throw setTimeout(()=>{if(!(P instanceof on)){let oe=P;P&&typeof P=="object"&&P.stack&&(oe=[P,P.stack]),Y("exiting due to exception: ".concat(oe))}quit(C)}),P}),typeof print<"u"&&(typeof console>"u"&&(console={}),console.log=print,console.warn=console.error=typeof printErr<"u"?printErr:print)}else if(h||p){if(p?v=self.location.href:typeof document<"u"&&document.currentScript&&(v=document.currentScript.src),r&&(v=r),v.indexOf("blob:")!==0?v=v.substr(0,v.replace(/[?#].*/,"").lastIndexOf("/")+1):v="",!(typeof window=="object"||typeof importScripts=="function"))throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");E=C=>{var P=new XMLHttpRequest;return P.open("GET",C,!1),P.send(null),P.responseText},p&&(R=C=>{var P=new XMLHttpRequest;return P.open("GET",C,!1),P.responseType="arraybuffer",P.send(null),new Uint8Array(P.response)}),M=(C,P,oe)=>{var pe=new XMLHttpRequest;pe.open("GET",C,!0),pe.responseType="arraybuffer",pe.onload=()=>{if(pe.status==200||pe.status==0&&pe.response){P(pe.response);return}oe()},pe.onerror=oe,pe.send(null)}}else throw new Error("environment detection error");var V=o.print||console.log.bind(console),Y=o.printErr||console.error.bind(console);Object.assign(o,d),d=null,Ma(),o.arguments&&o.arguments,Xe("arguments","arguments_"),o.thisProgram&&o.thisProgram,Xe("thisProgram","thisProgram"),o.quit&&(f=o.quit),Xe("quit","quit_"),b(typeof o.memoryInitializerPrefixURL>"u","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),b(typeof o.pthreadMainPrefixURL>"u","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),b(typeof o.cdInitializerPrefixURL>"u","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),b(typeof o.filePackagePrefixURL>"u","Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),b(typeof o.read>"u","Module.read option was removed (modify read_ in JS)"),b(typeof o.readAsync>"u","Module.readAsync option was removed (modify readAsync in JS)"),b(typeof o.readBinary>"u","Module.readBinary option was removed (modify readBinary in JS)"),b(typeof o.setWindowTitle>"u","Module.setWindowTitle option was removed (modify emscripten_set_window_title in JS)"),b(typeof o.TOTAL_MEMORY>"u","Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),Xe("asm","wasmExports"),Xe("read","read_"),Xe("readAsync","readAsync"),Xe("readBinary","readBinary"),Xe("setWindowTitle","setWindowTitle"),b(!y,"shell environment detected but not enabled at build time.  Add 'shell' to `-sENVIRONMENT` to enable.");var N;o.wasmBinary&&(N=o.wasmBinary),Xe("wasmBinary","wasmBinary"),typeof WebAssembly!="object"&&Pe("no native wasm support detected");var I,z=!1,F;function b(C,P){C||Pe("Assertion failed"+(P?": "+P:""))}var S,T,O,U;function k(){var C=I.buffer;o.HEAP8=S=new Int8Array(C),o.HEAP16=new Int16Array(C),o.HEAPU8=T=new Uint8Array(C),o.HEAPU16=new Uint16Array(C),o.HEAP32=O=new Int32Array(C),o.HEAPU32=U=new Uint32Array(C),o.HEAPF32=new Float32Array(C),o.HEAPF64=new Float64Array(C)}b(!o.STACK_SIZE,"STACK_SIZE can no longer be set at runtime.  Use -sSTACK_SIZE at link time"),b(typeof Int32Array<"u"&&typeof Float64Array<"u"&&Int32Array.prototype.subarray!=null&&Int32Array.prototype.set!=null,"JS engine does not provide full typed array support"),b(!o.wasmMemory,"Use of `wasmMemory` detected.  Use -sIMPORTED_MEMORY to define wasmMemory externally"),b(!o.INITIAL_MEMORY,"Detected runtime INITIAL_MEMORY setting.  Use -sIMPORTED_MEMORY to define wasmMemory dynamically");function K(){var C=Kr();b((C&3)==0),C==0&&(C+=4),U[C>>2]=34821223,U[C+4>>2]=2310721022,U[0]=1668509029}function G(){if(!z){var C=Kr();C==0&&(C+=4);var P=U[C>>2],oe=U[C+4>>2];(P!=34821223||oe!=2310721022)&&Pe("Stack overflow! Stack cookie has been overwritten at ".concat(Dt(C),", expected hex dwords 0x89BACDFE and 0x2135467, but received ").concat(Dt(oe)," ").concat(Dt(P))),U[0]!=1668509029&&Pe("Runtime error: The application has corrupted its heap memory area (address zero)!")}}(function(){var C=new Int16Array(1),P=new Int8Array(C.buffer);if(C[0]=25459,P[0]!==115||P[1]!==99)throw"Runtime error: expected the system to be little-endian! (Run with -sSUPPORT_BIG_ENDIAN to bypass)"})();var X=[],w=[],D=[],te=!1;function de(){if(o.preRun)for(typeof o.preRun=="function"&&(o.preRun=[o.preRun]);o.preRun.length;)_e(o.preRun.shift());In(X)}function me(){b(!te),te=!0,G(),In(w)}function ge(){if(G(),o.postRun)for(typeof o.postRun=="function"&&(o.postRun=[o.postRun]);o.postRun.length;)he(o.postRun.shift());In(D)}function _e(C){X.unshift(C)}function ze(C){w.unshift(C)}function he(C){D.unshift(C)}b(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),b(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),b(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),b(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var Ye=0,Ze=null,dt=null,bt={};function tr(C){Ye++,o.monitorRunDependencies&&o.monitorRunDependencies(Ye),b(!bt[C]),bt[C]=1,Ze===null&&typeof setInterval<"u"&&(Ze=setInterval(()=>{if(z){clearInterval(Ze),Ze=null;return}var P=!1;for(var oe in bt)P||(P=!0,Y("still waiting on run dependencies:")),Y("dependency: ".concat(oe));P&&Y("(end of list)")},1e4))}function et(C){if(Ye--,o.monitorRunDependencies&&o.monitorRunDependencies(Ye),b(bt[C]),delete bt[C],Ye==0&&(Ze!==null&&(clearInterval(Ze),Ze=null),dt)){var P=dt;dt=null,P()}}function Pe(C){o.onAbort&&o.onAbort(C),C="Aborted("+C+")",Y(C),z=!0,F=1,C.indexOf("RuntimeError: unreachable")>=0&&(C+='. "unreachable" may be due to ASYNCIFY_STACK_SIZE not being large enough (try increasing it)');var P=new WebAssembly.RuntimeError(C);throw u(P),P}var ve={error(){Pe("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with -sFORCE_FILESYSTEM")},init(){ve.error()},createDataFile(){ve.error()},createPreloadedFile(){ve.error()},createLazyFile(){ve.error()},open(){ve.error()},mkdev(){ve.error()},registerDevice(){ve.error()},analyzePath(){ve.error()},ErrnoError(){ve.error()}};o.FS_createDataFile=ve.createDataFile,o.FS_createPreloadedFile=ve.createPreloadedFile;var Xt="data:application/octet-stream;base64,",Ri=C=>C.startsWith(Xt),dn=C=>C.startsWith("file://");function Ue(C){return function(){b(te,"native function `".concat(C,"` called before runtime initialization"));var P=Jt[C];return b(P,"exported native function `".concat(C,"` not found")),P.apply(null,arguments)}}var ft;o.locateFile?(ft="decode.wasm",Ri(ft)||(ft=x(ft))):ft=new URL("/assets/decode-BC3G6QKc.wasm",import.meta.url).href;function Ii(C){if(C==ft&&N)return new Uint8Array(N);if(R)return R(C);throw"both async and sync fetching of the wasm failed"}function ya(C){if(!N&&(h||p)){if(typeof fetch=="function"&&!dn(C))return fetch(C,{credentials:"same-origin"}).then(P=>{if(!P.ok)throw"failed to load wasm binary file at '"+C+"'";return P.arrayBuffer()}).catch(()=>Ii(C));if(M)return new Promise((P,oe)=>{M(C,pe=>P(new Uint8Array(pe)),oe)})}return Promise.resolve().then(()=>Ii(C))}function Li(C,P,oe){return ya(C).then(pe=>WebAssembly.instantiate(pe,P)).then(pe=>pe).then(oe,pe=>{Y("failed to asynchronously prepare wasm: ".concat(pe)),dn(ft)&&Y("warning: Loading from a file URI (".concat(ft,") is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing")),Pe(pe)})}function Po(C,P,oe,pe){return!C&&typeof WebAssembly.instantiateStreaming=="function"&&!Ri(P)&&!dn(P)&&!m&&typeof fetch=="function"?fetch(P,{credentials:"same-origin"}).then(Ee=>{var Re=WebAssembly.instantiateStreaming(Ee,oe);return Re.then(pe,function(Ie){return Y("wasm streaming compile failed: ".concat(Ie)),Y("falling back to ArrayBuffer instantiation"),Li(P,oe,pe)})}):Li(P,oe,pe)}function wr(){var C={env:kr,wasi_snapshot_preview1:kr};function P(Ee,Re){return Jt=Ee.exports,Jt=be.instrumentWasmExports(Jt),I=Jt.memory,b(I,"memory not found in wasm exports"),k(),ze(Jt.__wasm_call_ctors),et("wasm-instantiate"),Jt}tr("wasm-instantiate");var oe=o;function pe(Ee){b(o===oe,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),oe=null,P(Ee.instance)}if(o.instantiateWasm)try{return o.instantiateWasm(C,P)}catch(Ee){Y("Module.instantiateWasm callback failed with error: ".concat(Ee)),u(Ee)}return Po(N,ft,C,pe).catch(u),{}}function Xe(C,P,oe=!0){Object.getOwnPropertyDescriptor(o,C)||Object.defineProperty(o,C,{configurable:!0,get(){let pe=oe?" (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)":"";Pe("`Module.".concat(C,"` has been replaced by `").concat(P,"`")+pe)}})}function Rn(C){Object.getOwnPropertyDescriptor(o,C)&&Pe("`Module.".concat(C,"` was supplied but `").concat(C,"` not included in INCOMING_MODULE_JS_API"))}function Nt(C){return C==="FS_createPath"||C==="FS_createDataFile"||C==="FS_createPreloadedFile"||C==="FS_unlink"||C==="addRunDependency"||C==="FS_createLazyFile"||C==="FS_createDevice"||C==="removeRunDependency"}function Ni(C,P){typeof globalThis<"u"&&Object.defineProperty(globalThis,C,{configurable:!0,get(){ht("`"+C+"` is not longer defined by emscripten. "+P)}})}Ni("buffer","Please use HEAP8.buffer or wasmMemory.buffer"),Ni("asm","Please use wasmExports instead");function Oo(C){typeof globalThis<"u"&&!Object.getOwnPropertyDescriptor(globalThis,C)&&Object.defineProperty(globalThis,C,{configurable:!0,get(){var P="`"+C+"` is a library symbol and not included by default; add it to your library.js __deps or to DEFAULT_LIBRARY_FUNCS_TO_INCLUDE on the command line",oe=C;oe.startsWith("_")||(oe="$"+C),P+=" (e.g. -sDEFAULT_LIBRARY_FUNCS_TO_INCLUDE='"+oe+"')",Nt(C)&&(P+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),ht(P)}}),Di(C)}function Di(C){Object.getOwnPropertyDescriptor(o,C)||Object.defineProperty(o,C,{configurable:!0,get(){var P="'"+C+"' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the Emscripten FAQ)";Nt(C)&&(P+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),Pe(P)}})}function on(C){this.name="ExitStatus",this.message="Program terminated with exit(".concat(C,")"),this.status=C}var In=C=>{for(;C.length>0;)C.shift()(o)};o.noExitRuntime;var Dt=C=>(b(typeof C=="number"),C>>>=0,"0x"+C.toString(16).padStart(8,"0")),ht=C=>{ht.shown||(ht.shown={}),ht.shown[C]||(ht.shown[C]=1,m&&(C="warning: "+C),Y(C))},_r=(C,P,oe)=>T.copyWithin(C,P,P+oe),Ro=()=>2147483648,ba=C=>{var P=I.buffer,oe=(C-P.byteLength+65535)/65536;try{return I.grow(oe),k(),1}catch(pe){Y("growMemory: Attempted to grow heap from ".concat(P.byteLength," bytes to ").concat(C," bytes, but got error: ").concat(pe))}},wa=C=>{var P=T.length;C>>>=0,b(C>P);var oe=Ro();if(C>oe)return Y("Cannot enlarge memory, requested ".concat(C," bytes, but the limit is ").concat(oe," bytes!")),!1;for(var pe=(ut,Vt)=>ut+(Vt-ut%Vt)%Vt,Ee=1;Ee<=4;Ee*=2){var Re=P*(1+.2/Ee);Re=Math.min(Re,C+100663296);var Ie=Math.min(oe,pe(Math.max(C,Re),65536)),Oe=ba(Ie);if(Oe)return!0}return Y("Failed to grow the heap from ".concat(P," bytes to ").concat(Ie," bytes, not enough memory!")),!1},Bn=0,$n=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0,_a=(C,P,oe)=>{for(var pe=P+oe,Ee=P;C[Ee]&&!(Ee>=pe);)++Ee;if(Ee-P>16&&C.buffer&&$n)return $n.decode(C.subarray(P,Ee));for(var Re="";P<Ee;){var Ie=C[P++];if(!(Ie&128)){Re+=String.fromCharCode(Ie);continue}var Oe=C[P++]&63;if((Ie&224)==192){Re+=String.fromCharCode((Ie&31)<<6|Oe);continue}var ut=C[P++]&63;if((Ie&240)==224?Ie=(Ie&15)<<12|Oe<<6|ut:((Ie&248)!=240&&ht("Invalid UTF-8 leading byte "+Dt(Ie)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!"),Ie=(Ie&7)<<18|Oe<<12|ut<<6|C[P++]&63),Ie<65536)Re+=String.fromCharCode(Ie);else{var Vt=Ie-65536;Re+=String.fromCharCode(55296|Vt>>10,56320|Vt&1023)}}return Re},xa=(C,P)=>(b(typeof C=="number"),C?_a(T,C,P):""),Sa=C=>{F=C,f(C,new on(C))},zi=(C,P)=>{if(F=C,Ne(),!P){var oe="program exited (with status: ".concat(C,"), but keepRuntimeAlive() is set (counter=").concat(Bn,") due to an async operation, so halting execution but not exiting the runtime or preventing further async execution (you can use emscripten_force_exit, if you want to force a true shutdown)");u(oe),Y(oe)}Sa(C)},Ui=zi,ka=C=>{Pe("fd_close called without SYSCALLS_REQUIRE_FILESYSTEM")},Io=(C,P)=>(b(C==C>>>0||C==(C|0)),b(P===(P|0)),P+2097152>>>0<4194305-!!C?(C>>>0)+P*4294967296:NaN);function sn(C,P,oe,pe,Ee){return Io(P,oe),70}var wt=[null,[],[]],_t=(C,P)=>{var oe=wt[C];b(oe),P===0||P===10?((C===1?V:Y)(_a(oe,0)),oe.length=0):oe.push(P)},nr=()=>{fn(0),wt[1].length&&_t(1,10),wt[2].length&&_t(2,10)},qi=(C,P,oe,pe)=>{for(var Ee=0,Re=0;Re<oe;Re++){var Ie=U[P>>2],Oe=U[P+4>>2];P+=8;for(var ut=0;ut<Oe;ut++)_t(C,T[Ie+ut]);Ee+=Oe}return U[pe>>2]=Ee,0},Yr=C=>{try{return C()}catch(P){Pe(P)}},Lo=C=>{if(C instanceof on||C=="unwind")return F;G(),C instanceof WebAssembly.RuntimeError&&Aa()<=0&&Y("Stack overflow detected.  You can try increasing -sSTACK_SIZE (currently set to 5242880)"),f(1,C)},we=()=>{},xr=C=>{if(z){Y("user callback triggered after runtime exited or application aborted.  Ignoring.");return}try{C(),we()}catch(P){Lo(P)}},ji=()=>{Bn+=1},rr=()=>{b(Bn>0),Bn-=1},be={instrumentWasmImports(C){var P=/^(_exec_decode|invoke_.*|__asyncjs__.*)$/;for(var oe in C)(function(pe){var Ee=C[pe];if(Ee.sig,typeof Ee=="function"){var Re=Ee.isAsync||P.test(pe);C[pe]=function(){var Ie=be.state;try{return Ee.apply(null,arguments)}finally{var Oe=Ie===be.State.Normal&&be.state===be.State.Disabled,ut=pe.startsWith("invoke_")&&!0;if(be.state!==Ie&&!Re&&!Oe&&!ut)throw new Error("import ".concat(pe," was not in ASYNCIFY_IMPORTS, but changed the state"))}}}})(oe)},instrumentWasmExports(C){var P={};for(var oe in C)(function(pe){var Ee=C[pe];typeof Ee=="function"?P[pe]=function(){be.exportCallStack.push(pe);try{return Ee.apply(null,arguments)}finally{if(!z){var Re=be.exportCallStack.pop();b(Re===pe),be.maybeStopUnwind()}}}:P[pe]=Ee})(oe);return P},State:{Normal:0,Unwinding:1,Rewinding:2,Disabled:3},state:0,StackSize:4096,currData:null,handleSleepReturnValue:0,exportCallStack:[],callStackNameToId:{},callStackIdToName:{},callStackId:0,asyncPromiseHandlers:null,sleepCallbacks:[],getCallStackId(C){var P=be.callStackNameToId[C];return P===void 0&&(P=be.callStackId++,be.callStackNameToId[C]=P,be.callStackIdToName[P]=C),P},maybeStopUnwind(){be.currData&&be.state===be.State.Unwinding&&be.exportCallStack.length===0&&(be.state=be.State.Normal,Yr(zt),typeof Fibers<"u"&&Fibers.trampoline())},whenDone(){return b(be.currData,"Tried to wait for an async operation when none is in progress."),b(!be.asyncPromiseHandlers,"Cannot have multiple async operations in flight at once"),new Promise((C,P)=>{be.asyncPromiseHandlers={resolve:C,reject:P}})},allocateData(){var C=Xr(12+be.StackSize);return be.setDataHeader(C,C+12,be.StackSize),be.setDataRewindFunc(C),C},setDataHeader(C,P,oe){U[C>>2]=P,U[C+4>>2]=P+oe},setDataRewindFunc(C){var P=be.exportCallStack[0],oe=be.getCallStackId(P);O[C+8>>2]=oe},getDataRewindFunc(C){var P=O[C+8>>2],oe=be.callStackIdToName[P],pe=Jt[oe];return pe},doRewind(C){var P=be.getDataRewindFunc(C);return P()},handleSleep(C){if(b(be.state!==be.State.Disabled,"Asyncify cannot be done during or after the runtime exits"),!z){if(be.state===be.State.Normal){var P=!1,oe=!1;C((pe=0)=>{if(b(!pe||typeof pe=="number"||typeof pe=="boolean"),!z&&(be.handleSleepReturnValue=pe,P=!0,!!oe)){b(!be.exportCallStack.length,"Waking up (starting to rewind) must be done from JS, without compiled code on the stack."),be.state=be.State.Rewinding,Yr(()=>Fa(be.currData)),typeof Browser<"u"&&Browser.mainLoop.func&&Browser.mainLoop.resume();var Ee,Re=!1;try{Ee=be.doRewind(be.currData)}catch(ut){Ee=ut,Re=!0}var Ie=!1;if(!be.currData){var Oe=be.asyncPromiseHandlers;Oe&&(be.asyncPromiseHandlers=null,(Re?Oe.reject:Oe.resolve)(Ee),Ie=!0)}if(Re&&!Ie)throw Ee}}),oe=!0,P||(be.state=be.State.Unwinding,be.currData=be.allocateData(),typeof Browser<"u"&&Browser.mainLoop.func&&Browser.mainLoop.pause(),Yr(()=>Ta(be.currData)))}else be.state===be.State.Rewinding?(be.state=be.State.Normal,Yr(Pa),Jr(be.currData),be.currData=null,be.sleepCallbacks.forEach(pe=>xr(pe))):Pe("invalid state: ".concat(be.state));return be.handleSleepReturnValue}},handleAsync(C){return be.handleSleep(P=>{C().then(P)})}},Sr=C=>{var P=o["_"+C];return b(P,"Cannot call unknown function "+C+", make sure it is exported"),P},ir=(C,P)=>{b(C.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),S.set(C,P)},xt=C=>{for(var P=0,oe=0;oe<C.length;++oe){var pe=C.charCodeAt(oe);pe<=127?P++:pe<=2047?P+=2:pe>=55296&&pe<=57343?(P+=4,++oe):P+=3}return P},Ca=(C,P,oe,pe)=>{if(b(typeof C=="string"),!(pe>0))return 0;for(var Ee=oe,Re=oe+pe-1,Ie=0;Ie<C.length;++Ie){var Oe=C.charCodeAt(Ie);if(Oe>=55296&&Oe<=57343){var ut=C.charCodeAt(++Ie);Oe=65536+((Oe&1023)<<10)|ut&1023}if(Oe<=127){if(oe>=Re)break;P[oe++]=Oe}else if(Oe<=2047){if(oe+1>=Re)break;P[oe++]=192|Oe>>6,P[oe++]=128|Oe&63}else if(Oe<=65535){if(oe+2>=Re)break;P[oe++]=224|Oe>>12,P[oe++]=128|Oe>>6&63,P[oe++]=128|Oe&63}else{if(oe+3>=Re)break;Oe>1114111&&ht("Invalid Unicode code point "+Dt(Oe)+" encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF)."),P[oe++]=240|Oe>>18,P[oe++]=128|Oe>>12&63,P[oe++]=128|Oe>>6&63,P[oe++]=128|Oe&63}}return P[oe]=0,oe-Ee},Ea=(C,P,oe)=>(b(typeof oe=="number","stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),Ca(C,T,P,oe)),wn=C=>{var P=xt(C)+1,oe=Zr(P);return Ea(C,oe,P),oe},Ft=(C,P,oe,pe,Ee)=>{var Re={string:Ut=>{var ei=0;return Ut!=null&&Ut!==0&&(ei=wn(Ut)),ei},array:Ut=>{var ei=Zr(Ut.length);return ir(Ut,ei),ei}};function Ie(Ut){return P==="string"?xa(Ut):P==="boolean"?!!Ut:Ut}var Oe=Sr(C),ut=[],Vt=0;if(b(P!=="array",'Return type should not be "array".'),pe)for(var St=0;St<pe.length;St++){var or=Re[oe[St]];or?(Vt===0&&(Vt=ar()),ut[St]=or(pe[St])):ut[St]=pe[St]}var Gr=be.currData,Qr=Oe.apply(null,ut);function Oa(Ut){return rr(),Vt!==0&&Wi(Vt),Ie(Ut)}var Er=Ee&&Ee.async;return ji(),be.currData!=Gr?(b(!(Gr&&be.currData),"We cannot start an async operation when one is already flight"),b(!(Gr&&!be.currData),"We cannot stop an async operation in flight"),b(Er,"The call to "+C+" is running asynchronously. If this was intended, add the async option to the ccall/cwrap call."),be.whenDone().then(Oa)):(Qr=Oa(Qr),Er?Promise.resolve(Qr):Qr)},No=(C,P,oe,pe)=>function(){return Ft(C,P,oe,arguments,pe)};function Ma(){Rn("fetchSettings")}var kr={emscripten_memcpy_js:_r,emscripten_resize_heap:wa,exit:Ui,fd_close:ka,fd_seek:sn,fd_write:qi};be.instrumentWasmImports(kr);var Jt=wr();o._exec_decode=Ue("exec_decode"),o._init_decode=Ue("init_decode");var Xr=o._malloc=Ue("malloc"),Jr=o._free=Ue("free"),fn=o._fflush=Ue("fflush"),Cr=()=>(Cr=Jt.emscripten_stack_init)(),Kr=()=>(Kr=Jt.emscripten_stack_get_end)(),ar=Ue("stackSave"),Wi=Ue("stackRestore"),Zr=Ue("stackAlloc"),Aa=()=>(Aa=Jt.emscripten_stack_get_current)();o.dynCall_iii=Ue("dynCall_iii"),o.dynCall_iidiiii=Ue("dynCall_iidiiii"),o.dynCall_vii=Ue("dynCall_vii"),o.dynCall_iiii=Ue("dynCall_iiii"),o.dynCall_ii=Ue("dynCall_ii"),o.dynCall_jiji=Ue("dynCall_jiji");var Ta=Ue("asyncify_start_unwind"),zt=Ue("asyncify_stop_unwind"),Fa=Ue("asyncify_start_rewind"),Pa=Ue("asyncify_stop_rewind");o.cwrap=No;var A=["writeI53ToI64","writeI53ToI64Clamped","writeI53ToI64Signaling","writeI53ToU64Clamped","writeI53ToU64Signaling","readI53FromI64","readI53FromU64","convertI32PairToI53","convertU32PairToI53","zeroMemory","isLeapYear","ydayFromDate","arraySum","addDays","setErrNo","inetPton4","inetNtop4","inetPton6","inetNtop6","readSockaddr","writeSockaddr","getHostByName","initRandomFill","randomFill","getCallstack","emscriptenLog","convertPCtoSourceLocation","readEmAsmArgs","jstoi_q","jstoi_s","getExecutableName","listenOnce","autoResumeAudioContext","dynCallLegacy","getDynCaller","dynCall","asmjsMangle","asyncLoad","alignMemory","mmapAlloc","handleAllocatorInit","HandleAllocator","getNativeTypeSize","STACK_SIZE","STACK_ALIGN","POINTER_SIZE","ASSERTIONS","uleb128Encode","generateFuncType","convertJsFunctionToWasm","getEmptyTableSlot","updateTableMap","getFunctionAddress","addFunction","removeFunction","reallyNegative","unSign","strLen","reSign","formatString","intArrayFromString","intArrayToString","AsciiToString","stringToAscii","UTF16ToString","stringToUTF16","lengthBytesUTF16","UTF32ToString","stringToUTF32","lengthBytesUTF32","stringToNewUTF8","registerKeyEventCallback","maybeCStringToJsString","findEventTarget","findCanvasEventTarget","getBoundingClientRect","fillMouseEventData","registerMouseEventCallback","registerWheelEventCallback","registerUiEventCallback","registerFocusEventCallback","fillDeviceOrientationEventData","registerDeviceOrientationEventCallback","fillDeviceMotionEventData","registerDeviceMotionEventCallback","screenOrientation","fillOrientationChangeEventData","registerOrientationChangeEventCallback","fillFullscreenChangeEventData","registerFullscreenChangeEventCallback","JSEvents_requestFullscreen","JSEvents_resizeCanvasForFullscreen","registerRestoreOldStyle","hideEverythingExceptGivenElement","restoreHiddenElements","setLetterbox","softFullscreenResizeWebGLRenderTarget","doRequestFullscreen","fillPointerlockChangeEventData","registerPointerlockChangeEventCallback","registerPointerlockErrorEventCallback","requestPointerLock","fillVisibilityChangeEventData","registerVisibilityChangeEventCallback","registerTouchEventCallback","fillGamepadEventData","registerGamepadEventCallback","registerBeforeUnloadEventCallback","fillBatteryEventData","battery","registerBatteryEventCallback","setCanvasElementSize","getCanvasElementSize","demangle","demangleAll","jsStackTrace","stackTrace","getEnvStrings","checkWasiClock","wasiRightsToMuslOFlags","wasiOFlagsToMuslOFlags","createDyncallWrapper","safeSetTimeout","setImmediateWrapped","clearImmediateWrapped","polyfillSetImmediate","getPromise","makePromise","idsToPromises","makePromiseCallback","ExceptionInfo","findMatchingCatch","setMainLoop","getSocketFromFD","getSocketAddress","heapObjectForWebGLType","heapAccessShiftForWebGLHeap","webgl_enable_ANGLE_instanced_arrays","webgl_enable_OES_vertex_array_object","webgl_enable_WEBGL_draw_buffers","webgl_enable_WEBGL_multi_draw","emscriptenWebGLGet","computeUnpackAlignedImageSize","colorChannelsInGlTextureFormat","emscriptenWebGLGetTexPixelData","__glGenObject","emscriptenWebGLGetUniform","webglGetUniformLocation","webglPrepareUniformLocationsBeforeFirstUse","webglGetLeftBracePos","emscriptenWebGLGetVertexAttrib","__glGetActiveAttribOrUniform","writeGLArray","registerWebGlEventCallback","SDL_unicode","SDL_ttfContext","SDL_audio","ALLOC_NORMAL","ALLOC_STACK","allocate","writeStringToMemory","writeAsciiToMemory"];A.forEach(Oo);var W=["run","addOnPreRun","addOnInit","addOnPreMain","addOnExit","addOnPostRun","addRunDependency","removeRunDependency","FS_createFolder","FS_createPath","FS_createLazyFile","FS_createLink","FS_createDevice","FS_readFile","out","err","callMain","abort","wasmMemory","wasmExports","stackAlloc","stackSave","stackRestore","getTempRet0","setTempRet0","writeStackCookie","checkStackCookie","convertI32PairToI53Checked","ptrToString","exitJS","getHeapMax","growMemory","ENV","MONTH_DAYS_REGULAR","MONTH_DAYS_LEAP","MONTH_DAYS_REGULAR_CUMULATIVE","MONTH_DAYS_LEAP_CUMULATIVE","ERRNO_CODES","ERRNO_MESSAGES","DNS","Protocols","Sockets","timers","warnOnce","UNWIND_CACHE","readEmAsmArgsArray","handleException","keepRuntimeAlive","runtimeKeepalivePush","runtimeKeepalivePop","callUserCallback","maybeExit","wasmTable","noExitRuntime","getCFunc","ccall","sigToWasmTypes","freeTableIndexes","functionsInTableMap","setValue","getValue","PATH","PATH_FS","UTF8Decoder","UTF8ArrayToString","UTF8ToString","stringToUTF8Array","stringToUTF8","lengthBytesUTF8","UTF16Decoder","stringToUTF8OnStack","writeArrayToMemory","JSEvents","specialHTMLTargets","currentFullscreenStrategy","restoreOldWindowedStyle","ExitStatus","flush_NO_FILESYSTEM","promiseMap","uncaughtExceptionCount","exceptionLast","exceptionCaught","Browser","wget","SYSCALLS","tempFixedLengthArray","miniTempWebGLFloatBuffers","miniTempWebGLIntBuffers","GL","emscripten_webgl_power_preferences","AL","GLUT","EGL","GLEW","IDBStore","runAndAbortIfError","Asyncify","Fibers","SDL","SDL_gfx","allocateUTF8","allocateUTF8OnStack"];W.forEach(Di);var ce;dt=function C(){ce||ke(),ce||(dt=C)};function ye(){Cr(),K()}function ke(){if(Ye>0||(ye(),de(),Ye>0))return;function C(){ce||(ce=!0,o.calledRun=!0,!z&&(me(),s(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),b(!o._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),ge()))}o.setStatus?(o.setStatus("Running..."),setTimeout(function(){setTimeout(function(){o.setStatus("")},1),C()},1)):C(),G()}function Ne(){var C=V,P=Y,oe=!1;V=Y=pe=>{oe=!0};try{nr()}catch(pe){}V=C,Y=P,oe&&(ht("stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the Emscripten FAQ), or make sure to emit a newline when you printf etc."),ht("(this may also be due to not including full filesystem support - try building with -sFORCE_FILESYSTEM)"))}if(o.preInit)for(typeof o.preInit=="function"&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return ke(),i.ready}})();const ua=await ow(),sw=ua.cwrap("init_decode","number",[],[]),lw=ua.cwrap("exec_decode","number",["number","number"],{async:!0}),Jp=2048,Kp=ua._malloc(Jp),uw=sw(),cw=async r=>{const i=ua._malloc(r.length*r.BYTES_PER_ELEMENT);ua.HEAPF32.set(r,i/r.BYTES_PER_ELEMENT),await lw(uw,i,Kp);const o=new Uint8Array(ua.HEAPU8.buffer,Kp,Jp);return new TextDecoder("utf8").decode(o).replaceAll("\0","").trim().split("\n").filter(s=>s.length>0).map(s=>{const u=s.split(",");return{db:Number(u[0]),dt:Number(u[1]),df:Number(u[2]),text:u[3]}})};var dw=(()=>{var r=import.meta.url;return async function(i={}){var o=i,s,u;o.ready=new Promise((A,W)=>{s=A,u=W}),["_exec_encode","_free","_malloc","_memory","_fflush","___indirect_function_table","onRuntimeInitialized"].forEach(A=>{Object.getOwnPropertyDescriptor(o.ready,A)||Object.defineProperty(o.ready,A,{get:()=>Pe("You are getting "+A+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js"),set:()=>Pe("You are setting "+A+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")})});var d=Object.assign({},o),f=(A,W)=>{throw W},h=typeof window=="object",p=typeof importScripts=="function",m=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",y=!h&&!m&&!p;if(o.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -sENVIRONMENT=web or -sENVIRONMENT=node)");var v="";function x(A){return o.locateFile?o.locateFile(A,v):v+A}var E,M,R;if(m){if(typeof process>"u"||!process.release||process.release.name!=="node")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");var j=process.versions.node,ie=j.split(".").slice(0,3);if(ie=ie[0]*1e4+ie[1]*100+ie[2].split("-")[0]*1,ie<16e4)throw new Error("This emscripten-generated code requires node v16.0.0 (detected v"+j+")");const{createRequire:A}=await co(async()=>{const{createRequire:W}=await Promise.resolve().then(()=>Ag);return{createRequire:W}},void 0);var B=A(import.meta.url),$=B("fs"),ae=B("path");p?v=ae.dirname(v)+"/":v=B("url").fileURLToPath(new URL("data:text/javascript;base64,aW1wb3J0ICogYXMgd2FzbSBmcm9tICIuL3BoYW50b21zZHJkc3BfYmcud2FzbSI7CmltcG9ydCB7IF9fd2JnX3NldF93YXNtIH0gZnJvbSAiLi9waGFudG9tc2RyZHNwX2JnLmpzIjsKX193Ymdfc2V0X3dhc20od2FzbSk7CmV4cG9ydCAqIGZyb20gIi4vcGhhbnRvbXNkcmRzcF9iZy5qcyI7Cgp3YXNtLl9fd2JpbmRnZW5fc3RhcnQoKTsK",import.meta.url)),E=(W,ce)=>(W=dn(W)?new URL(W):ae.normalize(W),$.readFileSync(W,ce?void 0:"utf8")),R=W=>{var ce=E(W,!0);return ce.buffer||(ce=new Uint8Array(ce)),b(ce.buffer),ce},M=(W,ce,ye,ke=!0)=>{W=dn(W)?new URL(W):ae.normalize(W),$.readFile(W,ke?void 0:"utf8",(Ne,C)=>{Ne?ye(Ne):ce(ke?C.buffer:C)})},!o.thisProgram&&process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),process.on("uncaughtException",W=>{if(W!=="unwind"&&!(W instanceof on)&&!(W.context instanceof on))throw W}),f=(W,ce)=>{throw process.exitCode=W,ce},o.inspect=()=>"[Emscripten Module object]"}else if(y){if(typeof process=="object"&&typeof B=="function"||typeof window=="object"||typeof importScripts=="function")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");typeof read<"u"&&(E=read),R=A=>{if(typeof readbuffer=="function")return new Uint8Array(readbuffer(A));let W=read(A,"binary");return b(typeof W=="object"),W},M=(A,W,ce)=>{setTimeout(()=>W(R(A)))},typeof clearTimeout>"u"&&(globalThis.clearTimeout=A=>{}),typeof setTimeout>"u"&&(globalThis.setTimeout=A=>typeof A=="function"?A():Pe()),typeof scriptArgs<"u"&&scriptArgs,typeof quit=="function"&&(f=(A,W)=>{throw setTimeout(()=>{if(!(W instanceof on)){let ce=W;W&&typeof W=="object"&&W.stack&&(ce=[W,W.stack]),Y("exiting due to exception: ".concat(ce))}quit(A)}),W}),typeof print<"u"&&(typeof console>"u"&&(console={}),console.log=print,console.warn=console.error=typeof printErr<"u"?printErr:print)}else if(h||p){if(p?v=self.location.href:typeof document<"u"&&document.currentScript&&(v=document.currentScript.src),r&&(v=r),v.indexOf("blob:")!==0?v=v.substr(0,v.replace(/[?#].*/,"").lastIndexOf("/")+1):v="",!(typeof window=="object"||typeof importScripts=="function"))throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");E=A=>{var W=new XMLHttpRequest;return W.open("GET",A,!1),W.send(null),W.responseText},p&&(R=A=>{var W=new XMLHttpRequest;return W.open("GET",A,!1),W.responseType="arraybuffer",W.send(null),new Uint8Array(W.response)}),M=(A,W,ce)=>{var ye=new XMLHttpRequest;ye.open("GET",A,!0),ye.responseType="arraybuffer",ye.onload=()=>{if(ye.status==200||ye.status==0&&ye.response){W(ye.response);return}ce()},ye.onerror=ce,ye.send(null)}}else throw new Error("environment detection error");var V=o.print||console.log.bind(console),Y=o.printErr||console.error.bind(console);Object.assign(o,d),d=null,Ea(),o.arguments&&o.arguments,Xe("arguments","arguments_"),o.thisProgram&&o.thisProgram,Xe("thisProgram","thisProgram"),o.quit&&(f=o.quit),Xe("quit","quit_"),b(typeof o.memoryInitializerPrefixURL>"u","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),b(typeof o.pthreadMainPrefixURL>"u","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),b(typeof o.cdInitializerPrefixURL>"u","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),b(typeof o.filePackagePrefixURL>"u","Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),b(typeof o.read>"u","Module.read option was removed (modify read_ in JS)"),b(typeof o.readAsync>"u","Module.readAsync option was removed (modify readAsync in JS)"),b(typeof o.readBinary>"u","Module.readBinary option was removed (modify readBinary in JS)"),b(typeof o.setWindowTitle>"u","Module.setWindowTitle option was removed (modify emscripten_set_window_title in JS)"),b(typeof o.TOTAL_MEMORY>"u","Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),Xe("asm","wasmExports"),Xe("read","read_"),Xe("readAsync","readAsync"),Xe("readBinary","readBinary"),Xe("setWindowTitle","setWindowTitle"),b(!y,"shell environment detected but not enabled at build time.  Add 'shell' to `-sENVIRONMENT` to enable.");var N;o.wasmBinary&&(N=o.wasmBinary),Xe("wasmBinary","wasmBinary"),typeof WebAssembly!="object"&&Pe("no native wasm support detected");var I,z=!1,F;function b(A,W){A||Pe("Assertion failed"+(W?": "+W:""))}var S,T,O,U;function k(){var A=I.buffer;o.HEAP8=S=new Int8Array(A),o.HEAP16=new Int16Array(A),o.HEAPU8=T=new Uint8Array(A),o.HEAPU16=new Uint16Array(A),o.HEAP32=O=new Int32Array(A),o.HEAPU32=U=new Uint32Array(A),o.HEAPF32=new Float32Array(A),o.HEAPF64=new Float64Array(A)}b(!o.STACK_SIZE,"STACK_SIZE can no longer be set at runtime.  Use -sSTACK_SIZE at link time"),b(typeof Int32Array<"u"&&typeof Float64Array<"u"&&Int32Array.prototype.subarray!=null&&Int32Array.prototype.set!=null,"JS engine does not provide full typed array support"),b(!o.wasmMemory,"Use of `wasmMemory` detected.  Use -sIMPORTED_MEMORY to define wasmMemory externally"),b(!o.INITIAL_MEMORY,"Detected runtime INITIAL_MEMORY setting.  Use -sIMPORTED_MEMORY to define wasmMemory dynamically");function K(){var A=Jt();b((A&3)==0),A==0&&(A+=4),U[A>>2]=34821223,U[A+4>>2]=2310721022,U[0]=1668509029}function G(){if(!z){var A=Jt();A==0&&(A+=4);var W=U[A>>2],ce=U[A+4>>2];(W!=34821223||ce!=2310721022)&&Pe("Stack overflow! Stack cookie has been overwritten at ".concat(Dt(A),", expected hex dwords 0x89BACDFE and 0x2135467, but received ").concat(Dt(ce)," ").concat(Dt(W))),U[0]!=1668509029&&Pe("Runtime error: The application has corrupted its heap memory area (address zero)!")}}(function(){var A=new Int16Array(1),W=new Int8Array(A.buffer);if(A[0]=25459,W[0]!==115||W[1]!==99)throw"Runtime error: expected the system to be little-endian! (Run with -sSUPPORT_BIG_ENDIAN to bypass)"})();var X=[],w=[],D=[],te=!1;function de(){if(o.preRun)for(typeof o.preRun=="function"&&(o.preRun=[o.preRun]);o.preRun.length;)_e(o.preRun.shift());In(X)}function me(){b(!te),te=!0,G(),In(w)}function ge(){if(G(),o.postRun)for(typeof o.postRun=="function"&&(o.postRun=[o.postRun]);o.postRun.length;)he(o.postRun.shift());In(D)}function _e(A){X.unshift(A)}function ze(A){w.unshift(A)}function he(A){D.unshift(A)}b(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),b(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),b(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),b(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var Ye=0,Ze=null,dt=null,bt={};function tr(A){Ye++,o.monitorRunDependencies&&o.monitorRunDependencies(Ye),b(!bt[A]),bt[A]=1,Ze===null&&typeof setInterval<"u"&&(Ze=setInterval(()=>{if(z){clearInterval(Ze),Ze=null;return}var W=!1;for(var ce in bt)W||(W=!0,Y("still waiting on run dependencies:")),Y("dependency: ".concat(ce));W&&Y("(end of list)")},1e4))}function et(A){if(Ye--,o.monitorRunDependencies&&o.monitorRunDependencies(Ye),b(bt[A]),delete bt[A],Ye==0&&(Ze!==null&&(clearInterval(Ze),Ze=null),dt)){var W=dt;dt=null,W()}}function Pe(A){o.onAbort&&o.onAbort(A),A="Aborted("+A+")",Y(A),z=!0,F=1,A.indexOf("RuntimeError: unreachable")>=0&&(A+='. "unreachable" may be due to ASYNCIFY_STACK_SIZE not being large enough (try increasing it)');var W=new WebAssembly.RuntimeError(A);throw u(W),W}var ve={error(){Pe("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with -sFORCE_FILESYSTEM")},init(){ve.error()},createDataFile(){ve.error()},createPreloadedFile(){ve.error()},createLazyFile(){ve.error()},open(){ve.error()},mkdev(){ve.error()},registerDevice(){ve.error()},analyzePath(){ve.error()},ErrnoError(){ve.error()}};o.FS_createDataFile=ve.createDataFile,o.FS_createPreloadedFile=ve.createPreloadedFile;var Xt="data:application/octet-stream;base64,",Ri=A=>A.startsWith(Xt),dn=A=>A.startsWith("file://");function Ue(A){return function(){b(te,"native function `".concat(A,"` called before runtime initialization"));var W=Ft[A];return b(W,"exported native function `".concat(A,"` not found")),W.apply(null,arguments)}}var ft;o.locateFile?(ft="encode.wasm",Ri(ft)||(ft=x(ft))):ft=new URL("/assets/encode-Boqet107.wasm",import.meta.url).href;function Ii(A){if(A==ft&&N)return new Uint8Array(N);if(R)return R(A);throw"both async and sync fetching of the wasm failed"}function ya(A){if(!N&&(h||p)){if(typeof fetch=="function"&&!dn(A))return fetch(A,{credentials:"same-origin"}).then(W=>{if(!W.ok)throw"failed to load wasm binary file at '"+A+"'";return W.arrayBuffer()}).catch(()=>Ii(A));if(M)return new Promise((W,ce)=>{M(A,ye=>W(new Uint8Array(ye)),ce)})}return Promise.resolve().then(()=>Ii(A))}function Li(A,W,ce){return ya(A).then(ye=>WebAssembly.instantiate(ye,W)).then(ye=>ye).then(ce,ye=>{Y("failed to asynchronously prepare wasm: ".concat(ye)),dn(ft)&&Y("warning: Loading from a file URI (".concat(ft,") is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing")),Pe(ye)})}function Po(A,W,ce,ye){return!A&&typeof WebAssembly.instantiateStreaming=="function"&&!Ri(W)&&!dn(W)&&!m&&typeof fetch=="function"?fetch(W,{credentials:"same-origin"}).then(ke=>{var Ne=WebAssembly.instantiateStreaming(ke,ce);return Ne.then(ye,function(C){return Y("wasm streaming compile failed: ".concat(C)),Y("falling back to ArrayBuffer instantiation"),Li(W,ce,ye)})}):Li(W,ce,ye)}function wr(){var A={env:wn,wasi_snapshot_preview1:wn};function W(ke,Ne){return Ft=ke.exports,Ft=we.instrumentWasmExports(Ft),I=Ft.memory,b(I,"memory not found in wasm exports"),k(),ze(Ft.__wasm_call_ctors),et("wasm-instantiate"),Ft}tr("wasm-instantiate");var ce=o;function ye(ke){b(o===ce,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),ce=null,W(ke.instance)}if(o.instantiateWasm)try{return o.instantiateWasm(A,W)}catch(ke){Y("Module.instantiateWasm callback failed with error: ".concat(ke)),u(ke)}return Po(N,ft,A,ye).catch(u),{}}function Xe(A,W,ce=!0){Object.getOwnPropertyDescriptor(o,A)||Object.defineProperty(o,A,{configurable:!0,get(){let ye=ce?" (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)":"";Pe("`Module.".concat(A,"` has been replaced by `").concat(W,"`")+ye)}})}function Rn(A){Object.getOwnPropertyDescriptor(o,A)&&Pe("`Module.".concat(A,"` was supplied but `").concat(A,"` not included in INCOMING_MODULE_JS_API"))}function Nt(A){return A==="FS_createPath"||A==="FS_createDataFile"||A==="FS_createPreloadedFile"||A==="FS_unlink"||A==="addRunDependency"||A==="FS_createLazyFile"||A==="FS_createDevice"||A==="removeRunDependency"}function Ni(A,W){typeof globalThis<"u"&&Object.defineProperty(globalThis,A,{configurable:!0,get(){ht("`"+A+"` is not longer defined by emscripten. "+W)}})}Ni("buffer","Please use HEAP8.buffer or wasmMemory.buffer"),Ni("asm","Please use wasmExports instead");function Oo(A){typeof globalThis<"u"&&!Object.getOwnPropertyDescriptor(globalThis,A)&&Object.defineProperty(globalThis,A,{configurable:!0,get(){var W="`"+A+"` is a library symbol and not included by default; add it to your library.js __deps or to DEFAULT_LIBRARY_FUNCS_TO_INCLUDE on the command line",ce=A;ce.startsWith("_")||(ce="$"+A),W+=" (e.g. -sDEFAULT_LIBRARY_FUNCS_TO_INCLUDE='"+ce+"')",Nt(A)&&(W+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),ht(W)}}),Di(A)}function Di(A){Object.getOwnPropertyDescriptor(o,A)||Object.defineProperty(o,A,{configurable:!0,get(){var W="'"+A+"' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the Emscripten FAQ)";Nt(A)&&(W+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),Pe(W)}})}function on(A){this.name="ExitStatus",this.message="Program terminated with exit(".concat(A,")"),this.status=A}var In=A=>{for(;A.length>0;)A.shift()(o)};o.noExitRuntime;var Dt=A=>(b(typeof A=="number"),A>>>=0,"0x"+A.toString(16).padStart(8,"0")),ht=A=>{ht.shown||(ht.shown={}),ht.shown[A]||(ht.shown[A]=1,m&&(A="warning: "+A),Y(A))},_r=(A,W,ce)=>T.copyWithin(A,W,W+ce),Ro=()=>2147483648,ba=A=>{var W=I.buffer,ce=(A-W.byteLength+65535)/65536;try{return I.grow(ce),k(),1}catch(ye){Y("growMemory: Attempted to grow heap from ".concat(W.byteLength," bytes to ").concat(A," bytes, but got error: ").concat(ye))}},wa=A=>{var W=T.length;A>>>=0,b(A>W);var ce=Ro();if(A>ce)return Y("Cannot enlarge memory, requested ".concat(A," bytes, but the limit is ").concat(ce," bytes!")),!1;for(var ye=(oe,pe)=>oe+(pe-oe%pe)%pe,ke=1;ke<=4;ke*=2){var Ne=W*(1+.2/ke);Ne=Math.min(Ne,A+100663296);var C=Math.min(ce,ye(Math.max(A,Ne),65536)),P=ba(C);if(P)return!0}return Y("Failed to grow the heap from ".concat(W," bytes to ").concat(C," bytes, not enough memory!")),!1},Bn=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0,$n=(A,W,ce)=>{for(var ye=W+ce,ke=W;A[ke]&&!(ke>=ye);)++ke;if(ke-W>16&&A.buffer&&Bn)return Bn.decode(A.subarray(W,ke));for(var Ne="";W<ke;){var C=A[W++];if(!(C&128)){Ne+=String.fromCharCode(C);continue}var P=A[W++]&63;if((C&224)==192){Ne+=String.fromCharCode((C&31)<<6|P);continue}var oe=A[W++]&63;if((C&240)==224?C=(C&15)<<12|P<<6|oe:((C&248)!=240&&ht("Invalid UTF-8 leading byte "+Dt(C)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!"),C=(C&7)<<18|P<<12|oe<<6|A[W++]&63),C<65536)Ne+=String.fromCharCode(C);else{var pe=C-65536;Ne+=String.fromCharCode(55296|pe>>10,56320|pe&1023)}}return Ne},_a=(A,W)=>(b(typeof A=="number"),A?$n(T,A,W):""),xa=A=>{Pe("fd_close called without SYSCALLS_REQUIRE_FILESYSTEM")},Sa=(A,W)=>(b(A==A>>>0||A==(A|0)),b(W===(W|0)),W+2097152>>>0<4194305-!!A?(A>>>0)+W*4294967296:NaN);function zi(A,W,ce,ye,ke){return Sa(W,ce),70}var Ui=[null,[],[]],ka=(A,W)=>{var ce=Ui[A];b(ce),W===0||W===10?((A===1?V:Y)($n(ce,0)),ce.length=0):ce.push(W)},Io=(A,W,ce,ye)=>{for(var ke=0,Ne=0;Ne<ce;Ne++){var C=U[W>>2],P=U[W+4>>2];W+=8;for(var oe=0;oe<P;oe++)ka(A,T[C+oe]);ke+=P}return U[ye>>2]=ke,0},sn=A=>{try{return A()}catch(W){Pe(W)}},wt=A=>{if(A instanceof on||A=="unwind")return F;G(),A instanceof WebAssembly.RuntimeError&&Cr()<=0&&Y("Stack overflow detected.  You can try increasing -sSTACK_SIZE (currently set to 2097152)"),f(1,A)},_t=0,nr=()=>{},qi=A=>{if(z){Y("user callback triggered after runtime exited or application aborted.  Ignoring.");return}try{A(),nr()}catch(W){wt(W)}},Yr=()=>{_t+=1},Lo=()=>{b(_t>0),_t-=1},we={instrumentWasmImports(A){var W=/^(_exec_encode|invoke_.*|__asyncjs__.*)$/;for(var ce in A)(function(ye){var ke=A[ye];if(ke.sig,typeof ke=="function"){var Ne=ke.isAsync||W.test(ye);A[ye]=function(){var C=we.state;try{return ke.apply(null,arguments)}finally{var P=C===we.State.Normal&&we.state===we.State.Disabled,oe=ye.startsWith("invoke_")&&!0;if(we.state!==C&&!Ne&&!P&&!oe)throw new Error("import ".concat(ye," was not in ASYNCIFY_IMPORTS, but changed the state"))}}}})(ce)},instrumentWasmExports(A){var W={};for(var ce in A)(function(ye){var ke=A[ye];typeof ke=="function"?W[ye]=function(){we.exportCallStack.push(ye);try{return ke.apply(null,arguments)}finally{if(!z){var Ne=we.exportCallStack.pop();b(Ne===ye),we.maybeStopUnwind()}}}:W[ye]=ke})(ce);return W},State:{Normal:0,Unwinding:1,Rewinding:2,Disabled:3},state:0,StackSize:4096,currData:null,handleSleepReturnValue:0,exportCallStack:[],callStackNameToId:{},callStackIdToName:{},callStackId:0,asyncPromiseHandlers:null,sleepCallbacks:[],getCallStackId(A){var W=we.callStackNameToId[A];return W===void 0&&(W=we.callStackId++,we.callStackNameToId[A]=W,we.callStackIdToName[W]=A),W},maybeStopUnwind(){we.currData&&we.state===we.State.Unwinding&&we.exportCallStack.length===0&&(we.state=we.State.Normal,sn(ar),typeof Fibers<"u"&&Fibers.trampoline())},whenDone(){return b(we.currData,"Tried to wait for an async operation when none is in progress."),b(!we.asyncPromiseHandlers,"Cannot have multiple async operations in flight at once"),new Promise((A,W)=>{we.asyncPromiseHandlers={resolve:A,reject:W}})},allocateData(){var A=No(12+we.StackSize);return we.setDataHeader(A,A+12,we.StackSize),we.setDataRewindFunc(A),A},setDataHeader(A,W,ce){U[A>>2]=W,U[A+4>>2]=W+ce},setDataRewindFunc(A){var W=we.exportCallStack[0],ce=we.getCallStackId(W);O[A+8>>2]=ce},getDataRewindFunc(A){var W=O[A+8>>2],ce=we.callStackIdToName[W],ye=Ft[ce];return ye},doRewind(A){var W=we.getDataRewindFunc(A);return W()},handleSleep(A){if(b(we.state!==we.State.Disabled,"Asyncify cannot be done during or after the runtime exits"),!z){if(we.state===we.State.Normal){var W=!1,ce=!1;A((ye=0)=>{if(b(!ye||typeof ye=="number"||typeof ye=="boolean"),!z&&(we.handleSleepReturnValue=ye,W=!0,!!ce)){b(!we.exportCallStack.length,"Waking up (starting to rewind) must be done from JS, without compiled code on the stack."),we.state=we.State.Rewinding,sn(()=>Wi(we.currData)),typeof Browser<"u"&&Browser.mainLoop.func&&Browser.mainLoop.resume();var ke,Ne=!1;try{ke=we.doRewind(we.currData)}catch(oe){ke=oe,Ne=!0}var C=!1;if(!we.currData){var P=we.asyncPromiseHandlers;P&&(we.asyncPromiseHandlers=null,(Ne?P.reject:P.resolve)(ke),C=!0)}if(Ne&&!C)throw ke}}),ce=!0,W||(we.state=we.State.Unwinding,we.currData=we.allocateData(),typeof Browser<"u"&&Browser.mainLoop.func&&Browser.mainLoop.pause(),sn(()=>Kr(we.currData)))}else we.state===we.State.Rewinding?(we.state=we.State.Normal,sn(Zr),Ma(we.currData),we.currData=null,we.sleepCallbacks.forEach(ye=>qi(ye))):Pe("invalid state: ".concat(we.state));return we.handleSleepReturnValue}},handleAsync(A){return we.handleSleep(W=>{A().then(W)})}},xr=A=>{var W=o["_"+A];return b(W,"Cannot call unknown function "+A+", make sure it is exported"),W},ji=(A,W)=>{b(A.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),S.set(A,W)},rr=A=>{for(var W=0,ce=0;ce<A.length;++ce){var ye=A.charCodeAt(ce);ye<=127?W++:ye<=2047?W+=2:ye>=55296&&ye<=57343?(W+=4,++ce):W+=3}return W},be=(A,W,ce,ye)=>{if(b(typeof A=="string"),!(ye>0))return 0;for(var ke=ce,Ne=ce+ye-1,C=0;C<A.length;++C){var P=A.charCodeAt(C);if(P>=55296&&P<=57343){var oe=A.charCodeAt(++C);P=65536+((P&1023)<<10)|oe&1023}if(P<=127){if(ce>=Ne)break;W[ce++]=P}else if(P<=2047){if(ce+1>=Ne)break;W[ce++]=192|P>>6,W[ce++]=128|P&63}else if(P<=65535){if(ce+2>=Ne)break;W[ce++]=224|P>>12,W[ce++]=128|P>>6&63,W[ce++]=128|P&63}else{if(ce+3>=Ne)break;P>1114111&&ht("Invalid Unicode code point "+Dt(P)+" encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF)."),W[ce++]=240|P>>18,W[ce++]=128|P>>12&63,W[ce++]=128|P>>6&63,W[ce++]=128|P&63}}return W[ce]=0,ce-ke},Sr=(A,W,ce)=>(b(typeof ce=="number","stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),be(A,T,W,ce)),ir=A=>{var W=rr(A)+1,ce=fn(W);return Sr(A,ce,W),ce},xt=(A,W,ce,ye,ke)=>{var Ne={string:St=>{var or=0;return St!=null&&St!==0&&(or=ir(St)),or},array:St=>{var or=fn(St.length);return ji(St,or),or}};function C(St){return W==="string"?_a(St):W==="boolean"?!!St:St}var P=xr(A),oe=[],pe=0;if(b(W!=="array",'Return type should not be "array".'),ye)for(var Ee=0;Ee<ye.length;Ee++){var Re=Ne[ce[Ee]];Re?(pe===0&&(pe=Xr()),oe[Ee]=Re(ye[Ee])):oe[Ee]=ye[Ee]}var Ie=we.currData,Oe=P.apply(null,oe);function ut(St){return Lo(),pe!==0&&Jr(pe),C(St)}var Vt=ke&&ke.async;return Yr(),we.currData!=Ie?(b(!(Ie&&we.currData),"We cannot start an async operation when one is already flight"),b(!(Ie&&!we.currData),"We cannot stop an async operation in flight"),b(Vt,"The call to "+A+" is running asynchronously. If this was intended, add the async option to the ccall/cwrap call."),we.whenDone().then(ut)):(Oe=ut(Oe),Vt?Promise.resolve(Oe):Oe)},Ca=(A,W,ce,ye)=>function(){return xt(A,W,ce,arguments,ye)};function Ea(){Rn("fetchSettings")}var wn={emscripten_memcpy_js:_r,emscripten_resize_heap:wa,fd_close:xa,fd_seek:zi,fd_write:Io};we.instrumentWasmImports(wn);var Ft=wr();o._exec_encode=Ue("exec_encode"),o._fflush=Ue("fflush");var No=o._malloc=Ue("malloc"),Ma=o._free=Ue("free"),kr=()=>(kr=Ft.emscripten_stack_init)(),Jt=()=>(Jt=Ft.emscripten_stack_get_end)(),Xr=Ue("stackSave"),Jr=Ue("stackRestore"),fn=Ue("stackAlloc"),Cr=()=>(Cr=Ft.emscripten_stack_get_current)();o.dynCall_iidiiii=Ue("dynCall_iidiiii"),o.dynCall_vii=Ue("dynCall_vii"),o.dynCall_ii=Ue("dynCall_ii"),o.dynCall_iiii=Ue("dynCall_iiii"),o.dynCall_jiji=Ue("dynCall_jiji");var Kr=Ue("asyncify_start_unwind"),ar=Ue("asyncify_stop_unwind"),Wi=Ue("asyncify_start_rewind"),Zr=Ue("asyncify_stop_rewind");o.cwrap=Ca;var Aa=["writeI53ToI64","writeI53ToI64Clamped","writeI53ToI64Signaling","writeI53ToU64Clamped","writeI53ToU64Signaling","readI53FromI64","readI53FromU64","convertI32PairToI53","convertU32PairToI53","zeroMemory","isLeapYear","ydayFromDate","arraySum","addDays","setErrNo","inetPton4","inetNtop4","inetPton6","inetNtop6","readSockaddr","writeSockaddr","getHostByName","initRandomFill","randomFill","getCallstack","emscriptenLog","convertPCtoSourceLocation","readEmAsmArgs","jstoi_q","jstoi_s","getExecutableName","listenOnce","autoResumeAudioContext","dynCallLegacy","getDynCaller","dynCall","asmjsMangle","asyncLoad","alignMemory","mmapAlloc","handleAllocatorInit","HandleAllocator","getNativeTypeSize","STACK_SIZE","STACK_ALIGN","POINTER_SIZE","ASSERTIONS","uleb128Encode","generateFuncType","convertJsFunctionToWasm","getEmptyTableSlot","updateTableMap","getFunctionAddress","addFunction","removeFunction","reallyNegative","unSign","strLen","reSign","formatString","intArrayFromString","intArrayToString","AsciiToString","stringToAscii","UTF16ToString","stringToUTF16","lengthBytesUTF16","UTF32ToString","stringToUTF32","lengthBytesUTF32","stringToNewUTF8","registerKeyEventCallback","maybeCStringToJsString","findEventTarget","findCanvasEventTarget","getBoundingClientRect","fillMouseEventData","registerMouseEventCallback","registerWheelEventCallback","registerUiEventCallback","registerFocusEventCallback","fillDeviceOrientationEventData","registerDeviceOrientationEventCallback","fillDeviceMotionEventData","registerDeviceMotionEventCallback","screenOrientation","fillOrientationChangeEventData","registerOrientationChangeEventCallback","fillFullscreenChangeEventData","registerFullscreenChangeEventCallback","JSEvents_requestFullscreen","JSEvents_resizeCanvasForFullscreen","registerRestoreOldStyle","hideEverythingExceptGivenElement","restoreHiddenElements","setLetterbox","softFullscreenResizeWebGLRenderTarget","doRequestFullscreen","fillPointerlockChangeEventData","registerPointerlockChangeEventCallback","registerPointerlockErrorEventCallback","requestPointerLock","fillVisibilityChangeEventData","registerVisibilityChangeEventCallback","registerTouchEventCallback","fillGamepadEventData","registerGamepadEventCallback","registerBeforeUnloadEventCallback","fillBatteryEventData","battery","registerBatteryEventCallback","setCanvasElementSize","getCanvasElementSize","demangle","demangleAll","jsStackTrace","stackTrace","getEnvStrings","checkWasiClock","wasiRightsToMuslOFlags","wasiOFlagsToMuslOFlags","createDyncallWrapper","safeSetTimeout","setImmediateWrapped","clearImmediateWrapped","polyfillSetImmediate","getPromise","makePromise","idsToPromises","makePromiseCallback","ExceptionInfo","findMatchingCatch","setMainLoop","getSocketFromFD","getSocketAddress","heapObjectForWebGLType","heapAccessShiftForWebGLHeap","webgl_enable_ANGLE_instanced_arrays","webgl_enable_OES_vertex_array_object","webgl_enable_WEBGL_draw_buffers","webgl_enable_WEBGL_multi_draw","emscriptenWebGLGet","computeUnpackAlignedImageSize","colorChannelsInGlTextureFormat","emscriptenWebGLGetTexPixelData","__glGenObject","emscriptenWebGLGetUniform","webglGetUniformLocation","webglPrepareUniformLocationsBeforeFirstUse","webglGetLeftBracePos","emscriptenWebGLGetVertexAttrib","__glGetActiveAttribOrUniform","writeGLArray","registerWebGlEventCallback","SDL_unicode","SDL_ttfContext","SDL_audio","ALLOC_NORMAL","ALLOC_STACK","allocate","writeStringToMemory","writeAsciiToMemory"];Aa.forEach(Oo);var Ta=["run","addOnPreRun","addOnInit","addOnPreMain","addOnExit","addOnPostRun","addRunDependency","removeRunDependency","FS_createFolder","FS_createPath","FS_createLazyFile","FS_createLink","FS_createDevice","FS_readFile","out","err","callMain","abort","wasmMemory","wasmExports","stackAlloc","stackSave","stackRestore","getTempRet0","setTempRet0","writeStackCookie","checkStackCookie","convertI32PairToI53Checked","ptrToString","exitJS","getHeapMax","growMemory","ENV","MONTH_DAYS_REGULAR","MONTH_DAYS_LEAP","MONTH_DAYS_REGULAR_CUMULATIVE","MONTH_DAYS_LEAP_CUMULATIVE","ERRNO_CODES","ERRNO_MESSAGES","DNS","Protocols","Sockets","timers","warnOnce","UNWIND_CACHE","readEmAsmArgsArray","handleException","keepRuntimeAlive","runtimeKeepalivePush","runtimeKeepalivePop","callUserCallback","maybeExit","wasmTable","noExitRuntime","getCFunc","ccall","sigToWasmTypes","freeTableIndexes","functionsInTableMap","setValue","getValue","PATH","PATH_FS","UTF8Decoder","UTF8ArrayToString","UTF8ToString","stringToUTF8Array","stringToUTF8","lengthBytesUTF8","UTF16Decoder","stringToUTF8OnStack","writeArrayToMemory","JSEvents","specialHTMLTargets","currentFullscreenStrategy","restoreOldWindowedStyle","ExitStatus","flush_NO_FILESYSTEM","promiseMap","uncaughtExceptionCount","exceptionLast","exceptionCaught","Browser","wget","SYSCALLS","tempFixedLengthArray","miniTempWebGLFloatBuffers","miniTempWebGLIntBuffers","GL","emscripten_webgl_power_preferences","AL","GLUT","EGL","GLEW","IDBStore","runAndAbortIfError","Asyncify","Fibers","SDL","SDL_gfx","allocateUTF8","allocateUTF8OnStack"];Ta.forEach(Di);var zt;dt=function A(){zt||Pa(),zt||(dt=A)};function Fa(){kr(),K()}function Pa(){if(Ye>0||(Fa(),de(),Ye>0))return;function A(){zt||(zt=!0,o.calledRun=!0,!z&&(me(),s(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),b(!o._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),ge()))}o.setStatus?(o.setStatus("Running..."),setTimeout(function(){setTimeout(function(){o.setStatus("")},1),A()},1)):A(),G()}if(o.preInit)for(typeof o.preInit=="function"&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return Pa(),i.ready}})();(await dw()).cwrap("exec_encode","number",["string","number","number"],{async:!0});const Zp=(r,i,o)=>({endTime:i,insertTime:o,type:"exponentialRampToValue",value:r}),Gp=(r,i,o)=>({endTime:i,insertTime:o,type:"linearRampToValue",value:r}),qu=(r,i)=>({startTime:i,type:"setValue",value:r}),Qp=(r,i,o)=>({duration:o,startTime:i,type:"setValueCurve",values:r}),e0=(r,i,{startTime:o,target:s,timeConstant:u})=>s+(i-s)*Math.exp((o-r)/u),ca=r=>r.type==="exponentialRampToValue",Bs=r=>r.type==="linearRampToValue",Vr=r=>ca(r)||Bs(r),ju=r=>r.type==="setValue",hr=r=>r.type==="setValueCurve",$s=(r,i,o,s)=>{const u=r[i];return u===void 0?s:Vr(u)||ju(u)?u.value:hr(u)?u.values[u.values.length-1]:e0(o,$s(r,i-1,u.startTime,s),u)},t0=(r,i,o,s,u)=>o===void 0?[s.insertTime,u]:Vr(o)?[o.endTime,o.value]:ju(o)?[o.startTime,o.value]:hr(o)?[o.startTime+o.duration,o.values[o.values.length-1]]:[o.startTime,$s(r,i-1,o.startTime,u)],Wu=r=>r.type==="cancelAndHold",Vu=r=>r.type==="cancelScheduledValues",Hr=r=>Wu(r)||Vu(r)?r.cancelTime:ca(r)||Bs(r)?r.endTime:r.startTime,n0=(r,i,o,{endTime:s,value:u})=>o===u?u:0<o&&0<u||o<0&&u<0?o*(u/o)**((r-i)/(s-i)):0,r0=(r,i,o,{endTime:s,value:u})=>o+(r-i)/(s-i)*(u-o),fw=(r,i)=>{const o=Math.floor(i),s=Math.ceil(i);return o===s?r[o]:(1-(i-o))*r[o]+(1-(s-i))*r[s]},hw=(r,{duration:i,startTime:o,values:s})=>{const u=(r-o)/i*(s.length-1);return fw(s,u)},Ys=r=>r.type==="setTarget";class pw{constructor(i){this._automationEvents=[],this._currenTime=0,this._defaultValue=i}[Symbol.iterator](){return this._automationEvents[Symbol.iterator]()}add(i){const o=Hr(i);if(Wu(i)||Vu(i)){const s=this._automationEvents.findIndex(d=>Vu(i)&&hr(d)?d.startTime+d.duration>=o:Hr(d)>=o),u=this._automationEvents[s];if(s!==-1&&(this._automationEvents=this._automationEvents.slice(0,s)),Wu(i)){const d=this._automationEvents[this._automationEvents.length-1];if(u!==void 0&&Vr(u)){if(d!==void 0&&Ys(d))throw new Error("The internal list is malformed.");const f=d===void 0?u.insertTime:hr(d)?d.startTime+d.duration:Hr(d),h=d===void 0?this._defaultValue:hr(d)?d.values[d.values.length-1]:d.value,p=ca(u)?n0(o,f,h,u):r0(o,f,h,u),m=ca(u)?Zp(p,o,this._currenTime):Gp(p,o,this._currenTime);this._automationEvents.push(m)}if(d!==void 0&&Ys(d)&&this._automationEvents.push(qu(this.getValue(o),o)),d!==void 0&&hr(d)&&d.startTime+d.duration>o){const f=o-d.startTime,h=(d.values.length-1)/d.duration,p=Math.max(2,1+Math.ceil(f*h)),m=f/(p-1)*h,y=d.values.slice(0,p);if(m<1)for(let v=1;v<p;v+=1){const x=m*v%1;y[v]=d.values[v-1]*(1-x)+d.values[v]*x}this._automationEvents[this._automationEvents.length-1]=Qp(y,d.startTime,f)}}}else{const s=this._automationEvents.findIndex(f=>Hr(f)>o),u=s===-1?this._automationEvents[this._automationEvents.length-1]:this._automationEvents[s-1];if(u!==void 0&&hr(u)&&Hr(u)+u.duration>o)return!1;const d=ca(i)?Zp(i.value,i.endTime,this._currenTime):Bs(i)?Gp(i.value,o,this._currenTime):i;if(s===-1)this._automationEvents.push(d);else{if(hr(i)&&o+i.duration>Hr(this._automationEvents[s]))return!1;this._automationEvents.splice(s,0,d)}}return!0}flush(i){const o=this._automationEvents.findIndex(s=>Hr(s)>i);if(o>1){const s=this._automationEvents.slice(o-1),u=s[0];Ys(u)&&s.unshift(qu($s(this._automationEvents,o-2,u.startTime,this._defaultValue),u.startTime)),this._automationEvents=s}}getValue(i){if(this._automationEvents.length===0)return this._defaultValue;const o=this._automationEvents.findIndex(f=>Hr(f)>i),s=this._automationEvents[o],u=(o===-1?this._automationEvents.length:o)-1,d=this._automationEvents[u];if(d!==void 0&&Ys(d)&&(s===void 0||!Vr(s)||s.insertTime>i))return e0(i,$s(this._automationEvents,u-1,d.startTime,this._defaultValue),d);if(d!==void 0&&ju(d)&&(s===void 0||!Vr(s)))return d.value;if(d!==void 0&&hr(d)&&(s===void 0||!Vr(s)||d.startTime+d.duration>i))return i<d.startTime+d.duration?hw(i,d):d.values[d.values.length-1];if(d!==void 0&&Vr(d)&&(s===void 0||!Vr(s)))return d.value;if(s!==void 0&&ca(s)){const[f,h]=t0(this._automationEvents,u,d,s,this._defaultValue);return n0(i,f,h,s)}if(s!==void 0&&Bs(s)){const[f,h]=t0(this._automationEvents,u,d,s,this._defaultValue);return r0(i,f,h,s)}return this._defaultValue}}const mw=r=>({cancelTime:r,type:"cancelAndHold"}),gw=r=>({cancelTime:r,type:"cancelScheduledValues"}),vw=(r,i)=>({endTime:i,type:"exponentialRampToValue",value:r}),yw=(r,i)=>({endTime:i,type:"linearRampToValue",value:r}),bw=(r,i,o)=>({startTime:i,target:r,timeConstant:o,type:"setTarget"}),ww=()=>new DOMException("","AbortError"),_w=r=>(i,o,[s,u,d],f)=>{r(i[u],[o,s,d],h=>h[0]===o&&h[1]===s,f)},xw=r=>(i,o,s)=>{const u=[];for(let d=0;d<s.numberOfInputs;d+=1)u.push(new Set);r.set(i,{activeInputs:u,outputs:new Set,passiveInputs:new WeakMap,renderer:o})},Sw=r=>(i,o)=>{r.set(i,{activeInputs:new Set,passiveInputs:new WeakMap,renderer:o})},da=new WeakSet,i0=new WeakMap,a0=new WeakMap,o0=new WeakMap,s0=new WeakMap,l0=new WeakMap,u0=new WeakMap,Hu=new WeakMap,c0=new WeakMap,d0={construct(){return d0}},kw=r=>{try{const i=new Proxy(r,d0);new i}catch(i){return!1}return!0},f0=/^import(?:(?:[\s]+[\w]+|(?:[\s]+[\w]+[\s]*,)?[\s]*\{[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?(?:[\s]*,[\s]*[\w]+(?:[\s]+as[\s]+[\w]+)?)*[\s]*}|(?:[\s]+[\w]+[\s]*,)?[\s]*\*[\s]+as[\s]+[\w]+)[\s]+from)?(?:[\s]*)("([^"\\]|\\.)+"|'([^'\\]|\\.)+')(?:[\s]*);?/,h0=(r,i)=>{const o=[];let s=r.replace(/^[\s]+/,""),u=s.match(f0);for(;u!==null;){const d=u[1].slice(1,-1),f=u[0].replace(/([\s]+)?;?$/,"").replace(d,new URL(d,i).toString());o.push(f),s=s.slice(u[0].length).replace(/^[\s]+/,""),u=s.match(f0)}return[o.join(";"),s]},p0=r=>{if(r!==void 0&&!Array.isArray(r))throw new TypeError("The parameterDescriptors property of given value for processorCtor is not an array.")},m0=r=>{if(!kw(r))throw new TypeError("The given value for processorCtor should be a constructor.");if(r.prototype===null||typeof r.prototype!="object")throw new TypeError("The given value for processorCtor should have a prototype.")},Cw=(r,i,o,s,u,d,f,h,p,m,y,v,x)=>{let E=0;return(M,R,j={credentials:"omit"})=>{const ie=y.get(M);if(ie!==void 0&&ie.has(R))return Promise.resolve();const B=m.get(M);if(B!==void 0){const V=B.get(R);if(V!==void 0)return V}const $=d(M),ae=$.audioWorklet===void 0?u(R).then(([V,Y])=>{const[N,I]=h0(V,Y),z="".concat(N,";((a,b)=>{(a[b]=a[b]||[]).push((AudioWorkletProcessor,global,registerProcessor,sampleRate,self,window)=>{").concat(I,"\n})})(window,'_AWGS')");return o(z)}).then(()=>{const V=x._AWGS.pop();if(V===void 0)throw new SyntaxError;s($.currentTime,$.sampleRate,()=>V(class{},void 0,(Y,N)=>{if(Y.trim()==="")throw i();const I=c0.get($);if(I!==void 0){if(I.has(Y))throw i();m0(N),p0(N.parameterDescriptors),I.set(Y,N)}else m0(N),p0(N.parameterDescriptors),c0.set($,new Map([[Y,N]]))},$.sampleRate,void 0,void 0))}):Promise.all([u(R),Promise.resolve(r(v,v))]).then(([[V,Y],N])=>{const I=E+1;E=I;const[z,F]=h0(V,Y),b="".concat(z,";((AudioWorkletProcessor,registerProcessor)=>{").concat(F,"\n})(").concat(N?"AudioWorkletProcessor":"class extends AudioWorkletProcessor {__b=new WeakSet();constructor(){super();(p=>p.postMessage=(q=>(m,t)=>q.call(p,m,t?t.filter(u=>!this.__b.has(u)):t))(p.postMessage))(this.port)}}",",(n,p)=>registerProcessor(n,class extends p{").concat(N?"":"__c = (a) => a.forEach(e=>this.__b.add(e.buffer));","process(i,o,p){").concat(N?"":"i.forEach(this.__c);o.forEach(this.__c);this.__c(Object.values(p));","return super.process(i.map(j=>j.some(k=>k.length===0)?[]:j),o,p)}}));registerProcessor('__sac").concat(I,"',class extends AudioWorkletProcessor{process(){return !1}})"),S=new Blob([b],{type:"application/javascript; charset=utf-8"}),T=URL.createObjectURL(S);return $.audioWorklet.addModule(T,j).then(()=>{if(h($))return $;const O=f($);return O.audioWorklet.addModule(T,j).then(()=>O)}).then(O=>{if(p===null)throw new SyntaxError;try{new p(O,"__sac".concat(I))}catch(U){throw new SyntaxError}}).finally(()=>URL.revokeObjectURL(T))});return B===void 0?m.set(M,new Map([[R,ae]])):B.set(R,ae),ae.then(()=>{const V=y.get(M);V===void 0?y.set(M,new Set([R])):V.add(R)}).finally(()=>{const V=m.get(M);V!==void 0&&V.delete(R)}),ae}},Br=(r,i)=>{const o=r.get(i);if(o===void 0)throw new Error("A value with the given key could not be found.");return o},Xs=(r,i)=>{const o=Array.from(r).filter(i);if(o.length>1)throw Error("More than one element was found.");if(o.length===0)throw Error("No element was found.");const[s]=o;return r.delete(s),s},g0=(r,i,o,s)=>{const u=Br(r,i),d=Xs(u,f=>f[0]===o&&f[1]===s);return u.size===0&&r.delete(i),d},bo=r=>Br(u0,r),fa=r=>{if(da.has(r))throw new Error("The AudioNode is already stored.");da.add(r),bo(r).forEach(i=>i(!0))},v0=r=>"port"in r,wo=r=>{if(!da.has(r))throw new Error("The AudioNode is not stored.");da.delete(r),bo(r).forEach(i=>i(!1))},Bu=(r,i)=>{!v0(r)&&i.every(o=>o.size===0)&&wo(r)},Ew=(r,i,o,s,u,d,f,h,p,m,y,v,x)=>{const E=new WeakMap;return(M,R,j,ie,B)=>{const{activeInputs:$,passiveInputs:ae}=d(R),{outputs:V}=d(M),Y=h(M),N=I=>{const z=p(R),F=p(M);if(I){const b=g0(ae,M,j,ie);r($,M,b,!1),!B&&!v(M)&&o(F,z,j,ie),x(R)&&fa(R)}else{const b=s($,M,j,ie);i(ae,ie,b,!1),!B&&!v(M)&&u(F,z,j,ie);const S=f(R);if(S===0)y(R)&&Bu(R,$);else{const T=E.get(R);T!==void 0&&clearTimeout(T),E.set(R,setTimeout(()=>{y(R)&&Bu(R,$)},S*1e3))}}};return m(V,[R,j,ie],I=>I[0]===R&&I[1]===j&&I[2]===ie,!0)?(Y.add(N),y(M)?r($,M,[j,ie,N],!0):i(ae,ie,[M,j,N],!0),!0):!1}},Mw=r=>(i,o,[s,u,d],f)=>{const h=i.get(s);h===void 0?i.set(s,new Set([[u,o,d]])):r(h,[u,o,d],p=>p[0]===u&&p[1]===o,f)},Aw=r=>(i,o)=>{const s=r(i,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});o.connect(s).connect(i.destination);const u=()=>{o.removeEventListener("ended",u),o.disconnect(s),s.disconnect()};o.addEventListener("ended",u)},Tw={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",fftSize:2048,maxDecibels:-30,minDecibels:-100,smoothingTimeConstant:.8},Fw=(r,i,o,s,u,d)=>class extends r{constructor(f,h){const p=u(f),m={...Tw,...h},y=s(p,m),v=d(p)?i():null;super(f,!1,y,v),this._nativeAnalyserNode=y}get fftSize(){return this._nativeAnalyserNode.fftSize}set fftSize(f){this._nativeAnalyserNode.fftSize=f}get frequencyBinCount(){return this._nativeAnalyserNode.frequencyBinCount}get maxDecibels(){return this._nativeAnalyserNode.maxDecibels}set maxDecibels(f){const h=this._nativeAnalyserNode.maxDecibels;if(this._nativeAnalyserNode.maxDecibels=f,!(f>this._nativeAnalyserNode.minDecibels))throw this._nativeAnalyserNode.maxDecibels=h,o()}get minDecibels(){return this._nativeAnalyserNode.minDecibels}set minDecibels(f){const h=this._nativeAnalyserNode.minDecibels;if(this._nativeAnalyserNode.minDecibels=f,!(this._nativeAnalyserNode.maxDecibels>f))throw this._nativeAnalyserNode.minDecibels=h,o()}get smoothingTimeConstant(){return this._nativeAnalyserNode.smoothingTimeConstant}set smoothingTimeConstant(f){this._nativeAnalyserNode.smoothingTimeConstant=f}getByteFrequencyData(f){this._nativeAnalyserNode.getByteFrequencyData(f)}getByteTimeDomainData(f){this._nativeAnalyserNode.getByteTimeDomainData(f)}getFloatFrequencyData(f){this._nativeAnalyserNode.getFloatFrequencyData(f)}getFloatTimeDomainData(f){this._nativeAnalyserNode.getFloatTimeDomainData(f)}},rn=(r,i)=>r.context===i,Pw=(r,i,o)=>()=>{const s=new WeakMap,u=async(d,f)=>{let h=i(d);if(!rn(h,f)){const p={channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,fftSize:h.fftSize,maxDecibels:h.maxDecibels,minDecibels:h.minDecibels,smoothingTimeConstant:h.smoothingTimeConstant};h=r(f,p)}return s.set(f,h),await o(d,f,h),h};return{render(d,f){const h=s.get(f);return h!==void 0?Promise.resolve(h):u(d,f)}}},$u=r=>{try{r.copyToChannel(new Float32Array(1),0,-1)}catch(i){return!1}return!0},Ei=()=>new DOMException("","IndexSizeError"),y0=r=>{r.getChannelData=(i=>o=>{try{return i.call(r,o)}catch(s){throw s.code===12?Ei():s}})(r.getChannelData)},Ow={numberOfChannels:1},Rw=(r,i,o,s,u,d,f,h)=>{let p=null;return class e2{constructor(y){if(u===null)throw new Error("Missing the native OfflineAudioContext constructor.");const{length:v,numberOfChannels:x,sampleRate:E}={...Ow,...y};p===null&&(p=new u(1,1,44100));const M=s!==null&&i(d,d)?new s({length:v,numberOfChannels:x,sampleRate:E}):p.createBuffer(x,v,E);if(M.numberOfChannels===0)throw o();return typeof M.copyFromChannel!="function"?(f(M),y0(M)):i($u,()=>$u(M))||h(M),r.add(M),M}static[Symbol.hasInstance](y){return y!==null&&typeof y=="object"&&Object.getPrototypeOf(y)===e2.prototype||r.has(y)}}},yn=-3402823466385289e23,cn=-yn,pr=r=>da.has(r),Iw={buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1},Lw=(r,i,o,s,u,d,f,h)=>class extends r{constructor(p,m){const y=d(p),v={...Iw,...m},x=u(y,v),E=f(y),M=E?i():null;super(p,!1,x,M),this._audioBufferSourceNodeRenderer=M,this._isBufferNullified=!1,this._isBufferSet=v.buffer!==null,this._nativeAudioBufferSourceNode=x,this._onended=null,this._playbackRate=o(this,E,x.playbackRate,cn,yn)}get buffer(){return this._isBufferNullified?null:this._nativeAudioBufferSourceNode.buffer}set buffer(p){if(this._nativeAudioBufferSourceNode.buffer=p,p!==null){if(this._isBufferSet)throw s();this._isBufferSet=!0}}get loop(){return this._nativeAudioBufferSourceNode.loop}set loop(p){this._nativeAudioBufferSourceNode.loop=p}get loopEnd(){return this._nativeAudioBufferSourceNode.loopEnd}set loopEnd(p){this._nativeAudioBufferSourceNode.loopEnd=p}get loopStart(){return this._nativeAudioBufferSourceNode.loopStart}set loopStart(p){this._nativeAudioBufferSourceNode.loopStart=p}get onended(){return this._onended}set onended(p){const m=typeof p=="function"?h(this,p):null;this._nativeAudioBufferSourceNode.onended=m;const y=this._nativeAudioBufferSourceNode.onended;this._onended=y!==null&&y===m?p:y}get playbackRate(){return this._playbackRate}start(p=0,m=0,y){if(this._nativeAudioBufferSourceNode.start(p,m,y),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.start=y===void 0?[p,m]:[p,m,y]),this.context.state!=="closed"){fa(this);const v=()=>{this._nativeAudioBufferSourceNode.removeEventListener("ended",v),pr(this)&&wo(this)};this._nativeAudioBufferSourceNode.addEventListener("ended",v)}}stop(p=0){this._nativeAudioBufferSourceNode.stop(p),this._audioBufferSourceNodeRenderer!==null&&(this._audioBufferSourceNodeRenderer.stop=p)}},Nw=(r,i,o,s,u)=>()=>{const d=new WeakMap;let f=null,h=null;const p=async(m,y)=>{let v=o(m);const x=rn(v,y);if(!x){const E={buffer:v.buffer,channelCount:v.channelCount,channelCountMode:v.channelCountMode,channelInterpretation:v.channelInterpretation,loop:v.loop,loopEnd:v.loopEnd,loopStart:v.loopStart,playbackRate:v.playbackRate.value};v=i(y,E),f!==null&&v.start(...f),h!==null&&v.stop(h)}return d.set(y,v),x?await r(y,m.playbackRate,v.playbackRate):await s(y,m.playbackRate,v.playbackRate),await u(m,y,v),v};return{set start(m){f=m},set stop(m){h=m},render(m,y){const v=d.get(y);return v!==void 0?Promise.resolve(v):p(m,y)}}},Dw=r=>"playbackRate"in r,zw=r=>"frequency"in r&&"gain"in r,Uw=r=>"offset"in r,qw=r=>!("frequency"in r)&&"gain"in r,jw=r=>"detune"in r&&"frequency"in r&&!("gain"in r),Ww=r=>"pan"in r,bn=r=>Br(i0,r),_o=r=>Br(o0,r),Yu=(r,i)=>{const{activeInputs:o}=bn(r);o.forEach(u=>u.forEach(([d])=>{i.includes(r)||Yu(d,[...i,r])}));const s=Dw(r)?[r.playbackRate]:v0(r)?Array.from(r.parameters.values()):zw(r)?[r.Q,r.detune,r.frequency,r.gain]:Uw(r)?[r.offset]:qw(r)?[r.gain]:jw(r)?[r.detune,r.frequency]:Ww(r)?[r.pan]:[];for(const u of s){const d=_o(u);d!==void 0&&d.activeInputs.forEach(([f])=>Yu(f,i))}pr(r)&&wo(r)},Vw=r=>{Yu(r.destination,[])},Hw=r=>r===void 0||typeof r=="number"||typeof r=="string"&&(r==="balanced"||r==="interactive"||r==="playback"),Bw=(r,i,o,s,u,d,f,h,p)=>class extends r{constructor(m={}){if(p===null)throw new Error("Missing the native AudioContext constructor.");let y;try{y=new p(m)}catch(E){throw E.code===12&&E.message==="sampleRate is not in range"?o():E}if(y===null)throw s();if(!Hw(m.latencyHint))throw new TypeError("The provided value '".concat(m.latencyHint,"' is not a valid enum value of type AudioContextLatencyCategory."));if(m.sampleRate!==void 0&&y.sampleRate!==m.sampleRate)throw o();super(y,2);const{latencyHint:v}=m,{sampleRate:x}=y;if(this._baseLatency=typeof y.baseLatency=="number"?y.baseLatency:v==="balanced"?512/x:v==="interactive"||v===void 0?256/x:v==="playback"?1024/x:Math.max(2,Math.min(128,Math.round(v*x/128)))*128/x,this._nativeAudioContext=y,p.name==="webkitAudioContext"?(this._nativeGainNode=y.createGain(),this._nativeOscillatorNode=y.createOscillator(),this._nativeGainNode.gain.value=1e-37,this._nativeOscillatorNode.connect(this._nativeGainNode).connect(y.destination),this._nativeOscillatorNode.start()):(this._nativeGainNode=null,this._nativeOscillatorNode=null),this._state=null,y.state==="running"){this._state="suspended";const E=()=>{this._state==="suspended"&&(this._state=null),y.removeEventListener("statechange",E)};y.addEventListener("statechange",E)}}get baseLatency(){return this._baseLatency}get state(){return this._state!==null?this._state:this._nativeAudioContext.state}close(){return this.state==="closed"?this._nativeAudioContext.close().then(()=>{throw i()}):(this._state==="suspended"&&(this._state=null),this._nativeAudioContext.close().then(()=>{this._nativeGainNode!==null&&this._nativeOscillatorNode!==null&&(this._nativeOscillatorNode.stop(),this._nativeGainNode.disconnect(),this._nativeOscillatorNode.disconnect()),Vw(this)}))}createMediaElementSource(m){return new u(this,{mediaElement:m})}createMediaStreamDestination(){return new d(this)}createMediaStreamSource(m){return new f(this,{mediaStream:m})}createMediaStreamTrackSource(m){return new h(this,{mediaStreamTrack:m})}resume(){return this._state==="suspended"?new Promise((m,y)=>{const v=()=>{this._nativeAudioContext.removeEventListener("statechange",v),this._nativeAudioContext.state==="running"?m():this.resume().then(m,y)};this._nativeAudioContext.addEventListener("statechange",v)}):this._nativeAudioContext.resume().catch(m=>{throw m===void 0||m.code===15?i():m})}suspend(){return this._nativeAudioContext.suspend().catch(m=>{throw m===void 0?i():m})}},$w=(r,i,o,s,u,d,f,h)=>class extends r{constructor(p,m){const y=d(p),v=f(y),x=u(y,m,v),E=v?i(h):null;super(p,!1,x,E),this._isNodeOfNativeOfflineAudioContext=v,this._nativeAudioDestinationNode=x}get channelCount(){return this._nativeAudioDestinationNode.channelCount}set channelCount(p){if(this._isNodeOfNativeOfflineAudioContext)throw s();if(p>this._nativeAudioDestinationNode.maxChannelCount)throw o();this._nativeAudioDestinationNode.channelCount=p}get channelCountMode(){return this._nativeAudioDestinationNode.channelCountMode}set channelCountMode(p){if(this._isNodeOfNativeOfflineAudioContext)throw s();this._nativeAudioDestinationNode.channelCountMode=p}get maxChannelCount(){return this._nativeAudioDestinationNode.maxChannelCount}},Yw=r=>{const i=new WeakMap,o=async(s,u)=>{const d=u.destination;return i.set(u,d),await r(s,u,d),d};return{render(s,u){const d=i.get(u);return d!==void 0?Promise.resolve(d):o(s,u)}}},Xw=(r,i,o,s,u,d,f,h)=>(p,m)=>{const y=m.listener,v=()=>{const V=new Float32Array(1),Y=i(m,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:9}),N=f(m);let I=!1,z=[0,0,-1,0,1,0],F=[0,0,0];const b=()=>{if(I)return;I=!0;const U=s(m,256,9,0);U.onaudioprocess=({inputBuffer:k})=>{const K=[d(k,V,0),d(k,V,1),d(k,V,2),d(k,V,3),d(k,V,4),d(k,V,5)];K.some((X,w)=>X!==z[w])&&(y.setOrientation(...K),z=K);const G=[d(k,V,6),d(k,V,7),d(k,V,8)];G.some((X,w)=>X!==F[w])&&(y.setPosition(...G),F=G)},Y.connect(U)},S=U=>k=>{k!==z[U]&&(z[U]=k,y.setOrientation(...z))},T=U=>k=>{k!==F[U]&&(F[U]=k,y.setPosition(...F))},O=(U,k,K)=>{const G=o(m,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:k});G.connect(Y,0,U),G.start(),Object.defineProperty(G.offset,"defaultValue",{get(){return k}});const X=r({context:p},N,G.offset,cn,yn);return h(X,"value",w=>()=>w.call(X),w=>D=>{try{w.call(X,D)}catch(te){if(te.code!==9)throw te}b(),N&&K(D)}),X.cancelAndHoldAtTime=(w=>N?()=>{throw u()}:(...D)=>{const te=w.apply(X,D);return b(),te})(X.cancelAndHoldAtTime),X.cancelScheduledValues=(w=>N?()=>{throw u()}:(...D)=>{const te=w.apply(X,D);return b(),te})(X.cancelScheduledValues),X.exponentialRampToValueAtTime=(w=>N?()=>{throw u()}:(...D)=>{const te=w.apply(X,D);return b(),te})(X.exponentialRampToValueAtTime),X.linearRampToValueAtTime=(w=>N?()=>{throw u()}:(...D)=>{const te=w.apply(X,D);return b(),te})(X.linearRampToValueAtTime),X.setTargetAtTime=(w=>N?()=>{throw u()}:(...D)=>{const te=w.apply(X,D);return b(),te})(X.setTargetAtTime),X.setValueAtTime=(w=>N?()=>{throw u()}:(...D)=>{const te=w.apply(X,D);return b(),te})(X.setValueAtTime),X.setValueCurveAtTime=(w=>N?()=>{throw u()}:(...D)=>{const te=w.apply(X,D);return b(),te})(X.setValueCurveAtTime),X};return{forwardX:O(0,0,S(0)),forwardY:O(1,0,S(1)),forwardZ:O(2,-1,S(2)),positionX:O(6,0,T(0)),positionY:O(7,0,T(1)),positionZ:O(8,0,T(2)),upX:O(3,0,S(3)),upY:O(4,1,S(4)),upZ:O(5,0,S(5))}},{forwardX:x,forwardY:E,forwardZ:M,positionX:R,positionY:j,positionZ:ie,upX:B,upY:$,upZ:ae}=y.forwardX===void 0?v():y;return{get forwardX(){return x},get forwardY(){return E},get forwardZ(){return M},get positionX(){return R},get positionY(){return j},get positionZ(){return ie},get upX(){return B},get upY(){return $},get upZ(){return ae}}},Js=r=>"context"in r,xo=r=>Js(r[0]),Mi=(r,i,o,s)=>{for(const u of r)if(o(u)){if(s)return!1;throw Error("The set contains at least one similar element.")}return r.add(i),!0},b0=(r,i,[o,s],u)=>{Mi(r,[i,o,s],d=>d[0]===i&&d[1]===o,u)},w0=(r,[i,o,s],u)=>{const d=r.get(i);d===void 0?r.set(i,new Set([[o,s]])):Mi(d,[o,s],f=>f[0]===o,u)},ha=r=>"inputs"in r,Ks=(r,i,o,s)=>{if(ha(i)){const u=i.inputs[s];return r.connect(u,o,0),[u,o,0]}return r.connect(i,o,s),[i,o,s]},_0=(r,i,o)=>{for(const s of r)if(s[0]===i&&s[1]===o)return r.delete(s),s;return null},Jw=(r,i,o)=>Xs(r,s=>s[0]===i&&s[1]===o),x0=(r,i)=>{if(!bo(r).delete(i))throw new Error("Missing the expected event listener.")},S0=(r,i,o)=>{const s=Br(r,i),u=Xs(s,d=>d[0]===o);return s.size===0&&r.delete(i),u},Zs=(r,i,o,s)=>{ha(i)?r.disconnect(i.inputs[s],o,0):r.disconnect(i,o,s)},mt=r=>Br(a0,r),So=r=>Br(s0,r),Ai=r=>Hu.has(r),Gs=r=>!da.has(r),k0=(r,i)=>new Promise(o=>{if(i!==null)o(!0);else{const s=r.createScriptProcessor(256,1,1),u=r.createGain(),d=r.createBuffer(1,2,44100),f=d.getChannelData(0);f[0]=1,f[1]=1;const h=r.createBufferSource();h.buffer=d,h.loop=!0,h.connect(s).connect(r.destination),h.connect(u),h.disconnect(u),s.onaudioprocess=p=>{const m=p.inputBuffer.getChannelData(0);Array.prototype.some.call(m,y=>y===1)?o(!0):o(!1),h.stop(),s.onaudioprocess=null,h.disconnect(s),s.disconnect(r.destination)},h.start()}}),Xu=(r,i)=>{const o=new Map;for(const s of r)for(const u of s){const d=o.get(u);o.set(u,d===void 0?1:d+1)}o.forEach((s,u)=>i(u,s))},C0=r=>"context"in r,Kw=r=>{const i=new Map;r.connect=(o=>(s,u=0,d=0)=>{const f=C0(s)?o(s,u,d):o(s,u),h=i.get(s);return h===void 0?i.set(s,[{input:d,output:u}]):h.every(p=>p.input!==d||p.output!==u)&&h.push({input:d,output:u}),f})(r.connect.bind(r)),r.disconnect=(o=>(s,u,d)=>{if(o.apply(r),s===void 0)i.clear();else if(typeof s=="number")for(const[f,h]of i){const p=h.filter(m=>m.output!==s);p.length===0?i.delete(f):i.set(f,p)}else if(i.has(s))if(u===void 0)i.delete(s);else{const f=i.get(s);if(f!==void 0){const h=f.filter(p=>p.output!==u&&(p.input!==d||d===void 0));h.length===0?i.delete(s):i.set(s,h)}}for(const[f,h]of i)h.forEach(p=>{C0(f)?r.connect(f,p.output,p.input):r.connect(f,p.output)})})(r.disconnect)},Zw=(r,i,o,s)=>{const{activeInputs:u,passiveInputs:d}=_o(i),{outputs:f}=bn(r),h=bo(r),p=m=>{const y=mt(r),v=So(i);if(m){const x=S0(d,r,o);b0(u,r,x,!1),!s&&!Ai(r)&&y.connect(v,o)}else{const x=Jw(u,r,o);w0(d,x,!1),!s&&!Ai(r)&&y.disconnect(v,o)}};return Mi(f,[i,o],m=>m[0]===i&&m[1]===o,!0)?(h.add(p),pr(r)?b0(u,r,[o,p],!0):w0(d,[r,o,p],!0),!0):!1},Gw=(r,i,o,s)=>{const{activeInputs:u,passiveInputs:d}=bn(i),f=_0(u[s],r,o);return f===null?[g0(d,r,o,s)[2],!1]:[f[2],!0]},Qw=(r,i,o)=>{const{activeInputs:s,passiveInputs:u}=_o(i),d=_0(s,r,o);return d===null?[S0(u,r,o)[1],!1]:[d[2],!0]},Ju=(r,i,o,s,u)=>{const[d,f]=Gw(r,o,s,u);if(d!==null&&(x0(r,d),f&&!i&&!Ai(r)&&Zs(mt(r),mt(o),s,u)),pr(o)){const{activeInputs:h}=bn(o);Bu(o,h)}},Ku=(r,i,o,s)=>{const[u,d]=Qw(r,o,s);u!==null&&(x0(r,u),d&&!i&&!Ai(r)&&mt(r).disconnect(So(o),s))},e5=(r,i)=>{const o=bn(r),s=[];for(const u of o.outputs)xo(u)?Ju(r,i,...u):Ku(r,i,...u),s.push(u[0]);return o.outputs.clear(),s},t5=(r,i,o)=>{const s=bn(r),u=[];for(const d of s.outputs)d[1]===o&&(xo(d)?Ju(r,i,...d):Ku(r,i,...d),u.push(d[0]),s.outputs.delete(d));return u},n5=(r,i,o,s,u)=>{const d=bn(r);return Array.from(d.outputs).filter(f=>f[0]===o&&(s===void 0||f[1]===s)&&(u===void 0||f[2]===u)).map(f=>(xo(f)?Ju(r,i,...f):Ku(r,i,...f),d.outputs.delete(f),f[0]))},r5=(r,i,o,s,u,d,f,h,p,m,y,v,x,E,M,R)=>class extends m{constructor(j,ie,B,$){super(B),this._context=j,this._nativeAudioNode=B;const ae=y(j);v(ae)&&o(k0,()=>k0(ae,R))!==!0&&Kw(B),a0.set(this,B),u0.set(this,new Set),j.state!=="closed"&&ie&&fa(this),r(this,$,B)}get channelCount(){return this._nativeAudioNode.channelCount}set channelCount(j){this._nativeAudioNode.channelCount=j}get channelCountMode(){return this._nativeAudioNode.channelCountMode}set channelCountMode(j){this._nativeAudioNode.channelCountMode=j}get channelInterpretation(){return this._nativeAudioNode.channelInterpretation}set channelInterpretation(j){this._nativeAudioNode.channelInterpretation=j}get context(){return this._context}get numberOfInputs(){return this._nativeAudioNode.numberOfInputs}get numberOfOutputs(){return this._nativeAudioNode.numberOfOutputs}connect(j,ie=0,B=0){if(ie<0||ie>=this._nativeAudioNode.numberOfOutputs)throw u();const $=y(this._context),ae=M($);if(x(j)||E(j))throw d();if(Js(j)){const Y=mt(j);try{const N=Ks(this._nativeAudioNode,Y,ie,B),I=Gs(this);(ae||I)&&this._nativeAudioNode.disconnect(...N),this.context.state!=="closed"&&!I&&Gs(j)&&fa(j)}catch(N){throw N.code===12?d():N}if(i(this,j,ie,B,ae)){const N=p([this],j);Xu(N,s(ae))}return j}const V=So(j);if(V.name==="playbackRate"&&V.maxValue===1024)throw f();try{this._nativeAudioNode.connect(V,ie),(ae||Gs(this))&&this._nativeAudioNode.disconnect(V,ie)}catch(Y){throw Y.code===12?d():Y}if(Zw(this,j,ie,ae)){const Y=p([this],j);Xu(Y,s(ae))}}disconnect(j,ie,B){let $;const ae=y(this._context),V=M(ae);if(j===void 0)$=e5(this,V);else if(typeof j=="number"){if(j<0||j>=this.numberOfOutputs)throw u();$=t5(this,V,j)}else{if(ie!==void 0&&(ie<0||ie>=this.numberOfOutputs)||Js(j)&&B!==void 0&&(B<0||B>=j.numberOfInputs))throw u();if($=n5(this,V,j,ie,B),$.length===0)throw d()}for(const Y of $){const N=p([this],Y);Xu(N,h)}}},i5=(r,i,o,s,u,d,f,h,p,m,y,v,x)=>(E,M,R,j=null,ie=null)=>{const B=R.value,$=new pw(B),ae=M?s($):null,V={get defaultValue(){return B},get maxValue(){return j===null?R.maxValue:j},get minValue(){return ie===null?R.minValue:ie},get value(){return R.value},set value(Y){R.value=Y,V.setValueAtTime(Y,E.context.currentTime)},cancelAndHoldAtTime(Y){if(typeof R.cancelAndHoldAtTime=="function")ae===null&&$.flush(E.context.currentTime),$.add(u(Y)),R.cancelAndHoldAtTime(Y);else{const N=Array.from($).pop();ae===null&&$.flush(E.context.currentTime),$.add(u(Y));const I=Array.from($).pop();R.cancelScheduledValues(Y),N!==I&&I!==void 0&&(I.type==="exponentialRampToValue"?R.exponentialRampToValueAtTime(I.value,I.endTime):I.type==="linearRampToValue"?R.linearRampToValueAtTime(I.value,I.endTime):I.type==="setValue"?R.setValueAtTime(I.value,I.startTime):I.type==="setValueCurve"&&R.setValueCurveAtTime(I.values,I.startTime,I.duration))}return V},cancelScheduledValues(Y){return ae===null&&$.flush(E.context.currentTime),$.add(d(Y)),R.cancelScheduledValues(Y),V},exponentialRampToValueAtTime(Y,N){if(Y===0)throw new RangeError;if(!Number.isFinite(N)||N<0)throw new RangeError;const I=E.context.currentTime;return ae===null&&$.flush(I),Array.from($).length===0&&($.add(m(B,I)),R.setValueAtTime(B,I)),$.add(f(Y,N)),R.exponentialRampToValueAtTime(Y,N),V},linearRampToValueAtTime(Y,N){const I=E.context.currentTime;return ae===null&&$.flush(I),Array.from($).length===0&&($.add(m(B,I)),R.setValueAtTime(B,I)),$.add(h(Y,N)),R.linearRampToValueAtTime(Y,N),V},setTargetAtTime(Y,N,I){return ae===null&&$.flush(E.context.currentTime),$.add(p(Y,N,I)),R.setTargetAtTime(Y,N,I),V},setValueAtTime(Y,N){return ae===null&&$.flush(E.context.currentTime),$.add(m(Y,N)),R.setValueAtTime(Y,N),V},setValueCurveAtTime(Y,N,I){const z=Y instanceof Float32Array?Y:new Float32Array(Y);if(v!==null&&v.name==="webkitAudioContext"){const F=N+I,b=E.context.sampleRate,S=Math.ceil(N*b),T=Math.floor(F*b),O=T-S,U=new Float32Array(O);for(let K=0;K<O;K+=1){const G=(z.length-1)/I*((S+K)/b-N),X=Math.floor(G),w=Math.ceil(G);U[K]=X===w?z[X]:(1-(G-X))*z[X]+(1-(w-G))*z[w]}ae===null&&$.flush(E.context.currentTime),$.add(y(U,N,I)),R.setValueCurveAtTime(U,N,I);const k=T/b;k<F&&x(V,U[U.length-1],k),x(V,z[z.length-1],F)}else ae===null&&$.flush(E.context.currentTime),$.add(y(z,N,I)),R.setValueCurveAtTime(z,N,I);return V}};return o.set(V,R),i.set(V,E),r(V,ae),V},a5=r=>({replay(i){for(const o of r)if(o.type==="exponentialRampToValue"){const{endTime:s,value:u}=o;i.exponentialRampToValueAtTime(u,s)}else if(o.type==="linearRampToValue"){const{endTime:s,value:u}=o;i.linearRampToValueAtTime(u,s)}else if(o.type==="setTarget"){const{startTime:s,target:u,timeConstant:d}=o;i.setTargetAtTime(u,s,d)}else if(o.type==="setValue"){const{startTime:s,value:u}=o;i.setValueAtTime(u,s)}else if(o.type==="setValueCurve"){const{duration:s,startTime:u,values:d}=o;i.setValueCurveAtTime(d,u,s)}else throw new Error("Can't apply an unknown automation.")}}),o5=(r,i,o,s,u,d,f,h,p,m,y,v,x,E,M,R,j,ie,B,$)=>class extends M{constructor(ae,V){super(ae,V),this._nativeContext=ae,this._audioWorklet=r===void 0?void 0:{addModule:(Y,N)=>r(this,Y,N)}}get audioWorklet(){return this._audioWorklet}createAnalyser(){return new i(this)}createBiquadFilter(){return new u(this)}createBuffer(ae,V,Y){return new o({length:V,numberOfChannels:ae,sampleRate:Y})}createBufferSource(){return new s(this)}createChannelMerger(ae=6){return new d(this,{numberOfInputs:ae})}createChannelSplitter(ae=6){return new f(this,{numberOfOutputs:ae})}createConstantSource(){return new h(this)}createConvolver(){return new p(this)}createDelay(ae=1){return new y(this,{maxDelayTime:ae})}createDynamicsCompressor(){return new v(this)}createGain(){return new x(this)}createIIRFilter(ae,V){return new E(this,{feedback:V,feedforward:ae})}createOscillator(){return new R(this)}createPanner(){return new j(this)}createPeriodicWave(ae,V,Y={disableNormalization:!1}){return new ie(this,{...Y,imag:V,real:ae})}createStereoPanner(){return new B(this)}createWaveShaper(){return new $(this)}decodeAudioData(ae,V,Y){return m(this._nativeContext,ae).then(N=>(typeof V=="function"&&V(N),N),N=>{throw typeof Y=="function"&&Y(N),N})}},s5={Q:1,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",detune:0,frequency:350,gain:0,type:"lowpass"},l5=(r,i,o,s,u,d,f,h)=>class extends r{constructor(p,m){const y=d(p),v={...s5,...m},x=u(y,v),E=f(y),M=E?o():null;super(p,!1,x,M),this._Q=i(this,E,x.Q,cn,yn),this._detune=i(this,E,x.detune,1200*Math.log2(cn),-1200*Math.log2(cn)),this._frequency=i(this,E,x.frequency,p.sampleRate/2,0),this._gain=i(this,E,x.gain,40*Math.log10(cn),yn),this._nativeBiquadFilterNode=x,h(this,1)}get detune(){return this._detune}get frequency(){return this._frequency}get gain(){return this._gain}get Q(){return this._Q}get type(){return this._nativeBiquadFilterNode.type}set type(p){this._nativeBiquadFilterNode.type=p}getFrequencyResponse(p,m,y){try{this._nativeBiquadFilterNode.getFrequencyResponse(p,m,y)}catch(v){throw v.code===11?s():v}if(p.length!==m.length||m.length!==y.length)throw s()}},u5=(r,i,o,s,u)=>()=>{const d=new WeakMap,f=async(h,p)=>{let m=o(h);const y=rn(m,p);if(!y){const v={Q:m.Q.value,channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,detune:m.detune.value,frequency:m.frequency.value,gain:m.gain.value,type:m.type};m=i(p,v)}return d.set(p,m),y?(await r(p,h.Q,m.Q),await r(p,h.detune,m.detune),await r(p,h.frequency,m.frequency),await r(p,h.gain,m.gain)):(await s(p,h.Q,m.Q),await s(p,h.detune,m.detune),await s(p,h.frequency,m.frequency),await s(p,h.gain,m.gain)),await u(h,p,m),m};return{render(h,p){const m=d.get(p);return m!==void 0?Promise.resolve(m):f(h,p)}}},c5=(r,i)=>(o,s)=>{const u=i.get(o);if(u!==void 0)return u;const d=r.get(o);if(d!==void 0)return d;try{const f=s();return f instanceof Promise?(r.set(o,f),f.catch(()=>!1).then(h=>(r.delete(o),i.set(o,h),h))):(i.set(o,f),f)}catch(f){return i.set(o,!1),!1}},d5={channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:6},f5=(r,i,o,s,u)=>class extends r{constructor(d,f){const h=s(d),p={...d5,...f},m=o(h,p),y=u(h)?i():null;super(d,!1,m,y)}},h5=(r,i,o)=>()=>{const s=new WeakMap,u=async(d,f)=>{let h=i(d);if(!rn(h,f)){const p={channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,numberOfInputs:h.numberOfInputs};h=r(f,p)}return s.set(f,h),await o(d,f,h),h};return{render(d,f){const h=s.get(f);return h!==void 0?Promise.resolve(h):u(d,f)}}},p5={channelCount:6,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:6},m5=(r,i,o,s,u,d)=>class extends r{constructor(f,h){const p=s(f),m=d({...p5,...h}),y=o(p,m),v=u(p)?i():null;super(f,!1,y,v)}},g5=(r,i,o)=>()=>{const s=new WeakMap,u=async(d,f)=>{let h=i(d);if(!rn(h,f)){const p={channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,numberOfOutputs:h.numberOfOutputs};h=r(f,p)}return s.set(f,h),await o(d,f,h),h};return{render(d,f){const h=s.get(f);return h!==void 0?Promise.resolve(h):u(d,f)}}},v5=r=>(i,o,s)=>r(o,i,s),y5=r=>(i,o)=>{const s=r(i,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),u=i.createBuffer(1,2,44100);return s.buffer=u,s.loop=!0,s.connect(o),s.start(),()=>{s.stop(),s.disconnect(o)}},b5={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",offset:1},w5=(r,i,o,s,u,d,f)=>class extends r{constructor(h,p){const m=u(h),y={...b5,...p},v=s(m,y),x=d(m),E=x?o():null;super(h,!1,v,E),this._constantSourceNodeRenderer=E,this._nativeConstantSourceNode=v,this._offset=i(this,x,v.offset,cn,yn),this._onended=null}get offset(){return this._offset}get onended(){return this._onended}set onended(h){const p=typeof h=="function"?f(this,h):null;this._nativeConstantSourceNode.onended=p;const m=this._nativeConstantSourceNode.onended;this._onended=m!==null&&m===p?h:m}start(h=0){if(this._nativeConstantSourceNode.start(h),this._constantSourceNodeRenderer!==null&&(this._constantSourceNodeRenderer.start=h),this.context.state!=="closed"){fa(this);const p=()=>{this._nativeConstantSourceNode.removeEventListener("ended",p),pr(this)&&wo(this)};this._nativeConstantSourceNode.addEventListener("ended",p)}}stop(h=0){this._nativeConstantSourceNode.stop(h),this._constantSourceNodeRenderer!==null&&(this._constantSourceNodeRenderer.stop=h)}},_5=(r,i,o,s,u)=>()=>{const d=new WeakMap;let f=null,h=null;const p=async(m,y)=>{let v=o(m);const x=rn(v,y);if(!x){const E={channelCount:v.channelCount,channelCountMode:v.channelCountMode,channelInterpretation:v.channelInterpretation,offset:v.offset.value};v=i(y,E),f!==null&&v.start(f),h!==null&&v.stop(h)}return d.set(y,v),x?await r(y,m.offset,v.offset):await s(y,m.offset,v.offset),await u(m,y,v),v};return{set start(m){f=m},set stop(m){h=m},render(m,y){const v=d.get(y);return v!==void 0?Promise.resolve(v):p(m,y)}}},x5=r=>i=>(r[0]=i,r[0]),S5={buffer:null,channelCount:2,channelCountMode:"clamped-max",channelInterpretation:"speakers",disableNormalization:!1},k5=(r,i,o,s,u,d)=>class extends r{constructor(f,h){const p=s(f),m={...S5,...h},y=o(p,m),v=u(p)?i():null;super(f,!1,y,v),this._isBufferNullified=!1,this._nativeConvolverNode=y,m.buffer!==null&&d(this,m.buffer.duration)}get buffer(){return this._isBufferNullified?null:this._nativeConvolverNode.buffer}set buffer(f){if(this._nativeConvolverNode.buffer=f,f===null&&this._nativeConvolverNode.buffer!==null){const h=this._nativeConvolverNode.context;this._nativeConvolverNode.buffer=h.createBuffer(1,1,h.sampleRate),this._isBufferNullified=!0,d(this,0)}else this._isBufferNullified=!1,d(this,this._nativeConvolverNode.buffer===null?0:this._nativeConvolverNode.buffer.duration)}get normalize(){return this._nativeConvolverNode.normalize}set normalize(f){this._nativeConvolverNode.normalize=f}},C5=(r,i,o)=>()=>{const s=new WeakMap,u=async(d,f)=>{let h=i(d);if(!rn(h,f)){const p={buffer:h.buffer,channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,disableNormalization:!h.normalize};h=r(f,p)}return s.set(f,h),ha(h)?await o(d,f,h.inputs[0]):await o(d,f,h),h};return{render(d,f){const h=s.get(f);return h!==void 0?Promise.resolve(h):u(d,f)}}},E5=()=>new DOMException("","DataCloneError"),E0=r=>{const{port1:i,port2:o}=new MessageChannel;return new Promise(s=>{const u=()=>{o.onmessage=null,i.close(),o.close(),s()};o.onmessage=()=>u();try{i.postMessage(r,[r])}catch(d){}finally{u()}})},M5=(r,i,o,s,u,d,f,h,p,m,y)=>(v,x)=>{const E=f(v)?v:d(v);if(u.has(x)){const M=o();return Promise.reject(M)}try{u.add(x)}catch(M){}return i(p,()=>p(E))?E.decodeAudioData(x).then(M=>(E0(x).catch(()=>{}),i(h,()=>h(M))||y(M),r.add(M),M)):new Promise((M,R)=>{const j=async()=>{try{await E0(x)}catch(B){}},ie=B=>{R(B),j()};try{E.decodeAudioData(x,B=>{typeof B.copyFromChannel!="function"&&(m(B),y0(B)),r.add(B),j().then(()=>M(B))},B=>{ie(B===null?s():B)})}catch(B){ie(B)}})},A5=(r,i,o,s,u,d,f,h)=>(p,m)=>{const y=i.get(p);if(y===void 0)throw new Error("Missing the expected cycle count.");const v=d(p.context),x=h(v);if(y===m){if(i.delete(p),!x&&f(p)){const E=s(p),{outputs:M}=o(p);for(const R of M)if(xo(R)){const j=s(R[0]);r(E,j,R[1],R[2])}else{const j=u(R[0]);E.connect(j,R[1])}}}else i.set(p,y-m)},T5={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",delayTime:0,maxDelayTime:1},F5=(r,i,o,s,u,d,f)=>class extends r{constructor(h,p){const m=u(h),y={...T5,...p},v=s(m,y),x=d(m),E=x?o(y.maxDelayTime):null;super(h,!1,v,E),this._delayTime=i(this,x,v.delayTime),f(this,y.maxDelayTime)}get delayTime(){return this._delayTime}},P5=(r,i,o,s,u)=>d=>{const f=new WeakMap,h=async(p,m)=>{let y=o(p);const v=rn(y,m);if(!v){const x={channelCount:y.channelCount,channelCountMode:y.channelCountMode,channelInterpretation:y.channelInterpretation,delayTime:y.delayTime.value,maxDelayTime:d};y=i(m,x)}return f.set(m,y),v?await r(m,p.delayTime,y.delayTime):await s(m,p.delayTime,y.delayTime),await u(p,m,y),y};return{render(p,m){const y=f.get(m);return y!==void 0?Promise.resolve(y):h(p,m)}}},O5=r=>(i,o,s,u)=>r(i[u],d=>d[0]===o&&d[1]===s),R5=r=>"delayTime"in r,I5=(r,i,o)=>function s(u,d){const f=Js(d)?d:o(r,d);if(R5(f))return[];if(u[0]===f)return[u];if(u.includes(f))return[];const{outputs:h}=i(f);return Array.from(h).map(p=>s([...u,f],p[0])).reduce((p,m)=>p.concat(m),[])},L5={attack:.003,channelCount:2,channelCountMode:"clamped-max",channelInterpretation:"speakers",knee:30,ratio:12,release:.25,threshold:-24},N5=(r,i,o,s,u,d,f,h)=>class extends r{constructor(p,m){const y=d(p),v={...L5,...m},x=s(y,v),E=f(y),M=E?o():null;super(p,!1,x,M),this._attack=i(this,E,x.attack),this._knee=i(this,E,x.knee),this._nativeDynamicsCompressorNode=x,this._ratio=i(this,E,x.ratio),this._release=i(this,E,x.release),this._threshold=i(this,E,x.threshold),h(this,.006)}get attack(){return this._attack}get channelCount(){return this._nativeDynamicsCompressorNode.channelCount}set channelCount(p){const m=this._nativeDynamicsCompressorNode.channelCount;if(this._nativeDynamicsCompressorNode.channelCount=p,p>2)throw this._nativeDynamicsCompressorNode.channelCount=m,u()}get channelCountMode(){return this._nativeDynamicsCompressorNode.channelCountMode}set channelCountMode(p){const m=this._nativeDynamicsCompressorNode.channelCountMode;if(this._nativeDynamicsCompressorNode.channelCountMode=p,p==="max")throw this._nativeDynamicsCompressorNode.channelCountMode=m,u()}get knee(){return this._knee}get ratio(){return this._ratio}get reduction(){return typeof this._nativeDynamicsCompressorNode.reduction.value=="number"?this._nativeDynamicsCompressorNode.reduction.value:this._nativeDynamicsCompressorNode.reduction}get release(){return this._release}get threshold(){return this._threshold}},D5=(r,i,o,s,u)=>()=>{const d=new WeakMap,f=async(h,p)=>{let m=o(h);const y=rn(m,p);if(!y){const v={attack:m.attack.value,channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,knee:m.knee.value,ratio:m.ratio.value,release:m.release.value,threshold:m.threshold.value};m=i(p,v)}return d.set(p,m),y?(await r(p,h.attack,m.attack),await r(p,h.knee,m.knee),await r(p,h.ratio,m.ratio),await r(p,h.release,m.release),await r(p,h.threshold,m.threshold)):(await s(p,h.attack,m.attack),await s(p,h.knee,m.knee),await s(p,h.ratio,m.ratio),await s(p,h.release,m.release),await s(p,h.threshold,m.threshold)),await u(h,p,m),m};return{render(h,p){const m=d.get(p);return m!==void 0?Promise.resolve(m):f(h,p)}}},z5=()=>new DOMException("","EncodingError"),U5=r=>i=>new Promise((o,s)=>{if(r===null){s(new SyntaxError);return}const u=r.document.head;if(u===null)s(new SyntaxError);else{const d=r.document.createElement("script"),f=new Blob([i],{type:"application/javascript"}),h=URL.createObjectURL(f),p=r.onerror,m=()=>{r.onerror=p,URL.revokeObjectURL(h)};r.onerror=(y,v,x,E,M)=>{if(v===h||v===r.location.href&&x===1&&E===1)return m(),s(M),!1;if(p!==null)return p(y,v,x,E,M)},d.onerror=()=>{m(),s(new SyntaxError)},d.onload=()=>{m(),o()},d.src=h,d.type="module",u.appendChild(d)}}),q5=r=>class{constructor(i){this._nativeEventTarget=i,this._listeners=new WeakMap}addEventListener(i,o,s){if(o!==null){let u=this._listeners.get(o);u===void 0&&(u=r(this,o),typeof o=="function"&&this._listeners.set(o,u)),this._nativeEventTarget.addEventListener(i,u,s)}}dispatchEvent(i){return this._nativeEventTarget.dispatchEvent(i)}removeEventListener(i,o,s){const u=o===null?void 0:this._listeners.get(o);this._nativeEventTarget.removeEventListener(i,u===void 0?null:u,s)}},j5=r=>(i,o,s)=>{Object.defineProperties(r,{currentFrame:{configurable:!0,get(){return Math.round(i*o)}},currentTime:{configurable:!0,get(){return i}}});try{return s()}finally{r!==null&&(delete r.currentFrame,delete r.currentTime)}},W5=r=>async i=>{try{const o=await fetch(i);if(o.ok)return[await o.text(),o.url]}catch(o){}throw r()},V5={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",gain:1},H5=(r,i,o,s,u,d)=>class extends r{constructor(f,h){const p=u(f),m={...V5,...h},y=s(p,m),v=d(p),x=v?o():null;super(f,!1,y,x),this._gain=i(this,v,y.gain,cn,yn)}get gain(){return this._gain}},B5=(r,i,o,s,u)=>()=>{const d=new WeakMap,f=async(h,p)=>{let m=o(h);const y=rn(m,p);if(!y){const v={channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,gain:m.gain.value};m=i(p,v)}return d.set(p,m),y?await r(p,h.gain,m.gain):await s(p,h.gain,m.gain),await u(h,p,m),m};return{render(h,p){const m=d.get(p);return m!==void 0?Promise.resolve(m):f(h,p)}}},$5=r=>i=>{const o=r(i);if(o.renderer===null)throw new Error("Missing the renderer of the given AudioNode in the audio graph.");return o.renderer},Y5=r=>i=>{var o;return(o=r.get(i))!==null&&o!==void 0?o:0},X5=r=>i=>{const o=r(i);if(o.renderer===null)throw new Error("Missing the renderer of the given AudioParam in the audio graph.");return o.renderer},an=()=>new DOMException("","InvalidStateError"),J5=r=>i=>{const o=r.get(i);if(o===void 0)throw an();return o},K5=(r,i)=>o=>{let s=r.get(o);if(s!==void 0)return s;if(i===null)throw new Error("Missing the native OfflineAudioContext constructor.");return s=new i(1,1,44100),r.set(o,s),s},Qs=()=>new DOMException("","InvalidAccessError"),Z5=r=>{r.getFrequencyResponse=(i=>(o,s,u)=>{if(o.length!==s.length||s.length!==u.length)throw Qs();return i.call(r,o,s,u)})(r.getFrequencyResponse)},G5={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers"},Q5=(r,i,o,s,u,d)=>class extends r{constructor(f,h){const p=s(f),m=u(p),y={...G5,...h},v=i(p,m?null:f.baseLatency,y),x=m?o(y.feedback,y.feedforward):null;super(f,!1,v,x),Z5(v),this._nativeIIRFilterNode=v,d(this,1)}getFrequencyResponse(f,h,p){return this._nativeIIRFilterNode.getFrequencyResponse(f,h,p)}},M0=(r,i,o,s,u,d,f,h,p,m,y)=>{const v=m.length;let x=h;for(let E=0;E<v;E+=1){let M=o[0]*m[E];for(let R=1;R<u;R+=1){const j=x-R&p-1;M+=o[R]*d[j],M-=r[R]*f[j]}for(let R=u;R<s;R+=1)M+=o[R]*d[x-R&p-1];for(let R=u;R<i;R+=1)M-=r[R]*f[x-R&p-1];d[x]=m[E],f[x]=M,x=x+1&p-1,y[E]=M}return x},e3=(r,i,o,s)=>{const u=o instanceof Float64Array?o:new Float64Array(o),d=s instanceof Float64Array?s:new Float64Array(s),f=u.length,h=d.length,p=Math.min(f,h);if(u[0]!==1){for(let M=0;M<f;M+=1)d[M]/=u[0];for(let M=1;M<h;M+=1)u[M]/=u[0]}const m=32,y=new Float32Array(m),v=new Float32Array(m),x=i.createBuffer(r.numberOfChannels,r.length,r.sampleRate),E=r.numberOfChannels;for(let M=0;M<E;M+=1){const R=r.getChannelData(M),j=x.getChannelData(M);y.fill(0),v.fill(0),M0(u,f,d,h,p,y,v,0,m,R,j)}return x},t3=(r,i,o,s,u)=>(d,f)=>{const h=new WeakMap;let p=null;const m=async(y,v)=>{let x=null,E=i(y);const M=rn(E,v);if(v.createIIRFilter===void 0?x=r(v,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}):M||(E=v.createIIRFilter(f,d)),h.set(v,x===null?E:x),x!==null){if(p===null){if(o===null)throw new Error("Missing the native OfflineAudioContext constructor.");const j=new o(y.context.destination.channelCount,y.context.length,v.sampleRate);p=(async()=>{await s(y,j,j.destination);const ie=await u(j);return e3(ie,v,d,f)})()}const R=await p;return x.buffer=R,x.start(0),x}return await s(y,v,E),E};return{render(y,v){const x=h.get(v);return x!==void 0?Promise.resolve(x):m(y,v)}}},n3=(r,i,o,s,u,d)=>f=>(h,p)=>{const m=r.get(h);if(m===void 0){if(!f&&d(h)){const y=s(h),{outputs:v}=o(h);for(const x of v)if(xo(x)){const E=s(x[0]);i(y,E,x[1],x[2])}else{const E=u(x[0]);y.disconnect(E,x[1])}}r.set(h,p)}else r.set(h,m+p)},r3=r=>i=>r!==null&&i instanceof r,i3=r=>i=>r!==null&&typeof r.AudioNode=="function"&&i instanceof r.AudioNode,a3=r=>i=>r!==null&&typeof r.AudioParam=="function"&&i instanceof r.AudioParam,o3=(r,i)=>o=>r(o)||i(o),s3=r=>i=>r!==null&&i instanceof r,l3=r=>r!==null&&r.isSecureContext,u3=(r,i,o,s)=>class extends r{constructor(u,d){const f=o(u),h=i(f,d);if(s(f))throw TypeError();super(u,!0,h,null),this._nativeMediaElementAudioSourceNode=h}get mediaElement(){return this._nativeMediaElementAudioSourceNode.mediaElement}},c3={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers"},d3=(r,i,o,s)=>class extends r{constructor(u,d){const f=o(u);if(s(f))throw new TypeError;const h={...c3,...d},p=i(f,h);super(u,!1,p,null),this._nativeMediaStreamAudioDestinationNode=p}get stream(){return this._nativeMediaStreamAudioDestinationNode.stream}},f3=(r,i,o,s)=>class extends r{constructor(u,d){const f=o(u),h=i(f,d);if(s(f))throw new TypeError;super(u,!0,h,null),this._nativeMediaStreamAudioSourceNode=h}get mediaStream(){return this._nativeMediaStreamAudioSourceNode.mediaStream}},h3=(r,i,o)=>class extends r{constructor(s,u){const d=o(s),f=i(d,u);super(s,!0,f,null)}},p3=(r,i,o,s,u,d)=>class extends o{constructor(f,h){super(f),this._nativeContext=f,l0.set(this,f),s(f)&&u.set(f,new Set),this._destination=new r(this,h),this._listener=i(this,f),this._onstatechange=null}get currentTime(){return this._nativeContext.currentTime}get destination(){return this._destination}get listener(){return this._listener}get onstatechange(){return this._onstatechange}set onstatechange(f){const h=typeof f=="function"?d(this,f):null;this._nativeContext.onstatechange=h;const p=this._nativeContext.onstatechange;this._onstatechange=p!==null&&p===h?f:p}get sampleRate(){return this._nativeContext.sampleRate}get state(){return this._nativeContext.state}},Zu=r=>{const i=new Uint32Array([1179011410,40,1163280727,544501094,16,131073,44100,176400,1048580,1635017060,4,0]);try{const o=r.decodeAudioData(i.buffer,()=>{});return o===void 0?!1:(o.catch(()=>{}),!0)}catch(o){}return!1},g3=(r,i)=>(o,s,u)=>{const d=new Set;return o.connect=(f=>(h,p=0,m=0)=>{const y=d.size===0;if(i(h))return f.call(o,h,p,m),r(d,[h,p,m],v=>v[0]===h&&v[1]===p&&v[2]===m,!0),y&&s(),h;f.call(o,h,p),r(d,[h,p],v=>v[0]===h&&v[1]===p,!0),y&&s()})(o.connect),o.disconnect=(f=>(h,p,m)=>{const y=d.size>0;if(h===void 0)f.apply(o),d.clear();else if(typeof h=="number"){f.call(o,h);for(const x of d)x[1]===h&&d.delete(x)}else{i(h)?f.call(o,h,p,m):f.call(o,h,p);for(const x of d)x[0]===h&&(p===void 0||x[1]===p)&&(m===void 0||x[2]===m)&&d.delete(x)}const v=d.size===0;y&&v&&u()})(o.disconnect),o},ct=(r,i,o)=>{const s=i[o];s!==void 0&&s!==r[o]&&(r[o]=s)},Lt=(r,i)=>{ct(r,i,"channelCount"),ct(r,i,"channelCountMode"),ct(r,i,"channelInterpretation")},A0=r=>typeof r.getFloatTimeDomainData=="function",v3=r=>{r.getFloatTimeDomainData=i=>{const o=new Uint8Array(i.length);r.getByteTimeDomainData(o);const s=Math.max(o.length,r.fftSize);for(let u=0;u<s;u+=1)i[u]=(o[u]-128)*.0078125;return i}},y3=(r,i)=>(o,s)=>{const u=o.createAnalyser();if(Lt(u,s),!(s.maxDecibels>s.minDecibels))throw i();return ct(u,s,"fftSize"),ct(u,s,"maxDecibels"),ct(u,s,"minDecibels"),ct(u,s,"smoothingTimeConstant"),r(A0,()=>A0(u))||v3(u),u},b3=r=>r===null?null:r.hasOwnProperty("AudioBuffer")?r.AudioBuffer:null,yt=(r,i,o)=>{const s=i[o];s!==void 0&&s!==r[o].value&&(r[o].value=s)},w3=r=>{r.start=(i=>{let o=!1;return(s=0,u=0,d)=>{if(o)throw an();i.call(r,s,u,d),o=!0}})(r.start)},Gu=r=>{r.start=(i=>(o=0,s=0,u)=>{if(typeof u=="number"&&u<0||s<0||o<0)throw new RangeError("The parameters can't be negative.");i.call(r,o,s,u)})(r.start)},Qu=r=>{r.stop=(i=>(o=0)=>{if(o<0)throw new RangeError("The parameter can't be negative.");i.call(r,o)})(r.stop)},_3=(r,i,o,s,u,d,f,h,p,m,y)=>(v,x)=>{const E=v.createBufferSource();return Lt(E,x),yt(E,x,"playbackRate"),ct(E,x,"buffer"),ct(E,x,"loop"),ct(E,x,"loopEnd"),ct(E,x,"loopStart"),i(o,()=>o(v))||w3(E),i(s,()=>s(v))||p(E),i(u,()=>u(v))||m(E,v),i(d,()=>d(v))||Gu(E),i(f,()=>f(v))||y(E,v),i(h,()=>h(v))||Qu(E),r(v,E),E},x3=r=>r===null?null:r.hasOwnProperty("AudioContext")?r.AudioContext:r.hasOwnProperty("webkitAudioContext")?r.webkitAudioContext:null,S3=(r,i)=>(o,s,u)=>{const d=o.destination;if(d.channelCount!==s)try{d.channelCount=s}catch(h){}u&&d.channelCountMode!=="explicit"&&(d.channelCountMode="explicit"),d.maxChannelCount===0&&Object.defineProperty(d,"maxChannelCount",{value:s});const f=r(o,{channelCount:s,channelCountMode:d.channelCountMode,channelInterpretation:d.channelInterpretation,gain:1});return i(f,"channelCount",h=>()=>h.call(f),h=>p=>{h.call(f,p);try{d.channelCount=p}catch(m){if(p>d.maxChannelCount)throw m}}),i(f,"channelCountMode",h=>()=>h.call(f),h=>p=>{h.call(f,p),d.channelCountMode=p}),i(f,"channelInterpretation",h=>()=>h.call(f),h=>p=>{h.call(f,p),d.channelInterpretation=p}),Object.defineProperty(f,"maxChannelCount",{get:()=>d.maxChannelCount}),f.connect(d),f},k3=r=>r===null?null:r.hasOwnProperty("AudioWorkletNode")?r.AudioWorkletNode:null,C3=(r,i)=>r===null?512:Math.max(512,Math.min(16384,Math.pow(2,Math.round(Math.log2(r*i))))),T0=(r,i)=>{const o=r.createBiquadFilter();return Lt(o,i),yt(o,i,"Q"),yt(o,i,"detune"),yt(o,i,"frequency"),yt(o,i,"gain"),ct(o,i,"type"),o},E3=(r,i)=>(o,s)=>{const u=o.createChannelMerger(s.numberOfInputs);return r!==null&&r.name==="webkitAudioContext"&&i(o,u),Lt(u,s),u},M3=r=>{const i=r.numberOfOutputs;Object.defineProperty(r,"channelCount",{get:()=>i,set:o=>{if(o!==i)throw an()}}),Object.defineProperty(r,"channelCountMode",{get:()=>"explicit",set:o=>{if(o!=="explicit")throw an()}}),Object.defineProperty(r,"channelInterpretation",{get:()=>"discrete",set:o=>{if(o!=="discrete")throw an()}})},ec=(r,i)=>{const o=r.createChannelSplitter(i.numberOfOutputs);return Lt(o,i),M3(o),o},A3=(r,i,o,s,u)=>(d,f)=>{if(d.createConstantSource===void 0)return o(d,f);const h=d.createConstantSource();return Lt(h,f),yt(h,f,"offset"),i(s,()=>s(d))||Gu(h),i(u,()=>u(d))||Qu(h),r(d,h),h},pa=(r,i)=>(r.connect=i.connect.bind(i),r.disconnect=i.disconnect.bind(i),r),T3=(r,i,o,s)=>(u,{offset:d,...f})=>{const h=u.createBuffer(1,2,44100),p=i(u,{buffer:null,channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",loop:!1,loopEnd:0,loopStart:0,playbackRate:1}),m=o(u,{...f,gain:d}),y=h.getChannelData(0);y[0]=1,y[1]=1,p.buffer=h,p.loop=!0;const v={get bufferSize(){},get channelCount(){return m.channelCount},set channelCount(M){m.channelCount=M},get channelCountMode(){return m.channelCountMode},set channelCountMode(M){m.channelCountMode=M},get channelInterpretation(){return m.channelInterpretation},set channelInterpretation(M){m.channelInterpretation=M},get context(){return m.context},get inputs(){return[]},get numberOfInputs(){return p.numberOfInputs},get numberOfOutputs(){return m.numberOfOutputs},get offset(){return m.gain},get onended(){return p.onended},set onended(M){p.onended=M},addEventListener(...M){return p.addEventListener(M[0],M[1],M[2])},dispatchEvent(...M){return p.dispatchEvent(M[0])},removeEventListener(...M){return p.removeEventListener(M[0],M[1],M[2])},start(M=0){p.start.call(p,M)},stop(M=0){p.stop.call(p,M)}},x=()=>p.connect(m),E=()=>p.disconnect(m);return r(u,p),s(pa(v,m),x,E)},F3=(r,i)=>(o,s)=>{const u=o.createConvolver();if(Lt(u,s),s.disableNormalization===u.normalize&&(u.normalize=!s.disableNormalization),ct(u,s,"buffer"),s.channelCount>2||(i(u,"channelCount",d=>()=>d.call(u),d=>f=>{if(f>2)throw r();return d.call(u,f)}),s.channelCountMode==="max"))throw r();return i(u,"channelCountMode",d=>()=>d.call(u),d=>f=>{if(f==="max")throw r();return d.call(u,f)}),u},F0=(r,i)=>{const o=r.createDelay(i.maxDelayTime);return Lt(o,i),yt(o,i,"delayTime"),o},P3=r=>(i,o)=>{const s=i.createDynamicsCompressor();if(Lt(s,o),o.channelCount>2||o.channelCountMode==="max")throw r();return yt(s,o,"attack"),yt(s,o,"knee"),yt(s,o,"ratio"),yt(s,o,"release"),yt(s,o,"threshold"),s},Vn=(r,i)=>{const o=r.createGain();return Lt(o,i),yt(o,i,"gain"),o},O3=r=>(i,o,s)=>{if(i.createIIRFilter===void 0)return r(i,o,s);const u=i.createIIRFilter(s.feedforward,s.feedback);return Lt(u,s),u};function R3(r,i){const o=i[0]*i[0]+i[1]*i[1];return[(r[0]*i[0]+r[1]*i[1])/o,(r[1]*i[0]-r[0]*i[1])/o]}function I3(r,i){return[r[0]*i[0]-r[1]*i[1],r[0]*i[1]+r[1]*i[0]]}function P0(r,i){let o=[0,0];for(let s=r.length-1;s>=0;s-=1)o=I3(o,i),o[0]+=r[s];return o}const L3=(r,i,o,s)=>(u,d,{channelCount:f,channelCountMode:h,channelInterpretation:p,feedback:m,feedforward:y})=>{const v=C3(d,u.sampleRate),x=m instanceof Float64Array?m:new Float64Array(m),E=y instanceof Float64Array?y:new Float64Array(y),M=x.length,R=E.length,j=Math.min(M,R);if(M===0||M>20)throw s();if(x[0]===0)throw i();if(R===0||R>20)throw s();if(E[0]===0)throw i();if(x[0]!==1){for(let N=0;N<R;N+=1)E[N]/=x[0];for(let N=1;N<M;N+=1)x[N]/=x[0]}const ie=o(u,v,f,f);ie.channelCount=f,ie.channelCountMode=h,ie.channelInterpretation=p;const B=32,$=[],ae=[],V=[];for(let N=0;N<f;N+=1){$.push(0);const I=new Float32Array(B),z=new Float32Array(B);I.fill(0),z.fill(0),ae.push(I),V.push(z)}ie.onaudioprocess=N=>{const I=N.inputBuffer,z=N.outputBuffer,F=I.numberOfChannels;for(let b=0;b<F;b+=1){const S=I.getChannelData(b),T=z.getChannelData(b);$[b]=M0(x,M,E,R,j,ae[b],V[b],$[b],B,S,T)}};const Y=u.sampleRate/2;return pa({get bufferSize(){return v},get channelCount(){return ie.channelCount},set channelCount(N){ie.channelCount=N},get channelCountMode(){return ie.channelCountMode},set channelCountMode(N){ie.channelCountMode=N},get channelInterpretation(){return ie.channelInterpretation},set channelInterpretation(N){ie.channelInterpretation=N},get context(){return ie.context},get inputs(){return[ie]},get numberOfInputs(){return ie.numberOfInputs},get numberOfOutputs(){return ie.numberOfOutputs},addEventListener(...N){return ie.addEventListener(N[0],N[1],N[2])},dispatchEvent(...N){return ie.dispatchEvent(N[0])},getFrequencyResponse(N,I,z){if(N.length!==I.length||I.length!==z.length)throw r();const F=N.length;for(let b=0;b<F;b+=1){const S=-Math.PI*(N[b]/Y),T=[Math.cos(S),Math.sin(S)],O=P0(E,T),U=P0(x,T),k=R3(O,U);I[b]=Math.sqrt(k[0]*k[0]+k[1]*k[1]),z[b]=Math.atan2(k[1],k[0])}},removeEventListener(...N){return ie.removeEventListener(N[0],N[1],N[2])}},ie)},N3=(r,i)=>r.createMediaElementSource(i.mediaElement),D3=(r,i)=>{const o=r.createMediaStreamDestination();return Lt(o,i),o.numberOfOutputs===1&&Object.defineProperty(o,"numberOfOutputs",{get:()=>0}),o},z3=(r,{mediaStream:i})=>{const o=i.getAudioTracks();o.sort((d,f)=>d.id<f.id?-1:d.id>f.id?1:0);const s=o.slice(0,1),u=r.createMediaStreamSource(new MediaStream(s));return Object.defineProperty(u,"mediaStream",{value:i}),u},U3=(r,i)=>(o,{mediaStreamTrack:s})=>{if(typeof o.createMediaStreamTrackSource=="function")return o.createMediaStreamTrackSource(s);const u=new MediaStream([s]),d=o.createMediaStreamSource(u);if(s.kind!=="audio")throw r();if(i(o))throw new TypeError;return d},q3=r=>r===null?null:r.hasOwnProperty("OfflineAudioContext")?r.OfflineAudioContext:r.hasOwnProperty("webkitOfflineAudioContext")?r.webkitOfflineAudioContext:null,j3=(r,i,o,s,u,d)=>(f,h)=>{const p=f.createOscillator();return Lt(p,h),yt(p,h,"detune"),yt(p,h,"frequency"),h.periodicWave!==void 0?p.setPeriodicWave(h.periodicWave):ct(p,h,"type"),i(o,()=>o(f))||Gu(p),i(s,()=>s(f))||d(p,f),i(u,()=>u(f))||Qu(p),r(f,p),p},W3=r=>(i,o)=>{const s=i.createPanner();return s.orientationX===void 0?r(i,o):(Lt(s,o),yt(s,o,"orientationX"),yt(s,o,"orientationY"),yt(s,o,"orientationZ"),yt(s,o,"positionX"),yt(s,o,"positionY"),yt(s,o,"positionZ"),ct(s,o,"coneInnerAngle"),ct(s,o,"coneOuterAngle"),ct(s,o,"coneOuterGain"),ct(s,o,"distanceModel"),ct(s,o,"maxDistance"),ct(s,o,"panningModel"),ct(s,o,"refDistance"),ct(s,o,"rolloffFactor"),s)},V3=(r,i,o,s,u,d,f,h,p,m)=>(y,{coneInnerAngle:v,coneOuterAngle:x,coneOuterGain:E,distanceModel:M,maxDistance:R,orientationX:j,orientationY:ie,orientationZ:B,panningModel:$,positionX:ae,positionY:V,positionZ:Y,refDistance:N,rolloffFactor:I,...z})=>{const F=y.createPanner();if(z.channelCount>2||z.channelCountMode==="max")throw f();Lt(F,z);const b={channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete"},S=o(y,{...b,channelInterpretation:"speakers",numberOfInputs:6}),T=s(y,{...z,gain:1}),O=s(y,{...b,gain:1}),U=s(y,{...b,gain:0}),k=s(y,{...b,gain:0}),K=s(y,{...b,gain:0}),G=s(y,{...b,gain:0}),X=s(y,{...b,gain:0}),w=u(y,256,6,1),D=d(y,{...b,curve:new Float32Array([1,1]),oversample:"none"});let te=[j,ie,B],de=[ae,V,Y];const me=new Float32Array(1);w.onaudioprocess=({inputBuffer:he})=>{const Ye=[p(he,me,0),p(he,me,1),p(he,me,2)];Ye.some((dt,bt)=>dt!==te[bt])&&(F.setOrientation(...Ye),te=Ye);const Ze=[p(he,me,3),p(he,me,4),p(he,me,5)];Ze.some((dt,bt)=>dt!==de[bt])&&(F.setPosition(...Ze),de=Ze)},Object.defineProperty(U.gain,"defaultValue",{get:()=>0}),Object.defineProperty(k.gain,"defaultValue",{get:()=>0}),Object.defineProperty(K.gain,"defaultValue",{get:()=>0}),Object.defineProperty(G.gain,"defaultValue",{get:()=>0}),Object.defineProperty(X.gain,"defaultValue",{get:()=>0});const ge={get bufferSize(){},get channelCount(){return F.channelCount},set channelCount(he){if(he>2)throw f();T.channelCount=he,F.channelCount=he},get channelCountMode(){return F.channelCountMode},set channelCountMode(he){if(he==="max")throw f();T.channelCountMode=he,F.channelCountMode=he},get channelInterpretation(){return F.channelInterpretation},set channelInterpretation(he){T.channelInterpretation=he,F.channelInterpretation=he},get coneInnerAngle(){return F.coneInnerAngle},set coneInnerAngle(he){F.coneInnerAngle=he},get coneOuterAngle(){return F.coneOuterAngle},set coneOuterAngle(he){F.coneOuterAngle=he},get coneOuterGain(){return F.coneOuterGain},set coneOuterGain(he){if(he<0||he>1)throw i();F.coneOuterGain=he},get context(){return F.context},get distanceModel(){return F.distanceModel},set distanceModel(he){F.distanceModel=he},get inputs(){return[T]},get maxDistance(){return F.maxDistance},set maxDistance(he){if(he<0)throw new RangeError;F.maxDistance=he},get numberOfInputs(){return F.numberOfInputs},get numberOfOutputs(){return F.numberOfOutputs},get orientationX(){return O.gain},get orientationY(){return U.gain},get orientationZ(){return k.gain},get panningModel(){return F.panningModel},set panningModel(he){F.panningModel=he},get positionX(){return K.gain},get positionY(){return G.gain},get positionZ(){return X.gain},get refDistance(){return F.refDistance},set refDistance(he){if(he<0)throw new RangeError;F.refDistance=he},get rolloffFactor(){return F.rolloffFactor},set rolloffFactor(he){if(he<0)throw new RangeError;F.rolloffFactor=he},addEventListener(...he){return T.addEventListener(he[0],he[1],he[2])},dispatchEvent(...he){return T.dispatchEvent(he[0])},removeEventListener(...he){return T.removeEventListener(he[0],he[1],he[2])}};v!==ge.coneInnerAngle&&(ge.coneInnerAngle=v),x!==ge.coneOuterAngle&&(ge.coneOuterAngle=x),E!==ge.coneOuterGain&&(ge.coneOuterGain=E),M!==ge.distanceModel&&(ge.distanceModel=M),R!==ge.maxDistance&&(ge.maxDistance=R),j!==ge.orientationX.value&&(ge.orientationX.value=j),ie!==ge.orientationY.value&&(ge.orientationY.value=ie),B!==ge.orientationZ.value&&(ge.orientationZ.value=B),$!==ge.panningModel&&(ge.panningModel=$),ae!==ge.positionX.value&&(ge.positionX.value=ae),V!==ge.positionY.value&&(ge.positionY.value=V),Y!==ge.positionZ.value&&(ge.positionZ.value=Y),N!==ge.refDistance&&(ge.refDistance=N),I!==ge.rolloffFactor&&(ge.rolloffFactor=I),(te[0]!==1||te[1]!==0||te[2]!==0)&&F.setOrientation(...te),(de[0]!==0||de[1]!==0||de[2]!==0)&&F.setPosition(...de);const _e=()=>{T.connect(F),r(T,D,0,0),D.connect(O).connect(S,0,0),D.connect(U).connect(S,0,1),D.connect(k).connect(S,0,2),D.connect(K).connect(S,0,3),D.connect(G).connect(S,0,4),D.connect(X).connect(S,0,5),S.connect(w).connect(y.destination)},ze=()=>{T.disconnect(F),h(T,D,0,0),D.disconnect(O),O.disconnect(S),D.disconnect(U),U.disconnect(S),D.disconnect(k),k.disconnect(S),D.disconnect(K),K.disconnect(S),D.disconnect(G),G.disconnect(S),D.disconnect(X),X.disconnect(S),S.disconnect(w),w.disconnect(y.destination)};return m(pa(ge,F),_e,ze)},H3=r=>(i,{disableNormalization:o,imag:s,real:u})=>{const d=s instanceof Float32Array?s:new Float32Array(s),f=u instanceof Float32Array?u:new Float32Array(u),h=i.createPeriodicWave(f,d,{disableNormalization:o});if(Array.from(s).length<2)throw r();return h},el=(r,i,o,s)=>r.createScriptProcessor(i,o,s),B3=(r,i)=>(o,s)=>{const u=s.channelCountMode;if(u==="clamped-max")throw i();if(o.createStereoPanner===void 0)return r(o,s);const d=o.createStereoPanner();return Lt(d,s),yt(d,s,"pan"),Object.defineProperty(d,"channelCountMode",{get:()=>u,set:f=>{if(f!==u)throw i()}}),d},$3=(r,i,o,s,u,d)=>{const f=new Float32Array([1,1]),h=Math.PI/2,p={channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete"},m={...p,oversample:"none"},y=(E,M,R,j)=>{const ie=new Float32Array(16385),B=new Float32Array(16385);for(let I=0;I<16385;I+=1){const z=I/16384*h;ie[I]=Math.cos(z),B[I]=Math.sin(z)}const $=o(E,{...p,gain:0}),ae=s(E,{...m,curve:ie}),V=s(E,{...m,curve:f}),Y=o(E,{...p,gain:0}),N=s(E,{...m,curve:B});return{connectGraph(){M.connect($),M.connect(V.inputs===void 0?V:V.inputs[0]),M.connect(Y),V.connect(R),R.connect(ae.inputs===void 0?ae:ae.inputs[0]),R.connect(N.inputs===void 0?N:N.inputs[0]),ae.connect($.gain),N.connect(Y.gain),$.connect(j,0,0),Y.connect(j,0,1)},disconnectGraph(){M.disconnect($),M.disconnect(V.inputs===void 0?V:V.inputs[0]),M.disconnect(Y),V.disconnect(R),R.disconnect(ae.inputs===void 0?ae:ae.inputs[0]),R.disconnect(N.inputs===void 0?N:N.inputs[0]),ae.disconnect($.gain),N.disconnect(Y.gain),$.disconnect(j,0,0),Y.disconnect(j,0,1)}}},v=(E,M,R,j)=>{const ie=new Float32Array(16385),B=new Float32Array(16385),$=new Float32Array(16385),ae=new Float32Array(16385),V=Math.floor(16385/2);for(let k=0;k<16385;k+=1)if(k>V){const K=(k-V)/(16384-V)*h;ie[k]=Math.cos(K),B[k]=Math.sin(K),$[k]=0,ae[k]=1}else{const K=k/(16384-V)*h;ie[k]=1,B[k]=0,$[k]=Math.cos(K),ae[k]=Math.sin(K)}const Y=i(E,{channelCount:2,channelCountMode:"explicit",channelInterpretation:"discrete",numberOfOutputs:2}),N=o(E,{...p,gain:0}),I=s(E,{...m,curve:ie}),z=o(E,{...p,gain:0}),F=s(E,{...m,curve:B}),b=s(E,{...m,curve:f}),S=o(E,{...p,gain:0}),T=s(E,{...m,curve:$}),O=o(E,{...p,gain:0}),U=s(E,{...m,curve:ae});return{connectGraph(){M.connect(Y),M.connect(b.inputs===void 0?b:b.inputs[0]),Y.connect(N,0),Y.connect(z,0),Y.connect(S,1),Y.connect(O,1),b.connect(R),R.connect(I.inputs===void 0?I:I.inputs[0]),R.connect(F.inputs===void 0?F:F.inputs[0]),R.connect(T.inputs===void 0?T:T.inputs[0]),R.connect(U.inputs===void 0?U:U.inputs[0]),I.connect(N.gain),F.connect(z.gain),T.connect(S.gain),U.connect(O.gain),N.connect(j,0,0),S.connect(j,0,0),z.connect(j,0,1),O.connect(j,0,1)},disconnectGraph(){M.disconnect(Y),M.disconnect(b.inputs===void 0?b:b.inputs[0]),Y.disconnect(N,0),Y.disconnect(z,0),Y.disconnect(S,1),Y.disconnect(O,1),b.disconnect(R),R.disconnect(I.inputs===void 0?I:I.inputs[0]),R.disconnect(F.inputs===void 0?F:F.inputs[0]),R.disconnect(T.inputs===void 0?T:T.inputs[0]),R.disconnect(U.inputs===void 0?U:U.inputs[0]),I.disconnect(N.gain),F.disconnect(z.gain),T.disconnect(S.gain),U.disconnect(O.gain),N.disconnect(j,0,0),S.disconnect(j,0,0),z.disconnect(j,0,1),O.disconnect(j,0,1)}}},x=(E,M,R,j,ie)=>{if(M===1)return y(E,R,j,ie);if(M===2)return v(E,R,j,ie);throw u()};return(E,{channelCount:M,channelCountMode:R,pan:j,...ie})=>{if(R==="max")throw u();const B=r(E,{...ie,channelCount:1,channelCountMode:R,numberOfInputs:2}),$=o(E,{...ie,channelCount:M,channelCountMode:R,gain:1}),ae=o(E,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:j});let{connectGraph:V,disconnectGraph:Y}=x(E,M,$,ae,B);Object.defineProperty(ae.gain,"defaultValue",{get:()=>0}),Object.defineProperty(ae.gain,"maxValue",{get:()=>1}),Object.defineProperty(ae.gain,"minValue",{get:()=>-1});const N={get bufferSize(){},get channelCount(){return $.channelCount},set channelCount(b){$.channelCount!==b&&(I&&Y(),{connectGraph:V,disconnectGraph:Y}=x(E,b,$,ae,B),I&&V()),$.channelCount=b},get channelCountMode(){return $.channelCountMode},set channelCountMode(b){if(b==="clamped-max"||b==="max")throw u();$.channelCountMode=b},get channelInterpretation(){return $.channelInterpretation},set channelInterpretation(b){$.channelInterpretation=b},get context(){return $.context},get inputs(){return[$]},get numberOfInputs(){return $.numberOfInputs},get numberOfOutputs(){return $.numberOfOutputs},get pan(){return ae.gain},addEventListener(...b){return $.addEventListener(b[0],b[1],b[2])},dispatchEvent(...b){return $.dispatchEvent(b[0])},removeEventListener(...b){return $.removeEventListener(b[0],b[1],b[2])}};let I=!1;const z=()=>{V(),I=!0},F=()=>{Y(),I=!1};return d(pa(N,B),z,F)}},Y3=(r,i,o,s,u,d,f)=>(h,p)=>{const m=h.createWaveShaper();if(d!==null&&d.name==="webkitAudioContext"&&h.createGain().gain.automationRate===void 0)return o(h,p);Lt(m,p);const y=p.curve===null||p.curve instanceof Float32Array?p.curve:new Float32Array(p.curve);if(y!==null&&y.length<2)throw i();ct(m,{curve:y},"curve"),ct(m,p,"oversample");let v=null,x=!1;return f(m,"curve",E=>()=>E.call(m),E=>M=>(E.call(m,M),x&&(s(M)&&v===null?v=r(h,m):!s(M)&&v!==null&&(v(),v=null)),M)),u(m,()=>{x=!0,s(m.curve)&&(v=r(h,m))},()=>{x=!1,v!==null&&(v(),v=null)})},X3=(r,i,o,s,u)=>(d,{curve:f,oversample:h,...p})=>{const m=d.createWaveShaper(),y=d.createWaveShaper();Lt(m,p),Lt(y,p);const v=o(d,{...p,gain:1}),x=o(d,{...p,gain:-1}),E=o(d,{...p,gain:1}),M=o(d,{...p,gain:-1});let R=null,j=!1,ie=null;const B={get bufferSize(){},get channelCount(){return m.channelCount},set channelCount(V){v.channelCount=V,x.channelCount=V,m.channelCount=V,E.channelCount=V,y.channelCount=V,M.channelCount=V},get channelCountMode(){return m.channelCountMode},set channelCountMode(V){v.channelCountMode=V,x.channelCountMode=V,m.channelCountMode=V,E.channelCountMode=V,y.channelCountMode=V,M.channelCountMode=V},get channelInterpretation(){return m.channelInterpretation},set channelInterpretation(V){v.channelInterpretation=V,x.channelInterpretation=V,m.channelInterpretation=V,E.channelInterpretation=V,y.channelInterpretation=V,M.channelInterpretation=V},get context(){return m.context},get curve(){return ie},set curve(V){if(V!==null&&V.length<2)throw i();if(V===null)m.curve=V,y.curve=V;else{const Y=V.length,N=new Float32Array(Y+2-Y%2),I=new Float32Array(Y+2-Y%2);N[0]=V[0],I[0]=-V[Y-1];const z=Math.ceil((Y+1)/2),F=(Y+1)/2-1;for(let b=1;b<z;b+=1){const S=b/z*F,T=Math.floor(S),O=Math.ceil(S);N[b]=T===O?V[T]:(1-(S-T))*V[T]+(1-(O-S))*V[O],I[b]=T===O?-V[Y-1-T]:-((1-(S-T))*V[Y-1-T])-(1-(O-S))*V[Y-1-O]}N[z]=Y%2===1?V[z-1]:(V[z-2]+V[z-1])/2,m.curve=N,y.curve=I}ie=V,j&&(s(ie)&&R===null?R=r(d,v):R!==null&&(R(),R=null))},get inputs(){return[v]},get numberOfInputs(){return m.numberOfInputs},get numberOfOutputs(){return m.numberOfOutputs},get oversample(){return m.oversample},set oversample(V){m.oversample=V,y.oversample=V},addEventListener(...V){return v.addEventListener(V[0],V[1],V[2])},dispatchEvent(...V){return v.dispatchEvent(V[0])},removeEventListener(...V){return v.removeEventListener(V[0],V[1],V[2])}};f!==null&&(B.curve=f instanceof Float32Array?f:new Float32Array(f)),h!==B.oversample&&(B.oversample=h);const $=()=>{v.connect(m).connect(E),v.connect(x).connect(y).connect(M).connect(E),j=!0,s(ie)&&(R=r(d,v))},ae=()=>{v.disconnect(m),m.disconnect(E),v.disconnect(x),x.disconnect(y),y.disconnect(M),M.disconnect(E),j=!1,R!==null&&(R(),R=null)};return u(pa(B,E),$,ae)},Pn=()=>new DOMException("","NotSupportedError"),J3={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",detune:0,frequency:440,periodicWave:void 0,type:"sine"},K3=(r,i,o,s,u,d,f)=>class extends r{constructor(h,p){const m=u(h),y={...J3,...p},v=o(m,y),x=d(m),E=x?s():null,M=h.sampleRate/2;super(h,!1,v,E),this._detune=i(this,x,v.detune,153600,-153600),this._frequency=i(this,x,v.frequency,M,-M),this._nativeOscillatorNode=v,this._onended=null,this._oscillatorNodeRenderer=E,this._oscillatorNodeRenderer!==null&&y.periodicWave!==void 0&&(this._oscillatorNodeRenderer.periodicWave=y.periodicWave)}get detune(){return this._detune}get frequency(){return this._frequency}get onended(){return this._onended}set onended(h){const p=typeof h=="function"?f(this,h):null;this._nativeOscillatorNode.onended=p;const m=this._nativeOscillatorNode.onended;this._onended=m!==null&&m===p?h:m}get type(){return this._nativeOscillatorNode.type}set type(h){this._nativeOscillatorNode.type=h,this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.periodicWave=null)}setPeriodicWave(h){this._nativeOscillatorNode.setPeriodicWave(h),this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.periodicWave=h)}start(h=0){if(this._nativeOscillatorNode.start(h),this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.start=h),this.context.state!=="closed"){fa(this);const p=()=>{this._nativeOscillatorNode.removeEventListener("ended",p),pr(this)&&wo(this)};this._nativeOscillatorNode.addEventListener("ended",p)}}stop(h=0){this._nativeOscillatorNode.stop(h),this._oscillatorNodeRenderer!==null&&(this._oscillatorNodeRenderer.stop=h)}},Z3=(r,i,o,s,u)=>()=>{const d=new WeakMap;let f=null,h=null,p=null;const m=async(y,v)=>{let x=o(y);const E=rn(x,v);if(!E){const M={channelCount:x.channelCount,channelCountMode:x.channelCountMode,channelInterpretation:x.channelInterpretation,detune:x.detune.value,frequency:x.frequency.value,periodicWave:f===null?void 0:f,type:x.type};x=i(v,M),h!==null&&x.start(h),p!==null&&x.stop(p)}return d.set(v,x),E?(await r(v,y.detune,x.detune),await r(v,y.frequency,x.frequency)):(await s(v,y.detune,x.detune),await s(v,y.frequency,x.frequency)),await u(y,v,x),x};return{set periodicWave(y){f=y},set start(y){h=y},set stop(y){p=y},render(y,v){const x=d.get(v);return x!==void 0?Promise.resolve(x):m(y,v)}}},G3={channelCount:2,channelCountMode:"clamped-max",channelInterpretation:"speakers",coneInnerAngle:360,coneOuterAngle:360,coneOuterGain:0,distanceModel:"inverse",maxDistance:1e4,orientationX:1,orientationY:0,orientationZ:0,panningModel:"equalpower",positionX:0,positionY:0,positionZ:0,refDistance:1,rolloffFactor:1},Q3=(r,i,o,s,u,d,f)=>class extends r{constructor(h,p){const m=u(h),y={...G3,...p},v=o(m,y),x=d(m),E=x?s():null;super(h,!1,v,E),this._nativePannerNode=v,this._orientationX=i(this,x,v.orientationX,cn,yn),this._orientationY=i(this,x,v.orientationY,cn,yn),this._orientationZ=i(this,x,v.orientationZ,cn,yn),this._positionX=i(this,x,v.positionX,cn,yn),this._positionY=i(this,x,v.positionY,cn,yn),this._positionZ=i(this,x,v.positionZ,cn,yn),f(this,1)}get coneInnerAngle(){return this._nativePannerNode.coneInnerAngle}set coneInnerAngle(h){this._nativePannerNode.coneInnerAngle=h}get coneOuterAngle(){return this._nativePannerNode.coneOuterAngle}set coneOuterAngle(h){this._nativePannerNode.coneOuterAngle=h}get coneOuterGain(){return this._nativePannerNode.coneOuterGain}set coneOuterGain(h){this._nativePannerNode.coneOuterGain=h}get distanceModel(){return this._nativePannerNode.distanceModel}set distanceModel(h){this._nativePannerNode.distanceModel=h}get maxDistance(){return this._nativePannerNode.maxDistance}set maxDistance(h){this._nativePannerNode.maxDistance=h}get orientationX(){return this._orientationX}get orientationY(){return this._orientationY}get orientationZ(){return this._orientationZ}get panningModel(){return this._nativePannerNode.panningModel}set panningModel(h){this._nativePannerNode.panningModel=h}get positionX(){return this._positionX}get positionY(){return this._positionY}get positionZ(){return this._positionZ}get refDistance(){return this._nativePannerNode.refDistance}set refDistance(h){this._nativePannerNode.refDistance=h}get rolloffFactor(){return this._nativePannerNode.rolloffFactor}set rolloffFactor(h){this._nativePannerNode.rolloffFactor=h}},e4=(r,i,o,s,u,d,f,h,p,m)=>()=>{const y=new WeakMap;let v=null;const x=async(E,M)=>{let R=null,j=d(E);const ie={channelCount:j.channelCount,channelCountMode:j.channelCountMode,channelInterpretation:j.channelInterpretation},B={...ie,coneInnerAngle:j.coneInnerAngle,coneOuterAngle:j.coneOuterAngle,coneOuterGain:j.coneOuterGain,distanceModel:j.distanceModel,maxDistance:j.maxDistance,panningModel:j.panningModel,refDistance:j.refDistance,rolloffFactor:j.rolloffFactor},$=rn(j,M);if("bufferSize"in j)R=s(M,{...ie,gain:1});else if(!$){const ae={...B,orientationX:j.orientationX.value,orientationY:j.orientationY.value,orientationZ:j.orientationZ.value,positionX:j.positionX.value,positionY:j.positionY.value,positionZ:j.positionZ.value};j=u(M,ae)}if(y.set(M,R===null?j:R),R!==null){if(v===null){if(f===null)throw new Error("Missing the native OfflineAudioContext constructor.");const b=new f(6,E.context.length,M.sampleRate),S=i(b,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"speakers",numberOfInputs:6});S.connect(b.destination),v=(async()=>{const T=await Promise.all([E.orientationX,E.orientationY,E.orientationZ,E.positionX,E.positionY,E.positionZ].map(async(O,U)=>{const k=o(b,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",offset:U===0?1:0});return await h(b,O,k.offset),k}));for(let O=0;O<6;O+=1)T[O].connect(S,0,O),T[O].start(0);return m(b)})()}const ae=await v,V=s(M,{...ie,gain:1});await p(E,M,V);const Y=[];for(let b=0;b<ae.numberOfChannels;b+=1)Y.push(ae.getChannelData(b));let N=[Y[0][0],Y[1][0],Y[2][0]],I=[Y[3][0],Y[4][0],Y[5][0]],z=s(M,{...ie,gain:1}),F=u(M,{...B,orientationX:N[0],orientationY:N[1],orientationZ:N[2],positionX:I[0],positionY:I[1],positionZ:I[2]});V.connect(z).connect(F.inputs[0]),F.connect(R);for(let b=128;b<ae.length;b+=128){const S=[Y[0][b],Y[1][b],Y[2][b]],T=[Y[3][b],Y[4][b],Y[5][b]];if(S.some((O,U)=>O!==N[U])||T.some((O,U)=>O!==I[U])){N=S,I=T;const O=b/M.sampleRate;z.gain.setValueAtTime(0,O),z=s(M,{...ie,gain:0}),F=u(M,{...B,orientationX:N[0],orientationY:N[1],orientationZ:N[2],positionX:I[0],positionY:I[1],positionZ:I[2]}),z.gain.setValueAtTime(1,O),V.connect(z).connect(F.inputs[0]),F.connect(R)}}return R}return $?(await r(M,E.orientationX,j.orientationX),await r(M,E.orientationY,j.orientationY),await r(M,E.orientationZ,j.orientationZ),await r(M,E.positionX,j.positionX),await r(M,E.positionY,j.positionY),await r(M,E.positionZ,j.positionZ)):(await h(M,E.orientationX,j.orientationX),await h(M,E.orientationY,j.orientationY),await h(M,E.orientationZ,j.orientationZ),await h(M,E.positionX,j.positionX),await h(M,E.positionY,j.positionY),await h(M,E.positionZ,j.positionZ)),ha(j)?await p(E,M,j.inputs[0]):await p(E,M,j),j};return{render(E,M){const R=y.get(M);return R!==void 0?Promise.resolve(R):x(E,M)}}},t4={disableNormalization:!1},n4=(r,i,o,s)=>class t2{constructor(d,f){const h=i(d),p=s({...t4,...f}),m=r(h,p);return o.add(m),m}static[Symbol.hasInstance](d){return d!==null&&typeof d=="object"&&Object.getPrototypeOf(d)===t2.prototype||o.has(d)}},r4=(r,i)=>(o,s,u)=>(r(s).replay(u),i(s,o,u)),i4=(r,i,o)=>async(s,u,d)=>{const f=r(s);await Promise.all(f.activeInputs.map((h,p)=>Array.from(h).map(async([m,y])=>{const v=await i(m).render(m,u),x=s.context.destination;!o(m)&&(s!==x||!o(s))&&v.connect(d,y,p)})).reduce((h,p)=>[...h,...p],[]))},a4=(r,i,o)=>async(s,u,d)=>{const f=i(s);await Promise.all(Array.from(f.activeInputs).map(async([h,p])=>{const m=await r(h).render(h,u);o(h)||m.connect(d,p)}))},o4=(r,i,o,s)=>u=>r(Zu,()=>Zu(u))?Promise.resolve(r(s,s)).then(d=>{if(!d){const f=o(u,512,0,1);u.oncomplete=()=>{f.onaudioprocess=null,f.disconnect()},f.onaudioprocess=()=>u.currentTime,f.connect(u.destination)}return u.startRendering()}):new Promise(d=>{const f=i(u,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});u.oncomplete=h=>{f.disconnect(),d(h.renderedBuffer)},f.connect(u.destination),u.startRendering()}),s4=r=>(i,o)=>r.set(i,o),l4={channelCount:2,channelCountMode:"explicit",channelInterpretation:"speakers",pan:0},u4=(r,i,o,s,u,d)=>class extends r{constructor(f,h){const p=u(f),m={...l4,...h},y=o(p,m),v=d(p),x=v?s():null;super(f,!1,y,x),this._pan=i(this,v,y.pan)}get pan(){return this._pan}},c4=(r,i,o,s,u)=>()=>{const d=new WeakMap,f=async(h,p)=>{let m=o(h);const y=rn(m,p);if(!y){const v={channelCount:m.channelCount,channelCountMode:m.channelCountMode,channelInterpretation:m.channelInterpretation,pan:m.pan.value};m=i(p,v)}return d.set(p,m),y?await r(p,h.pan,m.pan):await s(p,h.pan,m.pan),ha(m)?await u(h,p,m.inputs[0]):await u(h,p,m),m};return{render(h,p){const m=d.get(p);return m!==void 0?Promise.resolve(m):f(h,p)}}},d4=r=>()=>{if(r===null)return!1;try{new r({length:1,sampleRate:44100})}catch(i){return!1}return!0},f4=(r,i)=>async()=>{if(r===null)return!0;if(i===null)return!1;const o=new Blob(['class A extends AudioWorkletProcessor{process(i){this.port.postMessage(i,[i[0][0].buffer])}}registerProcessor("a",A)'],{type:"application/javascript; charset=utf-8"}),s=new i(1,128,44100),u=URL.createObjectURL(o);let d=!1,f=!1;try{await s.audioWorklet.addModule(u);const h=new r(s,"a",{numberOfOutputs:0}),p=s.createOscillator();h.port.onmessage=()=>d=!0,h.onprocessorerror=()=>f=!0,p.connect(h),p.start(0),await s.startRendering(),await new Promise(m=>setTimeout(m))}catch(h){}finally{URL.revokeObjectURL(u)}return d&&!f},h4=(r,i)=>()=>{if(i===null)return Promise.resolve(!1);const o=new i(1,1,44100),s=r(o,{channelCount:1,channelCountMode:"explicit",channelInterpretation:"discrete",gain:0});return new Promise(u=>{o.oncomplete=()=>{s.disconnect(),u(o.currentTime!==0)},o.startRendering()})},p4=()=>new DOMException("","UnknownError"),g4={channelCount:2,channelCountMode:"max",channelInterpretation:"speakers",curve:null,oversample:"none"},v4=(r,i,o,s,u,d,f)=>class extends r{constructor(h,p){const m=u(h),y={...g4,...p},v=o(m,y),x=d(m)?s():null;super(h,!0,v,x),this._isCurveNullified=!1,this._nativeWaveShaperNode=v,f(this,1)}get curve(){return this._isCurveNullified?null:this._nativeWaveShaperNode.curve}set curve(h){if(h===null)this._isCurveNullified=!0,this._nativeWaveShaperNode.curve=new Float32Array([0,0]);else{if(h.length<2)throw i();this._isCurveNullified=!1,this._nativeWaveShaperNode.curve=h}}get oversample(){return this._nativeWaveShaperNode.oversample}set oversample(h){this._nativeWaveShaperNode.oversample=h}},y4=(r,i,o)=>()=>{const s=new WeakMap,u=async(d,f)=>{let h=i(d);if(!rn(h,f)){const p={channelCount:h.channelCount,channelCountMode:h.channelCountMode,channelInterpretation:h.channelInterpretation,curve:h.curve,oversample:h.oversample};h=r(f,p)}return s.set(f,h),ha(h)?await o(d,f,h.inputs[0]):await o(d,f,h),h};return{render(d,f){const h=s.get(f);return h!==void 0?Promise.resolve(h):u(d,f)}}},b4=()=>typeof window>"u"?null:window,w4=(r,i)=>o=>{o.copyFromChannel=(s,u,d=0)=>{const f=r(d),h=r(u);if(h>=o.numberOfChannels)throw i();const p=o.length,m=o.getChannelData(h),y=s.length;for(let v=f<0?-f:0;v+f<p&&v<y;v+=1)s[v]=m[v+f]},o.copyToChannel=(s,u,d=0)=>{const f=r(d),h=r(u);if(h>=o.numberOfChannels)throw i();const p=o.length,m=o.getChannelData(h),y=s.length;for(let v=f<0?-f:0;v+f<p&&v<y;v+=1)m[v+f]=s[v]}},_4=r=>i=>{i.copyFromChannel=(o=>(s,u,d=0)=>{const f=r(d),h=r(u);if(f<i.length)return o.call(i,s,h,f)})(i.copyFromChannel),i.copyToChannel=(o=>(s,u,d=0)=>{const f=r(d),h=r(u);if(f<i.length)return o.call(i,s,h,f)})(i.copyToChannel)},x4=r=>(i,o)=>{const s=o.createBuffer(1,1,44100);i.buffer===null&&(i.buffer=s),r(i,"buffer",u=>()=>{const d=u.call(i);return d===s?null:d},u=>d=>u.call(i,d===null?s:d))},S4=(r,i)=>(o,s)=>{s.channelCount=1,s.channelCountMode="explicit",Object.defineProperty(s,"channelCount",{get:()=>1,set:()=>{throw r()}}),Object.defineProperty(s,"channelCountMode",{get:()=>"explicit",set:()=>{throw r()}});const u=o.createBufferSource();i(s,()=>{const d=s.numberOfInputs;for(let f=0;f<d;f+=1)u.connect(s,0,f)},()=>u.disconnect(s))},O0=(r,i,o)=>r.copyFromChannel===void 0?r.getChannelData(o)[0]:(r.copyFromChannel(i,o),i[0]),R0=r=>{if(r===null)return!1;const i=r.length;return i%2!==0?r[Math.floor(i/2)]!==0:r[i/2-1]+r[i/2]!==0},ko=(r,i,o,s)=>{let u=r;for(;!u.hasOwnProperty(i);)u=Object.getPrototypeOf(u);const{get:d,set:f}=Object.getOwnPropertyDescriptor(u,i);Object.defineProperty(r,i,{get:o(d),set:s(f)})},k4=r=>({...r,channelCount:r.numberOfOutputs}),C4=r=>{const{imag:i,real:o}=r;return i===void 0?o===void 0?{...r,imag:[0,0],real:[0,0]}:{...r,imag:Array.from(o,()=>0),real:o}:o===void 0?{...r,imag:i,real:Array.from(i,()=>0)}:{...r,imag:i,real:o}},I0=(r,i,o)=>{try{r.setValueAtTime(i,o)}catch(s){if(s.code!==9)throw s;I0(r,i,o+1e-7)}},E4=r=>{const i=r.createBufferSource();i.start();try{i.start()}catch(o){return!0}return!1},M4=r=>{const i=r.createBufferSource(),o=r.createBuffer(1,1,44100);i.buffer=o;try{i.start(0,1)}catch(s){return!1}return!0},A4=r=>{const i=r.createBufferSource();i.start();try{i.stop()}catch(o){return!1}return!0},tc=r=>{const i=r.createOscillator();try{i.start(-1)}catch(o){return o instanceof RangeError}return!1},L0=r=>{const i=r.createBuffer(1,1,44100),o=r.createBufferSource();o.buffer=i,o.start(),o.stop();try{return o.stop(),!0}catch(s){return!1}},nc=r=>{const i=r.createOscillator();try{i.stop(-1)}catch(o){return o instanceof RangeError}return!1},T4=r=>{r.start=(i=>(o=0,s=0,u)=>{const d=r.buffer,f=d===null?s:Math.min(d.duration,s);d!==null&&f>d.duration-.5/r.context.sampleRate?i.call(r,o,0,0):i.call(r,o,f,u)})(r.start)},N0=(r,i)=>{const o=i.createGain();r.connect(o);const s=(u=>()=>{u.call(r,o),r.removeEventListener("ended",s)})(r.disconnect);r.addEventListener("ended",s),pa(r,o),r.stop=(u=>{let d=!1;return(f=0)=>{if(d)try{u.call(r,f)}catch(h){o.gain.setValueAtTime(0,f)}else u.call(r,f),d=!0}})(r.stop)},Co=(r,i)=>o=>{const s={value:r};return Object.defineProperties(o,{currentTarget:s,target:s}),typeof i=="function"?i.call(r,o):i.handleEvent.call(r,o)},F4=_w(Mi),P4=Mw(Mi),O4=O5(Xs),D0=new WeakMap,R4=Y5(D0),mr=c5(new Map,new WeakMap),Qn=b4(),z0=y3(mr,Ei),U0=$5(bn),en=i4(bn,U0,Ai),I4=Pw(z0,mt,en),lt=J5(l0),Ti=q3(Qn),at=s3(Ti),q0=new WeakMap,j0=q5(Co),Eo=x3(Qn),W0=r3(Eo),V0=i3(Qn),L4=a3(Qn),rc=k3(Qn),Tt=r5(xw(i0),Ew(F4,P4,Ks,O4,Zs,bn,R4,bo,mt,Mi,pr,Ai,Gs),mr,n3(Hu,Zs,bn,mt,So,pr),Ei,Qs,Pn,A5(Ks,Hu,bn,mt,So,lt,pr,at),I5(q0,bn,Br),j0,lt,W0,V0,L4,at,rc),N4=Fw(Tt,I4,Ei,z0,lt,at),H0=new WeakSet,B0=b3(Qn),$0=x5(new Uint32Array(1)),Y0=w4($0,Ei),X0=_4($0),ic=Rw(H0,mr,Pn,B0,Ti,d4(B0),Y0,X0),tl=Aw(Vn),J0=a4(U0,_o,Ai),gr=v5(J0),Mo=_3(tl,mr,E4,M4,A4,tc,L0,nc,T4,x4(ko),N0),vr=r4(X5(_o),J0),D4=Nw(gr,Mo,mt,vr,en),er=i5(Sw(o0),q0,s0,a5,mw,gw,vw,yw,bw,qu,Qp,Eo,I0),K0=Lw(Tt,D4,er,an,Mo,lt,at,Co),z4=$w(Tt,Yw,Ei,an,S3(Vn,ko),lt,at,en),U4=u5(gr,T0,mt,vr,en),Fi=s4(D0),Ao=l5(Tt,er,U4,Qs,T0,lt,at,Fi),ma=g3(Mi,V0),q4=S4(an,ma),ga=E3(Eo,q4),j4=h5(ga,mt,en),W4=f5(Tt,j4,ga,lt,at),V4=g5(ec,mt,en),H4=m5(Tt,V4,ec,lt,at,k4),B4=T3(tl,Mo,Vn,ma),nl=A3(tl,mr,B4,tc,nc),$4=_5(gr,nl,mt,vr,en),Y4=w5(Tt,er,$4,nl,lt,at,Co),Z0=F3(Pn,ko),X4=C5(Z0,mt,en),G0=k5(Tt,X4,Z0,lt,at,Fi),J4=P5(gr,F0,mt,vr,en),K4=F5(Tt,er,J4,F0,lt,at,Fi),Q0=P3(Pn),Z4=D5(gr,Q0,mt,vr,en),em=N5(Tt,er,Z4,Q0,Pn,lt,at,Fi),G4=B5(gr,Vn,mt,vr,en),tm=H5(Tt,er,G4,Vn,lt,at),Q4=L3(Qs,an,el,Pn),nm=o4(mr,Vn,el,h4(Vn,Ti)),e_=t3(Mo,mt,Ti,en,nm),t_=O3(Q4),rm=Q5(Tt,t_,e_,lt,at,Fi),n_=Xw(er,ga,nl,el,Pn,O0,at,ko),r_=new WeakMap,i_=p3(z4,n_,j0,at,r_,Co),im=j3(tl,mr,tc,L0,nc,N0),a_=Z3(gr,im,mt,vr,en),o_=K3(Tt,er,im,a_,lt,at,Co),am=y5(Mo),s_=X3(am,an,Vn,R0,ma),rl=Y3(am,an,s_,R0,ma,Eo,ko),l_=V3(Ks,an,ga,Vn,el,rl,Pn,Zs,O0,ma),om=W3(l_),u_=e4(gr,ga,nl,Vn,om,mt,Ti,vr,en,nm),c_=Q3(Tt,er,om,u_,lt,at,Fi),d_=H3(Ei),f_=n4(d_,lt,new WeakSet,C4),h_=$3(ga,ec,Vn,rl,Pn,ma),sm=B3(h_,Pn),p_=c4(gr,sm,mt,vr,en),m_=u4(Tt,er,sm,p_,lt,at),g_=y4(rl,mt,en),v_=v4(Tt,an,rl,g_,lt,at,Fi),y_=l3(Qn),b_=j5(Qn),w_=new WeakMap,__=K5(w_,Ti),x_=y_?Cw(mr,Pn,U5(Qn),b_,W5(ww),lt,__,at,rc,new WeakMap,new WeakMap,f4(rc,Ti),Qn):void 0,S_=o3(W0,at),k_=M5(H0,mr,E5,z5,new WeakSet,lt,S_,$u,Zu,Y0,X0),C_=o5(x_,N4,ic,K0,Ao,W4,H4,Y4,G0,k_,K4,em,tm,rm,i_,o_,c_,f_,m_,v_),E_=u3(Tt,N3,lt,at),lm=d3(Tt,D3,lt,at),M_=f3(Tt,z3,lt,at),A_=U3(an,at),T_=h3(Tt,A_,lt),F_=Bw(C_,an,Pn,p4,E_,lm,M_,T_,Eo);class P_{constructor(i){if(this.isRecording=!1,this.recordedAudio=[],this.delayHistory=[],this.maxDelayHistorySize=10,this.endpoint=i,this.playAmount=0,this.playMovingAverage=[],this.playSampleLength=1,this.audioQueue=[],this.demodulation="USB",this.accumulator=[],this.decodeFT8=!1,this.farthestDistance=0,this.nb=!1,this.mute=!1,this.squelchMute=!1,this.squelch=!1,this.squelchThreshold=0,this.power=1,this.ctcss=!1,this.audioCtx&&this.audioCtx.state=="running")startaudio=document.getElementById("startaudio"),startaudio&&startaudio.remove();else{const o=()=>{this.audioCtx&&this.audioCtx.state!=="running"&&this.audioCtx.resume();const s=document.getElementById("startaudio");s&&s.remove(),document.documentElement.removeEventListener("mousedown",o)};document.documentElement.addEventListener("mousedown",o)}this.mode=0,this.d=10,this.v=10,this.n2=10,this.n1=10,this.var=10,this.highThres=1,this.initTimer()}async init(){return this.promise?this.promise:(this.promise=new Promise((i,o)=>{this.resolvePromise=i,this.rejectPromise=o}),this.audioSocket=new WebSocket(this.endpoint),this.audioSocket.binaryType="arraybuffer",this.firstAudioMessage=!0,this.audioSocket.onmessage=this.socketMessageInitial.bind(this),this.promise)}stop(){this.audioSocket.close(),this.decoder.free()}initAudio(i){const o=this.audioOutputSps;try{this.audioCtx=new F_({sampleRate:o})}catch(s){this.resolvePromise();return}this.audioStartTime=this.audioCtx.currentTime,this.playTime=this.audioCtx.currentTime+.1,this.playStartTime=this.audioCtx.currentTime,this.decoder=ew(i.audio_compression,this.audioMaxSps,this.trueAudioSps,this.audioOutputSps),this.bassBoost=new Ao(this.audioCtx),this.bassBoost.type="lowshelf",this.bassBoost.frequency.value=150,this.bassBoost.Q.value=.7,this.bassBoost.gain.value=6,this.bandpass=new Ao(this.audioCtx),this.bandpass.type="peaking",this.bandpass.frequency.value=1800,this.bandpass.Q.value=1.2,this.bandpass.gain.value=3,this.highPass=new Ao(this.audioCtx),this.highPass.type="highpass",this.highPass.frequency.value=60,this.highPass.Q.value=.7,this.presenceBoost=new Ao(this.audioCtx),this.presenceBoost.type="peaking",this.presenceBoost.frequency.value=3500,this.presenceBoost.Q.value=1.5,this.presenceBoost.gain.value=4,this.convolverNode=new G0(this.audioCtx),this.setLowpass(15e3),this.compressor=new em(this.audioCtx),this.compressor.threshold.value=-24,this.compressor.knee.value=30,this.compressor.ratio.value=12,this.compressor.attack.value=.003,this.compressor.release.value=.25,this.gainNode=new tm(this.audioCtx),this.setGain(5),this.destinationNode=new lm(this.audioCtx),this.convolverNode.connect(this.highPass),this.highPass.connect(this.bandpass),this.bandpass.connect(this.bassBoost),this.bassBoost.connect(this.presenceBoost),this.presenceBoost.connect(this.compressor),this.compressor.connect(this.gainNode),this.gainNode.connect(this.destinationNode),this.gainNode.connect(this.audioCtx.destination),this.audioInputNode=this.convolverNode,this.updateFilters(),this.resolvePromise(i)}updateFilters(){switch(this.demodulation){case"USB":case"LSB":case"CW":this.bassBoost.gain.value=12,this.bandpass.frequency.value=1800,this.bandpass.Q.value=1.2,this.bandpass.gain.value=3,this.highPass.frequency.value=60,this.presenceBoost.gain.value=4,this.setLowpass(3e3);break;case"CW":this.bassBoost.gain.value=0,this.bandpass.frequency.value=700,this.bandpass.Q.value=1.2,this.bandpass.gain.value=2,this.highPass.frequency.value=400,this.presenceBoost.gain.value=1,this.setLowpass(1e3);break;case"AM":this.bassBoost.gain.value=20,this.bandpass.frequency.value=1500,this.bandpass.Q.value=1,this.bandpass.gain.value=2,this.highPass.frequency.value=50,this.presenceBoost.gain.value=3,this.setLowpass(4500);break;case"FM":this.bassBoost.gain.value=30,this.bandpass.frequency.value=1500,this.bandpass.Q.value=.8,this.bandpass.gain.value=2,this.highPass.frequency.value=this.ctcss?300:30,this.presenceBoost.gain.value=3,this.setLowpass(15e3);break}}setFIRFilter(i){const o=new ic({length:i.length,numberOfChannels:1,sampleRate:this.audioOutputSps});o.copyToChannel(i,0,0),this.convolverNode.buffer=o}setLowpass(i){const o=this.audioOutputSps;if(i>=o/2){this.setFIRFilter(Float32Array.of(1));return}const s=bp(i/o,1e3/o,.001);this.setFIRFilter(s)}setFT8Decoding(i){this.decodeFT8=i}setFmDeemph(i){if(i===0){this.audioInputNode=this.convolverNode;return}const o=1/i,s=this.audioOutputSps,u=-(2*s*Math.tan(o/(2*s)))/(2*s),d=(1+u)/(1-u),f=-u/(1-u),h=[f*1,f*1],p=[1,-d];this.fmDeemphNode=new rm(this.audioCtx,{feedforward:h,feedback:p}),this.fmDeemphNode.connect(this.convolverNode),this.audioInputNode=this.fmDeemphNode}socketMessageInitial(i){const o=JSON.parse(i.data);this.settings=o,this.fftSize=o.fft_size,this.audioMaxSize=o.fft_result_size,this.baseFreq=o.basefreq,this.totalBandwidth=o.total_bandwidth,this.sps=o.sps,this.audioOverlap=o.fft_overlap/2,this.audioMaxSps=o.audio_max_sps,this.grid_locator=o.grid_locator,this.smeter_offset=o.smeter_offset,this.audioL=o.defaults.l,this.audioM=o.defaults.m,this.audioR=o.defaults.r;const s=Math.ceil(this.audioMaxSps*this.audioMaxSize/this.sps/4)*4;this.trueAudioSps=s/this.audioMaxSize*this.sps,this.audioOutputSps=Math.min(this.audioMaxSps,96e3),this.audioSocket.onmessage=this.socketMessage.bind(this),this.initAudio(o),console.log("Audio Samplerate: ",this.trueAudioSps)}socketMessage(i){if(i.data instanceof ArrayBuffer){const o=Lp(new Uint8Array(i.data)),s=o.pwr;this.power=.5*this.power+.5*s||1;const u=20*Math.log10(Math.sqrt(this.power)/2);this.dBPower=u,this.squelch&&u<this.squelchThreshold?this.squelchMute=!0:this.squelchMute=!1,this.decode(o.data)}}decode(i){if(!this.audioCtx)return;let o=this.decoder.decode(i);if(o.length!==0){if(this.intervals=this.intervals||Xp(1e4,0),this.lens=this.lens||Xp(1e4,0),this.lastReceived=this.lastReceived||0,this.lastReceived===0)this.lastReceived=performance.now();else{const s=performance.now(),u=s-this.lastReceived;this.intervals.push(u),this.lastReceived=s,this.lens.push(o.length);let d=!0;this.mode===0?Math.abs(u-this.n1)>Math.abs(this.v)*2+800&&(this.var=0,this.mode=1):(this.var=this.var/2+Math.abs((2*u-this.n1-this.n2)/8),this.var<=63&&(this.mode=0,d=!1)),d&&(this.mode===0?this.d=.125*u+.875*this.d:this.d=this.d+u-this.n1,this.v=.125*Math.abs(u-this.d)+.875*this.v),this.n2=this.n1,this.n1=u}this.pcmArray=o,this.signalDecoder&&this.signalDecoder.decode(o),this.playAudio(o)}}updateAudioParams(){this.demodulation=="CW"?this.audioSocket.send(JSON.stringify({cmd:"window",l:this.audioLOffset,m:this.audioMOffset,r:this.audioROffset})):this.audioSocket.send(JSON.stringify({cmd:"window",l:this.audioL,m:this.audioM,r:this.audioR}))}setAudioDemodulation(i){this.demodulation=i,i=="CW"&&(i="USB"),this.updateFilters(),this.audioSocket.send(JSON.stringify({cmd:"demodulation",demodulation:i}))}setAudioRange(i,o,s,u,d,f){this.audioL=Math.floor(i),this.audioM=o,this.audioR=Math.ceil(s),this.actualL=i,this.actualR=s,this.audioLOffset=Math.floor(u),this.audioMOffset=d,this.audioROffset=Math.ceil(f),this.actualLOffset=u,this.actualROffset=f,this.updateAudioParams()}getAudioRange(){return[this.actualL,this.audioM,this.actualR]}setAudioOptions(i){this.audioOptions=i,this.audioSocket.send(JSON.stringify({cmd:"options",options:i}))}setGain(i){i/=35,this.gain=i,this.gainNode.gain.value=i}setMute(i){i!==this.mute&&(this.mute=i,this.audioSocket.send(JSON.stringify({cmd:"mute",mute:i})))}setCTCSSFilter(i){this.ctcss=i,this.updateFilters()}setAGCSpeed(i,o,s){if(!this.compressor)return;let u,d;switch(i){case"off":this.compressor.ratio.value=1,this.compressor.threshold.value=0,u=null,d=null;break;case"custom":o!==void 0&&s!==void 0?(this.compressor.ratio.value=12,this.compressor.threshold.value=-24,this.compressor.attack.value=o/1e3,this.compressor.release.value=s/1e3,u=o,d=s):(this.compressor.ratio.value=12,this.compressor.threshold.value=-24,this.compressor.attack.value=.003,this.compressor.release.value=.25,u=3,d=250);break;case"fast":this.compressor.ratio.value=12,this.compressor.threshold.value=-24,this.compressor.attack.value=.001,this.compressor.release.value=.05,u=null,d=null;break;case"medium":this.compressor.ratio.value=12,this.compressor.threshold.value=-24,this.compressor.attack.value=.01,this.compressor.release.value=.15,u=null,d=null;break;case"slow":this.compressor.ratio.value=12,this.compressor.threshold.value=-24,this.compressor.attack.value=.05,this.compressor.release.value=.5,u=null,d=null;break;case"default":default:this.compressor.ratio.value=12,this.compressor.threshold.value=-24,this.compressor.attack.value=.003,this.compressor.release.value=.25,u=null,d=null;break}const f={cmd:"agc",speed:i};u!==null&&d!==null&&(f.attack=u,f.release=d),this.audioSocket.send(JSON.stringify(f))}setBufferSize(i){switch(i){case"small":this.bufferThreshold=.05;break;case"large":this.bufferThreshold=.2;break;case"medium":default:this.bufferThreshold=.1;break}const o=this.audioCtx.currentTime;this.playTime=o+this.bufferThreshold,this.audioSocket.send(JSON.stringify({cmd:"buffer",size:i}))}getAudioDelay(){if(!this.audioCtx)return 0;const i=this.playTime-this.audioCtx.currentTime,o=Math.max(0,i*1e3);return this.delayHistory.push(o),this.delayHistory.length>this.maxDelayHistorySize&&this.delayHistory.shift(),this.delayHistory.reduce((s,u)=>s+u,0)/this.delayHistory.length}setSquelch(i){this.squelch=i}setSquelchThreshold(i){this.squelchThreshold=i}getPowerDb(){return this.dBPower}setUserID(i){this.audioSocket.send(JSON.stringify({cmd:"userid",userid:i}))}setSignalDecoder(i){this.signalDecoder=i}getSignalDecoder(){return this.signalDecoder}gridSquareToLatLong(i){const o=i.toUpperCase();let s=(o.charCodeAt(0)-65)*20-180,u=(o.charCodeAt(1)-65)*10-90;return o.length>=4&&(s+=(o.charCodeAt(2)-48)*2,u+=o.charCodeAt(3)-48),o.length==6?(s+=(o.charCodeAt(4)-65)*.08333333333333333,u+=(o.charCodeAt(5)-65)*.041666666666666664,s+=.041666666666666664,u+=.010416666666666666):o.length==4&&(s+=1,u+=.5),[u,s]}initTimer(){setInterval(()=>{this.updateCollectionStatus()},1e3)}extractGridLocators(i){const o=/[A-R]{2}[0-9]{2}([A-X]{2})?/gi,s=i.match(o);return s?Array.from(new Set(s)):[]}calculateDistance(i,o,s,u){function d(x){return x*Math.PI/180}var f=6371,h=d(s-i),p=d(u-o),m=Math.sin(h/2)*Math.sin(h/2)+Math.cos(d(i))*Math.cos(d(s))*Math.sin(p/2)*Math.sin(p/2),y=2*Math.atan2(Math.sqrt(m),Math.sqrt(1-m)),v=f*y;return v}updateCollectionStatus(){const i=15-new Date().getSeconds()%15;i===15&&!this.isCollecting?this.startCollection():i===1&&this.isCollecting&&this.stopCollection()}startCollection(){this.isCollecting=!0,this.accumulator=[]}async stopCollection(){if(this.isCollecting=!1,this.decodeFT8){const i=new Float32Array(this.accumulator.flat());let o=await cw(i);const s=document.getElementById("ft8MessagesList");let u=this.gridSquareToLatLong(this.grid_locator);for(let d of o){let f=this.extractGridLocators(d.text);if(f.length>0){let h=this.gridSquareToLatLong(f[0]),p=this.calculateDistance(u[0],u[1],h[0],h[1]);p>this.farthestDistance&&(this.farthestDistance=p,document.getElementById("farthest-distance").textContent="Farthest Distance: ".concat(this.farthestDistance.toFixed(2)," km"));const m=document.createElement("div");m.classList.add("glass-message","p-2","rounded-lg","text-sm","flex","justify-between","items-center");const y=document.createElement("div");y.classList.add("flex-grow"),y.textContent=d.text,m.appendChild(y);const v=document.createElement("div");v.classList.add("flex","flex-col","items-end","ml-2","text-xs");const x=document.createElement("div");f.forEach((M,R)=>{const j=document.createElement("a");j.href="https://www.levinecentral.com/ham/grid_square.php?&Grid=".concat(M,"&Zoom=13&sm=y"),j.classList.add("text-yellow-300","hover:underline"),j.textContent=M,j.target="_blank",R>0&&x.appendChild(document.createTextNode(", ")),x.appendChild(j)}),v.appendChild(x);const E=document.createElement("div");E.textContent="".concat(p.toFixed(2)," km"),v.appendChild(E),m.appendChild(v),s.appendChild(m)}}setTimeout(()=>{s.scrollTop=s.scrollHeight},500)}}playAudio(i){if(this.mute||this.squelchMute&&this.squelch||this.audioCtx.state!=="running")return;this.isCollecting&&this.decodeFT8&&this.accumulator.push(...i);const o=this.playPCM(i,this.playTime,this.audioOutputSps,1),s=this.audioCtx.currentTime,u=this.bufferThreshold||.1;this.playTime-s<=u?this.playTime=s+u+o:this.playTime-s>.5?this.playTime=s+u:this.playTime+=o,this.isRecording&&this.recordedAudio.push(...i)}playPCM(i,o,s,u){if(!this.audioInputNode)return console.warn("Audio not initialized"),0;const d=new K0(this.audioCtx),f=new ic({length:i.length,numberOfChannels:1,sampleRate:this.audioOutputSps});f.copyToChannel(i,0,0),d.buffer=f,d.connect(this.audioInputNode);const h=Math.max(o,this.audioCtx.currentTime);return d.start(h),d.onended=()=>{d.disconnect()},f.duration}startRecording(){this.isRecording||(this.isRecording=!0,this.recordedChunks=[],this.mediaRecorder=new MediaRecorder(this.destinationNode.stream),this.mediaRecorder.ondataavailable=i=>{i.data.size>0&&this.recordedChunks.push(i.data)},this.mediaRecorder.start())}stopRecording(){this.isRecording&&(this.isRecording=!1,this.mediaRecorder.stop())}downloadRecording(){if(this.recordedChunks.length===0){console.warn("No recorded audio to download");return}new Blob(this.recordedChunks,{type:"audio/webm"}).arrayBuffer().then(i=>{this.audioCtx.decodeAudioData(i).then(o=>{const s=this.createWavFile(o),u=URL.createObjectURL(new Blob([s],{type:"audio/wav"})),d=document.createElement("a");document.body.appendChild(d),d.style.display="none",d.href=u,d.download="recorded_audio.wav",d.click(),window.URL.revokeObjectURL(u)})})}createWavFile(i){const o=i.numberOfChannels,s=i.sampleRate,u=1,d=16,f=d/8,h=o*f,p=s*h,m=i.length*h,y=44+m,v=new ArrayBuffer(y),x=new DataView(v),E=(R,j,ie)=>{for(let B=0;B<ie.length;B++)R.setUint8(j+B,ie.charCodeAt(B))};E(x,0,"RIFF"),x.setUint32(4,y-8,!0),E(x,8,"WAVE"),E(x,12,"fmt "),x.setUint32(16,16,!0),x.setUint16(20,u,!0),x.setUint16(22,o,!0),x.setUint32(24,s,!0),x.setUint32(28,p,!0),x.setUint16(32,h,!0),x.setUint16(34,d,!0),E(x,36,"data"),x.setUint32(40,m,!0);const M=44;for(let R=0;R<i.numberOfChannels;R++){const j=i.getChannelData(R);for(let ie=0;ie<j.length;ie++){const B=Math.max(-1,Math.min(1,j[ie]));x.setInt16(M+(ie*o+R)*f,B*32767,!0)}}return v}}var um,cm;function dm(){return cm||(cm=1,um={jet:[{index:0,rgb:[0,0,131]},{index:.125,rgb:[0,60,170]},{index:.375,rgb:[5,255,255]},{index:.625,rgb:[255,255,0]},{index:.875,rgb:[250,0,0]},{index:1,rgb:[128,0,0]}],hsv:[{index:0,rgb:[255,0,0]},{index:.169,rgb:[253,255,2]},{index:.173,rgb:[247,255,2]},{index:.337,rgb:[0,252,4]},{index:.341,rgb:[0,252,10]},{index:.506,rgb:[1,249,255]},{index:.671,rgb:[2,0,253]},{index:.675,rgb:[8,0,253]},{index:.839,rgb:[255,0,251]},{index:.843,rgb:[255,0,245]},{index:1,rgb:[255,0,6]}],hot:[{index:0,rgb:[0,0,0]},{index:.3,rgb:[230,0,0]},{index:.6,rgb:[255,210,0]},{index:1,rgb:[255,255,255]}],spring:[{index:0,rgb:[255,0,255]},{index:1,rgb:[255,255,0]}],summer:[{index:0,rgb:[0,128,102]},{index:1,rgb:[255,255,102]}],autumn:[{index:0,rgb:[255,0,0]},{index:1,rgb:[255,255,0]}],winter:[{index:0,rgb:[0,0,255]},{index:1,rgb:[0,255,128]}],bone:[{index:0,rgb:[0,0,0]},{index:.376,rgb:[84,84,116]},{index:.753,rgb:[169,200,200]},{index:1,rgb:[255,255,255]}],copper:[{index:0,rgb:[0,0,0]},{index:.804,rgb:[255,160,102]},{index:1,rgb:[255,199,127]}],greys:[{index:0,rgb:[0,0,0]},{index:1,rgb:[255,255,255]}],yignbu:[{index:0,rgb:[8,29,88]},{index:.125,rgb:[37,52,148]},{index:.25,rgb:[34,94,168]},{index:.375,rgb:[29,145,192]},{index:.5,rgb:[65,182,196]},{index:.625,rgb:[127,205,187]},{index:.75,rgb:[199,233,180]},{index:.875,rgb:[237,248,217]},{index:1,rgb:[255,255,217]}],greens:[{index:0,rgb:[0,68,27]},{index:.125,rgb:[0,109,44]},{index:.25,rgb:[35,139,69]},{index:.375,rgb:[65,171,93]},{index:.5,rgb:[116,196,118]},{index:.625,rgb:[161,217,155]},{index:.75,rgb:[199,233,192]},{index:.875,rgb:[229,245,224]},{index:1,rgb:[247,252,245]}],yiorrd:[{index:0,rgb:[128,0,38]},{index:.125,rgb:[189,0,38]},{index:.25,rgb:[227,26,28]},{index:.375,rgb:[252,78,42]},{index:.5,rgb:[253,141,60]},{index:.625,rgb:[254,178,76]},{index:.75,rgb:[254,217,118]},{index:.875,rgb:[255,237,160]},{index:1,rgb:[255,255,204]}],bluered:[{index:0,rgb:[0,0,255]},{index:1,rgb:[255,0,0]}],rdbu:[{index:0,rgb:[5,10,172]},{index:.35,rgb:[106,137,247]},{index:.5,rgb:[190,190,190]},{index:.6,rgb:[220,170,132]},{index:.7,rgb:[230,145,90]},{index:1,rgb:[178,10,28]}],picnic:[{index:0,rgb:[0,0,255]},{index:.1,rgb:[51,153,255]},{index:.2,rgb:[102,204,255]},{index:.3,rgb:[153,204,255]},{index:.4,rgb:[204,204,255]},{index:.5,rgb:[255,255,255]},{index:.6,rgb:[255,204,255]},{index:.7,rgb:[255,153,255]},{index:.8,rgb:[255,102,204]},{index:.9,rgb:[255,102,102]},{index:1,rgb:[255,0,0]}],rainbow:[{index:0,rgb:[150,0,90]},{index:.125,rgb:[0,0,200]},{index:.25,rgb:[0,25,255]},{index:.375,rgb:[0,152,255]},{index:.5,rgb:[44,255,150]},{index:.625,rgb:[151,255,0]},{index:.75,rgb:[255,234,0]},{index:.875,rgb:[255,111,0]},{index:1,rgb:[255,0,0]}],portland:[{index:0,rgb:[12,51,131]},{index:.25,rgb:[10,136,186]},{index:.5,rgb:[242,211,56]},{index:.75,rgb:[242,143,56]},{index:1,rgb:[217,30,30]}],blackbody:[{index:0,rgb:[0,0,0]},{index:.2,rgb:[230,0,0]},{index:.4,rgb:[230,210,0]},{index:.7,rgb:[255,255,255]},{index:1,rgb:[160,200,255]}],earth:[{index:0,rgb:[0,0,130]},{index:.1,rgb:[0,180,180]},{index:.2,rgb:[40,210,40]},{index:.4,rgb:[230,230,50]},{index:.6,rgb:[120,70,20]},{index:1,rgb:[255,255,255]}],electric:[{index:0,rgb:[0,0,0]},{index:.15,rgb:[30,0,100]},{index:.4,rgb:[120,0,100]},{index:.6,rgb:[160,90,0]},{index:.8,rgb:[230,200,0]},{index:1,rgb:[255,250,220]}],alpha:[{index:0,rgb:[255,255,255,0]},{index:1,rgb:[255,255,255,1]}],viridis:[{index:0,rgb:[68,1,84]},{index:.13,rgb:[71,44,122]},{index:.25,rgb:[59,81,139]},{index:.38,rgb:[44,113,142]},{index:.5,rgb:[33,144,141]},{index:.63,rgb:[39,173,129]},{index:.75,rgb:[92,200,99]},{index:.88,rgb:[170,220,50]},{index:1,rgb:[253,231,37]}],inferno:[{index:0,rgb:[0,0,4]},{index:.13,rgb:[31,12,72]},{index:.25,rgb:[85,15,109]},{index:.38,rgb:[136,34,106]},{index:.5,rgb:[186,54,85]},{index:.63,rgb:[227,89,51]},{index:.75,rgb:[249,140,10]},{index:.88,rgb:[249,201,50]},{index:1,rgb:[252,255,164]}],magma:[{index:0,rgb:[0,0,4]},{index:.13,rgb:[28,16,68]},{index:.25,rgb:[79,18,123]},{index:.38,rgb:[129,37,129]},{index:.5,rgb:[181,54,122]},{index:.63,rgb:[229,80,100]},{index:.75,rgb:[251,135,97]},{index:.88,rgb:[254,194,135]},{index:1,rgb:[252,253,191]}],plasma:[{index:0,rgb:[13,8,135]},{index:.13,rgb:[75,3,161]},{index:.25,rgb:[125,3,168]},{index:.38,rgb:[168,34,150]},{index:.5,rgb:[203,70,121]},{index:.63,rgb:[229,107,93]},{index:.75,rgb:[248,148,65]},{index:.88,rgb:[253,195,40]},{index:1,rgb:[240,249,33]}],warm:[{index:0,rgb:[125,0,179]},{index:.13,rgb:[172,0,187]},{index:.25,rgb:[219,0,170]},{index:.38,rgb:[255,0,130]},{index:.5,rgb:[255,63,74]},{index:.63,rgb:[255,123,0]},{index:.75,rgb:[234,176,0]},{index:.88,rgb:[190,228,0]},{index:1,rgb:[147,255,0]}],cool:[{index:0,rgb:[125,0,179]},{index:.13,rgb:[116,0,218]},{index:.25,rgb:[98,74,237]},{index:.38,rgb:[68,146,231]},{index:.5,rgb:[0,204,197]},{index:.63,rgb:[0,247,146]},{index:.75,rgb:[0,255,88]},{index:.88,rgb:[40,255,8]},{index:1,rgb:[147,255,0]}],"rainbow-soft":[{index:0,rgb:[125,0,179]},{index:.1,rgb:[199,0,180]},{index:.2,rgb:[255,0,121]},{index:.3,rgb:[255,108,0]},{index:.4,rgb:[222,194,0]},{index:.5,rgb:[150,255,0]},{index:.6,rgb:[0,255,55]},{index:.7,rgb:[0,246,150]},{index:.8,rgb:[50,167,222]},{index:.9,rgb:[103,51,235]},{index:1,rgb:[124,0,186]}],bathymetry:[{index:0,rgb:[40,26,44]},{index:.13,rgb:[59,49,90]},{index:.25,rgb:[64,76,139]},{index:.38,rgb:[63,110,151]},{index:.5,rgb:[72,142,158]},{index:.63,rgb:[85,174,163]},{index:.75,rgb:[120,206,163]},{index:.88,rgb:[187,230,172]},{index:1,rgb:[253,254,204]}],cdom:[{index:0,rgb:[47,15,62]},{index:.13,rgb:[87,23,86]},{index:.25,rgb:[130,28,99]},{index:.38,rgb:[171,41,96]},{index:.5,rgb:[206,67,86]},{index:.63,rgb:[230,106,84]},{index:.75,rgb:[242,149,103]},{index:.88,rgb:[249,193,135]},{index:1,rgb:[254,237,176]}],chlorophyll:[{index:0,rgb:[18,36,20]},{index:.13,rgb:[25,63,41]},{index:.25,rgb:[24,91,59]},{index:.38,rgb:[13,119,72]},{index:.5,rgb:[18,148,80]},{index:.63,rgb:[80,173,89]},{index:.75,rgb:[132,196,122]},{index:.88,rgb:[175,221,162]},{index:1,rgb:[215,249,208]}],density:[{index:0,rgb:[54,14,36]},{index:.13,rgb:[89,23,80]},{index:.25,rgb:[110,45,132]},{index:.38,rgb:[120,77,178]},{index:.5,rgb:[120,113,213]},{index:.63,rgb:[115,151,228]},{index:.75,rgb:[134,185,227]},{index:.88,rgb:[177,214,227]},{index:1,rgb:[230,241,241]}],"freesurface-blue":[{index:0,rgb:[30,4,110]},{index:.13,rgb:[47,14,176]},{index:.25,rgb:[41,45,236]},{index:.38,rgb:[25,99,212]},{index:.5,rgb:[68,131,200]},{index:.63,rgb:[114,156,197]},{index:.75,rgb:[157,181,203]},{index:.88,rgb:[200,208,216]},{index:1,rgb:[241,237,236]}],"freesurface-red":[{index:0,rgb:[60,9,18]},{index:.13,rgb:[100,17,27]},{index:.25,rgb:[142,20,29]},{index:.38,rgb:[177,43,27]},{index:.5,rgb:[192,87,63]},{index:.63,rgb:[205,125,105]},{index:.75,rgb:[216,162,148]},{index:.88,rgb:[227,199,193]},{index:1,rgb:[241,237,236]}],oxygen:[{index:0,rgb:[64,5,5]},{index:.13,rgb:[106,6,15]},{index:.25,rgb:[144,26,7]},{index:.38,rgb:[168,64,3]},{index:.5,rgb:[188,100,4]},{index:.63,rgb:[206,136,11]},{index:.75,rgb:[220,174,25]},{index:.88,rgb:[231,215,44]},{index:1,rgb:[248,254,105]}],par:[{index:0,rgb:[51,20,24]},{index:.13,rgb:[90,32,35]},{index:.25,rgb:[129,44,34]},{index:.38,rgb:[159,68,25]},{index:.5,rgb:[182,99,19]},{index:.63,rgb:[199,134,22]},{index:.75,rgb:[212,171,35]},{index:.88,rgb:[221,210,54]},{index:1,rgb:[225,253,75]}],phase:[{index:0,rgb:[145,105,18]},{index:.13,rgb:[184,71,38]},{index:.25,rgb:[186,58,115]},{index:.38,rgb:[160,71,185]},{index:.5,rgb:[110,97,218]},{index:.63,rgb:[50,123,164]},{index:.75,rgb:[31,131,110]},{index:.88,rgb:[77,129,34]},{index:1,rgb:[145,105,18]}],salinity:[{index:0,rgb:[42,24,108]},{index:.13,rgb:[33,50,162]},{index:.25,rgb:[15,90,145]},{index:.38,rgb:[40,118,137]},{index:.5,rgb:[59,146,135]},{index:.63,rgb:[79,175,126]},{index:.75,rgb:[120,203,104]},{index:.88,rgb:[193,221,100]},{index:1,rgb:[253,239,154]}],temperature:[{index:0,rgb:[4,35,51]},{index:.13,rgb:[23,51,122]},{index:.25,rgb:[85,59,157]},{index:.38,rgb:[129,79,143]},{index:.5,rgb:[175,95,130]},{index:.63,rgb:[222,112,101]},{index:.75,rgb:[249,146,66]},{index:.88,rgb:[249,196,65]},{index:1,rgb:[232,250,91]}],turbidity:[{index:0,rgb:[34,31,27]},{index:.13,rgb:[65,50,41]},{index:.25,rgb:[98,69,52]},{index:.38,rgb:[131,89,57]},{index:.5,rgb:[161,112,59]},{index:.63,rgb:[185,140,66]},{index:.75,rgb:[202,174,88]},{index:.88,rgb:[216,209,126]},{index:1,rgb:[233,246,171]}],"velocity-blue":[{index:0,rgb:[17,32,64]},{index:.13,rgb:[35,52,116]},{index:.25,rgb:[29,81,156]},{index:.38,rgb:[31,113,162]},{index:.5,rgb:[50,144,169]},{index:.63,rgb:[87,173,176]},{index:.75,rgb:[149,196,189]},{index:.88,rgb:[203,221,211]},{index:1,rgb:[254,251,230]}],"velocity-green":[{index:0,rgb:[23,35,19]},{index:.13,rgb:[24,64,38]},{index:.25,rgb:[11,95,45]},{index:.38,rgb:[39,123,35]},{index:.5,rgb:[95,146,12]},{index:.63,rgb:[152,165,18]},{index:.75,rgb:[201,186,69]},{index:.88,rgb:[233,216,137]},{index:1,rgb:[255,253,205]}],cubehelix:[{index:0,rgb:[0,0,0]},{index:.07,rgb:[22,5,59]},{index:.13,rgb:[60,4,105]},{index:.2,rgb:[109,1,135]},{index:.27,rgb:[161,0,147]},{index:.33,rgb:[210,2,142]},{index:.4,rgb:[251,11,123]},{index:.47,rgb:[255,29,97]},{index:.53,rgb:[255,54,69]},{index:.6,rgb:[255,85,46]},{index:.67,rgb:[255,120,34]},{index:.73,rgb:[255,157,37]},{index:.8,rgb:[241,191,57]},{index:.87,rgb:[224,220,93]},{index:.93,rgb:[218,241,142]},{index:1,rgb:[227,253,198]}]}),um}var ac,fm;function O_(){if(fm)return ac;fm=1;function r(i,o,s){return i*(1-s)+o*s}return ac=r,ac}var oc,hm;function R_(){if(hm)return oc;hm=1;var r=dm(),i=O_();oc=o;function o(f){var h,p,m,y,v,x,E,M,B,R,j;if(f||(f={}),M=(f.nshades||72)-1,E=f.format||"hex",x=f.colormap,x||(x="jet"),typeof x=="string"){if(x=x.toLowerCase(),!r[x])throw Error(x+" not a supported colorscale");v=r[x]}else if(Array.isArray(x))v=x.slice();else throw Error("unsupported colormap option",x);if(v.length>M+1)throw new Error(x+" map requires nshades to be at least size "+v.length);Array.isArray(f.alpha)?f.alpha.length!==2?R=[1,1]:R=f.alpha.slice():typeof f.alpha=="number"?R=[f.alpha,f.alpha]:R=[1,1],h=v.map(function(V){return Math.round(V.index*M)}),R[0]=Math.min(Math.max(R[0],0),1),R[1]=Math.min(Math.max(R[1],0),1);var ie=v.map(function(V,Y){var N=v[Y].index,I=v[Y].rgb.slice();return I.length===4&&I[3]>=0&&I[3]<=1||(I[3]=R[0]+(R[1]-R[0])*N),I}),B=[];for(j=0;j<h.length-1;++j){y=h[j+1]-h[j],p=ie[j],m=ie[j+1];for(var $=0;$<y;$++){var ae=$/y;B.push([Math.round(i(p[0],m[0],ae)),Math.round(i(p[1],m[1],ae)),Math.round(i(p[2],m[2],ae)),i(p[3],m[3],ae)])}}return B.push(v[v.length-1].rgb.concat(R[1])),E==="hex"?B=B.map(u):E==="rgbaString"?B=B.map(d):E==="float"&&(B=B.map(s)),B}function s(f){return[f[0]/255,f[1]/255,f[2]/255,f[3]]}function u(f){for(var h,p="#",m=0;m<3;++m)h=f[m],h=h.toString(16),p+=("00"+h).substr(h.length);return p}function d(f){return"rgba("+f.join(",")+")"}return oc}var I_=R_();const L_=Fs(I_);dm();const N_=[[.18995,.07176,.23217],[.19483,.08339,.26149],[.19956,.09498,.29024],[.20415,.10652,.31844],[.2086,.11802,.34607],[.21291,.12947,.37314],[.21708,.14087,.39964],[.22111,.15223,.42558],[.225,.16354,.45096],[.22875,.17481,.47578],[.23236,.18603,.50004],[.23582,.1972,.52373],[.23915,.20833,.54686],[.24234,.21941,.56942],[.24539,.23044,.59142],[.2483,.24143,.61286],[.25107,.25237,.63374],[.25369,.26327,.65406],[.25618,.27412,.67381],[.25853,.28492,.693],[.26074,.29568,.71162],[.2628,.30639,.72968],[.26473,.31706,.74718],[.26652,.32768,.76412],[.26816,.33825,.7805],[.26967,.34878,.79631],[.27103,.35926,.81156],[.27226,.3697,.82624],[.27334,.38008,.84037],[.27429,.39043,.85393],[.27509,.40072,.86692],[.27576,.41097,.87936],[.27628,.42118,.89123],[.27667,.43134,.90254],[.27691,.44145,.91328],[.27701,.45152,.92347],[.27698,.46153,.93309],[.2768,.47151,.94214],[.27648,.48144,.95064],[.27603,.49132,.95857],[.27543,.50115,.96594],[.27469,.51094,.97275],[.27381,.52069,.97899],[.27273,.5304,.98461],[.27106,.54015,.9893],[.26878,.54995,.99303],[.26592,.55979,.99583],[.26252,.56967,.99773],[.25862,.57958,.99876],[.25425,.5895,.99896],[.24946,.59943,.99835],[.24427,.60937,.99697],[.23874,.61931,.99485],[.23288,.62923,.99202],[.22676,.63913,.98851],[.22039,.64901,.98436],[.21382,.65886,.97959],[.20708,.66866,.97423],[.20021,.67842,.96833],[.19326,.68812,.9619],[.18625,.69775,.95498],[.17923,.70732,.94761],[.17223,.7168,.93981],[.16529,.7262,.93161],[.15844,.73551,.92305],[.15173,.74472,.91416],[.14519,.75381,.90496],[.13886,.76279,.8955],[.13278,.77165,.8858],[.12698,.78037,.8759],[.12151,.78896,.86581],[.11639,.7974,.85559],[.11167,.80569,.84525],[.10738,.81381,.83484],[.10357,.82177,.82437],[.10026,.82955,.81389],[.0975,.83714,.80342],[.09532,.84455,.79299],[.09377,.85175,.78264],[.09287,.85875,.7724],[.09267,.86554,.7623],[.0932,.87211,.75237],[.09451,.87844,.74265],[.09662,.88454,.73316],[.09958,.8904,.72393],[.10342,.896,.715],[.10815,.90142,.70599],[.11374,.90673,.69651],[.12014,.91193,.6866],[.12733,.91701,.67627],[.13526,.92197,.66556],[.14391,.9268,.65448],[.15323,.93151,.64308],[.16319,.93609,.63137],[.17377,.94053,.61938],[.18491,.94484,.60713],[.19659,.94901,.59466],[.20877,.95304,.58199],[.22142,.95692,.56914],[.23449,.96065,.55614],[.24797,.96423,.54303],[.2618,.96765,.52981],[.27597,.97092,.51653],[.29042,.97403,.50321],[.30513,.97697,.48987],[.32006,.97974,.47654],[.33517,.98234,.46325],[.35043,.98477,.45002],[.36581,.98702,.43688],[.38127,.98909,.42386],[.39678,.99098,.41098],[.41229,.99268,.39826],[.42778,.99419,.38575],[.44321,.99551,.37345],[.45854,.99663,.3614],[.47375,.99755,.34963],[.48879,.99828,.33816],[.50362,.99879,.32701],[.51822,.9991,.31622],[.53255,.99919,.30581],[.54658,.99907,.29581],[.56026,.99873,.28623],[.57357,.99817,.27712],[.58646,.99739,.26849],[.59891,.99638,.26038],[.61088,.99514,.2528],[.62233,.99366,.24579],[.63323,.99195,.23937],[.64362,.98999,.23356],[.65394,.98775,.22835],[.66428,.98524,.2237],[.67462,.98246,.2196],[.68494,.97941,.21602],[.69525,.9761,.21294],[.70553,.97255,.21032],[.71577,.96875,.20815],[.72596,.9647,.2064],[.7361,.96043,.20504],[.74617,.95593,.20406],[.75617,.95121,.20343],[.76608,.94627,.20311],[.77591,.94113,.2031],[.78563,.93579,.20336],[.79524,.93025,.20386],[.80473,.92452,.20459],[.8141,.91861,.20552],[.82333,.91253,.20663],[.83241,.90627,.20788],[.84133,.89986,.20926],[.8501,.89328,.21074],[.85868,.88655,.2123],[.86709,.87968,.21391],[.8753,.87267,.21555],[.88331,.86553,.21719],[.89112,.85826,.2188],[.8987,.85087,.22038],[.90605,.84337,.22188],[.91317,.83576,.22328],[.92004,.82806,.22456],[.92666,.82025,.2257],[.93301,.81236,.22667],[.93909,.80439,.22744],[.94489,.79634,.228],[.95039,.78823,.22831],[.9556,.78005,.22836],[.96049,.77181,.22811],[.96507,.76352,.22754],[.96931,.75519,.22663],[.97323,.74682,.22536],[.97679,.73842,.22369],[.98,.73,.22161],[.98289,.7214,.21918],[.98549,.7125,.2165],[.98781,.7033,.21358],[.98986,.69382,.21043],[.99163,.68408,.20706],[.99314,.67408,.20348],[.99438,.66386,.19971],[.99535,.65341,.19577],[.99607,.64277,.19165],[.99654,.63193,.18738],[.99675,.62093,.18297],[.99672,.60977,.17842],[.99644,.59846,.17376],[.99593,.58703,.16899],[.99517,.57549,.16412],[.99419,.56386,.15918],[.99297,.55214,.15417],[.99153,.54036,.1491],[.98987,.52854,.14398],[.98799,.51667,.13883],[.9859,.50479,.13367],[.9836,.49291,.12849],[.98108,.48104,.12332],[.97837,.4692,.11817],[.97545,.4574,.11305],[.97234,.44565,.10797],[.96904,.43399,.10294],[.96555,.42241,.09798],[.96187,.41093,.0931],[.95801,.39958,.08831],[.95398,.38836,.08362],[.94977,.37729,.07905],[.94538,.36638,.07461],[.94084,.35566,.07031],[.93612,.34513,.06616],[.93125,.33482,.06218],[.92623,.32473,.05837],[.92105,.31489,.05475],[.91572,.3053,.05134],[.91024,.29599,.04814],[.90463,.28696,.04516],[.89888,.27824,.04243],[.89298,.26981,.03993],[.88691,.26152,.03753],[.88066,.25334,.03521],[.87422,.24526,.03297],[.8676,.2373,.03082],[.86079,.22945,.02875],[.8538,.2217,.02677],[.84662,.21407,.02487],[.83926,.20654,.02305],[.83172,.19912,.02131],[.82399,.19182,.01966],[.81608,.18462,.01809],[.80799,.17753,.0166],[.79971,.17055,.0152],[.79125,.16368,.01387],[.7826,.15693,.01264],[.77377,.15028,.01148],[.76476,.14374,.01041],[.75556,.13731,.00942],[.74617,.13098,.00851],[.73661,.12477,.00769],[.72686,.11867,.00695],[.71692,.11268,.00629],[.7068,.1068,.00571],[.6965,.10102,.00522],[.68602,.09536,.00481],[.67535,.0898,.00449],[.66449,.08436,.00424],[.65345,.07902,.00408],[.64223,.0738,.00401],[.63082,.06868,.00401],[.61923,.06367,.0041],[.60746,.05878,.00427],[.5955,.05399,.00453],[.58336,.04931,.00486],[.57103,.04474,.00529],[.55852,.04028,.00579],[.54583,.03593,.00638],[.53295,.03169,.00705],[.51989,.02756,.0078],[.50664,.02354,.00863],[.49321,.01963,.00955],[.4796,.01583,.01055]],D_=[[0,0,0],[0,0,.007843],[0,0,.015686],[0,0,.023529],[0,0,.031373],[0,0,.039216],[0,0,.047059],[0,0,.054902],[0,0,.062745],[0,0,.070588],[0,0,.078431],[0,0,.086275],[0,0,.094118],[0,0,.101961],[0,0,.109804],[0,0,.117647],[0,0,.12549],[0,0,.133333],[0,0,.141176],[0,0,.14902],[0,0,.156863],[0,0,.164706],[0,0,.172549],[0,0,.180392],[0,0,.188235],[0,0,.196078],[0,0,.203922],[0,0,.211765],[0,0,.219608],[0,0,.227451],[0,0,.235294],[0,0,.243137],[0,0,.25098],[0,0,.258824],[0,0,.266667],[0,0,.27451],[0,0,.282353],[0,0,.290196],[0,0,.298039],[0,0,.305882],[0,0,.313725],[0,0,.321569],[0,0,.329412],[0,0,.337255],[0,0,.345098],[0,0,.352941],[0,0,.360784],[0,0,.368627],[0,0,.376471],[0,0,.384314],[0,0,.392157],[0,0,.4],[0,0,.407843],[0,0,.415686],[0,0,.423529],[0,0,.431373],[0,0,.439216],[0,0,.447059],[0,0,.454902],[0,0,.462745],[0,0,.470588],[0,0,.478431],[0,0,.486275],[0,0,.494118],[0,0,.501961],[.011765,0,.509804],[.023529,0,.517647],[.035294,0,.52549],[.047059,0,.533333],[.058824,0,.541176],[.070588,0,.54902],[.082353,0,.556863],[.094118,0,.564706],[.105882,0,.572549],[.117647,0,.580392],[.129412,0,.588235],[.141176,0,.596078],[.152941,0,.603922],[.164706,0,.611765],[.176471,0,.619608],[.188235,0,.627451],[.2,0,.635294],[.211765,0,.643137],[.223529,0,.65098],[.235294,0,.658824],[.247059,0,.666667],[.258824,0,.67451],[.270588,0,.682353],[.282353,0,.690196],[.294118,0,.698039],[.305882,0,.705882],[.317647,0,.713725],[.329412,0,.721569],[.341176,0,.729412],[.352941,0,.737255],[.364706,0,.745098],[.376471,0,.752941],[.388235,0,.760784],[.4,0,.768627],[.411765,0,.776471],[.423529,0,.784314],[.435294,0,.792157],[.447059,0,.8],[.458824,0,.807843],[.470588,0,.815686],[.482353,0,.823529],[.494118,0,.831373],[.505882,0,.839216],[.517647,0,.847059],[.529412,0,.854902],[.541176,0,.862745],[.552941,0,.870588],[.564706,0,.878431],[.576471,0,.886275],[.588235,0,.894118],[.6,0,.901961],[.611765,0,.909804],[.623529,0,.917647],[.635294,0,.92549],[.647059,0,.933333],[.658824,0,.941176],[.670588,0,.94902],[.682353,0,.956863],[.694118,0,.964706],[.705882,0,.972549],[.717647,0,.980392],[.729412,0,.988235],[.741176,0,.996078],[.752941,0,1],[.756863,.12549,.992157],[.760784,.176471,.984314],[.764706,.215686,.976471],[.768627,.25098,.968627],[.772549,.278431,.960784],[.776471,.305882,.952941],[.780392,.329412,.945098],[.784314,.352941,.937255],[.788235,.376471,.929412],[.792157,.396078,.921569],[.796078,.415686,.913725],[.8,.431373,.905882],[.803922,.45098,.898039],[.807843,.466667,.890196],[.811765,.482353,.882353],[.815686,.501961,.87451],[.819608,.513725,.866667],[.823529,.529412,.858824],[.827451,.545098,.85098],[.831373,.560784,.843137],[.835294,.572549,.835294],[.839216,.588235,.827451],[.843137,.6,.819608],[.847059,.611765,.811765],[.85098,.627451,.803922],[.854902,.639216,.796078],[.858824,.65098,.788235],[.862745,.662745,.780392],[.866667,.67451,.772549],[.870588,.686275,.764706],[.87451,.698039,.756863],[.878431,.709804,.74902],[.882353,.717647,.741176],[.886275,.729412,.733333],[.890196,.741176,.72549],[.894118,.752941,.717647],[.898039,.760784,.709804],[.901961,.772549,.701961],[.905882,.780392,.694118],[.909804,.792157,.686275],[.913725,.8,.678431],[.917647,.811765,.670588],[.921569,.819608,.662745],[.92549,.831373,.654902],[.929412,.839216,.647059],[.933333,.85098,.639216],[.937255,.858824,.631373],[.941176,.866667,.623529],[.945098,.878431,.615686],[.94902,.886275,.607843],[.952941,.894118,.6],[.956863,.901961,.592157],[.960784,.909804,.584314],[.964706,.921569,.576471],[.968627,.929412,.568627],[.972549,.937255,.560784],[.976471,.945098,.552941],[.980392,.952941,.545098],[.984314,.960784,.537255],[.988235,.968627,.529412],[.992157,.976471,.521569],[.996078,.984314,.513725],[1,.992157,.505882],[1,1,.501961],[1,1,.509804],[1,1,.517647],[1,1,.52549],[1,1,.533333],[1,1,.541176],[1,1,.54902],[1,1,.556863],[1,1,.564706],[1,1,.572549],[1,1,.580392],[1,1,.588235],[1,1,.596078],[1,1,.603922],[1,1,.611765],[1,1,.619608],[1,1,.627451],[1,1,.635294],[1,1,.643137],[1,1,.65098],[1,1,.658824],[1,1,.666667],[1,1,.67451],[1,1,.682353],[1,1,.690196],[1,1,.698039],[1,1,.705882],[1,1,.713725],[1,1,.721569],[1,1,.729412],[1,1,.737255],[1,1,.745098],[1,1,.752941],[1,1,.760784],[1,1,.768627],[1,1,.776471],[1,1,.784314],[1,1,.792157],[1,1,.8],[1,1,.807843],[1,1,.815686],[1,1,.823529],[1,1,.831373],[1,1,.839216],[1,1,.847059],[1,1,.854902],[1,1,.862745],[1,1,.870588],[1,1,.878431],[1,1,.886275],[1,1,.894118],[1,1,.901961],[1,1,.909804],[1,1,.917647],[1,1,.92549],[1,1,.933333],[1,1,.941176],[1,1,.94902],[1,1,.956863],[1,1,.964706],[1,1,.972549],[1,1,.980392],[1,1,.988235],[1,1,.996078]],yr=[];for(let r=0;r<256;r++)r<20?yr.push([0,0,0]):r>=20&&r<70?yr.push([0,0,140*(r-20)/50]):r>=70&&r<100?yr.push([60*(r-70)/30,125*(r-70)/30,115*(r-70)/30+140]):r>=100&&r<150?yr.push([195*(r-100)/50+60,130*(r-100)/50+125,255-255*(r-100)/50]):r>=150&&r<250?yr.push([255,255-255*(r-150)/100,0]):r>=250&&yr.push([255,255*(r-250)/5,255*(r-250)/5]),yr[r]=yr[r].map(i=>i/255);const va=[];for(let r=0;r<256;r++){if(r<105){const i=13*r/105,o=0,s=134*r/105;va.push([i,o,s])}else if(r<192){const i=98+157*(r-96)/63,o=0+254*(r-96)/63,s=176+19*(r-96)/63;va.push([i,o,s])}else va.push([255,254,195]);va[r]=va[r].map(i=>i/255)}const Pi=[];for(let r=0;r<256;r++){if(r<64){const i=128+r*2;Pi.push([0,0,i])}else if(r<128){const i=(r-64)*4,o=255-(r-64)*4;Pi.push([0,i,o])}else if(r<192){const i=(r-128)*4;Pi.push([i,255,0])}else{const i=255-(r-192)*4;Pi.push([255,i,0])}Pi[r]=Pi[r].map(i=>Math.min(i,255)/255)}const pm={turbo:N_,gqrx:yr,twente:va,twentev2:D_,SpectraVU:Pi};function mm(r){return r.map(i=>{const o=new Uint8ClampedArray(4);return i.length<4&&(i=[...i,255]),o.set(i.map(s=>Math.round(s*255))),o})}function gm(r){let i;return r in pm?i=pm[r]:i=L_({colormap:r,nshades:256,format:"float",alpha:1}),i}var z_={},vm={},To,ym;function Yt(){if(ym)return To;ym=1;var r=function(i){return i&&i.Math===Math&&i};return To=r(typeof globalThis=="object"&&globalThis)||r(typeof window=="object"&&window)||r(typeof self=="object"&&self)||r(typeof Gh=="object"&&Gh)||r(typeof To=="object"&&To)||function(){return this}()||Function("return this")(),To}var sc={},bm,wm;function br(){return wm||(wm=1,bm=function(r){try{return!!r()}catch(i){return!0}}),bm}var lc,_m;function Oi(){if(_m)return lc;_m=1;var r=br();return lc=!r(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!==7}),lc}var uc,xm;function il(){if(xm)return uc;xm=1;var r=br();return uc=!r(function(){var i=(function(){}).bind();return typeof i!="function"||i.hasOwnProperty("prototype")}),uc}var cc,Sm;function dc(){if(Sm)return cc;Sm=1;var r=il(),i=Function.prototype.call;return cc=r?i.bind(i):function(){return i.apply(i,arguments)},cc}var fc={},km;function U_(){if(km)return fc;km=1;var r={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,o=i&&!r.call({1:2},1);return fc.f=o?function(s){var u=i(this,s);return!!u&&u.enumerable}:r,fc}var Cm,Em;function Mm(){return Em||(Em=1,Cm=function(r,i){return{enumerable:!(r&1),configurable:!(r&2),writable:!(r&4),value:i}}),Cm}var hc,Am;function Hn(){if(Am)return hc;Am=1;var r=il(),i=Function.prototype,o=i.call,s=r&&i.bind.bind(o,o);return hc=r?s:function(u){return function(){return o.apply(u,arguments)}},hc}var pc,Tm;function mc(){if(Tm)return pc;Tm=1;var r=Hn(),i=r({}.toString),o=r("".slice);return pc=function(s){return o(i(s),8,-1)},pc}var gc,Fm;function q_(){if(Fm)return gc;Fm=1;var r=Hn(),i=br(),o=mc(),s=Object,u=r("".split);return gc=i(function(){return!s("z").propertyIsEnumerable(0)})?function(d){return o(d)==="String"?u(d,""):s(d)}:s,gc}var Pm,Om;function Rm(){return Om||(Om=1,Pm=function(r){return r==null}),Pm}var vc,Im;function Lm(){if(Im)return vc;Im=1;var r=Rm(),i=TypeError;return vc=function(o){if(r(o))throw new i("Can't call method on "+o);return o},vc}var yc,Nm;function bc(){if(Nm)return yc;Nm=1;var r=q_(),i=Lm();return yc=function(o){return r(i(o))},yc}var wc,Dm;function On(){if(Dm)return wc;Dm=1;var r=typeof document=="object"&&document.all;return wc=typeof r>"u"&&r!==void 0?function(i){return typeof i=="function"||i===r}:function(i){return typeof i=="function"},wc}var _c,zm;function Fo(){if(zm)return _c;zm=1;var r=On();return _c=function(i){return typeof i=="object"?i!==null:r(i)},_c}var xc,Um;function Sc(){if(Um)return xc;Um=1;var r=Yt(),i=On(),o=function(s){return i(s)?s:void 0};return xc=function(s,u){return arguments.length<2?o(r[s]):r[s]&&r[s][u]},xc}var kc,qm;function j_(){if(qm)return kc;qm=1;var r=Hn();return kc=r({}.isPrototypeOf),kc}var Cc,jm;function al(){if(jm)return Cc;jm=1;var r=Yt(),i=r.navigator,o=i&&i.userAgent;return Cc=o?String(o):"",Cc}var Ec,Wm;function W_(){if(Wm)return Ec;Wm=1;var r=Yt(),i=al(),o=r.process,s=r.Deno,u=o&&o.versions||s&&s.version,d=u&&u.v8,f,h;return d&&(f=d.split("."),h=f[0]>0&&f[0]<4?1:+(f[0]+f[1])),!h&&i&&(f=i.match(/Edge\/(\d+)/),(!f||f[1]>=74)&&(f=i.match(/Chrome\/(\d+)/),f&&(h=+f[1]))),Ec=h,Ec}var Mc,Vm;function Hm(){if(Vm)return Mc;Vm=1;var r=W_(),i=br(),o=Yt(),s=o.String;return Mc=!!Object.getOwnPropertySymbols&&!i(function(){var u=Symbol("symbol detection");return!s(u)||!(Object(u)instanceof Symbol)||!Symbol.sham&&r&&r<41}),Mc}var Ac,Bm;function $m(){if(Bm)return Ac;Bm=1;var r=Hm();return Ac=r&&!Symbol.sham&&typeof Symbol.iterator=="symbol",Ac}var Tc,Ym;function Xm(){if(Ym)return Tc;Ym=1;var r=Sc(),i=On(),o=j_(),s=$m(),u=Object;return Tc=s?function(d){return typeof d=="symbol"}:function(d){var f=r("Symbol");return i(f)&&o(f.prototype,u(d))},Tc}var Fc,Jm;function V_(){if(Jm)return Fc;Jm=1;var r=String;return Fc=function(i){try{return r(i)}catch(o){return"Object"}},Fc}var Pc,Km;function Zm(){if(Km)return Pc;Km=1;var r=On(),i=V_(),o=TypeError;return Pc=function(s){if(r(s))return s;throw new o(i(s)+" is not a function")},Pc}var Oc,Gm;function H_(){if(Gm)return Oc;Gm=1;var r=Zm(),i=Rm();return Oc=function(o,s){var u=o[s];return i(u)?void 0:r(u)},Oc}var Rc,Qm;function B_(){if(Qm)return Rc;Qm=1;var r=dc(),i=On(),o=Fo(),s=TypeError;return Rc=function(u,d){var f,h;if(d==="string"&&i(f=u.toString)&&!o(h=r(f,u))||i(f=u.valueOf)&&!o(h=r(f,u))||d!=="string"&&i(f=u.toString)&&!o(h=r(f,u)))return h;throw new s("Can't convert object to primitive value")},Rc}var Ic={exports:{}},e1,t1;function $_(){return t1||(t1=1,e1=!1),e1}var Lc,n1;function Nc(){if(n1)return Lc;n1=1;var r=Yt(),i=Object.defineProperty;return Lc=function(o,s){try{i(r,o,{value:s,configurable:!0,writable:!0})}catch(u){r[o]=s}return s},Lc}var r1;function Dc(){if(r1)return Ic.exports;r1=1;var r=$_(),i=Yt(),o=Nc(),s="__core-js_shared__",u=Ic.exports=i[s]||o(s,{});return(u.versions||(u.versions=[])).push({version:"3.44.0",mode:r?"pure":"global",copyright:"\xA9 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"}),Ic.exports}var zc,i1;function a1(){if(i1)return zc;i1=1;var r=Dc();return zc=function(i,o){return r[i]||(r[i]=o||{})},zc}var Uc,o1;function Y_(){if(o1)return Uc;o1=1;var r=Lm(),i=Object;return Uc=function(o){return i(r(o))},Uc}var qc,s1;function $r(){if(s1)return qc;s1=1;var r=Hn(),i=Y_(),o=r({}.hasOwnProperty);return qc=Object.hasOwn||function(s,u){return o(i(s),u)},qc}var jc,l1;function u1(){if(l1)return jc;l1=1;var r=Hn(),i=0,o=Math.random(),s=r(1.1.toString);return jc=function(u){return"Symbol("+(u===void 0?"":u)+")_"+s(++i+o,36)},jc}var Wc,c1;function X_(){if(c1)return Wc;c1=1;var r=Yt(),i=a1(),o=$r(),s=u1(),u=Hm(),d=$m(),f=r.Symbol,h=i("wks"),p=d?f.for||f:f&&f.withoutSetter||s;return Wc=function(m){return o(h,m)||(h[m]=u&&o(f,m)?f[m]:p("Symbol."+m)),h[m]},Wc}var Vc,d1;function J_(){if(d1)return Vc;d1=1;var r=dc(),i=Fo(),o=Xm(),s=H_(),u=B_(),d=X_(),f=TypeError,h=d("toPrimitive");return Vc=function(p,m){if(!i(p)||o(p))return p;var y=s(p,h),v;if(y){if(m===void 0&&(m="default"),v=r(y,p,m),!i(v)||o(v))return v;throw new f("Can't convert object to primitive value")}return m===void 0&&(m="number"),u(p,m)},Vc}var Hc,f1;function h1(){if(f1)return Hc;f1=1;var r=J_(),i=Xm();return Hc=function(o){var s=r(o,"string");return i(s)?s:s+""},Hc}var Bc,p1;function m1(){if(p1)return Bc;p1=1;var r=Yt(),i=Fo(),o=r.document,s=i(o)&&i(o.createElement);return Bc=function(u){return s?o.createElement(u):{}},Bc}var $c,g1;function v1(){if(g1)return $c;g1=1;var r=Oi(),i=br(),o=m1();return $c=!r&&!i(function(){return Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a!==7}),$c}var y1;function b1(){if(y1)return sc;y1=1;var r=Oi(),i=dc(),o=U_(),s=Mm(),u=bc(),d=h1(),f=$r(),h=v1(),p=Object.getOwnPropertyDescriptor;return sc.f=r?p:function(m,y){if(m=u(m),y=d(y),h)try{return p(m,y)}catch(v){}if(f(m,y))return s(!i(o.f,m,y),m[y])},sc}var Yc={},Xc,w1;function K_(){if(w1)return Xc;w1=1;var r=Oi(),i=br();return Xc=r&&i(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!==42}),Xc}var Jc,_1;function x1(){if(_1)return Jc;_1=1;var r=Fo(),i=String,o=TypeError;return Jc=function(s){if(r(s))return s;throw new o(i(s)+" is not an object")},Jc}var S1;function Kc(){if(S1)return Yc;S1=1;var r=Oi(),i=v1(),o=K_(),s=x1(),u=h1(),d=TypeError,f=Object.defineProperty,h=Object.getOwnPropertyDescriptor,p="enumerable",m="configurable",y="writable";return Yc.f=r?o?function(v,x,E){if(s(v),x=u(x),s(E),typeof v=="function"&&x==="prototype"&&"value"in E&&y in E&&!E[y]){var M=h(v,x);M&&M[y]&&(v[x]=E.value,E={configurable:m in E?E[m]:M[m],enumerable:p in E?E[p]:M[p],writable:!1})}return f(v,x,E)}:f:function(v,x,E){if(s(v),x=u(x),s(E),i)try{return f(v,x,E)}catch(M){}if("get"in E||"set"in E)throw new d("Accessors not supported");return"value"in E&&(v[x]=E.value),v},Yc}var Zc,k1;function C1(){if(k1)return Zc;k1=1;var r=Oi(),i=Kc(),o=Mm();return Zc=r?function(s,u,d){return i.f(s,u,o(1,d))}:function(s,u,d){return s[u]=d,s},Zc}var Gc={exports:{}},Qc,E1;function Z_(){if(E1)return Qc;E1=1;var r=Oi(),i=$r(),o=Function.prototype,s=r&&Object.getOwnPropertyDescriptor,u=i(o,"name"),d=u&&(function(){}).name==="something",f=u&&(!r||r&&s(o,"name").configurable);return Qc={EXISTS:u,PROPER:d,CONFIGURABLE:f},Qc}var ed,M1;function G_(){if(M1)return ed;M1=1;var r=Hn(),i=On(),o=Dc(),s=r(Function.toString);return i(o.inspectSource)||(o.inspectSource=function(u){return s(u)}),ed=o.inspectSource,ed}var td,A1;function Q_(){if(A1)return td;A1=1;var r=Yt(),i=On(),o=r.WeakMap;return td=i(o)&&/native code/.test(String(o)),td}var nd,T1;function e6(){if(T1)return nd;T1=1;var r=a1(),i=u1(),o=r("keys");return nd=function(s){return o[s]||(o[s]=i(s))},nd}var F1,P1;function O1(){return P1||(P1=1,F1={}),F1}var rd,R1;function t6(){if(R1)return rd;R1=1;var r=Q_(),i=Yt(),o=Fo(),s=C1(),u=$r(),d=Dc(),f=e6(),h=O1(),p="Object already initialized",m=i.TypeError,y=i.WeakMap,v,x,E,M=function(B){return E(B)?x(B):v(B,{})},R=function(B){return function($){var ae;if(!o($)||(ae=x($)).type!==B)throw new m("Incompatible receiver, "+B+" required");return ae}};if(r||d.state){var j=d.state||(d.state=new y);j.get=j.get,j.has=j.has,j.set=j.set,v=function(B,$){if(j.has(B))throw new m(p);return $.facade=B,j.set(B,$),$},x=function(B){return j.get(B)||{}},E=function(B){return j.has(B)}}else{var ie=f("state");h[ie]=!0,v=function(B,$){if(u(B,ie))throw new m(p);return $.facade=B,s(B,ie,$),$},x=function(B){return u(B,ie)?B[ie]:{}},E=function(B){return u(B,ie)}}return rd={set:v,get:x,has:E,enforce:M,getterFor:R},rd}var I1;function n6(){if(I1)return Gc.exports;I1=1;var r=Hn(),i=br(),o=On(),s=$r(),u=Oi(),d=Z_().CONFIGURABLE,f=G_(),h=t6(),p=h.enforce,m=h.get,y=String,v=Object.defineProperty,x=r("".slice),E=r("".replace),M=r([].join),R=u&&!i(function(){return v(function(){},"length",{value:8}).length!==8}),j=String(String).split("String"),ie=Gc.exports=function(B,$,ae){x(y($),0,7)==="Symbol("&&($="["+E(y($),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),ae&&ae.getter&&($="get "+$),ae&&ae.setter&&($="set "+$),(!s(B,"name")||d&&B.name!==$)&&(u?v(B,"name",{value:$,configurable:!0}):B.name=$),R&&ae&&s(ae,"arity")&&B.length!==ae.arity&&v(B,"length",{value:ae.arity});try{ae&&s(ae,"constructor")&&ae.constructor?u&&v(B,"prototype",{writable:!1}):B.prototype&&(B.prototype=void 0)}catch(Y){}var V=p(B);return s(V,"source")||(V.source=M(j,typeof $=="string"?$:"")),B};return Function.prototype.toString=ie(function(){return o(this)&&m(this).source||f(this)},"toString"),Gc.exports}var id,L1;function r6(){if(L1)return id;L1=1;var r=On(),i=Kc(),o=n6(),s=Nc();return id=function(u,d,f,h){h||(h={});var p=h.enumerable,m=h.name!==void 0?h.name:d;if(r(f)&&o(f,m,h),h.global)p?u[d]=f:s(d,f);else{try{h.unsafe?u[d]&&(p=!0):delete u[d]}catch(y){}p?u[d]=f:i.f(u,d,{value:f,enumerable:!1,configurable:!h.nonConfigurable,writable:!h.nonWritable})}return u},id}var ad={},od,N1;function i6(){if(N1)return od;N1=1;var r=Math.ceil,i=Math.floor;return od=Math.trunc||function(o){var s=+o;return(s>0?i:r)(s)},od}var sd,D1;function z1(){if(D1)return sd;D1=1;var r=i6();return sd=function(i){var o=+i;return o!==o||o===0?0:r(o)},sd}var ld,U1;function a6(){if(U1)return ld;U1=1;var r=z1(),i=Math.max,o=Math.min;return ld=function(s,u){var d=r(s);return d<0?i(d+u,0):o(d,u)},ld}var ud,q1;function o6(){if(q1)return ud;q1=1;var r=z1(),i=Math.min;return ud=function(o){var s=r(o);return s>0?i(s,9007199254740991):0},ud}var cd,j1;function s6(){if(j1)return cd;j1=1;var r=o6();return cd=function(i){return r(i.length)},cd}var dd,W1;function l6(){if(W1)return dd;W1=1;var r=bc(),i=a6(),o=s6(),s=function(u){return function(d,f,h){var p=r(d),m=o(p);if(m===0)return!u&&-1;var y=i(h,m),v;if(u&&f!==f){for(;m>y;)if(v=p[y++],v!==v)return!0}else for(;m>y;y++)if((u||y in p)&&p[y]===f)return u||y||0;return!u&&-1}};return dd={includes:s(!0),indexOf:s(!1)},dd}var fd,V1;function u6(){if(V1)return fd;V1=1;var r=Hn(),i=$r(),o=bc(),s=l6().indexOf,u=O1(),d=r([].push);return fd=function(f,h){var p=o(f),m=0,y=[],v;for(v in p)!i(u,v)&&i(p,v)&&d(y,v);for(;h.length>m;)i(p,v=h[m++])&&(~s(y,v)||d(y,v));return y},fd}var H1,B1;function c6(){return B1||(B1=1,H1=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]),H1}var $1;function d6(){if($1)return ad;$1=1;var r=u6(),i=c6(),o=i.concat("length","prototype");return ad.f=Object.getOwnPropertyNames||function(s){return r(s,o)},ad}var Y1={},X1;function f6(){return X1||(X1=1,Y1.f=Object.getOwnPropertySymbols),Y1}var hd,J1;function h6(){if(J1)return hd;J1=1;var r=Sc(),i=Hn(),o=d6(),s=f6(),u=x1(),d=i([].concat);return hd=r("Reflect","ownKeys")||function(f){var h=o.f(u(f)),p=s.f;return p?d(h,p(f)):h},hd}var pd,K1;function p6(){if(K1)return pd;K1=1;var r=$r(),i=h6(),o=b1(),s=Kc();return pd=function(u,d,f){for(var h=i(d),p=s.f,m=o.f,y=0;y<h.length;y++){var v=h[y];!r(u,v)&&!(f&&r(f,v))&&p(u,v,m(d,v))}},pd}var md,Z1;function m6(){if(Z1)return md;Z1=1;var r=br(),i=On(),o=/#|\.prototype\./,s=function(p,m){var y=d[u(p)];return y===h?!0:y===f?!1:i(m)?r(m):!!m},u=s.normalize=function(p){return String(p).replace(o,".").toLowerCase()},d=s.data={},f=s.NATIVE="N",h=s.POLYFILL="P";return md=s,md}var gd,G1;function Q1(){if(G1)return gd;G1=1;var r=Yt(),i=b1().f,o=C1(),s=r6(),u=Nc(),d=p6(),f=m6();return gd=function(h,p){var m=h.target,y=h.global,v=h.stat,x,E,M,R,j,ie;if(y?E=r:v?E=r[m]||u(m,{}):E=r[m]&&r[m].prototype,E)for(M in p){if(j=p[M],h.dontCallGetSet?(ie=i(E,M),R=ie&&ie.value):R=E[M],x=f(y?M:m+(v?".":"#")+M,h.forced),!x&&R!==void 0){if(typeof j==typeof R)continue;d(j,R)}(h.sham||R&&R.sham)&&o(j,"sham",!0),s(E,M,j,h)}},gd}var vd,eg;function tg(){if(eg)return vd;eg=1;var r=il(),i=Function.prototype,o=i.apply,s=i.call;return vd=typeof Reflect=="object"&&Reflect.apply||(r?s.bind(o):function(){return s.apply(o,arguments)}),vd}var yd,ng;function g6(){if(ng)return yd;ng=1;var r=mc(),i=Hn();return yd=function(o){if(r(o)==="Function")return i(o)},yd}var bd,rg;function v6(){if(rg)return bd;rg=1;var r=g6(),i=Zm(),o=il(),s=r(r.bind);return bd=function(u,d){return i(u),d===void 0?u:o?s(u,d):function(){return u.apply(d,arguments)}},bd}var wd,ig;function y6(){if(ig)return wd;ig=1;var r=Sc();return wd=r("document","documentElement"),wd}var _d,ag;function og(){if(ag)return _d;ag=1;var r=Hn();return _d=r([].slice),_d}var xd,sg;function lg(){if(sg)return xd;sg=1;var r=TypeError;return xd=function(i,o){if(i<o)throw new r("Not enough arguments");return i},xd}var Sd,ug;function b6(){if(ug)return Sd;ug=1;var r=al();return Sd=/(?:ipad|iphone|ipod).*applewebkit/i.test(r),Sd}var kd,cg;function dg(){if(cg)return kd;cg=1;var r=Yt(),i=al(),o=mc(),s=function(u){return i.slice(0,u.length)===u};return kd=function(){return s("Bun/")?"BUN":s("Cloudflare-Workers")?"CLOUDFLARE":s("Deno/")?"DENO":s("Node.js/")?"NODE":r.Bun&&typeof Bun.version=="string"?"BUN":r.Deno&&typeof Deno.version=="object"?"DENO":o(r.process)==="process"?"NODE":r.window&&r.document?"BROWSER":"REST"}(),kd}var Cd,fg;function w6(){if(fg)return Cd;fg=1;var r=dg();return Cd=r==="NODE",Cd}var Ed,hg;function pg(){if(hg)return Ed;hg=1;var r=Yt(),i=tg(),o=v6(),s=On(),u=$r(),d=br(),f=y6(),h=og(),p=m1(),m=lg(),y=b6(),v=w6(),x=r.setImmediate,E=r.clearImmediate,M=r.process,R=r.Dispatch,j=r.Function,ie=r.MessageChannel,B=r.String,$=0,ae={},V="onreadystatechange",Y,N,I,z;d(function(){Y=r.location});var F=function(O){if(u(ae,O)){var U=ae[O];delete ae[O],U()}},b=function(O){return function(){F(O)}},S=function(O){F(O.data)},T=function(O){r.postMessage(B(O),Y.protocol+"//"+Y.host)};return(!x||!E)&&(x=function(O){m(arguments.length,1);var U=s(O)?O:j(O),k=h(arguments,1);return ae[++$]=function(){i(U,void 0,k)},N($),$},E=function(O){delete ae[O]},v?N=function(O){M.nextTick(b(O))}:R&&R.now?N=function(O){R.now(b(O))}:ie&&!y?(I=new ie,z=I.port2,I.port1.onmessage=S,N=o(z.postMessage,z)):r.addEventListener&&s(r.postMessage)&&!r.importScripts&&Y&&Y.protocol!=="file:"&&!d(T)?(N=T,r.addEventListener("message",S,!1)):V in p("script")?N=function(O){f.appendChild(p("script"))[V]=function(){f.removeChild(this),F(O)}}:N=function(O){setTimeout(b(O),0)}),Ed={set:x,clear:E},Ed}var mg;function _6(){if(mg)return vm;mg=1;var r=Q1(),i=Yt(),o=pg().clear;return r({global:!0,bind:!0,enumerable:!0,forced:i.clearImmediate!==o},{clearImmediate:o}),vm}var gg={},Md,vg;function x6(){if(vg)return Md;vg=1;var r=Yt(),i=tg(),o=On(),s=dg(),u=al(),d=og(),f=lg(),h=r.Function,p=/MSIE .\./.test(u)||s==="BUN"&&function(){var m=r.Bun.version.split(".");return m.length<3||m[0]==="0"&&(m[1]<3||m[1]==="3"&&m[2]==="0")}();return Md=function(m,y){var v=y?2:1;return p?function(x,E){var M=f(arguments.length,1)>v,R=o(x)?x:h(x),j=M?d(arguments,v):[],ie=M?function(){i(R,this,j)}:R;return y?m(ie,E):m(ie)}:m},Md}var yg;function S6(){if(yg)return gg;yg=1;var r=Q1(),i=Yt(),o=pg().set,s=x6(),u=i.setImmediate?s(o,!1):o;return r({global:!0,bind:!0,enumerable:!0,forced:i.setImmediate!==u},{setImmediate:u}),gg}var bg;function wg(){return bg||(bg=1,_6(),S6()),z_}var Ad,_g;function xg(){if(_g)return Ad;_g=1;var r=Yt();return Ad=r,Ad}var Td,Sg;function k6(){if(Sg)return Td;Sg=1,wg();var r=xg();return Td=r.setImmediate,Td}var Fd,kg;function C6(){if(kg)return Fd;kg=1;var r=k6();return Fd=r,Fd}C6();var Pd,Cg;function E6(){if(Cg)return Pd;Cg=1,wg();var r=xg();return Pd=r.clearImmediate,Pd}var Od,Eg;function M6(){if(Eg)return Od;Eg=1;var r=E6();return Od=r,Od}M6();class A6{constructor(){this.events={}}subscribe(i,o){this.events[i]||(this.events[i]=[]),this.events[i].push(o)}publish(i,o){this.events[i]&&this.events[i].forEach(s=>s(o))}}const ol=new A6;class T6{constructor(i,o){this.markers=[],this.currentBand=null,this.endpoint=i,this.zoomFactor=1,this.autoAdjust=!1,this.adjustmentBuffer=[],this.bufferSize=50,this.dampeningFactor=.1,this.spectrum=!1,this.waterfall=!1,this.frequencyMarkerComponent=null,this.pendingMarkers=[],this.waterfallQueue=new Bp,this.drawnWaterfallQueue=new Bp,this.lagTime=0,this.spectrumAlpha=.5,this.spectrumFiltered=[[-1,-1],[0]],this.waterfallColourShift=130,this.minWaterfall=-30,this.maxWaterfall=110,this.colormap=[],this.setColormap("gqrx"),this.clients={},this.clientColormap=mm(gm("rainbow")),this.updateTimeout=setTimeout(()=>{},0),this.lineResets=0,this.wfheight=200*window.devicePixelRatio;const s={AM:"AM",FM:"FM",LSB:"LSB",USB:"USB",CW:"CW-U"};this.bands=[{name:"2200M HAM",startFreq:135700,endFreq:137800,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:135700,endFreq:137800}]},{name:"630M HAM",startFreq:472e3,endFreq:479e3,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:472e3,endFreq:475e3},{mode:s.LSB,startFreq:475e3,endFreq:479e3}]},{name:"600M HAM",startFreq:501e3,endFreq:504e3,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:501e3,endFreq:504e3}]},{name:"160M HAM",startFreq:181e4,endFreq:2e6,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:181e4,endFreq:184e4},{mode:s.LSB,startFreq:184e4,endFreq:2e6}]},{name:"80M HAM",startFreq:35e5,endFreq:39e5,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:35e5,endFreq:36e5},{mode:s.LSB,startFreq:36e5,endFreq:39e5}]},{name:"60M HAM",startFreq:5351500,endFreq:5366500,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.USB,startFreq:5351500,endFreq:5366500}]},{name:"49M AM",startFreq:59e5,endFreq:62e5,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:59e5,endFreq:62e5}]},{name:"40M HAM",startFreq:7e6,endFreq:72e5,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:7e6,endFreq:705e4},{mode:s.LSB,startFreq:705e4,endFreq:72e5}]},{name:"41M AM",startFreq:72e5,endFreq:745e4,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:72e5,endFreq:745e4}]},{name:"31M AM",startFreq:94e5,endFreq:99e5,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:94e5,endFreq:99e5}]},{name:"30M HAM",startFreq:101e5,endFreq:1015e4,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:101e5,endFreq:1015e4}]},{name:"25M AM",startFreq:116e5,endFreq:121e5,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:116e5,endFreq:121e5}]},{name:"22M AM",startFreq:1357e4,endFreq:1387e4,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:1357e4,endFreq:1387e4}]},{name:"20M HAM",startFreq:14e6,endFreq:1435e4,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:14e6,endFreq:1407e4},{mode:s.USB,startFreq:1407e4,endFreq:1435e4}]},{name:"19M AM",startFreq:151e5,endFreq:158e5,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:151e5,endFreq:158e5}]},{name:"16M AM",startFreq:1748e4,endFreq:179e5,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:1748e4,endFreq:179e5}]},{name:"17M HAM",startFreq:18068e3,endFreq:18168e3,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:18068e3,endFreq:181e5},{mode:s.USB,startFreq:181e5,endFreq:18168e3}]},{name:"15M AM",startFreq:189e5,endFreq:1902e4,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:189e5,endFreq:1902e4}]},{name:"15M HAM",startFreq:21e6,endFreq:2145e4,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:21e6,endFreq:2107e4},{mode:s.USB,startFreq:2107e4,endFreq:2145e4}]},{name:"13M AM",startFreq:2145e4,endFreq:2185e4,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:2145e4,endFreq:2185e4}]},{name:"12M HAM",startFreq:2489e4,endFreq:2499e4,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:2489e4,endFreq:2492e4},{mode:s.USB,startFreq:2492e4,endFreq:2499e4}]},{name:"11M AM",startFreq:2567e4,endFreq:261e5,color:"rgba(199, 12, 193, 0.6)",modes:[{mode:s.AM,startFreq:2567e4,endFreq:261e5}]},{name:"CB",startFreq:26965e3,endFreq:27405e3,color:"rgba(3, 227, 252, 0.6)",modes:[{mode:s.AM,startFreq:26965e3,endFreq:27405e3}]},{name:"10M HAM",startFreq:28e6,endFreq:297e5,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:28e6,endFreq:2807e4},{mode:s.USB,startFreq:2807e4,endFreq:297e5}]},{name:"6M HAM",startFreq:5e7,endFreq:54e6,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:5e7,endFreq:501e5},{mode:s.USB,startFreq:501e5,endFreq:54e6}]},{name:"4M HAM",startFreq:6995e4,endFreq:6995e4,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.FM,startFreq:6995e4,endFreq:6995e4}]},{name:"4M HAM",startFreq:70112500,endFreq:70412500,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.FM,startFreq:70112500,endFreq:70412500}]},{name:"2M HAM",startFreq:144e6,endFreq:148e6,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:144e6,endFreq:1441e5},{mode:s.USB,startFreq:1441e5,endFreq:1443e5},{mode:s.FM,startFreq:1443e5,endFreq:148e6}]},{name:"70CM HAM",startFreq:43e7,endFreq:44e7,color:"rgba(50, 168, 72, 0.6)",modes:[{mode:s.CW,startFreq:43e7,endFreq:4301e5},{mode:s.USB,startFreq:4301e5,endFreq:4321e5},{mode:s.FM,startFreq:4321e5,endFreq:44e7}]}],this.reusableUint8Array=null,this.reusableImageData=null,this.isDrawing=!1,this.pendingFrame=null,this.cachedCanvasXtoFreq=new Map,this.cachedFreqToCanvasX=new Map,this.cacheSize=1e3,this.markerBatchSize=100,this.markerProcessingIndex=0}addMarker(i,o,s){this.markers.push({frequency:i,name:o,mode:s}),this.markers.sort((u,d)=>u.frequency-d.frequency)}initCanvas(i){this.canvasElem=i.canvasElem,this.ctx=this.canvasElem.getContext("2d"),this.ctx.imageSmoothingEnabled=!1,this.canvasWidth=this.canvasElem.width,this.canvasHeight=this.canvasElem.height,this.backgroundColor=window.getComputedStyle(document.body,null).getPropertyValue("background-color"),this.curLine=this.canvasHeight/2,this.ctx.fillRect(0,0,this.canvasElem.width,this.canvasElem.height),this.graduationCanvasElem=i.graduationCanvasElem,this.graduationCtx=this.graduationCanvasElem.getContext("2d"),this.bandPlanCanvasElem=i.bandPlanCanvasElem,this.bandPlanCtx=this.bandPlanCanvasElem.getContext("2d"),this.spectrumCanvasElem=i.spectrumCanvasElem,this.spectrumCtx=this.spectrumCanvasElem.getContext("2d"),this.spectrumCanvasElem.addEventListener("mousemove",this.spectrumMouseMove.bind(this)),this.spectrumCanvasElem.addEventListener("mouseleave",this.spectrumMouseLeave.bind(this)),this.tempCanvasElem=i.tempCanvasElem,this.tempCtx=this.tempCanvasElem.getContext("2d"),this.tempCanvasElem.height=this.wfheight,this.waterfall=!0,this.mobile=!1;let o;this.resizeCallback=()=>{const s=this.canvasWidth,u=this.canvasHeight;this.setCanvasWidth(),!(s===this.canvasWidth&&u===this.canvasHeight)&&(this.ctx.fillStyle=this.backgroundColor,this.ctx.fillRect(0,0,this.canvasWidth,this.canvasHeight),this.curLine=Math.ceil(this.curLine*this.canvasHeight/u),this.waterfallQueue.clear(),this.drawnWaterfallQueue.clear(),this.clearFrequencyCache(),this.updateGraduation(),this.updateBandPlan(),this.lineResets=0,this.waterfallSocket&&this.waterfallSocket.readyState===WebSocket.OPEN&&this.waterfallSocket.send(JSON.stringify({cmd:"window",l:this.waterfallL,r:this.waterfallR})))},window.addEventListener("resize",()=>{this.updateGraduation(),this.updateBandPlan(),o&&clearTimeout(o),o=setTimeout(this.resizeCallback,100)})}async init(){return this.promise?this.promise:(this.promise=new Promise((i,o)=>{this.resolvePromise=i,this.rejectPromise=o}),this.connectWebSocket(),this.promise)}connectWebSocket(){this.waterfallSocket=new WebSocket(this.endpoint),this.waterfallSocket.binaryType="arraybuffer",this.firstWaterfallMessage=!0,this.waterfallSocket.onmessage=this.socketMessageInitial.bind(this),this.waterfallSocket.onerror=i=>{console.error("WebSocket error:",i),this.rejectPromise&&this.rejectPromise(i)},this.waterfallSocket.onclose=()=>{console.log("WebSocket closed, attempting to reconnect..."),setTimeout(()=>{this.waterfallSocket.readyState===WebSocket.CLOSED&&this.connectWebSocket()},3e3)}}stop(){this.waterfallSocket.close()}setCanvasWidth(){const i=window.devicePixelRatio||1,o=window.innerWidth;let s=o>1372?1372:o;s*=i,this.mobile=o<768||"ontouchstart"in window,this.canvasElem.width=s,this.canvasScale=s/1372,this.spectrumCanvasElem.width=s,this.spectrumCanvasElem.height=s/1372*128,this.graduationCanvasElem.width=s,this.graduationCanvasElem.height=s/1372*30,this.bandPlanCanvasElem.width=s,this.bandPlanCanvasElem.height=s/1372*40,this.canvasElem.height=this.wfheight,this.canvasWidth=s,this.canvasHeight=this.canvasElem.height,this.bufferCanvas=document.createElement("canvas"),this.bufferCanvas.width=this.canvasWidth,this.bufferCanvas.height=this.canvasHeight,this.bufferContext=this.bufferCanvas.getContext("2d",{alpha:!1}),this.ctx.imageSmoothingEnabled=!1,this.bufferContext.imageSmoothingEnabled=!1,this.spectrumCtx.imageSmoothingEnabled=!1,this.graduationCtx.imageSmoothingEnabled=!1,this.bandPlanCtx.imageSmoothingEnabled=!1}setFrequencyMarkerComponent(i){this.frequencyMarkerComponent=i,this.addMarkersToComponent()}addMarkersToComponent(){this.frequencyMarkerComponent&&this.pendingMarkers.length>0&&(this.frequencyMarkerComponent.insertAll(this.pendingMarkers),this.frequencyMarkerComponent.finalizeList(),this.pendingMarkers=[])}socketMessageInitial(i){if(!(i.data instanceof ArrayBuffer)){const o=JSON.parse(i.data);if(!o.fft_size)return;if(o.markers)try{const d=JSON.parse(o.markers);d.markers&&Array.isArray(d.markers)&&(this.pendingMarkers=d.markers.map(f=>({f:f.frequency,d:f.name,m:f.mode})),this.addMarkersToComponent())}catch(d){console.error("Error parsing markers:",d)}this.waterfallMaxSize=o.fft_result_size,this.fftSize=o.fft_size,this.baseFreq=o.basefreq,this.sps=o.sps,this.totalBandwidth=o.total_bandwidth,this.overlap=o.overlap,this.setCanvasWidth(),this.tempCanvasElem.width=o.waterfall_size,this.ctx.fillRect(0,0,this.canvasElem.width,this.canvasElem.height);const s=Math.max(1,Math.floor(this.sps/this.fftSize/5)*2),u=this.sps/this.fftSize/(s/2);console.log("Waterfall FPS: "+u),requestAnimationFrame(this.drawSpectrogram.bind(this)),this.waterfallL=0,this.waterfallR=this.waterfallMaxSize,this.waterfallSocket.onmessage=this.socketMessage.bind(this),this.firstWaterfallMessage=!1,this.waterfallDecoder=nw(o.waterfall_compression),this.updateGraduation(),this.updateBandPlan(),this.resolvePromise(o)}}socketMessage(i){i.data instanceof ArrayBuffer&&this.enqueueSpectrogram(i.data)}getMouseX(i,o){const s=i.getBoundingClientRect(),u=i.width/s.width;return(o.clientX-s.left)*u}enqueueSpectrogram(i){if(this.waterfallDecoder.decode(i).forEach(o=>{this.waterfallQueue.unshift(o)}),!this.waterfall&&!this.spectrum){this.waterfallQueue.clear();return}for(;this.waterfallQueue.length>5;)this.waterfallQueue.pop()}accumulateAdjustmentData(i){this.adjustmentBuffer.push(...i),this.adjustmentBuffer.length>=this.bufferSize&&(this.adjustWaterfallLimits(this.adjustmentBuffer),this.adjustmentBuffer=[])}adjustWaterfallLimits(i){const o=Math.min(...i)-20,s=Math.max(...i)-20;this.minWaterfall+=(o-this.minWaterfall)*this.dampeningFactor,this.maxWaterfall+=(s-this.maxWaterfall)*this.dampeningFactor}transformValue(i){let o=(Math.max(this.minWaterfall,Math.min(this.maxWaterfall,i))-this.minWaterfall)/(this.maxWaterfall-this.minWaterfall),s=Math.floor(o*255);return Math.max(0,Math.min(255,s))}clearFrequencyCache(){this.cachedCanvasXtoFreq.clear(),this.cachedFreqToCanvasX.clear()}idxToFreq(i){return i/this.waterfallMaxSize*this.totalBandwidth+this.baseFreq}idxToCanvasX(i){return(i-this.waterfallL)/(this.waterfallR-this.waterfallL)*this.canvasWidth}canvasXtoFreq(i){if(this.cachedCanvasXtoFreq.has(i))return this.cachedCanvasXtoFreq.get(i);const o=i/this.canvasWidth*(this.waterfallR-this.waterfallL)+this.waterfallL,s=this.idxToFreq(o);return this.cachedCanvasXtoFreq.size<this.cacheSize&&this.cachedCanvasXtoFreq.set(i,s),s}freqToIdx(i){return(i-this.baseFreq)/this.totalBandwidth*this.waterfallMaxSize}freqToCanvasX(i){if(this.cachedFreqToCanvasX.has(i))return this.cachedFreqToCanvasX.get(i);const o=this.freqToIdx(i),s=this.idxToCanvasX(o);return this.cachedFreqToCanvasX.size<this.cacheSize&&this.cachedFreqToCanvasX.set(i,s),s}calculateOffsets(i,o,s){const u=this.canvasWidth/(this.waterfallR-this.waterfallL),d=(o-this.waterfallL)*u,f=(s-this.waterfallL)*u,h=new Uint8Array(i.length);for(let p=0;p<h.length;p++)h[p]=this.transformValue(i[p]);return[h,d,f]}drawSpectrogram(){const i=()=>{if(this.waterfallQueue.length===0){requestAnimationFrame(i);return}const{data:o,l:s,r:u}=this.waterfallQueue.pop(),[d,f,h]=this.calculateOffsets(o,s,u);this.autoAdjust&&this.accumulateAdjustmentData(d),this.waterfall&&this.drawWaterfall(d,f,h,s,u),this.spectrum&&this.drawSpectrum(d,f,h,s,u),requestAnimationFrame(i)};requestAnimationFrame(i)}async redrawWaterfall(){const i=this.drawnWaterfallQueue.toArray(),o=this.lineResets,s=this.curLine,u=d=>{const f=s+1+d+(this.lineResets-o)*this.canvasHeight/2,[h,p,m]=i[d],[y,v,x]=this.calculateOffsets(h,p,m);this.drawWaterfallLine(y,v,x,f),d+1<i.length&&(this.updateImmediate=setImmediate(()=>u(d+1)))};clearImmediate(this.updateImmediate),i.length&&u(0)}drawWaterfall(i,o,s,u,d){o=Math.floor(o),s=Math.ceil(s),this.bufferContext.drawImage(this.ctx.canvas,0,1,this.canvasWidth,this.canvasHeight-1,0,0,this.canvasWidth,this.canvasHeight-1),this.drawWaterfallLine(i,o,s,0,this.bufferContext),this.ctx.clearRect(0,0,this.canvasWidth,this.canvasHeight),this.ctx.drawImage(this.bufferCanvas,0,0)}drawWaterfallLine(i,o,s,u,d){o=Math.floor(o),s=Math.ceil(s),u=Math.floor(u);const f=s-o,h=d.createImageData(f,1);for(let p=0;p<f;p++){const m=Math.floor(p*(i.length-1)/(f-1)),y=Math.floor(i[m]),v=p*4;h.data.set(this.colormap[y],v)}d.putImageData(h,o,d.canvas.height-1-u)}drawSpectrum(i,o,s,u,d){(u!==this.spectrumFiltered[0][0]||d!==this.spectrumFiltered[0][1])&&(this.spectrumFiltered[1]=i,this.spectrumFiltered[0]=[u,d]);for(let x=0;x<i.length;x++)this.spectrumFiltered[1][x]=this.spectrumAlpha*i[x]+(1-this.spectrumAlpha)*this.spectrumFiltered[1][x];i=this.spectrumFiltered[1];const f=(s-o)/i.length;this.canvasScale;const h=this.spectrumCanvasElem.height,p=i.map(x=>h-x/255*h);this.spectrumCtx.clearRect(0,0,this.spectrumCanvasElem.width,h);const m=this.spectrumCtx.createLinearGradient(0,0,0,h);m.addColorStop(0,"rgba(3, 157, 252, 0.8)"),m.addColorStop(1,"rgba(3, 157, 252, 0.2)"),this.spectrumCtx.lineWidth=2,this.spectrumCtx.strokeStyle="rgba(3, 157, 252, 0.8)",this.spectrumCtx.fillStyle=m,this.spectrumCtx.shadowColor="rgba(3, 157, 252, 0.5)",this.spectrumCtx.shadowBlur=10,this.spectrumCtx.beginPath(),this.spectrumCtx.moveTo(o,h);let y=o,v=p[0];this.spectrumCtx.lineTo(y,v);for(let x=1;x<p.length;x++){const E=o+x*f,M=p[x],R=(y+E)/2;this.spectrumCtx.quadraticCurveTo(y,v,R,(v+M)/2),y=E,v=M}this.spectrumCtx.lineTo(s,v),this.spectrumCtx.lineTo(s,h),this.spectrumCtx.closePath(),this.spectrumCtx.fill(),this.spectrumCtx.stroke(),this.spectrumCtx.shadowBlur=0,this.spectrumFreq&&(this.spectrumCtx.font="14px Arial",this.spectrumCtx.fillStyle="rgba(255, 255, 255, 0.9)",this.spectrumCtx.shadowColor="rgba(255, 255, 255, 0.5)",this.spectrumCtx.shadowBlur=5,this.spectrumCtx.fillText("".concat((this.spectrumFreq/1e6).toFixed(6)," MHz"),10,20),this.spectrumCtx.strokeStyle="rgba(255, 255, 255, 0.5)",this.spectrumCtx.lineWidth=1,this.spectrumCtx.setLineDash([5,3]),this.spectrumCtx.beginPath(),this.spectrumCtx.moveTo(this.spectrumX,0),this.spectrumCtx.lineTo(this.spectrumX,h),this.spectrumCtx.stroke(),this.spectrumCtx.setLineDash([]))}checkBandAndSetMode(i){let o=null,s=null;for(const u of this.bands)if(i>=u.startFreq&&i<=u.endFreq){o=u;for(const d of u.modes)if(i>=d.startFreq&&i<=d.endFreq){s=d.mode;break}break}return o!==this.currentBand||o&&s!==this.currentMode?(this.currentBand=o,this.currentMode=s,o?(ol.publish("setMode",s),s):(ol.publish("outOfBand"),null)):null}updateGraduation(){const i=this.idxToFreq(this.waterfallL),o=this.idxToFreq(this.waterfallR),s=this.canvasScale;let u=1;for(;(o-i)/u>8;)u*=10;u/=10,this.graduationCtx.fillStyle="white",this.graduationCtx.strokeStyle="white",this.graduationCtx.clearRect(0,0,this.graduationCanvasElem.width,this.graduationCanvasElem.height);let d=i;i%u!==0&&(d=i+(u-i%u));for(let f=d;f<=o;f+=u)f!=0&&f.toString().match(/0*$/g)[0].length;for(this.mobile?this.graduationCtx.font="".concat(12*s,"px Inter"):this.graduationCtx.font="".concat(10*s,"px Inter");d<=o;d+=u){const f=(d-i)/(o-i)*this.canvasWidth;let h=5,p=!1;if(d%(u*10)===0?(h=10,p=!0):d%(u*5)===0&&(h=7,p=!0),p){this.graduationCtx.textAlign="center";const m=d/1e3;this.graduationCtx.fillText(m.toString(),f,20*s)}this.graduationCtx.lineWidth=1*s,this.graduationCtx.beginPath(),this.graduationCtx.moveTo(f,(5+(5-h))*s),this.graduationCtx.lineTo(f,10*s),this.graduationCtx.stroke()}this.drawClients()}updateBandPlan(){this.idxToFreq(this.waterfallL),this.idxToFreq(this.waterfallR);const i=this.canvasScale;this.bandPlanCtx.clearRect(0,0,this.bandPlanCanvasElem.width,this.bandPlanCanvasElem.height);const o=10*i,s=25*i;this.bands.forEach(u=>{const d=this.freqToIdx(u.startFreq),f=this.freqToIdx(u.endFreq),h=this.idxToCanvasX(d),p=this.idxToCanvasX(f),m=p-h,y=this.bandPlanCanvasElem.height-o-s;this.bandPlanCtx.strokeStyle=u.color,this.bandPlanCtx.lineWidth=2*i,this.bandPlanCtx.lineCap="round",this.bandPlanCtx.beginPath(),this.bandPlanCtx.moveTo(h,y),this.bandPlanCtx.lineTo(p,y),this.bandPlanCtx.shadowColor=u.color,this.bandPlanCtx.shadowBlur=3*i,this.bandPlanCtx.stroke(),this.bandPlanCtx.shadowColor="transparent",this.bandPlanCtx.shadowBlur=0;let v=this.mobile?12*i:10*i;if(this.bandPlanCtx.font="".concat(v,"px Inter"),this.bandPlanCtx.fillStyle="white",this.bandPlanCtx.textAlign="center",this.bandPlanCtx.textBaseline="top",this.bandPlanCtx.measureText(u.name).width<=m-4*i){const x=y+o+2*i;this.bandPlanCtx.shadowColor="rgba(0, 0, 0, 0.5)",this.bandPlanCtx.shadowBlur=2*i,this.bandPlanCtx.shadowOffsetY=1*i,this.bandPlanCtx.fillText(u.name,(h+p)/2,x),this.bandPlanCtx.shadowColor="transparent",this.bandPlanCtx.shadowBlur=0,this.bandPlanCtx.shadowOffsetY=0}})}abbreviateBandName(i,o,s){if(this.bandPlanCtx.font="".concat(s,"px Inter"),this.bandPlanCtx.measureText(i).width<=o-4*this.canvasScale)return i;const u=i.split(" ");return u.length===1?i.substring(0,Math.floor(o/(s*.6))):u.map(d=>d[0]).join("")}setClients(i){this.clients=i}drawClients(){Object.entries(this.clients).filter(([i,o])=>o[1]<this.waterfallR&&o[1]>=this.waterfallL).forEach(([i,o])=>{const s=this.idxToCanvasX(o[1]),[u,d,f,h]=this.clientColormap[parseInt(i.substring(0,2),16)];this.graduationCtx.fillStyle="rgba(".concat(u,", ").concat(d,", ").concat(f,", ").concat(h,")"),this.graduationCtx.strokeStyle="rgba(".concat(u,", ").concat(d,", ").concat(f,", ").concat(h,")"),this.graduationCtx.beginPath(),this.graduationCtx.moveTo(s,0),this.graduationCtx.lineTo(s+2,5),this.graduationCtx.stroke(),this.graduationCtx.beginPath(),this.graduationCtx.moveTo(s,0),this.graduationCtx.lineTo(s-2,5),this.graduationCtx.stroke()})}applyBlur(i,o,s,u){const d=i.data,f=new Uint8ClampedArray(d);for(let h=0;h<s;h++)for(let p=0;p<o;p++){let m=0,y=0,v=0,x=0,E=0;for(let R=-u;R<=u;R++)for(let j=-u;j<=u;j++){const ie=p+j,B=h+R;if(ie>=0&&ie<o&&B>=0&&B<s){const $=(B*o+ie)*4;m+=f[$],y+=f[$+1],v+=f[$+2],x+=f[$+3],E++}}const M=(h*o+p)*4;d[M]=m/E,d[M+1]=y/E,d[M+2]=v/E,d[M+3]=x/E}return i}setWaterfallRange(i,o){if(i>=o)return;const s=o-i;i<0&&o>this.waterfallMaxSize?(i=0,o=this.waterfallMaxSize):i<0?(i=0,o=s):o>this.waterfallMaxSize&&(o=this.waterfallMaxSize,i=o-s);const u=this.waterfallL,d=this.waterfallR;this.waterfallL=i,this.waterfallR=o,this.clearFrequencyCache(),this.waterfallSocket.send(JSON.stringify({cmd:"window",l:this.waterfallL,r:this.waterfallR}));const f=this.idxToCanvasX(u),h=this.idxToCanvasX(d),p=h-f;this.ctx.drawImage(this.canvasElem,0,0,this.canvasWidth,this.canvasHeight,f,0,p,this.canvasHeight),d-u<=o-i+1&&(this.ctx.fillRect(0,0,f,this.canvasHeight),this.ctx.fillRect(h,0,this.canvasWidth-h,this.canvasHeight)),this.updateGraduation(),this.updateBandPlan(),typeof ol<"u"&&ol.publish("waterfallRangeChanged")}getWaterfallRange(){return[this.waterfallL,this.waterfallR]}setWaterfallLagTime(i){this.lagTime=Math.max(0,i)}setOffset(i){this.waterfallColourShift=i}setMinOffset(i){this.minWaterfall=i}setMaxOffset(i){this.maxWaterfall=i}setAlpha(i){this.spectrumAlpha=i}setColormapArray(i){this.colormap=mm(i)}setColormap(i){this.setColormapArray(gm(i))}setUserID(i){this.waterfallSocket.send(JSON.stringify({cmd:"userid",userid:i}))}setSpectrum(i){this.spectrum=i,i==!0?(this.wfheight=200*window.devicePixelRatio,typeof this.resizeCallback=="function"&&this.resizeCallback()):i==!1&&(this.wfheight=200*window.devicePixelRatio,typeof this.resizeCallback=="function"&&this.resizeCallback())}setWaterfallBig(i){i==!0?(this.wfheight=300*window.devicePixelRatio,typeof this.resizeCallback=="function"&&this.resizeCallback()):i==!1&&(this.wfheight=200*window.devicePixelRatio,typeof this.resizeCallback=="function"&&this.resizeCallback())}setWaterfall(i){this.waterfall=i}resetRedrawTimeout(i){}canvasWheel(i){if(window.getComputedStyle(i.target).cursor=="resize")return;const o=(i.coords||{x:this.getMouseX(this.spectrumCanvasElem,i)}).x;i.preventDefault();const s=i.deltaY||i.scale,u=this.waterfallL,d=this.waterfallR,f=i.scaleAmount||.85;if(d-u<=64&&s<0)return!1;s>0?this.zoomFactor!=1&&(this.zoomFactor=this.zoomFactor-1):s<0&&(this.zoomFactor=this.zoomFactor+1);const h=(d-u)*o/this.canvasWidth+u;let p=h-u,m=d-h;s<0?(p*=f,m*=f):s>0&&(p*=1/f,m*=1/f);const y=Math.round(h-p),v=Math.round(h+m);return this.setWaterfallRange(y,v),!1}mouseMove(i){const o=i.movementX,s=Math.round(o/this.canvasElem.getBoundingClientRect().width*(this.waterfallR-this.waterfallL)),u=this.waterfallL-s,d=this.waterfallR-s;this.setWaterfallRange(u,d)}spectrumMouseMove(i){const o=this.getMouseX(this.spectrumCanvasElem,i),s=this.canvasXtoFreq(o);this.spectrumFreq=s,this.spectrumX=o}spectrumMouseLeave(i){this.spectrumFreq=void 0,this.spectrumX=void 0}destroy(){this.waterfallSocket&&this.waterfallSocket.close(),this.updateImmediate&&clearImmediate(this.updateImmediate),this.clearFrequencyCache(),this.waterfallQueue.clear(),this.drawnWaterfallQueue.clear()}}class F6{constructor(i){this.endpoint=i,this.signalClients={},this.lastModified=performance.now()}init(){return this.promise?this.promise:(this.eventSocket=new WebSocket(this.endpoint),this.eventSocket.binaryType="arraybuffer",this.eventSocket.onmessage=this.socketMessage.bind(this),this.promise=new Promise((i,o)=>{this.eventSocket.onopen=i,this.resolvePromise=i,this.rejectPromise=o}),this.promise)}socketMessage(i){const o=JSON.parse(i.data);if(this.data=o,"signal_list"in o&&(this.signalClients=o.signal_list),"signal_changes"in o){const s=o.signal_changes;for(const[u,d]of Object.entries(s))d[0]===-1&&d[1]===-1?delete this.signalClients[u]:this.signalClients[u]=d}if("waterfall_clients"in o){const s=o.signal_clients,u=parseInt(o.waterfall_kbits+o.audio_kbits);document.getElementById("total_user_count").innerHTML='\n        <div class="flex items-center">\n          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-fuchsia-400" viewBox="0 0 20 20" fill="currentColor">\n            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />\n          </svg>\n          <span class="text-fuchsia-400 font-medium">'.concat(s," ").concat(s>1?"Users":"User",'</span>\n        </div>\n        <div class="flex items-center">\n          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-400" viewBox="0 0 20 20" fill="currentColor">\n            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />\n          </svg>\n          <span class="text-blue-400 font-medium">').concat(u," kbit/s</span>\n        </div>\n      ")}this.lastModified=performance.now()}setUserID(i){this.eventSocket.send(JSON.stringify({cmd:"userid",userid:i}))}getSignalClients(){let i={};return Object.assign(i,this.signalClients),i}getLastModified(){return this.lastModified}}const P6="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let O6=(r=21)=>{let i="",o=crypto.getRandomValues(new Uint8Array(r|=0));for(;r--;)i+=P6[o[r]&63];return i},Rd,sl,Id,Ld;Rd=window.location,sl="".concat(Rd.protocol.replace("http","ws"),"//").concat(Rd.host),Id=new T6(sl+"/waterfall"),Ts=new P_(sl+"/audio"),Ld=new F6(sl+"/events");async function R6(){await rw(),await Promise.all([Id.init(),Ts.init(),Ld.init()]),Ts.settings;const r=O6();[Id,Ts,Ld].forEach(i=>{i.setUserID(r)})}class Mg extends Kn.Component{constructor(i){super(i),this.state={hasError:!1}}static getDerivedStateFromError(i){return{hasError:!0,error:i}}componentDidCatch(i,o){console.error("ErrorBoundary caught an error:",i,o),this.setState({error:i,errorInfo:o})}render(){var i,o;return this.state.hasError?this.props.fallback?this.props.fallback:Me.jsx("div",{className:"error-boundary",children:Me.jsxs("div",{className:"error-content",children:[Me.jsx("h2",{children:"Something went wrong"}),Me.jsx("p",{children:"An error occurred while rendering this component."}),Me.jsxs("details",{className:"error-details",children:[Me.jsx("summary",{children:"Error Details"}),Me.jsx("pre",{children:(i=this.state.error)==null?void 0:i.toString()}),Me.jsx("pre",{children:(o=this.state.errorInfo)==null?void 0:o.componentStack})]}),Me.jsx("button",{onClick:()=>this.setState({hasError:!1,error:void 0,errorInfo:void 0}),className:"retry-button",children:"Try Again"})]})}):this.props.children}}const I6=()=>Me.jsxs("div",{className:"loading-spinner",children:[Me.jsx("div",{className:"spinner"}),Me.jsx("span",{children:"Loading..."})]}),L6=({children:r,fallback:i=Me.jsx(I6,{}),errorFallback:o})=>Me.jsx(Mg,{fallback:o,children:Me.jsx(Kn.Suspense,{fallback:i,children:r})}),N6=Kn.lazy(()=>co(()=>import("./AudioPanel-Cc-IZi1g.js").then(async r=>(await r.__tla,r)),[]));function D6(){const{frequency:r,demodulation:i,bandwidth:o,currentVFO:s,vfoSwitchNotification:u,NREnabled:d,NBEnabled:f,ANEnabled:h,mute:p,setFrequency:m,setAudioPanelState:y}=Kh(),v=Kn.useRef(null),[x,E]=Kn.useState(!1),M=Kn.useCallback(R=>{console.log("Frequency change:",R)},[]);return Kn.useEffect(()=>{(async()=>{try{await R6(),E(!0),[...document.getElementsByTagName("button"),...document.getElementsByTagName("input")].forEach(R=>{R.disabled=!1}),console.log("Backend initialized successfully")}catch(R){console.error("Failed to initialize backend:",R)}})()},[]),x?Me.jsx(Mg,{children:Me.jsx("main",{className:"main-container",children:Me.jsx("div",{className:"h-screen overflow-hidden flex flex-col min-h-screen",children:Me.jsx("div",{className:"w-full flex-grow overflow-y-auto",children:Me.jsx("div",{className:"app-content",children:Me.jsxs("div",{className:"content-inner",children:[Me.jsxs("div",{className:"main-panels",id:"middle-column",children:[Me.jsx(L6,{children:Me.jsx(N6,{ref:v,currentFrequency:parseFloat(r)*1e3,onStateChange:R=>{y(R)}})}),Me.jsx("div",{className:"center-panel",children:Me.jsx("div",{className:"frequency-display",id:"smeter-tut",children:Me.jsxs("div",{className:"frequency-panel",children:[Me.jsxs("div",{className:"frequency-section",children:[Me.jsx("input",{className:"frequency-input",type:"text",value:r,onChange:R=>m(R.target.value),size:3,name:"frequency",onKeyDown:R=>{if(R.key==="Enter"){const j=parseFloat(r)*1e3;M({detail:j})}}}),Me.jsxs("div",{className:"frequency-info",children:[Me.jsxs("span",{className:"vfo-badge clickable",onClick:()=>{},title:"Click to switch VFO (or press V)",children:["VFO ",s]}),Me.jsx("span",{className:"separator",children:"\u2022"}),Me.jsx("span",{className:"mode-text",children:i}),Me.jsx("span",{className:"separator",children:"\u2022"}),Me.jsxs("span",{className:"bandwidth-text",children:[o," kHz"]})]})]}),Me.jsxs("div",{className:"status-section",children:[Me.jsx("div",{className:"status-indicators",children:[{label:"MUTED",enabled:p,color:"red"},{label:"NR",enabled:d,color:"green"},{label:"NB",enabled:f,color:"green"},{label:"AN",enabled:h,color:"green"}].map(R=>Me.jsx("div",{className:"status-indicator ".concat(R.enabled?"active ".concat(R.color):""),children:Me.jsx("span",{children:R.label})},R.label))}),Me.jsx("canvas",{id:"sMeter",width:"250",height:"40"})]})]})})})]}),u&&Me.jsx("div",{className:"vfo-notification",children:Me.jsxs("div",{className:"notification-content",children:[Me.jsx("div",{className:"vfo-icon-wrapper",children:Me.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"vfo-icon",children:Me.jsx("path",{d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",fill:"currentColor"})})}),Me.jsx("span",{className:"notification-text",children:u})]})})]})})})})})}):Me.jsx("div",{className:"loading-screen",children:Me.jsxs("div",{className:"loading-content",children:[Me.jsx("div",{className:"spinner"}),Me.jsx("h2",{children:"Initializing SDR Application..."}),Me.jsx("p",{children:"Connecting to backend services..."})]})})}d2.createRoot(document.getElementById("root")).render(Me.jsx(Kn.StrictMode,{children:Me.jsx(D6,{})}));const Ag=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}))})();export{Gb as $,Lh as _,zS as __tla,Qb as __vite_legacy_guard,Ts as a,Nh as b,Dh as c,zh as d,Uh as e,qh as f,jh as g,Wh as h,Vh as i,Me as j,Hh as k,Bh as l,$h as m,Yh as n,Xh as o,Jh as p,Kn as r,Kh as u};
