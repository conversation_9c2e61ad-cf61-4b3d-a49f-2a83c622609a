{"version": 3, "sources": ["../../cbor-x/decode.js", "../../cbor-x/encode.js", "../../cbor-x/iterators.js"], "sourcesContent": ["let decoder\ntry {\n\tdecoder = new TextDecoder()\n} catch(error) {}\nlet src\nlet srcEnd\nlet position = 0\nlet alreadySet\nconst EMPTY_ARRAY = []\nconst LEGACY_RECORD_INLINE_ID = 105\nconst RECORD_DEFINITIONS_ID = 0xdffe\nconst RECORD_INLINE_ID = 0xdfff // temporary first-come first-serve tag // proposed tag: 0x7265 // 're'\nconst BUNDLED_STRINGS_ID = 0xdff9\nconst PACKED_TABLE_TAG_ID = 51\nconst PACKED_REFERENCE_TAG_ID = 6\nconst STOP_CODE = {}\nlet maxArraySize = 112810000 // This is the maximum array size in V8. We would potentially detect and set it higher\n// for JSC, but this is pretty large and should be sufficient for most use cases\nlet maxMapSize = 16810000 // JavaScript has a fixed maximum map size of about 16710000, but JS itself enforces this,\n// so we don't need to\n\nlet maxObjectSize = 16710000; // This is the maximum number of keys in a Map. It takes over a minute to create this\n// many keys in an object, so also probably a reasonable choice there.\nlet strings = EMPTY_ARRAY\nlet stringPosition = 0\nlet currentDecoder = {}\nlet currentStructures\nlet srcString\nlet srcStringStart = 0\nlet srcStringEnd = 0\nlet bundledStrings\nlet referenceMap\nlet currentExtensions = []\nlet currentExtensionRanges = []\nlet packedValues\nlet dataView\nlet restoreMapsAsObject\nlet defaultOptions = {\n\tuseRecords: false,\n\tmapsAsObjects: true\n}\nlet sequentialMode = false\nlet inlineObjectReadThreshold = 2;\nvar BlockedFunction // we use search and replace to change the next call to BlockedFunction to avoid CSP issues for\n// no-eval build\ntry {\n\tnew Function('')\n} catch(error) {\n\t// if eval variants are not supported, do not create inline object readers ever\n\tinlineObjectReadThreshold = Infinity\n}\n\n\n\nexport class Decoder {\n\tconstructor(options) {\n\t\tif (options) {\n\t\t\tif ((options.keyMap || options._keyMap) && !options.useRecords) {\n\t\t\t\toptions.useRecords = false\n\t\t\t\toptions.mapsAsObjects = true\n\t\t\t}\n\t\t\tif (options.useRecords === false && options.mapsAsObjects === undefined)\n\t\t\t\toptions.mapsAsObjects = true\n\t\t\tif (options.getStructures)\n\t\t\t\toptions.getShared = options.getStructures\n\t\t\tif (options.getShared && !options.structures)\n\t\t\t\t(options.structures = []).uninitialized = true // this is what we use to denote an uninitialized structures\n\t\t\tif (options.keyMap) {\n\t\t\t\tthis.mapKey = new Map()\n\t\t\t\tfor (let [k,v] of Object.entries(options.keyMap)) this.mapKey.set(v,k)\n\t\t\t}\n\t\t}\n\t\tObject.assign(this, options)\n\t}\n\t/*\n\tdecodeKey(key) {\n\t\treturn this.keyMap\n\t\t\t? Object.keys(this.keyMap)[Object.values(this.keyMap).indexOf(key)] || key\n\t\t\t: key\n\t}\n\t*/\n\tdecodeKey(key) {\n\t\treturn this.keyMap ? this.mapKey.get(key) || key : key\n\t}\n\t\n\tencodeKey(key) {\n\t\treturn this.keyMap && this.keyMap.hasOwnProperty(key) ? this.keyMap[key] : key\n\t}\n\n\tencodeKeys(rec) {\n\t\tif (!this._keyMap) return rec\n\t\tlet map = new Map()\n\t\tfor (let [k,v] of Object.entries(rec)) map.set((this._keyMap.hasOwnProperty(k) ? this._keyMap[k] : k), v)\n\t\treturn map\n\t}\n\n\tdecodeKeys(map) {\n\t\tif (!this._keyMap || map.constructor.name != 'Map') return map\n\t\tif (!this._mapKey) {\n\t\t\tthis._mapKey = new Map()\n\t\t\tfor (let [k,v] of Object.entries(this._keyMap)) this._mapKey.set(v,k)\n\t\t}\n\t\tlet res = {}\n\t\t//map.forEach((v,k) => res[Object.keys(this._keyMap)[Object.values(this._keyMap).indexOf(k)] || k] = v)\n\t\tmap.forEach((v,k) => res[safeKey(this._mapKey.has(k) ? this._mapKey.get(k) : k)] =  v)\n\t\treturn res\n\t}\n\t\n\tmapDecode(source, end) {\n\t\n\t\tlet res = this.decode(source)\n\t\tif (this._keyMap) { \n\t\t\t//Experiemntal support for Optimised KeyMap  decoding \n\t\t\tswitch (res.constructor.name) {\n\t\t\t\tcase 'Array': return res.map(r => this.decodeKeys(r))\n\t\t\t\t//case 'Map': return this.decodeKeys(res)\n\t\t\t}\n\t\t}\n\t\treturn res\n\t}\n\n\tdecode(source, end) {\n\t\tif (src) {\n\t\t\t// re-entrant execution, save the state and restore it after we do this decode\n\t\t\treturn saveState(() => {\n\t\t\t\tclearSource()\n\t\t\t\treturn this ? this.decode(source, end) : Decoder.prototype.decode.call(defaultOptions, source, end)\n\t\t\t})\n\t\t}\n\t\tsrcEnd = end > -1 ? end : source.length\n\t\tposition = 0\n\t\tstringPosition = 0\n\t\tsrcStringEnd = 0\n\t\tsrcString = null\n\t\tstrings = EMPTY_ARRAY\n\t\tbundledStrings = null\n\t\tsrc = source\n\t\t// this provides cached access to the data view for a buffer if it is getting reused, which is a recommend\n\t\t// technique for getting data from a database where it can be copied into an existing buffer instead of creating\n\t\t// new ones\n\t\ttry {\n\t\t\tdataView = source.dataView || (source.dataView = new DataView(source.buffer, source.byteOffset, source.byteLength))\n\t\t} catch(error) {\n\t\t\t// if it doesn't have a buffer, maybe it is the wrong type of object\n\t\t\tsrc = null\n\t\t\tif (source instanceof Uint8Array)\n\t\t\t\tthrow error\n\t\t\tthrow new Error('Source must be a Uint8Array or Buffer but was a ' + ((source && typeof source == 'object') ? source.constructor.name : typeof source))\n\t\t}\n\t\tif (this instanceof Decoder) {\n\t\t\tcurrentDecoder = this\n\t\t\tpackedValues = this.sharedValues &&\n\t\t\t\t(this.pack ? new Array(this.maxPrivatePackedValues || 16).concat(this.sharedValues) :\n\t\t\t\tthis.sharedValues)\n\t\t\tif (this.structures) {\n\t\t\t\tcurrentStructures = this.structures\n\t\t\t\treturn checkedRead()\n\t\t\t} else if (!currentStructures || currentStructures.length > 0) {\n\t\t\t\tcurrentStructures = []\n\t\t\t}\n\t\t} else {\n\t\t\tcurrentDecoder = defaultOptions\n\t\t\tif (!currentStructures || currentStructures.length > 0)\n\t\t\t\tcurrentStructures = []\n\t\t\tpackedValues = null\n\t\t}\n\t\treturn checkedRead()\n\t}\n\tdecodeMultiple(source, forEach) {\n\t\tlet values, lastPosition = 0\n\t\ttry {\n\t\t\tlet size = source.length\n\t\t\tsequentialMode = true\n\t\t\tlet value = this ? this.decode(source, size) : defaultDecoder.decode(source, size)\n\t\t\tif (forEach) {\n\t\t\t\tif (forEach(value) === false) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tif (forEach(checkedRead()) === false) {\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tvalues = [ value ]\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tvalues.push(checkedRead())\n\t\t\t\t}\n\t\t\t\treturn values\n\t\t\t}\n\t\t} catch(error) {\n\t\t\terror.lastPosition = lastPosition\n\t\t\terror.values = values\n\t\t\tthrow error\n\t\t} finally {\n\t\t\tsequentialMode = false\n\t\t\tclearSource()\n\t\t}\n\t}\n}\nexport function getPosition() {\n\treturn position\n}\nexport function checkedRead() {\n\ttry {\n\t\tlet result = read()\n\t\tif (bundledStrings) {\n\t\t\tif (position >= bundledStrings.postBundlePosition) {\n\t\t\t\tlet error = new Error('Unexpected bundle position');\n\t\t\t\terror.incomplete = true;\n\t\t\t\tthrow error\n\t\t\t}\n\t\t\t// bundled strings to skip past\n\t\t\tposition = bundledStrings.postBundlePosition;\n\t\t\tbundledStrings = null;\n\t\t}\n\n\t\tif (position == srcEnd) {\n\t\t\t// finished reading this source, cleanup references\n\t\t\tcurrentStructures = null\n\t\t\tsrc = null\n\t\t\tif (referenceMap)\n\t\t\t\treferenceMap = null\n\t\t} else if (position > srcEnd) {\n\t\t\t// over read\n\t\t\tlet error = new Error('Unexpected end of CBOR data')\n\t\t\terror.incomplete = true\n\t\t\tthrow error\n\t\t} else if (!sequentialMode) {\n\t\t\tthrow new Error('Data read, but end of buffer not reached')\n\t\t}\n\t\t// else more to read, but we are reading sequentially, so don't clear source yet\n\t\treturn result\n\t} catch(error) {\n\t\tclearSource()\n\t\tif (error instanceof RangeError || error.message.startsWith('Unexpected end of buffer')) {\n\t\t\terror.incomplete = true\n\t\t}\n\t\tthrow error\n\t}\n}\n\nexport function read() {\n\tlet token = src[position++]\n\tlet majorType = token >> 5\n\ttoken = token & 0x1f\n\tif (token > 0x17) {\n\t\tswitch (token) {\n\t\t\tcase 0x18:\n\t\t\t\ttoken = src[position++]\n\t\t\t\tbreak\n\t\t\tcase 0x19:\n\t\t\t\tif (majorType == 7) {\n\t\t\t\t\treturn getFloat16()\n\t\t\t\t}\n\t\t\t\ttoken = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tbreak\n\t\t\tcase 0x1a:\n\t\t\t\tif (majorType == 7) {\n\t\t\t\t\tlet value = dataView.getFloat32(position)\n\t\t\t\t\tif (currentDecoder.useFloat32 > 2) {\n\t\t\t\t\t\t// this does rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\tlet multiplier = mult10[((src[position] & 0x7f) << 1) | (src[position + 1] >> 7)]\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\treturn ((multiplier * value + (value > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n\t\t\t\t\t}\n\t\t\t\t\tposition += 4\n\t\t\t\t\treturn value\n\t\t\t\t}\n\t\t\t\ttoken = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tbreak\n\t\t\tcase 0x1b:\n\t\t\t\tif (majorType == 7) {\n\t\t\t\t\tlet value = dataView.getFloat64(position)\n\t\t\t\t\tposition += 8\n\t\t\t\t\treturn value\n\t\t\t\t}\n\t\t\t\tif (majorType > 1) {\n\t\t\t\t\tif (dataView.getUint32(position) > 0)\n\t\t\t\t\t\tthrow new Error('JavaScript does not support arrays, maps, or strings with length over 4294967295')\n\t\t\t\t\ttoken = dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentDecoder.int64AsNumber) {\n\t\t\t\t\ttoken = dataView.getUint32(position) * 0x100000000\n\t\t\t\t\ttoken += dataView.getUint32(position + 4)\n\t\t\t\t} else\n\t\t\t\t\ttoken = dataView.getBigUint64(position)\n\t\t\t\tposition += 8\n\t\t\t\tbreak\n\t\t\tcase 0x1f: \n\t\t\t\t// indefinite length\n\t\t\t\tswitch(majorType) {\n\t\t\t\t\tcase 2: // byte string\n\t\t\t\t\tcase 3: // text string\n\t\t\t\t\t\tthrow new Error('Indefinite length not supported for byte or text strings')\n\t\t\t\t\tcase 4: // array\n\t\t\t\t\t\tlet array = []\n\t\t\t\t\t\tlet value, i = 0\n\t\t\t\t\t\twhile ((value = read()) != STOP_CODE) {\n\t\t\t\t\t\t\tif (i >= maxArraySize) throw new Error(`Array length exceeds ${maxArraySize}`)\n\t\t\t\t\t\t\tarray[i++] = value\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn majorType == 4 ? array : majorType == 3 ? array.join('') : Buffer.concat(array)\n\t\t\t\t\tcase 5: // map\n\t\t\t\t\t\tlet key\n\t\t\t\t\t\tif (currentDecoder.mapsAsObjects) {\n\t\t\t\t\t\t\tlet object = {}\n\t\t\t\t\t\t\tlet i = 0;\n\t\t\t\t\t\t\tif (currentDecoder.keyMap) {\n\t\t\t\t\t\t\t\twhile((key = read()) != STOP_CODE) {\n\t\t\t\t\t\t\t\t\tif (i++ >= maxMapSize) throw new Error(`Property count exceeds ${maxMapSize}`)\n\t\t\t\t\t\t\t\t\tobject[safeKey(currentDecoder.decodeKey(key))] = read()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\twhile ((key = read()) != STOP_CODE) {\n\t\t\t\t\t\t\t\t\tif (i++ >= maxMapSize) throw new Error(`Property count exceeds ${maxMapSize}`)\n\t\t\t\t\t\t\t\t\tobject[safeKey(key)] = read()\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn object\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (restoreMapsAsObject) {\n\t\t\t\t\t\t\t\tcurrentDecoder.mapsAsObjects = true\n\t\t\t\t\t\t\t\trestoreMapsAsObject = false\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlet map = new Map()\n\t\t\t\t\t\t\tif (currentDecoder.keyMap) {\n\t\t\t\t\t\t\t\tlet i = 0;\n\t\t\t\t\t\t\t\twhile((key = read()) != STOP_CODE) {\n\t\t\t\t\t\t\t\t\tif (i++ >= maxMapSize) {\n\t\t\t\t\t\t\t\t\t\tthrow new Error(`Map size exceeds ${maxMapSize}`);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tmap.set(currentDecoder.decodeKey(key), read())\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tlet i = 0;\n\t\t\t\t\t\t\t\twhile ((key = read()) != STOP_CODE) {\n\t\t\t\t\t\t\t\t\tif (i++ >= maxMapSize) {\n\t\t\t\t\t\t\t\t\t\tthrow new Error(`Map size exceeds ${maxMapSize}`);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tmap.set(key, read())\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn map\n\t\t\t\t\t\t}\n\t\t\t\t\tcase 7:\n\t\t\t\t\t\treturn STOP_CODE\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tthrow new Error('Invalid major type for indefinite length ' + majorType)\n\t\t\t\t}\n\t\t\tdefault:\n\t\t\t\tthrow new Error('Unknown token ' + token)\n\t\t}\n\t}\n\tswitch (majorType) {\n\t\tcase 0: // positive int\n\t\t\treturn token\n\t\tcase 1: // negative int\n\t\t\treturn ~token\n\t\tcase 2: // buffer\n\t\t\treturn readBin(token)\n\t\tcase 3: // string\n\t\t\tif (srcStringEnd >= position) {\n\t\t\t\treturn srcString.slice(position - srcStringStart, (position += token) - srcStringStart)\n\t\t\t}\n\t\t\tif (srcStringEnd == 0 && srcEnd < 140 && token < 32) {\n\t\t\t\t// for small blocks, avoiding the overhead of the extract call is helpful\n\t\t\t\tlet string = token < 16 ? shortStringInJS(token) : longStringInJS(token)\n\t\t\t\tif (string != null)\n\t\t\t\t\treturn string\n\t\t\t}\n\t\t\treturn readFixedString(token)\n\t\tcase 4: // array\n\t\t\tif (token >= maxArraySize) throw new Error(`Array length exceeds ${maxArraySize}`)\n\t\t\tlet array = new Array(token)\n\t\t  //if (currentDecoder.keyMap) for (let i = 0; i < token; i++) array[i] = currentDecoder.decodeKey(read())\t\n\t\t\t//else \n\t\t\tfor (let i = 0; i < token; i++) array[i] = read()\n\t\t\treturn array\n\t\tcase 5: // map\n\t\t\tif (token >= maxMapSize) throw new Error(`Map size exceeds ${maxArraySize}`)\n\t\t\tif (currentDecoder.mapsAsObjects) {\n\t\t\t\tlet object = {}\n\t\t\t\tif (currentDecoder.keyMap) for (let i = 0; i < token; i++) object[safeKey(currentDecoder.decodeKey(read()))] = read()\n\t\t\t\telse for (let i = 0; i < token; i++) object[safeKey(read())] = read()\n\t\t\t\treturn object\n\t\t\t} else {\n\t\t\t\tif (restoreMapsAsObject) {\n\t\t\t\t\tcurrentDecoder.mapsAsObjects = true\n\t\t\t\t\trestoreMapsAsObject = false\n\t\t\t\t}\n\t\t\t\tlet map = new Map()\n\t\t\t\tif (currentDecoder.keyMap) for (let i = 0; i < token; i++) map.set(currentDecoder.decodeKey(read()),read())\n\t\t\t\telse for (let i = 0; i < token; i++) map.set(read(), read())\n\t\t\t\treturn map\n\t\t\t}\n\t\tcase 6: // extension\n\t\t\tif (token >= BUNDLED_STRINGS_ID) {\n\t\t\t\tlet structure = currentStructures[token & 0x1fff] // check record structures first\n\t\t\t\t// At some point we may provide an option for dynamic tag assignment with a range like token >= 8 && (token < 16 || (token > 0x80 && token < 0xc0) || (token > 0x130 && token < 0x4000))\n\t\t\t\tif (structure) {\n\t\t\t\t\tif (!structure.read) structure.read = createStructureReader(structure)\n\t\t\t\t\treturn structure.read()\n\t\t\t\t}\n\t\t\t\tif (token < 0x10000) {\n\t\t\t\t\tif (token == RECORD_INLINE_ID) { // we do a special check for this so that we can keep the\n\t\t\t\t\t\t// currentExtensions as densely stored array (v8 stores arrays densely under about 3000 elements)\n\t\t\t\t\t\tlet length = readJustLength()\n\t\t\t\t\t\tlet id = read()\n\t\t\t\t\t\tlet structure = read()\n\t\t\t\t\t\trecordDefinition(id, structure)\n\t\t\t\t\t\tlet object = {}\n\t\t\t\t\t\tif (currentDecoder.keyMap) for (let i = 2; i < length; i++) {\n\t\t\t\t\t\t\tlet key = currentDecoder.decodeKey(structure[i - 2])\n\t\t\t\t\t\t\tobject[safeKey(key)] = read()\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse for (let i = 2; i < length; i++) {\n\t\t\t\t\t\t\tlet key = structure[i - 2]\n\t\t\t\t\t\t\tobject[safeKey(key)] = read()\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn object\n\t\t\t\t\t}\n\t\t\t\t\telse if (token == RECORD_DEFINITIONS_ID) {\n\t\t\t\t\t\tlet length = readJustLength()\n\t\t\t\t\t\tlet id = read()\n\t\t\t\t\t\tfor (let i = 2; i < length; i++) {\n\t\t\t\t\t\t\trecordDefinition(id++, read())\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn read()\n\t\t\t\t\t} else if (token == BUNDLED_STRINGS_ID) {\n\t\t\t\t\t\treturn readBundleExt()\n\t\t\t\t\t}\n\t\t\t\t\tif (currentDecoder.getShared) {\n\t\t\t\t\t\tloadShared()\n\t\t\t\t\t\tstructure = currentStructures[token & 0x1fff]\n\t\t\t\t\t\tif (structure) {\n\t\t\t\t\t\t\tif (!structure.read)\n\t\t\t\t\t\t\t\tstructure.read = createStructureReader(structure)\n\t\t\t\t\t\t\treturn structure.read()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet extension = currentExtensions[token]\n\t\t\tif (extension) {\n\t\t\t\tif (extension.handlesRead)\n\t\t\t\t\treturn extension(read)\n\t\t\t\telse\n\t\t\t\t\treturn extension(read())\n\t\t\t} else {\n\t\t\t\tlet input = read()\n\t\t\t\tfor (let i = 0; i < currentExtensionRanges.length; i++) {\n\t\t\t\t\tlet value = currentExtensionRanges[i](token, input)\n\t\t\t\t\tif (value !== undefined)\n\t\t\t\t\t\treturn value\n\t\t\t\t}\n\t\t\t\treturn new Tag(input, token)\n\t\t\t}\n\t\tcase 7: // fixed value\n\t\t\tswitch (token) {\n\t\t\t\tcase 0x14: return false\n\t\t\t\tcase 0x15: return true\n\t\t\t\tcase 0x16: return null\n\t\t\t\tcase 0x17: return; // undefined\n\t\t\t\tcase 0x1f:\n\t\t\t\tdefault:\n\t\t\t\t\tlet packedValue = (packedValues || getPackedValues())[token]\n\t\t\t\t\tif (packedValue !== undefined)\n\t\t\t\t\t\treturn packedValue\n\t\t\t\t\tthrow new Error('Unknown token ' + token)\n\t\t\t}\n\t\tdefault: // negative int\n\t\t\tif (isNaN(token)) {\n\t\t\t\tlet error = new Error('Unexpected end of CBOR data')\n\t\t\t\terror.incomplete = true\n\t\t\t\tthrow error\n\t\t\t}\n\t\t\tthrow new Error('Unknown CBOR token ' + token)\n\t}\n}\nconst validName = /^[a-zA-Z_$][a-zA-Z\\d_$]*$/\nfunction createStructureReader(structure) {\n\tif (!structure) throw new Error('Structure is required in record definition');\n\tfunction readObject() {\n\t\t// get the array size from the header\n\t\tlet length = src[position++]\n\t\t//let majorType = token >> 5\n\t\tlength = length & 0x1f\n\t\tif (length > 0x17) {\n\t\t\tswitch (length) {\n\t\t\t\tcase 0x18:\n\t\t\t\t\tlength = src[position++]\n\t\t\t\t\tbreak\n\t\t\t\tcase 0x19:\n\t\t\t\t\tlength = dataView.getUint16(position)\n\t\t\t\t\tposition += 2\n\t\t\t\t\tbreak\n\t\t\t\tcase 0x1a:\n\t\t\t\t\tlength = dataView.getUint32(position)\n\t\t\t\t\tposition += 4\n\t\t\t\t\tbreak\n\t\t\t\tdefault:\n\t\t\t\t\tthrow new Error('Expected array header, but got ' + src[position - 1])\n\t\t\t}\n\t\t}\n\t\t// This initial function is quick to instantiate, but runs slower. After several iterations pay the cost to build the faster function\n\t\tlet compiledReader = this.compiledReader // first look to see if we have the fast compiled function\n\t\twhile(compiledReader) {\n\t\t\t// we have a fast compiled object literal reader\n\t\t\tif (compiledReader.propertyCount === length)\n\t\t\t\treturn compiledReader(read) // with the right length, so we use it\n\t\t\tcompiledReader = compiledReader.next // see if there is another reader with the right length\n\t\t}\n\t\tif (this.slowReads++ >= inlineObjectReadThreshold) { // create a fast compiled reader\n\t\t\tlet array = this.length == length ? this : this.slice(0, length)\n\t\t\tcompiledReader = currentDecoder.keyMap \n\t\t\t? new Function('r', 'return {' + array.map(k => currentDecoder.decodeKey(k)).map(k => validName.test(k) ? safeKey(k) + ':r()' : ('[' + JSON.stringify(k) + ']:r()')).join(',') + '}')\n\t\t\t: new Function('r', 'return {' + array.map(key => validName.test(key) ? safeKey(key) + ':r()' : ('[' + JSON.stringify(key) + ']:r()')).join(',') + '}')\n\t\t\tif (this.compiledReader)\n\t\t\t\tcompiledReader.next = this.compiledReader // if there is an existing one, we store multiple readers as a linked list because it is usually pretty rare to have multiple readers (of different length) for the same structure\n\t\t\tcompiledReader.propertyCount = length\n\t\t\tthis.compiledReader = compiledReader\n\t\t\treturn compiledReader(read)\n\t\t}\n\t\tlet object = {}\n\t\tif (currentDecoder.keyMap) for (let i = 0; i < length; i++) object[safeKey(currentDecoder.decodeKey(this[i]))] = read()\n\t\telse for (let i = 0; i < length; i++) {\n\t\t\tobject[safeKey(this[i])] = read();\n\t\t}\n\t\treturn object\n\t}\n\tstructure.slowReads = 0\n\treturn readObject\n}\n\nfunction safeKey(key) {\n\t// protect against prototype pollution\n\tif (typeof key === 'string') return key === '__proto__' ? '__proto_' : key\n\tif (typeof key === 'number' || typeof key === 'boolean' || typeof key === 'bigint') return key.toString();\n\tif (key == null) return key + '';\n\t// protect against expensive (DoS) string conversions\n\tthrow new Error('Invalid property name type ' + typeof key);\n}\n\nlet readFixedString = readStringJS\nlet readString8 = readStringJS\nlet readString16 = readStringJS\nlet readString32 = readStringJS\n\nexport let isNativeAccelerationEnabled = false\nexport function setExtractor(extractStrings) {\n\tisNativeAccelerationEnabled = true\n\treadFixedString = readString(1)\n\treadString8 = readString(2)\n\treadString16 = readString(3)\n\treadString32 = readString(5)\n\tfunction readString(headerLength) {\n\t\treturn function readString(length) {\n\t\t\tlet string = strings[stringPosition++]\n\t\t\tif (string == null) {\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\treturn readStringJS(length)\n\t\t\t\tlet extraction = extractStrings(position, srcEnd, length, src)\n\t\t\t\tif (typeof extraction == 'string') {\n\t\t\t\t\tstring = extraction\n\t\t\t\t\tstrings = EMPTY_ARRAY\n\t\t\t\t} else {\n\t\t\t\t\tstrings = extraction\n\t\t\t\t\tstringPosition = 1\n\t\t\t\t\tsrcStringEnd = 1 // even if a utf-8 string was decoded, must indicate we are in the midst of extracted strings and can't skip strings\n\t\t\t\t\tstring = strings[0]\n\t\t\t\t\tif (string === undefined)\n\t\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet srcStringLength = string.length\n\t\t\tif (srcStringLength <= length) {\n\t\t\t\tposition += length\n\t\t\t\treturn string\n\t\t\t}\n\t\t\tsrcString = string\n\t\t\tsrcStringStart = position\n\t\t\tsrcStringEnd = position + srcStringLength\n\t\t\tposition += length\n\t\t\treturn string.slice(0, length) // we know we just want the beginning\n\t\t}\n\t}\n}\nfunction readStringJS(length) {\n\tlet result\n\tif (length < 16) {\n\t\tif (result = shortStringInJS(length))\n\t\t\treturn result\n\t}\n\tif (length > 64 && decoder)\n\t\treturn decoder.decode(src.subarray(position, position += length))\n\tconst end = position + length\n\tconst units = []\n\tresult = ''\n\twhile (position < end) {\n\t\tconst byte1 = src[position++]\n\t\tif ((byte1 & 0x80) === 0) {\n\t\t\t// 1 byte\n\t\t\tunits.push(byte1)\n\t\t} else if ((byte1 & 0xe0) === 0xc0) {\n\t\t\t// 2 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 6) | byte2)\n\t\t} else if ((byte1 & 0xf0) === 0xe0) {\n\t\t\t// 3 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3)\n\t\t} else if ((byte1 & 0xf8) === 0xf0) {\n\t\t\t// 4 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tconst byte4 = src[position++] & 0x3f\n\t\t\tlet unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4\n\t\t\tif (unit > 0xffff) {\n\t\t\t\tunit -= 0x10000\n\t\t\t\tunits.push(((unit >>> 10) & 0x3ff) | 0xd800)\n\t\t\t\tunit = 0xdc00 | (unit & 0x3ff)\n\t\t\t}\n\t\t\tunits.push(unit)\n\t\t} else {\n\t\t\tunits.push(byte1)\n\t\t}\n\n\t\tif (units.length >= 0x1000) {\n\t\t\tresult += fromCharCode.apply(String, units)\n\t\t\tunits.length = 0\n\t\t}\n\t}\n\n\tif (units.length > 0) {\n\t\tresult += fromCharCode.apply(String, units)\n\t}\n\n\treturn result\n}\nlet fromCharCode = String.fromCharCode\nfunction longStringInJS(length) {\n\tlet start = position\n\tlet bytes = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tconst byte = src[position++];\n\t\tif ((byte & 0x80) > 0) {\n\t\t\tposition = start\n    \t\t\treturn\n    \t\t}\n    \t\tbytes[i] = byte\n    \t}\n    \treturn fromCharCode.apply(String, bytes)\n}\nfunction shortStringInJS(length) {\n\tif (length < 4) {\n\t\tif (length < 2) {\n\t\t\tif (length === 0)\n\t\t\t\treturn ''\n\t\t\telse {\n\t\t\t\tlet a = src[position++]\n\t\t\t\tif ((a & 0x80) > 1) {\n\t\t\t\t\tposition -= 1\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a)\n\t\t\t}\n\t\t} else {\n\t\t\tlet a = src[position++]\n\t\t\tlet b = src[position++]\n\t\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0) {\n\t\t\t\tposition -= 2\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 3)\n\t\t\t\treturn fromCharCode(a, b)\n\t\t\tlet c = src[position++]\n\t\t\tif ((c & 0x80) > 0) {\n\t\t\t\tposition -= 3\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c)\n\t\t}\n\t} else {\n\t\tlet a = src[position++]\n\t\tlet b = src[position++]\n\t\tlet c = src[position++]\n\t\tlet d = src[position++]\n\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0 || (c & 0x80) > 0 || (d & 0x80) > 0) {\n\t\t\tposition -= 4\n\t\t\treturn\n\t\t}\n\t\tif (length < 6) {\n\t\t\tif (length === 4)\n\t\t\t\treturn fromCharCode(a, b, c, d)\n\t\t\telse {\n\t\t\t\tlet e = src[position++]\n\t\t\t\tif ((e & 0x80) > 0) {\n\t\t\t\t\tposition -= 5\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e)\n\t\t\t}\n\t\t} else if (length < 8) {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0) {\n\t\t\t\tposition -= 6\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 7)\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f)\n\t\t\tlet g = src[position++]\n\t\t\tif ((g & 0x80) > 0) {\n\t\t\t\tposition -= 7\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c, d, e, f, g)\n\t\t} else {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tlet g = src[position++]\n\t\t\tlet h = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0 || (g & 0x80) > 0 || (h & 0x80) > 0) {\n\t\t\t\tposition -= 8\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 10) {\n\t\t\t\tif (length === 8)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h)\n\t\t\t\telse {\n\t\t\t\t\tlet i = src[position++]\n\t\t\t\t\tif ((i & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 9\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i)\n\t\t\t\t}\n\t\t\t} else if (length < 12) {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0) {\n\t\t\t\t\tposition -= 10\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 11)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j)\n\t\t\t\tlet k = src[position++]\n\t\t\t\tif ((k & 0x80) > 0) {\n\t\t\t\t\tposition -= 11\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k)\n\t\t\t} else {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tlet k = src[position++]\n\t\t\t\tlet l = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0 || (k & 0x80) > 0 || (l & 0x80) > 0) {\n\t\t\t\t\tposition -= 12\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 14) {\n\t\t\t\t\tif (length === 12)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l)\n\t\t\t\t\telse {\n\t\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\t\tif ((m & 0x80) > 0) {\n\t\t\t\t\t\t\tposition -= 13\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\tlet n = src[position++]\n\t\t\t\t\tif ((m & 0x80) > 0 || (n & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 14\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tif (length < 15)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n)\n\t\t\t\t\tlet o = src[position++]\n\t\t\t\t\tif ((o & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 15\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction readBin(length) {\n\treturn currentDecoder.copyBuffers ?\n\t\t// specifically use the copying slice (not the node one)\n\t\tUint8Array.prototype.slice.call(src, position, position += length) :\n\t\tsrc.subarray(position, position += length)\n}\nfunction readExt(length) {\n\tlet type = src[position++]\n\tif (currentExtensions[type]) {\n\t\treturn currentExtensions[type](src.subarray(position, position += length))\n\t}\n\telse\n\t\tthrow new Error('Unknown extension type ' + type)\n}\nlet f32Array = new Float32Array(1)\nlet u8Array = new Uint8Array(f32Array.buffer, 0, 4)\nfunction getFloat16() {\n\tlet byte0 = src[position++]\n\tlet byte1 = src[position++]\n\tlet exponent = (byte0 & 0x7f) >> 2;\n\tif (exponent === 0x1f) { // specials\n\t\tif (byte1 || (byte0 & 3))\n\t\t\treturn NaN;\n\t\treturn (byte0 & 0x80) ? -Infinity : Infinity;\n\t}\n\tif (exponent === 0) { // sub-normals\n\t\t// significand with 10 fractional bits and divided by 2^14\n\t\tlet abs = (((byte0 & 3) << 8) | byte1) / (1 << 24)\n\t\treturn (byte0 & 0x80) ? -abs : abs\n\t}\n\n\tu8Array[3] = (byte0 & 0x80) | // sign bit\n\t\t((exponent >> 1) + 56) // 4 of 5 of the exponent bits, re-offset-ed\n\tu8Array[2] = ((byte0 & 7) << 5) | // last exponent bit and first two mantissa bits\n\t\t(byte1 >> 3) // next 5 bits of mantissa\n\tu8Array[1] = byte1 << 5; // last three bits of mantissa\n\tu8Array[0] = 0;\n\treturn f32Array[0];\n}\n\nlet keyCache = new Array(4096)\nfunction readKey() {\n\tlet length = src[position++]\n\tif (length >= 0x60 && length < 0x78) {\n\t\t// fixstr, potentially use key cache\n\t\tlength = length - 0x60\n\t\tif (srcStringEnd >= position) // if it has been extracted, must use it (and faster anyway)\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\telse if (!(srcStringEnd == 0 && srcEnd < 180))\n\t\t\treturn readFixedString(length)\n\t} else { // not cacheable, go back and do a standard read\n\t\tposition--\n\t\treturn read()\n\t}\n\tlet key = ((length << 5) ^ (length > 1 ? dataView.getUint16(position) : length > 0 ? src[position] : 0)) & 0xfff\n\tlet entry = keyCache[key]\n\tlet checkPosition = position\n\tlet end = position + length - 3\n\tlet chunk\n\tlet i = 0\n\tif (entry && entry.bytes == length) {\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = dataView.getUint32(checkPosition)\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t\tcheckPosition += 4\n\t\t}\n\t\tend += 3\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = src[checkPosition++]\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\tif (checkPosition === end) {\n\t\t\tposition = checkPosition\n\t\t\treturn entry.string\n\t\t}\n\t\tend -= 3\n\t\tcheckPosition = position\n\t}\n\tentry = []\n\tkeyCache[key] = entry\n\tentry.bytes = length\n\twhile (checkPosition < end) {\n\t\tchunk = dataView.getUint32(checkPosition)\n\t\tentry.push(chunk)\n\t\tcheckPosition += 4\n\t}\n\tend += 3\n\twhile (checkPosition < end) {\n\t\tchunk = src[checkPosition++]\n\t\tentry.push(chunk)\n\t}\n\t// for small blocks, avoiding the overhead of the extract call is helpful\n\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\tif (string != null)\n\t\treturn entry.string = string\n\treturn entry.string = readFixedString(length)\n}\n\nexport class Tag {\n\tconstructor(value, tag) {\n\t\tthis.value = value\n\t\tthis.tag = tag\n\t}\n}\n\ncurrentExtensions[0] = (dateString) => {\n\t// string date extension\n\treturn new Date(dateString)\n}\n\ncurrentExtensions[1] = (epochSec) => {\n\t// numeric date extension\n\treturn new Date(Math.round(epochSec * 1000))\n}\n\ncurrentExtensions[2] = (buffer) => {\n\t// bigint extension\n\tlet value = BigInt(0)\n\tfor (let i = 0, l = buffer.byteLength; i < l; i++) {\n\t\tvalue = BigInt(buffer[i]) + (value << BigInt(8))\n\t}\n\treturn value\n}\n\ncurrentExtensions[3] = (buffer) => {\n\t// negative bigint extension\n\treturn BigInt(-1) - currentExtensions[2](buffer)\n}\ncurrentExtensions[4] = (fraction) => {\n\t// best to reparse to maintain accuracy\n\treturn +(fraction[1] + 'e' + fraction[0])\n}\n\ncurrentExtensions[5] = (fraction) => {\n\t// probably not sufficiently accurate\n\treturn fraction[1] * Math.exp(fraction[0] * Math.log(2))\n}\n\n// the registration of the record definition extension\nconst recordDefinition = (id, structure) => {\n\tid = id - 0xe000\n\tlet existingStructure = currentStructures[id]\n\tif (existingStructure && existingStructure.isShared) {\n\t\t(currentStructures.restoreStructures || (currentStructures.restoreStructures = []))[id] = existingStructure\n\t}\n\tcurrentStructures[id] = structure\n\n\tstructure.read = createStructureReader(structure)\n}\ncurrentExtensions[LEGACY_RECORD_INLINE_ID] = (data) => {\n\tlet length = data.length\n\tlet structure = data[1]\n\trecordDefinition(data[0], structure)\n\tlet object = {}\n\tfor (let i = 2; i < length; i++) {\n\t\tlet key = structure[i - 2]\n\t\tobject[safeKey(key)] = data[i]\n\t}\n\treturn object\n}\ncurrentExtensions[14] = (value) => {\n\tif (bundledStrings)\n\t\treturn bundledStrings[0].slice(bundledStrings.position0, bundledStrings.position0 += value)\n\treturn new Tag(value, 14)\n}\ncurrentExtensions[15] = (value) => {\n\tif (bundledStrings)\n\t\treturn bundledStrings[1].slice(bundledStrings.position1, bundledStrings.position1 += value)\n\treturn new Tag(value, 15)\n}\nlet glbl = { Error, RegExp }\ncurrentExtensions[27] = (data) => { // http://cbor.schmorp.de/generic-object\n\treturn (glbl[data[0]] || Error)(data[1], data[2])\n}\nconst packedTable = (read) => {\n\tif (src[position++] != 0x84) {\n\t\tlet error = new Error('Packed values structure must be followed by a 4 element array')\n\t\tif (src.length < position)\n\t\t\terror.incomplete = true\n\t\tthrow error\n\t}\n\tlet newPackedValues = read() // packed values\n\tif (!newPackedValues || !newPackedValues.length) {\n\t\tlet error = new Error('Packed values structure must be followed by a 4 element array')\n\t\terror.incomplete = true\n\t\tthrow error\n\t}\n\tpackedValues = packedValues ? newPackedValues.concat(packedValues.slice(newPackedValues.length)) : newPackedValues\n\tpackedValues.prefixes = read()\n\tpackedValues.suffixes = read()\n\treturn read() // read the rump\n}\npackedTable.handlesRead = true\ncurrentExtensions[51] = packedTable\n\ncurrentExtensions[PACKED_REFERENCE_TAG_ID] = (data) => { // packed reference\n\tif (!packedValues) {\n\t\tif (currentDecoder.getShared)\n\t\t\tloadShared()\n\t\telse\n\t\t\treturn new Tag(data, PACKED_REFERENCE_TAG_ID)\n\t}\n\tif (typeof data == 'number')\n\t\treturn packedValues[16 + (data >= 0 ? 2 * data : (-2 * data - 1))]\n\tlet error = new Error('No support for non-integer packed references yet')\n\tif (data === undefined)\n\t\terror.incomplete = true\n\tthrow error\n}\n\n// The following code is an incomplete implementation of http://cbor.schmorp.de/stringref\n// the real thing would need to implemennt more logic to populate the stringRefs table and\n// maintain a stack of stringRef \"namespaces\".\n//\n// currentExtensions[25] = (id) => {\n// \treturn stringRefs[id]\n// }\n// currentExtensions[256] = (read) => {\n// \tstringRefs = []\n// \ttry {\n// \t\treturn read()\n// \t} finally {\n// \t\tstringRefs = null\n// \t}\n// }\n// currentExtensions[256].handlesRead = true\n\ncurrentExtensions[28] = (read) => { \n\t// shareable http://cbor.schmorp.de/value-sharing (for structured clones)\n\tif (!referenceMap) {\n\t\treferenceMap = new Map()\n\t\treferenceMap.id = 0\n\t}\n\tlet id = referenceMap.id++\n\tlet startingPosition = position\n\tlet token = src[position]\n\tlet target\n\t// TODO: handle Maps, Sets, and other types that can cycle; this is complicated, because you potentially need to read\n\t// ahead past references to record structure definitions\n\tif ((token >> 5) == 4)\n\t\ttarget = []\n\telse\n\t\ttarget = {}\n\n\tlet refEntry = { target } // a placeholder object\n\treferenceMap.set(id, refEntry)\n\tlet targetProperties = read() // read the next value as the target object to id\n\tif (refEntry.used) {// there is a cycle, so we have to assign properties to original target\n\t\tif (Object.getPrototypeOf(target) !== Object.getPrototypeOf(targetProperties)) {\n\t\t\t// this means that the returned target does not match the targetProperties, so we need rerun the read to\n\t\t\t// have the correctly create instance be assigned as a reference, then we do the copy the properties back to the\n\t\t\t// target\n\t\t\t// reset the position so that the read can be repeated\n\t\t\tposition = startingPosition\n\t\t\t// the returned instance is our new target for references\n\t\t\ttarget = targetProperties\n\t\t\treferenceMap.set(id, { target })\n\t\t\ttargetProperties = read()\n\t\t}\n\t\treturn Object.assign(target, targetProperties)\n\t}\n\trefEntry.target = targetProperties // the placeholder wasn't used, replace with the deserialized one\n\treturn targetProperties // no cycle, can just use the returned read object\n}\ncurrentExtensions[28].handlesRead = true\n\ncurrentExtensions[29] = (id) => {\n\t// sharedref http://cbor.schmorp.de/value-sharing (for structured clones)\n\tlet refEntry = referenceMap.get(id)\n\trefEntry.used = true\n\treturn refEntry.target\n}\n\ncurrentExtensions[258] = (array) => new Set(array); // https://github.com/input-output-hk/cbor-sets-spec/blob/master/CBOR_SETS.md\n(currentExtensions[259] = (read) => {\n\t// https://github.com/shanewholloway/js-cbor-codec/blob/master/docs/CBOR-259-spec\n\t// for decoding as a standard Map\n\tif (currentDecoder.mapsAsObjects) {\n\t\tcurrentDecoder.mapsAsObjects = false\n\t\trestoreMapsAsObject = true\n\t}\n\treturn read()\n}).handlesRead = true\nfunction combine(a, b) {\n\tif (typeof a === 'string')\n\t\treturn a + b\n\tif (a instanceof Array)\n\t\treturn a.concat(b)\n\treturn Object.assign({}, a, b)\n}\nfunction getPackedValues() {\n\tif (!packedValues) {\n\t\tif (currentDecoder.getShared)\n\t\t\tloadShared()\n\t\telse\n\t\t\tthrow new Error('No packed values available')\n\t}\n\treturn packedValues\n}\nconst SHARED_DATA_TAG_ID = 0x53687264 // ascii 'Shrd'\ncurrentExtensionRanges.push((tag, input) => {\n\tif (tag >= 225 && tag <= 255)\n\t\treturn combine(getPackedValues().prefixes[tag - 224], input)\n\tif (tag >= 28704 && tag <= 32767)\n\t\treturn combine(getPackedValues().prefixes[tag - 28672], input)\n\tif (tag >= 1879052288 && tag <= 2147483647)\n\t\treturn combine(getPackedValues().prefixes[tag - 1879048192], input)\n\tif (tag >= 216 && tag <= 223)\n\t\treturn combine(input, getPackedValues().suffixes[tag - 216])\n\tif (tag >= 27647 && tag <= 28671)\n\t\treturn combine(input, getPackedValues().suffixes[tag - 27639])\n\tif (tag >= 1811940352 && tag <= 1879048191)\n\t\treturn combine(input, getPackedValues().suffixes[tag - 1811939328])\n\tif (tag == SHARED_DATA_TAG_ID) {// we do a special check for this so that we can keep the currentExtensions as densely stored array (v8 stores arrays densely under about 3000 elements)\n\t\treturn {\n\t\t\tpackedValues: packedValues,\n\t\t\tstructures: currentStructures.slice(0),\n\t\t\tversion: input,\n\t\t}\n\t}\n\tif (tag == 55799) // self-descriptive CBOR tag, just return input value\n\t\treturn input\n})\n\nconst isLittleEndianMachine = new Uint8Array(new Uint16Array([1]).buffer)[0] == 1\nexport const typedArrays = [Uint8Array, Uint8ClampedArray, Uint16Array, Uint32Array,\n\ttypeof BigUint64Array == 'undefined' ? { name:'BigUint64Array' } : BigUint64Array, Int8Array, Int16Array, Int32Array,\n\ttypeof BigInt64Array == 'undefined' ? { name:'BigInt64Array' } : BigInt64Array, Float32Array, Float64Array]\nconst typedArrayTags = [64, 68, 69, 70, 71, 72, 77, 78, 79, 85, 86]\nfor (let i = 0; i < typedArrays.length; i++) {\n\tregisterTypedArray(typedArrays[i], typedArrayTags[i])\n}\nfunction registerTypedArray(TypedArray, tag) {\n\tlet dvMethod = 'get' + TypedArray.name.slice(0, -5)\n\tlet bytesPerElement;\n\tif (typeof TypedArray === 'function')\n\t\tbytesPerElement = TypedArray.BYTES_PER_ELEMENT;\n\telse\n\t\tTypedArray = null;\n\tfor (let littleEndian = 0; littleEndian < 2; littleEndian++) {\n\t\tif (!littleEndian && bytesPerElement == 1)\n\t\t\tcontinue\n\t\tlet sizeShift = bytesPerElement == 2 ? 1 : bytesPerElement == 4 ? 2 : bytesPerElement == 8 ? 3 : 0\n\t\tcurrentExtensions[littleEndian ? tag : (tag - 4)] = (bytesPerElement == 1 || littleEndian == isLittleEndianMachine) ? (buffer) => {\n\t\t\tif (!TypedArray)\n\t\t\t\tthrow new Error('Could not find typed array for code ' + tag)\n\t\t\tif (!currentDecoder.copyBuffers) {\n\t\t\t\t// try provide a direct view, but will only work if we are byte-aligned\n\t\t\t\tif (bytesPerElement === 1 ||\n\t\t\t\t\tbytesPerElement === 2 && !(buffer.byteOffset & 1) ||\n\t\t\t\t\tbytesPerElement === 4 && !(buffer.byteOffset & 3) ||\n\t\t\t\t\tbytesPerElement === 8 && !(buffer.byteOffset & 7))\n\t\t\t\t\treturn new TypedArray(buffer.buffer, buffer.byteOffset, buffer.byteLength >> sizeShift);\n\t\t\t}\n\t\t\t// we have to slice/copy here to get a new ArrayBuffer, if we are not word/byte aligned\n\t\t\treturn new TypedArray(Uint8Array.prototype.slice.call(buffer, 0).buffer)\n\t\t} : buffer => {\n\t\t\tif (!TypedArray)\n\t\t\t\tthrow new Error('Could not find typed array for code ' + tag)\n\t\t\tlet dv = new DataView(buffer.buffer, buffer.byteOffset, buffer.byteLength)\n\t\t\tlet elements = buffer.length >> sizeShift\n\t\t\tlet ta = new TypedArray(elements)\n\t\t\tlet method = dv[dvMethod]\n\t\t\tfor (let i = 0; i < elements; i++) {\n\t\t\t\tta[i] = method.call(dv, i << sizeShift, littleEndian)\n\t\t\t}\n\t\t\treturn ta\n\t\t}\n\t}\n}\n\nfunction readBundleExt() {\n\tlet length = readJustLength()\n\tlet bundlePosition = position + read()\n\tfor (let i = 2; i < length; i++) {\n\t\t// skip past bundles that were already read\n\t\tlet bundleLength = readJustLength() // this will increment position, so must add to position afterwards\n\t\tposition += bundleLength\n\t}\n\tlet dataPosition = position\n\tposition = bundlePosition\n\tbundledStrings = [readStringJS(readJustLength()), readStringJS(readJustLength())]\n\tbundledStrings.position0 = 0\n\tbundledStrings.position1 = 0\n\tbundledStrings.postBundlePosition = position\n\tposition = dataPosition\n\treturn read()\n}\n\nfunction readJustLength() {\n\tlet token = src[position++] & 0x1f\n\tif (token > 0x17) {\n\t\tswitch (token) {\n\t\t\tcase 0x18:\n\t\t\t\ttoken = src[position++]\n\t\t\t\tbreak\n\t\t\tcase 0x19:\n\t\t\t\ttoken = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tbreak\n\t\t\tcase 0x1a:\n\t\t\t\ttoken = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tbreak\n\t\t}\n\t}\n\treturn token\n}\n\nfunction loadShared() {\n\tif (currentDecoder.getShared) {\n\t\tlet sharedData = saveState(() => {\n\t\t\t// save the state in case getShared modifies our buffer\n\t\t\tsrc = null\n\t\t\treturn currentDecoder.getShared()\n\t\t}) || {}\n\t\tlet updatedStructures = sharedData.structures || []\n\t\tcurrentDecoder.sharedVersion = sharedData.version\n\t\tpackedValues = currentDecoder.sharedValues = sharedData.packedValues\n\t\tif (currentStructures === true)\n\t\t\tcurrentDecoder.structures = currentStructures = updatedStructures\n\t\telse\n\t\t\tcurrentStructures.splice.apply(currentStructures, [0, updatedStructures.length].concat(updatedStructures))\n\t}\n}\n\nfunction saveState(callback) {\n\tlet savedSrcEnd = srcEnd\n\tlet savedPosition = position\n\tlet savedStringPosition = stringPosition\n\tlet savedSrcStringStart = srcStringStart\n\tlet savedSrcStringEnd = srcStringEnd\n\tlet savedSrcString = srcString\n\tlet savedStrings = strings\n\tlet savedReferenceMap = referenceMap\n\tlet savedBundledStrings = bundledStrings\n\n\t// TODO: We may need to revisit this if we do more external calls to user code (since it could be slow)\n\tlet savedSrc = new Uint8Array(src.slice(0, srcEnd)) // we copy the data in case it changes while external data is processed\n\tlet savedStructures = currentStructures\n\tlet savedDecoder = currentDecoder\n\tlet savedSequentialMode = sequentialMode\n\tlet value = callback()\n\tsrcEnd = savedSrcEnd\n\tposition = savedPosition\n\tstringPosition = savedStringPosition\n\tsrcStringStart = savedSrcStringStart\n\tsrcStringEnd = savedSrcStringEnd\n\tsrcString = savedSrcString\n\tstrings = savedStrings\n\treferenceMap = savedReferenceMap\n\tbundledStrings = savedBundledStrings\n\tsrc = savedSrc\n\tsequentialMode = savedSequentialMode\n\tcurrentStructures = savedStructures\n\tcurrentDecoder = savedDecoder\n\tdataView = new DataView(src.buffer, src.byteOffset, src.byteLength)\n\treturn value\n}\nexport function clearSource() {\n\tsrc = null\n\treferenceMap = null\n\tcurrentStructures = null\n}\n\nexport function addExtension(extension) {\n\tcurrentExtensions[extension.tag] = extension.decode\n}\n\nexport function setSizeLimits(limits) {\n\tif (limits.maxMapSize) maxMapSize = limits.maxMapSize;\n\tif (limits.maxArraySize) maxArraySize = limits.maxArraySize;\n\tif (limits.maxObjectSize) maxObjectSize = limits.maxObjectSize;\n}\n\nexport const mult10 = new Array(147) // this is a table matching binary exponents to the multiplier to determine significant digit rounding\nfor (let i = 0; i < 256; i++) {\n\tmult10[i] = +('1e' + Math.floor(45.15 - i * 0.30103))\n}\nlet defaultDecoder = new Decoder({ useRecords: false })\nexport const decode = defaultDecoder.decode\nexport const decodeMultiple = defaultDecoder.decodeMultiple\nexport const FLOAT32_OPTIONS = {\n\tNEVER: 0,\n\tALWAYS: 1,\n\tDECIMAL_ROUND: 3,\n\tDECIMAL_FIT: 4\n}\nexport function roundFloat32(float32Number) {\n\tf32Array[0] = float32Number\n\tlet multiplier = mult10[((u8Array[3] & 0x7f) << 1) | (u8Array[2] >> 7)]\n\treturn ((multiplier * float32Number + (float32Number > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n}\n", "import { Decoder, mult10, Tag, typedArrays, addExtension as decodeAddExtension } from './decode.js'\nlet textEncoder\ntry {\n\ttextEncoder = new TextEncoder()\n} catch (error) {}\nlet extensions, extensionClasses\nconst Buffer = typeof globalThis === 'object' && globalThis.Buffer;\nconst hasNodeBuffer = typeof Buffer !== 'undefined'\nconst ByteArrayAllocate = hasNodeBuffer ? Buffer.allocUnsafeSlow : Uint8Array\nconst ByteArray = hasNodeBuffer ? Buffer : Uint8Array\nconst MAX_STRUCTURES = 0x100\nconst MAX_BUFFER_SIZE = hasNodeBuffer ? 0x100000000 : 0x7fd00000\nlet serializationId = 1\nlet throwOnIterable\nlet target\nlet targetView\nlet position = 0\nlet safeEnd\nlet bundledStrings = null\nconst MAX_BUNDLE_SIZE = 0xf000\nconst hasNonLatin = /[\\u0080-\\uFFFF]/\nconst RECORD_SYMBOL = Symbol('record-id')\nexport class Encoder extends Decoder {\n\tconstructor(options) {\n\t\tsuper(options)\n\t\tthis.offset = 0\n\t\tlet typeBuffer\n\t\tlet start\n\t\tlet sharedStructures\n\t\tlet hasSharedUpdate\n\t\tlet structures\n\t\tlet referenceMap\n\t\toptions = options || {}\n\t\tlet encodeUtf8 = ByteArray.prototype.utf8Write ? function(string, position, maxBytes) {\n\t\t\treturn target.utf8Write(string, position, maxBytes)\n\t\t} : (textEncoder && textEncoder.encodeInto) ?\n\t\t\tfunction(string, position) {\n\t\t\t\treturn textEncoder.encodeInto(string, target.subarray(position)).written\n\t\t\t} : false\n\n\t\tlet encoder = this\n\t\tlet hasSharedStructures = options.structures || options.saveStructures\n\t\tlet maxSharedStructures = options.maxSharedStructures\n\t\tif (maxSharedStructures == null)\n\t\t\tmaxSharedStructures = hasSharedStructures ? 128 : 0\n\t\tif (maxSharedStructures > 8190)\n\t\t\tthrow new Error('Maximum maxSharedStructure is 8190')\n\t\tlet isSequential = options.sequential\n\t\tif (isSequential) {\n\t\t\tmaxSharedStructures = 0\n\t\t}\n\t\tif (!this.structures)\n\t\t\tthis.structures = []\n\t\tif (this.saveStructures)\n\t\t\tthis.saveShared = this.saveStructures\n\t\tlet samplingPackedValues, packedObjectMap, sharedValues = options.sharedValues\n\t\tlet sharedPackedObjectMap\n\t\tif (sharedValues) {\n\t\t\tsharedPackedObjectMap = Object.create(null)\n\t\t\tfor (let i = 0, l = sharedValues.length; i < l; i++) {\n\t\t\t\tsharedPackedObjectMap[sharedValues[i]] = i\n\t\t\t}\n\t\t}\n\t\tlet recordIdsToRemove = []\n\t\tlet transitionsCount = 0\n\t\tlet serializationsSinceTransitionRebuild = 0\n\t\t\n\t\tthis.mapEncode = function(value, encodeOptions) {\n\t\t\t// Experimental support for premapping keys using _keyMap instad of keyMap - not optiimised yet)\n\t\t\tif (this._keyMap && !this._mapped) {\n\t\t\t\t//console.log('encoding ', value)\n\t\t\t\tswitch (value.constructor.name) {\n\t\t\t\t\tcase 'Array': \n\t\t\t\t\t\tvalue = value.map(r => this.encodeKeys(r))\n\t\t\t\t\t\tbreak\n\t\t\t\t\t//case 'Map': \n\t\t\t\t\t//\tvalue = this.encodeKeys(value)\n\t\t\t\t\t//\tbreak\n\t\t\t\t}\n\t\t\t\t//this._mapped = true\n\t\t\t}\n\t\t\treturn this.encode(value, encodeOptions)\n\t\t}\n\t\t\n\t\tthis.encode = function(value, encodeOptions)\t{\n\t\t\tif (!target) {\n\t\t\t\ttarget = new ByteArrayAllocate(8192)\n\t\t\t\ttargetView = new DataView(target.buffer, 0, 8192)\n\t\t\t\tposition = 0\n\t\t\t}\n\t\t\tsafeEnd = target.length - 10\n\t\t\tif (safeEnd - position < 0x800) {\n\t\t\t\t// don't start too close to the end, \n\t\t\t\ttarget = new ByteArrayAllocate(target.length)\n\t\t\t\ttargetView = new DataView(target.buffer, 0, target.length)\n\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\tposition = 0\n\t\t\t} else if (encodeOptions === REUSE_BUFFER_MODE)\n\t\t\t\tposition = (position + 7) & 0x7ffffff8 // Word align to make any future copying of this buffer faster\n\t\t\tstart = position\n\t\t\tif (encoder.useSelfDescribedHeader) {\n\t\t\t\ttargetView.setUint32(position, 0xd9d9f700) // tag two byte, then self-descriptive tag\n\t\t\t\tposition += 3\n\t\t\t}\n\t\t\treferenceMap = encoder.structuredClone ? new Map() : null\n\t\t\tif (encoder.bundleStrings && typeof value !== 'string') {\n\t\t\t\tbundledStrings = []\n\t\t\t\tbundledStrings.size = Infinity // force a new bundle start on first string\n\t\t\t} else\n\t\t\t\tbundledStrings = null\n\n\t\t\tsharedStructures = encoder.structures\n\t\t\tif (sharedStructures) {\n\t\t\t\tif (sharedStructures.uninitialized) {\n\t\t\t\t\tlet sharedData = encoder.getShared() || {}\n\t\t\t\t\tencoder.structures = sharedStructures = sharedData.structures || []\n\t\t\t\t\tencoder.sharedVersion = sharedData.version\n\t\t\t\t\tlet sharedValues = encoder.sharedValues = sharedData.packedValues\n\t\t\t\t\tif (sharedValues) {\n\t\t\t\t\t\tsharedPackedObjectMap = {}\n\t\t\t\t\t\tfor (let i = 0, l = sharedValues.length; i < l; i++)\n\t\t\t\t\t\t\tsharedPackedObjectMap[sharedValues[i]] = i\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet sharedStructuresLength = sharedStructures.length\n\t\t\t\tif (sharedStructuresLength > maxSharedStructures && !isSequential)\n\t\t\t\t\tsharedStructuresLength = maxSharedStructures\n\t\t\t\tif (!sharedStructures.transitions) {\n\t\t\t\t\t// rebuild our structure transitions\n\t\t\t\t\tsharedStructures.transitions = Object.create(null)\n\t\t\t\t\tfor (let i = 0; i < sharedStructuresLength; i++) {\n\t\t\t\t\t\tlet keys = sharedStructures[i]\n\t\t\t\t\t\t//console.log('shared struct keys:', keys)\n\t\t\t\t\t\tif (!keys)\n\t\t\t\t\t\t\tcontinue\n\t\t\t\t\t\tlet nextTransition, transition = sharedStructures.transitions\n\t\t\t\t\t\tfor (let j = 0, l = keys.length; j < l; j++) {\n\t\t\t\t\t\t\tif (transition[RECORD_SYMBOL] === undefined)\n\t\t\t\t\t\t\t\ttransition[RECORD_SYMBOL] = i\n\t\t\t\t\t\t\tlet key = keys[j]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttransition[RECORD_SYMBOL] = i | 0x100000\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!isSequential)\n\t\t\t\t\tsharedStructures.nextId = sharedStructuresLength\n\t\t\t}\n\t\t\tif (hasSharedUpdate)\n\t\t\t\thasSharedUpdate = false\n\t\t\tstructures = sharedStructures || []\n\t\t\tpackedObjectMap = sharedPackedObjectMap\n\t\t\tif (options.pack) {\n\t\t\t\tlet packedValues = new Map()\n\t\t\t\tpackedValues.values = []\n\t\t\t\tpackedValues.encoder = encoder\n\t\t\t\tpackedValues.maxValues = options.maxPrivatePackedValues || (sharedPackedObjectMap ? 16 : Infinity)\n\t\t\t\tpackedValues.objectMap = sharedPackedObjectMap || false\n\t\t\t\tpackedValues.samplingPackedValues = samplingPackedValues\n\t\t\t\tfindRepetitiveStrings(value, packedValues)\n\t\t\t\tif (packedValues.values.length > 0) {\n\t\t\t\t\ttarget[position++] = 0xd8 // one-byte tag\n\t\t\t\t\ttarget[position++] = 51 // tag 51 for packed shared structures https://www.potaroo.net/ietf/ids/draft-ietf-cbor-packed-03.txt\n\t\t\t\t\twriteArrayHeader(4)\n\t\t\t\t\tlet valuesArray = packedValues.values\n\t\t\t\t\tencode(valuesArray)\n\t\t\t\t\twriteArrayHeader(0) // prefixes\n\t\t\t\t\twriteArrayHeader(0) // suffixes\n\t\t\t\t\tpackedObjectMap = Object.create(sharedPackedObjectMap || null)\n\t\t\t\t\tfor (let i = 0, l = valuesArray.length; i < l; i++) {\n\t\t\t\t\t\tpackedObjectMap[valuesArray[i]] = i\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tthrowOnIterable = encodeOptions & THROW_ON_ITERABLE;\n\t\t\ttry {\n\t\t\t\tif (throwOnIterable)\n\t\t\t\t\treturn;\n\t\t\t\tencode(value)\n\t\t\t\tif (bundledStrings) {\n\t\t\t\t\twriteBundles(start, encode)\n\t\t\t\t}\n\t\t\t\tencoder.offset = position // update the offset so next serialization doesn't write over our buffer, but can continue writing to same buffer sequentially\n\t\t\t\tif (referenceMap && referenceMap.idsToInsert) {\n\t\t\t\t\tposition += referenceMap.idsToInsert.length * 2\n\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\tencoder.offset = position\n\t\t\t\t\tlet serialized = insertIds(target.subarray(start, position), referenceMap.idsToInsert)\n\t\t\t\t\treferenceMap = null\n\t\t\t\t\treturn serialized\n\t\t\t\t}\n\t\t\t\tif (encodeOptions & REUSE_BUFFER_MODE) {\n\t\t\t\t\ttarget.start = start\n\t\t\t\t\ttarget.end = position\n\t\t\t\t\treturn target\n\t\t\t\t}\n\t\t\t\treturn target.subarray(start, position) // position can change if we call encode again in saveShared, so we get the buffer now\n\t\t\t} finally {\n\t\t\t\tif (sharedStructures) {\n\t\t\t\t\tif (serializationsSinceTransitionRebuild < 10)\n\t\t\t\t\t\tserializationsSinceTransitionRebuild++\n\t\t\t\t\tif (sharedStructures.length > maxSharedStructures)\n\t\t\t\t\t\tsharedStructures.length = maxSharedStructures\n\t\t\t\t\tif (transitionsCount > 10000) {\n\t\t\t\t\t\t// force a rebuild occasionally after a lot of transitions so it can get cleaned up\n\t\t\t\t\t\tsharedStructures.transitions = null\n\t\t\t\t\t\tserializationsSinceTransitionRebuild = 0\n\t\t\t\t\t\ttransitionsCount = 0\n\t\t\t\t\t\tif (recordIdsToRemove.length > 0)\n\t\t\t\t\t\t\trecordIdsToRemove = []\n\t\t\t\t\t} else if (recordIdsToRemove.length > 0 && !isSequential) {\n\t\t\t\t\t\tfor (let i = 0, l = recordIdsToRemove.length; i < l; i++) {\n\t\t\t\t\t\t\trecordIdsToRemove[i][RECORD_SYMBOL] = undefined\n\t\t\t\t\t\t}\n\t\t\t\t\t\trecordIdsToRemove = []\n\t\t\t\t\t\t//sharedStructures.nextId = maxSharedStructures\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (hasSharedUpdate && encoder.saveShared) {\n\t\t\t\t\tif (encoder.structures.length > maxSharedStructures) {\n\t\t\t\t\t\tencoder.structures = encoder.structures.slice(0, maxSharedStructures)\n\t\t\t\t\t}\n\t\t\t\t\t// we can't rely on start/end with REUSE_BUFFER_MODE since they will (probably) change when we save\n\t\t\t\t\tlet returnBuffer = target.subarray(start, position)\n\t\t\t\t\tif (encoder.updateSharedData() === false)\n\t\t\t\t\t\treturn encoder.encode(value) // re-encode if it fails\n\t\t\t\t\treturn returnBuffer\n\t\t\t\t}\n\t\t\t\tif (encodeOptions & RESET_BUFFER_MODE)\n\t\t\t\t\tposition = start\n\t\t\t}\n\t\t}\n\t\tthis.findCommonStringsToPack = () => {\n\t\t\tsamplingPackedValues = new Map()\n\t\t\tif (!sharedPackedObjectMap)\n\t\t\t\tsharedPackedObjectMap = Object.create(null)\n\t\t\treturn (options) => {\n\t\t\t\tlet threshold = options && options.threshold || 4\n\t\t\t\tlet position = this.pack ? options.maxPrivatePackedValues || 16 : 0\n\t\t\t\tif (!sharedValues)\n\t\t\t\t\tsharedValues = this.sharedValues = []\n\t\t\t\tfor (let [ key, status ] of samplingPackedValues) {\n\t\t\t\t\tif (status.count > threshold) {\n\t\t\t\t\t\tsharedPackedObjectMap[key] = position++\n\t\t\t\t\t\tsharedValues.push(key)\n\t\t\t\t\t\thasSharedUpdate = true\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\twhile (this.saveShared && this.updateSharedData() === false) {}\n\t\t\t\tsamplingPackedValues = null\n\t\t\t}\n\t\t}\n\t\tconst encode = (value) => {\n\t\t\tif (position > safeEnd)\n\t\t\t\ttarget = makeRoom(position)\n\n\t\t\tvar type = typeof value\n\t\t\tvar length\n\t\t\tif (type === 'string') {\n\t\t\t\tif (packedObjectMap) {\n\t\t\t\t\tlet packedPosition = packedObjectMap[value]\n\t\t\t\t\tif (packedPosition >= 0) {\n\t\t\t\t\t\tif (packedPosition < 16)\n\t\t\t\t\t\t\ttarget[position++] = packedPosition + 0xe0 // simple values, defined in https://www.potaroo.net/ietf/ids/draft-ietf-cbor-packed-03.txt\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\ttarget[position++] = 0xc6 // tag 6 defined in https://www.potaroo.net/ietf/ids/draft-ietf-cbor-packed-03.txt\n\t\t\t\t\t\t\tif (packedPosition & 1)\n\t\t\t\t\t\t\t\tencode((15 - packedPosition) >> 1)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tencode((packedPosition - 16) >> 1)\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn\n/*\t\t\t\t\t\t} else if (packedStatus.serializationId != serializationId) {\n\t\t\t\t\t\t\tpackedStatus.serializationId = serializationId\n\t\t\t\t\t\t\tpackedStatus.count = 1\n\t\t\t\t\t\t\tif (options.sharedPack) {\n\t\t\t\t\t\t\t\tlet sharedCount = packedStatus.sharedCount = (packedStatus.sharedCount || 0) + 1\n\t\t\t\t\t\t\t\tif (shareCount > (options.sharedPack.threshold || 5)) {\n\t\t\t\t\t\t\t\t\tlet sharedPosition = packedStatus.position = packedStatus.nextSharedPosition\n\t\t\t\t\t\t\t\t\thasSharedUpdate = true\n\t\t\t\t\t\t\t\t\tif (sharedPosition < 16)\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = sharedPosition + 0xc0\n\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} // else any in-doc incrementation?*/\n\t\t\t\t\t} else if (samplingPackedValues && !options.pack) {\n\t\t\t\t\t\tlet status = samplingPackedValues.get(value)\n\t\t\t\t\t\tif (status)\n\t\t\t\t\t\t\tstatus.count++\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\tsamplingPackedValues.set(value, {\n\t\t\t\t\t\t\t\tcount: 1,\n\t\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet strLength = value.length\n\t\t\t\tif (bundledStrings && strLength >= 4 && strLength < 0x400) {\n\t\t\t\t\tif ((bundledStrings.size += strLength) > MAX_BUNDLE_SIZE) {\n\t\t\t\t\t\tlet extStart\n\t\t\t\t\t\tlet maxBytes = (bundledStrings[0] ? bundledStrings[0].length * 3 + bundledStrings[1].length : 0) + 10\n\t\t\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\t\t\t\t\t\ttarget[position++] = 0xd9 // tag 16-bit\n\t\t\t\t\t\ttarget[position++] = 0xdf // tag 0xdff9\n\t\t\t\t\t\ttarget[position++] = 0xf9\n\t\t\t\t\t\t// TODO: If we only have one bundle with any string data, only write one string bundle\n\t\t\t\t\t\ttarget[position++] = bundledStrings.position ? 0x84 : 0x82 // array of 4 or 2 elements depending on if we write bundles\n\t\t\t\t\t\ttarget[position++] = 0x1a // 32-bit unsigned int\n\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\tif (bundledStrings.position) {\n\t\t\t\t\t\t\twriteBundles(start, encode) // write the last bundles\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbundledStrings = ['', ''] // create new ones\n\t\t\t\t\t\tbundledStrings.size = 0\n\t\t\t\t\t\tbundledStrings.position = extStart\n\t\t\t\t\t}\n\t\t\t\t\tlet twoByte = hasNonLatin.test(value)\n\t\t\t\t\tbundledStrings[twoByte ? 0 : 1] += value\n\t\t\t\t\ttarget[position++] = twoByte ? 0xce : 0xcf\n\t\t\t\t\tencode(strLength);\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet headerSize\n\t\t\t\t// first we estimate the header size, so we can write to the correct location\n\t\t\t\tif (strLength < 0x20) {\n\t\t\t\t\theaderSize = 1\n\t\t\t\t} else if (strLength < 0x100) {\n\t\t\t\t\theaderSize = 2\n\t\t\t\t} else if (strLength < 0x10000) {\n\t\t\t\t\theaderSize = 3\n\t\t\t\t} else {\n\t\t\t\t\theaderSize = 5\n\t\t\t\t}\n\t\t\t\tlet maxBytes = strLength * 3\n\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\n\t\t\t\tif (strLength < 0x40 || !encodeUtf8) {\n\t\t\t\t\tlet i, c1, c2, strPosition = position + headerSize\n\t\t\t\t\tfor (i = 0; i < strLength; i++) {\n\t\t\t\t\t\tc1 = value.charCodeAt(i)\n\t\t\t\t\t\tif (c1 < 0x80) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1\n\t\t\t\t\t\t} else if (c1 < 0x800) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 | 0xc0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t(c1 & 0xfc00) === 0xd800 &&\n\t\t\t\t\t\t\t((c2 = value.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tc1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff)\n\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 18 | 0xf0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 | 0xe0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlength = strPosition - position - headerSize\n\t\t\t\t} else {\n\t\t\t\t\tlength = encodeUtf8(value, position + headerSize, maxBytes)\n\t\t\t\t}\n\n\t\t\t\tif (length < 0x18) {\n\t\t\t\t\ttarget[position++] = 0x60 | length\n\t\t\t\t} else if (length < 0x100) {\n\t\t\t\t\tif (headerSize < 2) {\n\t\t\t\t\t\ttarget.copyWithin(position + 2, position + 1, position + 1 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0x78\n\t\t\t\t\ttarget[position++] = length\n\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\tif (headerSize < 3) {\n\t\t\t\t\t\ttarget.copyWithin(position + 3, position + 2, position + 2 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0x79\n\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t} else {\n\t\t\t\t\tif (headerSize < 5) {\n\t\t\t\t\t\ttarget.copyWithin(position + 5, position + 3, position + 3 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0x7a\n\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\tposition += 4\n\t\t\t\t}\n\t\t\t\tposition += length\n\t\t\t} else if (type === 'number') {\n\t\t\t\tif (!this.alwaysUseFloat && value >>> 0 === value) {// positive integer, 32-bit or less\n\t\t\t\t\t// positive uint\n\t\t\t\t\tif (value < 0x18) {\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x100) {\n\t\t\t\t\t\ttarget[position++] = 0x18\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x10000) {\n\t\t\t\t\t\ttarget[position++] = 0x19\n\t\t\t\t\t\ttarget[position++] = value >> 8\n\t\t\t\t\t\ttarget[position++] = value & 0xff\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0x1a\n\t\t\t\t\t\ttargetView.setUint32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else if (!this.alwaysUseFloat && value >> 0 === value) { // negative integer\n\t\t\t\t\tif (value >= -0x18) {\n\t\t\t\t\t\ttarget[position++] = 0x1f - value\n\t\t\t\t\t} else if (value >= -0x100) {\n\t\t\t\t\t\ttarget[position++] = 0x38\n\t\t\t\t\t\ttarget[position++] = ~value\n\t\t\t\t\t} else if (value >= -0x10000) {\n\t\t\t\t\t\ttarget[position++] = 0x39\n\t\t\t\t\t\ttargetView.setUint16(position, ~value)\n\t\t\t\t\t\tposition += 2\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0x3a\n\t\t\t\t\t\ttargetView.setUint32(position, ~value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet useFloat32\n\t\t\t\t\tif ((useFloat32 = this.useFloat32) > 0 && value < 0x100000000 && value >= -0x80000000) {\n\t\t\t\t\t\ttarget[position++] = 0xfa\n\t\t\t\t\t\ttargetView.setFloat32(position, value)\n\t\t\t\t\t\tlet xShifted\n\t\t\t\t\t\tif (useFloat32 < 4 ||\n\t\t\t\t\t\t\t\t// this checks for rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\t\t((xShifted = value * mult10[((target[position] & 0x7f) << 1) | (target[position + 1] >> 7)]) >> 0) === xShifted) {\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\tposition-- // move back into position for writing a double\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xfb\n\t\t\t\t\ttargetView.setFloat64(position, value)\n\t\t\t\t\tposition += 8\n\t\t\t\t}\n\t\t\t} else if (type === 'object') {\n\t\t\t\tif (!value)\n\t\t\t\t\ttarget[position++] = 0xf6\n\t\t\t\telse {\n\t\t\t\t\tif (referenceMap) {\n\t\t\t\t\t\tlet referee = referenceMap.get(value)\n\t\t\t\t\t\tif (referee) {\n\t\t\t\t\t\t\ttarget[position++] = 0xd8\n\t\t\t\t\t\t\ttarget[position++] = 29 // http://cbor.schmorp.de/value-sharing\n\t\t\t\t\t\t\ttarget[position++] = 0x19 // 16-bit uint\n\t\t\t\t\t\t\tif (!referee.references) {\n\t\t\t\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert || (referenceMap.idsToInsert = [])\n\t\t\t\t\t\t\t\treferee.references = []\n\t\t\t\t\t\t\t\tidsToInsert.push(referee)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treferee.references.push(position - start)\n\t\t\t\t\t\t\tposition += 2 // TODO: also support 32-bit\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else \n\t\t\t\t\t\t\treferenceMap.set(value, { offset: position - start })\n\t\t\t\t\t}\n\t\t\t\t\tlet constructor = value.constructor\n\t\t\t\t\tif (constructor === Object) {\n\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t} else if (constructor === Array) {\n\t\t\t\t\t\tlength = value.length\n\t\t\t\t\t\tif (length < 0x18) {\n\t\t\t\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\twriteArrayHeader(length)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\t\t\tencode(value[i])\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (constructor === Map) {\n\t\t\t\t\t\tif (this.mapsAsObjects ? this.useTag259ForMaps !== false : this.useTag259ForMaps) {\n\t\t\t\t\t\t\t// use Tag 259 (https://github.com/shanewholloway/js-cbor-codec/blob/master/docs/CBOR-259-spec--explicit-maps.md) for maps if the user wants it that way\n\t\t\t\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\t\t\t\ttarget[position++] = 1\n\t\t\t\t\t\t\ttarget[position++] = 3\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlength = value.size\n\t\t\t\t\t\tif (length < 0x18) {\n\t\t\t\t\t\t\ttarget[position++] = 0xa0 | length\n\t\t\t\t\t\t} else if (length < 0x100) {\n\t\t\t\t\t\t\ttarget[position++] = 0xb8\n\t\t\t\t\t\t\ttarget[position++] = length\n\t\t\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\t\t\ttarget[position++] = 0xb9\n\t\t\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttarget[position++] = 0xba\n\t\t\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (encoder.keyMap) { \n\t\t\t\t\t\t\tfor (let [ key, entryValue ] of value) {\n\t\t\t\t\t\t\t\tencode(encoder.encodeKey(key))\n\t\t\t\t\t\t\t\tencode(entryValue)\n\t\t\t\t\t\t\t} \n\t\t\t\t\t\t} else { \n\t\t\t\t\t\t\tfor (let [ key, entryValue ] of value) {\n\t\t\t\t\t\t\t\tencode(key) \n\t\t\t\t\t\t\t\tencode(entryValue)\n\t\t\t\t\t\t\t} \t\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfor (let i = 0, l = extensions.length; i < l; i++) {\n\t\t\t\t\t\t\tlet extensionClass = extensionClasses[i]\n\t\t\t\t\t\t\tif (value instanceof extensionClass) {\n\t\t\t\t\t\t\t\tlet extension = extensions[i]\n\t\t\t\t\t\t\t\tlet tag = extension.tag\n\t\t\t\t\t\t\t\tif (tag == undefined)\n\t\t\t\t\t\t\t\t\ttag = extension.getTag && extension.getTag.call(this, value)\n\t\t\t\t\t\t\t\tif (tag < 0x18) {\n\t\t\t\t\t\t\t\t\ttarget[position++] = 0xc0 | tag\n\t\t\t\t\t\t\t\t} else if (tag < 0x100) {\n\t\t\t\t\t\t\t\t\ttarget[position++] = 0xd8\n\t\t\t\t\t\t\t\t\ttarget[position++] = tag\n\t\t\t\t\t\t\t\t} else if (tag < 0x10000) {\n\t\t\t\t\t\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\t\t\t\t\t\ttarget[position++] = tag >> 8\n\t\t\t\t\t\t\t\t\ttarget[position++] = tag & 0xff\n\t\t\t\t\t\t\t\t} else if (tag > -1) {\n\t\t\t\t\t\t\t\t\ttarget[position++] = 0xda\n\t\t\t\t\t\t\t\t\ttargetView.setUint32(position, tag)\n\t\t\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\t\t} // else undefined, don't write tag\n\t\t\t\t\t\t\t\textension.encode.call(this, value, encode, makeRoom)\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (value[Symbol.iterator]) {\n\t\t\t\t\t\t\tif (throwOnIterable) {\n\t\t\t\t\t\t\t\tlet error = new Error('Iterable should be serialized as iterator')\n\t\t\t\t\t\t\t\terror.iteratorNotHandled = true;\n\t\t\t\t\t\t\t\tthrow error;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttarget[position++] = 0x9f // indefinite length array\n\t\t\t\t\t\t\tfor (let entry of value) {\n\t\t\t\t\t\t\t\tencode(entry)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttarget[position++] = 0xff // stop-code\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (value[Symbol.asyncIterator] || isBlob(value)) {\n\t\t\t\t\t\t\tlet error = new Error('Iterable/blob should be serialized as iterator')\n\t\t\t\t\t\t\terror.iteratorNotHandled = true;\n\t\t\t\t\t\t\tthrow error;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (this.useToJSON && value.toJSON) {\n\t\t\t\t\t\t\tconst json = value.toJSON()\n\t\t\t\t\t\t\t// if for some reason value.toJSON returns itself it'll loop forever\n\t\t\t\t\t\t\tif (json !== value)\n\t\t\t\t\t\t\t\treturn encode(json)\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// no extension found, write as a plain object\n\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (type === 'boolean') {\n\t\t\t\ttarget[position++] = value ? 0xf5 : 0xf4\n\t\t\t} else if (type === 'bigint') {\n\t\t\t\tif (value < (BigInt(1)<<BigInt(64)) && value >= 0) {\n\t\t\t\t\t// use an unsigned int as long as it fits\n\t\t\t\t\ttarget[position++] = 0x1b\n\t\t\t\t\ttargetView.setBigUint64(position, value)\n\t\t\t\t} else if (value > -(BigInt(1)<<BigInt(64)) && value < 0) {\n\t\t\t\t\t// if we can fit an unsigned int, use that\n\t\t\t\t\ttarget[position++] = 0x3b\n\t\t\t\t\ttargetView.setBigUint64(position, -value - BigInt(1))\n\t\t\t\t} else {\n\t\t\t\t\t// overflow\n\t\t\t\t\tif (this.largeBigIntToFloat) {\n\t\t\t\t\t\ttarget[position++] = 0xfb\n\t\t\t\t\t\ttargetView.setFloat64(position, Number(value))\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (value >= BigInt(0))\n\t\t\t\t\t\t\ttarget[position++] = 0xc2 // tag 2\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\ttarget[position++] = 0xc3 // tag 2\n\t\t\t\t\t\t\tvalue = BigInt(-1) - value;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlet bytes = [];\n\t\t\t\t\t\twhile (value) {\n\t\t\t\t\t\t\tbytes.push(Number(value & BigInt(0xff)));\n\t\t\t\t\t\t\tvalue >>= BigInt(8);\n\t\t\t\t\t\t}\n\t\t\t\t\t\twriteBuffer(new Uint8Array(bytes.reverse()), makeRoom);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tposition += 8\n\t\t\t} else if (type === 'undefined') {\n\t\t\t\ttarget[position++] = 0xf7\n\t\t\t} else {\n\t\t\t\tthrow new Error('Unknown type: ' + type)\n\t\t\t}\n\t\t}\n\n\t\tconst writeObject = this.useRecords === false ? this.variableMapSize ? (object) => {\n\t\t\t// this method is slightly slower, but generates \"preferred serialization\" (optimally small for smaller objects)\n\t\t\tlet keys = Object.keys(object)\n\t\t\tlet vals = Object.values(object)\n\t\t\tlet length = keys.length\n\t\t\tif (length < 0x18) {\n\t\t\t\ttarget[position++] = 0xa0 | length\n\t\t\t} else if (length < 0x100) {\n\t\t\t\ttarget[position++] = 0xb8\n\t\t\t\ttarget[position++] = length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xb9\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xba\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tlet key\n\t\t\tif (encoder.keyMap) { \n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tencode(encoder.encodeKey(keys[i]))\n\t\t\t\t\tencode(vals[i])\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tencode(keys[i])\n\t\t\t\t\tencode(vals[i])\n\t\t\t\t}\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\ttarget[position++] = 0xb9 // always use map 16, so we can preallocate and set the length afterwards\n\t\t\tlet objectOffset = position - start\n\t\t\tposition += 2\n\t\t\tlet size = 0\n\t\t\tif (encoder.keyMap) {\n\t\t\t\tfor (let key in object) if (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tencode(encoder.encodeKey(key))\n\t\t\t\t\tencode(object[key])\n\t\t\t\t\tsize++\n\t\t\t\t}\n\t\t\t} else { \n\t\t\t\tfor (let key in object) if (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\t\tencode(key)\n\t\t\t\t\t\tencode(object[key])\n\t\t\t\t\tsize++\n\t\t\t\t}\n\t\t\t}\n\t\t\ttarget[objectOffset++ + start] = size >> 8\n\t\t\ttarget[objectOffset + start] = size & 0xff\n\t\t} :\n\t\t(object, skipValues) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet newTransitions = 0\n\t\t\tlet length = 0\n\t\t\tlet parentRecordId\n\t\t\tlet keys\n\t\t\tif (this.keyMap) {\n\t\t\t\tkeys = Object.keys(object).map(k => this.encodeKey(k))\n\t\t\t\tlength = keys.length\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tlet key = keys[i]\n\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\tnewTransitions++\n\t\t\t\t\t}\n\t\t\t\t\ttransition = nextTransition\n\t\t\t\t}\t\t\t\t\n\t\t\t} else {\n\t\t\t\tfor (let key in object) if (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\tif (transition[RECORD_SYMBOL] & 0x100000) {// this indicates it is a brancheable/extendable terminal node, so we will use this record id and extend it\n\t\t\t\t\t\t\tparentRecordId = transition[RECORD_SYMBOL] & 0xffff\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\tnewTransitions++\n\t\t\t\t\t}\n\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\tlength++\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\tif (recordId !== undefined) {\n\t\t\t\trecordId &= 0xffff\n\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\ttarget[position++] = (recordId >> 8) | 0xe0\n\t\t\t\ttarget[position++] = recordId & 0xff\n\t\t\t} else {\n\t\t\t\tif (!keys)\n\t\t\t\t\tkeys = transition.__keys__ || (transition.__keys__ = Object.keys(object))\n\t\t\t\tif (parentRecordId === undefined) {\n\t\t\t\t\trecordId = structures.nextId++\n\t\t\t\t\tif (!recordId) {\n\t\t\t\t\t\trecordId = 0\n\t\t\t\t\t\tstructures.nextId = 1\n\t\t\t\t\t}\n\t\t\t\t\tif (recordId >= MAX_STRUCTURES) {// cycle back around\n\t\t\t\t\t\tstructures.nextId = (recordId = maxSharedStructures) + 1\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\trecordId = parentRecordId\n\t\t\t\t}\n\t\t\t\tstructures[recordId] = keys\n\t\t\t\tif (recordId < maxSharedStructures) {\n\t\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\t\ttarget[position++] = (recordId >> 8) | 0xe0\n\t\t\t\t\ttarget[position++] = recordId & 0xff\n\t\t\t\t\ttransition = structures.transitions\n\t\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\t\tif (transition[RECORD_SYMBOL] === undefined || (transition[RECORD_SYMBOL] & 0x100000))\n\t\t\t\t\t\t\ttransition[RECORD_SYMBOL] = recordId\n\t\t\t\t\t\ttransition = transition[keys[i]]\n\t\t\t\t\t}\n\t\t\t\t\ttransition[RECORD_SYMBOL] = recordId | 0x100000 // indicates it is a extendable terminal\n\t\t\t\t\thasSharedUpdate = true\n\t\t\t\t} else {\n\t\t\t\t\ttransition[RECORD_SYMBOL] = recordId\n\t\t\t\t\ttargetView.setUint32(position, 0xd9dfff00) // tag two byte, then record definition id\n\t\t\t\t\tposition += 3\n\t\t\t\t\tif (newTransitions)\n\t\t\t\t\t\ttransitionsCount += serializationsSinceTransitionRebuild * newTransitions\n\t\t\t\t\t// record the removal of the id, we can maintain our shared structure\n\t\t\t\t\tif (recordIdsToRemove.length >= MAX_STRUCTURES - maxSharedStructures)\n\t\t\t\t\t\trecordIdsToRemove.shift()[RECORD_SYMBOL] = undefined // we are cycling back through, and have to remove old ones\n\t\t\t\t\trecordIdsToRemove.push(transition)\n\t\t\t\t\twriteArrayHeader(length + 2)\n\t\t\t\t\tencode(0xe000 + recordId)\n\t\t\t\t\tencode(keys)\n\t\t\t\t\tif (skipValues) return; // special exit for iterator\n\t\t\t\t\tfor (let key in object)\n\t\t\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key))\n\t\t\t\t\t\t\tencode(object[key])\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (length < 0x18) { // write the array header\n\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t} else {\n\t\t\t\twriteArrayHeader(length)\n\t\t\t}\n\t\t\tif (skipValues) return; // special exit for iterator\n\t\t\tfor (let key in object)\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key))\n\t\t\t\t\tencode(object[key])\n\t\t}\n\t\tconst makeRoom = (end) => {\n\t\t\tlet newSize\n\t\t\tif (end > 0x1000000) {\n\t\t\t\t// special handling for really large buffers\n\t\t\t\tif ((end - start) > MAX_BUFFER_SIZE)\n\t\t\t\t\tthrow new Error('Encoded buffer would be larger than maximum buffer size')\n\t\t\t\tnewSize = Math.min(MAX_BUFFER_SIZE,\n\t\t\t\t\tMath.round(Math.max((end - start) * (end > 0x4000000 ? 1.25 : 2), 0x400000) / 0x1000) * 0x1000)\n\t\t\t} else // faster handling for smaller buffers\n\t\t\t\tnewSize = ((Math.max((end - start) << 2, target.length - 1) >> 12) + 1) << 12\n\t\t\tlet newBuffer = new ByteArrayAllocate(newSize)\n\t\t\ttargetView = new DataView(newBuffer.buffer, 0, newSize)\n\t\t\tif (target.copy)\n\t\t\t\ttarget.copy(newBuffer, 0, start, end)\n\t\t\telse\n\t\t\t\tnewBuffer.set(target.slice(start, end))\n\t\t\tposition -= start\n\t\t\tstart = 0\n\t\t\tsafeEnd = newBuffer.length - 10\n\t\t\treturn target = newBuffer\n\t\t}\n\t\tlet chunkThreshold = 100;\n\t\tlet continuedChunkThreshold = 1000;\n\t\tthis.encodeAsIterable = function(value, options) {\n\t\t\treturn startEncoding(value, options, encodeObjectAsIterable);\n\t\t}\n\t\tthis.encodeAsAsyncIterable = function(value, options) {\n\t\t\treturn startEncoding(value, options, encodeObjectAsAsyncIterable);\n\t\t}\n\n\t\tfunction* encodeObjectAsIterable(object, iterateProperties, finalIterable) {\n\t\t\tlet constructor = object.constructor;\n\t\t\tif (constructor === Object) {\n\t\t\t\tlet useRecords = encoder.useRecords !== false;\n\t\t\t\tif (useRecords)\n\t\t\t\t\twriteObject(object, true); // write the record identifier\n\t\t\t\telse\n\t\t\t\t\twriteEntityLength(Object.keys(object).length, 0xa0);\n\t\t\t\tfor (let key in object) {\n\t\t\t\t\tlet value = object[key];\n\t\t\t\t\tif (!useRecords) encode(key);\n\t\t\t\t\tif (value && typeof value === 'object') {\n\t\t\t\t\t\tif (iterateProperties[key])\n\t\t\t\t\t\t\tyield* encodeObjectAsIterable(value, iterateProperties[key]);\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\tyield* tryEncode(value, iterateProperties, key);\n\t\t\t\t\t} else encode(value);\n\t\t\t\t}\n\t\t\t} else if (constructor === Array) {\n\t\t\t\tlet length = object.length;\n\t\t\t\twriteArrayHeader(length);\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tlet value = object[i];\n\t\t\t\t\tif (value && (typeof value === 'object' || position - start > chunkThreshold)) {\n\t\t\t\t\t\tif (iterateProperties.element)\n\t\t\t\t\t\t\tyield* encodeObjectAsIterable(value, iterateProperties.element);\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\tyield* tryEncode(value, iterateProperties, 'element');\n\t\t\t\t\t} else encode(value);\n\t\t\t\t}\n\t\t\t} else if (object[Symbol.iterator] && !object.buffer) { // iterator, but exclude typed arrays\n\t\t\t\ttarget[position++] = 0x9f; // start indefinite array\n\t\t\t\tfor (let value of object) {\n\t\t\t\t\tif (value && (typeof value === 'object' || position - start > chunkThreshold)) {\n\t\t\t\t\t\tif (iterateProperties.element)\n\t\t\t\t\t\t\tyield* encodeObjectAsIterable(value, iterateProperties.element);\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\tyield* tryEncode(value, iterateProperties, 'element');\n\t\t\t\t\t} else encode(value);\n\t\t\t\t}\n\t\t\t\ttarget[position++] = 0xff; // stop byte\n\t\t\t} else if (isBlob(object)){\n\t\t\t\twriteEntityLength(object.size, 0x40); // encode as binary data\n\t\t\t\tyield target.subarray(start, position);\n\t\t\t\tyield object; // directly return blobs, they have to be encoded asynchronously\n\t\t\t\trestartEncoding();\n\t\t\t} else if (object[Symbol.asyncIterator]) {\n\t\t\t\ttarget[position++] = 0x9f; // start indefinite array\n\t\t\t\tyield target.subarray(start, position);\n\t\t\t\tyield object; // directly return async iterators, they have to be encoded asynchronously\n\t\t\t\trestartEncoding();\n\t\t\t\ttarget[position++] = 0xff; // stop byte\n\t\t\t} else {\n\t\t\t\tencode(object);\n\t\t\t}\n\t\t\tif (finalIterable && position > start) yield target.subarray(start, position);\n\t\t\telse if (position - start > chunkThreshold) {\n\t\t\t\tyield target.subarray(start, position);\n\t\t\t\trestartEncoding();\n\t\t\t}\n\t\t}\n\t\tfunction* tryEncode(value, iterateProperties, key) {\n\t\t\tlet restart = position - start;\n\t\t\ttry {\n\t\t\t\tencode(value);\n\t\t\t\tif (position - start > chunkThreshold) {\n\t\t\t\t\tyield target.subarray(start, position);\n\t\t\t\t\trestartEncoding();\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (error.iteratorNotHandled) {\n\t\t\t\t\titerateProperties[key] = {};\n\t\t\t\t\tposition = start + restart; // restart our position so we don't have partial data from last encode\n\t\t\t\t\tyield* encodeObjectAsIterable.call(this, value, iterateProperties[key]);\n\t\t\t\t} else throw error;\n\t\t\t}\n\t\t}\n\t\tfunction restartEncoding() {\n\t\t\tchunkThreshold = continuedChunkThreshold;\n\t\t\tencoder.encode(null, THROW_ON_ITERABLE); // restart encoding\n\t\t}\n\t\tfunction startEncoding(value, options, encodeIterable) {\n\t\t\tif (options && options.chunkThreshold) // explicitly specified chunk sizes\n\t\t\t\tchunkThreshold = continuedChunkThreshold = options.chunkThreshold;\n\t\t\telse // we start with a smaller threshold to get initial bytes sent quickly\n\t\t\t\tchunkThreshold = 100;\n\t\t\tif (value && typeof value === 'object') {\n\t\t\t\tencoder.encode(null, THROW_ON_ITERABLE); // start encoding\n\t\t\t\treturn encodeIterable(value, encoder.iterateProperties || (encoder.iterateProperties = {}), true);\n\t\t\t}\n\t\t\treturn [encoder.encode(value)];\n\t\t}\n\n\t\tasync function* encodeObjectAsAsyncIterable(value, iterateProperties) {\n\t\t\tfor (let encodedValue of encodeObjectAsIterable(value, iterateProperties, true)) {\n\t\t\t\tlet constructor = encodedValue.constructor;\n\t\t\t\tif (constructor === ByteArray || constructor === Uint8Array)\n\t\t\t\t\tyield encodedValue;\n\t\t\t\telse if (isBlob(encodedValue)) {\n\t\t\t\t\tlet reader = encodedValue.stream().getReader();\n\t\t\t\t\tlet next;\n\t\t\t\t\twhile (!(next = await reader.read()).done) {\n\t\t\t\t\t\tyield next.value;\n\t\t\t\t\t}\n\t\t\t\t} else if (encodedValue[Symbol.asyncIterator]) {\n\t\t\t\t\tfor await (let asyncValue of encodedValue) {\n\t\t\t\t\t\trestartEncoding();\n\t\t\t\t\t\tif (asyncValue)\n\t\t\t\t\t\t\tyield* encodeObjectAsAsyncIterable(asyncValue, iterateProperties.async || (iterateProperties.async = {}));\n\t\t\t\t\t\telse yield encoder.encode(asyncValue);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tyield encodedValue;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tuseBuffer(buffer) {\n\t\t// this means we are finished using our own buffer and we can write over it safely\n\t\ttarget = buffer\n\t\ttargetView = new DataView(target.buffer, target.byteOffset, target.byteLength)\n\t\tposition = 0\n\t}\n\tclearSharedData() {\n\t\tif (this.structures)\n\t\t\tthis.structures = []\n\t\tif (this.sharedValues)\n\t\t\tthis.sharedValues = undefined\n\t}\n\tupdateSharedData() {\n\t\tlet lastVersion = this.sharedVersion || 0\n\t\tthis.sharedVersion = lastVersion + 1\n\t\tlet structuresCopy = this.structures.slice(0)\n\t\tlet sharedData = new SharedData(structuresCopy, this.sharedValues, this.sharedVersion)\n\t\tlet saveResults = this.saveShared(sharedData,\n\t\t\t\texistingShared => (existingShared && existingShared.version || 0) == lastVersion)\n\t\tif (saveResults === false) {\n\t\t\t// get updated structures and try again if the update failed\n\t\t\tsharedData = this.getShared() || {}\n\t\t\tthis.structures = sharedData.structures || []\n\t\t\tthis.sharedValues = sharedData.packedValues\n\t\t\tthis.sharedVersion = sharedData.version\n\t\t\tthis.structures.nextId = this.structures.length\n\t\t} else {\n\t\t\t// restore structures\n\t\t\tstructuresCopy.forEach((structure, i) => this.structures[i] = structure)\n\t\t}\n\t\t// saveShared may fail to write and reload, or may have reloaded to check compatibility and overwrite saved data, either way load the correct shared data\n\t\treturn saveResults\n\t}\n}\nfunction writeEntityLength(length, majorValue) {\n\tif (length < 0x18)\n\t\ttarget[position++] = majorValue | length\n\telse if (length < 0x100) {\n\t\ttarget[position++] = majorValue | 0x18\n\t\ttarget[position++] = length\n\t} else if (length < 0x10000) {\n\t\ttarget[position++] = majorValue | 0x19\n\t\ttarget[position++] = length >> 8\n\t\ttarget[position++] = length & 0xff\n\t} else {\n\t\ttarget[position++] = majorValue | 0x1a\n\t\ttargetView.setUint32(position, length)\n\t\tposition += 4\n\t}\n\n}\nclass SharedData {\n\tconstructor(structures, values, version) {\n\t\tthis.structures = structures\n\t\tthis.packedValues = values\n\t\tthis.version = version\n\t}\n}\n\nfunction writeArrayHeader(length) {\n\tif (length < 0x18)\n\t\ttarget[position++] = 0x80 | length\n\telse if (length < 0x100) {\n\t\ttarget[position++] = 0x98\n\t\ttarget[position++] = length\n\t} else if (length < 0x10000) {\n\t\ttarget[position++] = 0x99\n\t\ttarget[position++] = length >> 8\n\t\ttarget[position++] = length & 0xff\n\t} else {\n\t\ttarget[position++] = 0x9a\n\t\ttargetView.setUint32(position, length)\n\t\tposition += 4\n\t}\n}\n\nconst BlobConstructor = typeof Blob === 'undefined' ? function(){} : Blob;\nfunction isBlob(object) {\n\tif (object instanceof BlobConstructor)\n\t\treturn true;\n\tlet tag = object[Symbol.toStringTag];\n\treturn tag === 'Blob' || tag === 'File';\n}\nfunction findRepetitiveStrings(value, packedValues) {\n\tswitch(typeof value) {\n\t\tcase 'string':\n\t\t\tif (value.length > 3) {\n\t\t\t\tif (packedValues.objectMap[value] > -1 || packedValues.values.length >= packedValues.maxValues)\n\t\t\t\t\treturn\n\t\t\t\tlet packedStatus = packedValues.get(value)\n\t\t\t\tif (packedStatus) {\n\t\t\t\t\tif (++packedStatus.count == 2) {\n\t\t\t\t\t\tpackedValues.values.push(value)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tpackedValues.set(value, {\n\t\t\t\t\t\tcount: 1,\n\t\t\t\t\t})\n\t\t\t\t\tif (packedValues.samplingPackedValues) {\n\t\t\t\t\t\tlet status = packedValues.samplingPackedValues.get(value)\n\t\t\t\t\t\tif (status)\n\t\t\t\t\t\t\tstatus.count++\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\tpackedValues.samplingPackedValues.set(value, {\n\t\t\t\t\t\t\t\tcount: 1,\n\t\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tbreak\n\t\tcase 'object':\n\t\t\tif (value) {\n\t\t\t\tif (value instanceof Array) {\n\t\t\t\t\tfor (let i = 0, l = value.length; i < l; i++) {\n\t\t\t\t\t\tfindRepetitiveStrings(value[i], packedValues)\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\t\t\t\t\tlet includeKeys = !packedValues.encoder.useRecords\n\t\t\t\t\tfor (var key in value) {\n\t\t\t\t\t\tif (value.hasOwnProperty(key)) {\n\t\t\t\t\t\t\tif (includeKeys)\n\t\t\t\t\t\t\t\tfindRepetitiveStrings(key, packedValues)\n\t\t\t\t\t\t\tfindRepetitiveStrings(value[key], packedValues)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tbreak\n\t\tcase 'function': console.log(value)\n\t}\n}\nconst isLittleEndianMachine = new Uint8Array(new Uint16Array([1]).buffer)[0] == 1\nextensionClasses = [ Date, Set, Error, RegExp, Tag, ArrayBuffer,\n\tUint8Array, Uint8ClampedArray, Uint16Array, Uint32Array,\n\ttypeof BigUint64Array == 'undefined' ? function() {} : BigUint64Array, Int8Array, Int16Array, Int32Array,\n\ttypeof BigInt64Array == 'undefined' ? function() {} : BigInt64Array,\n\tFloat32Array, Float64Array, SharedData ]\n\n//Object.getPrototypeOf(Uint8Array.prototype).constructor /*TypedArray*/\nextensions = [{ // Date\n\ttag: 1,\n\tencode(date, encode) {\n\t\tlet seconds = date.getTime() / 1000\n\t\tif ((this.useTimestamp32 || date.getMilliseconds() === 0) && seconds >= 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 32\n\t\t\ttarget[position++] = 0x1a\n\t\t\ttargetView.setUint32(position, seconds)\n\t\t\tposition += 4\n\t\t} else {\n\t\t\t// Timestamp float64\n\t\t\ttarget[position++] = 0xfb\n\t\t\ttargetView.setFloat64(position, seconds)\n\t\t\tposition += 8\n\t\t}\n\t}\n}, { // Set\n\ttag: 258, // https://github.com/input-output-hk/cbor-sets-spec/blob/master/CBOR_SETS.md\n\tencode(set, encode) {\n\t\tlet array = Array.from(set)\n\t\tencode(array)\n\t}\n}, { // Error\n\ttag: 27, // http://cbor.schmorp.de/generic-object\n\tencode(error, encode) {\n\t\tencode([ error.name, error.message ])\n\t}\n}, { // RegExp\n\ttag: 27, // http://cbor.schmorp.de/generic-object\n\tencode(regex, encode) {\n\t\tencode([ 'RegExp', regex.source, regex.flags ])\n\t}\n}, { // Tag\n\tgetTag(tag) {\n\t\treturn tag.tag\n\t},\n\tencode(tag, encode) {\n\t\tencode(tag.value)\n\t}\n}, { // ArrayBuffer\n\tencode(arrayBuffer, encode, makeRoom) {\n\t\twriteBuffer(arrayBuffer, makeRoom)\n\t}\n}, { // Uint8Array\n\tgetTag(typedArray) {\n\t\tif (typedArray.constructor === Uint8Array) {\n\t\t\tif (this.tagUint8Array || hasNodeBuffer && this.tagUint8Array !== false)\n\t\t\t\treturn 64;\n\t\t} // else no tag\n\t},\n\tencode(typedArray, encode, makeRoom) {\n\t\twriteBuffer(typedArray, makeRoom)\n\t}\n},\n\ttypedArrayEncoder(68, 1),\n\ttypedArrayEncoder(69, 2),\n\ttypedArrayEncoder(70, 4),\n\ttypedArrayEncoder(71, 8),\n\ttypedArrayEncoder(72, 1),\n\ttypedArrayEncoder(77, 2),\n\ttypedArrayEncoder(78, 4),\n\ttypedArrayEncoder(79, 8),\n\ttypedArrayEncoder(85, 4),\n\ttypedArrayEncoder(86, 8),\n{\n\tencode(sharedData, encode) { // write SharedData\n\t\tlet packedValues = sharedData.packedValues || []\n\t\tlet sharedStructures = sharedData.structures || []\n\t\tif (packedValues.values.length > 0) {\n\t\t\ttarget[position++] = 0xd8 // one-byte tag\n\t\t\ttarget[position++] = 51 // tag 51 for packed shared structures https://www.potaroo.net/ietf/ids/draft-ietf-cbor-packed-03.txt\n\t\t\twriteArrayHeader(4)\n\t\t\tlet valuesArray = packedValues.values\n\t\t\tencode(valuesArray)\n\t\t\twriteArrayHeader(0) // prefixes\n\t\t\twriteArrayHeader(0) // suffixes\n\t\t\tpackedObjectMap = Object.create(sharedPackedObjectMap || null)\n\t\t\tfor (let i = 0, l = valuesArray.length; i < l; i++) {\n\t\t\t\tpackedObjectMap[valuesArray[i]] = i\n\t\t\t}\n\t\t}\n\t\tif (sharedStructures) {\n\t\t\ttargetView.setUint32(position, 0xd9dffe00)\n\t\t\tposition += 3\n\t\t\tlet definitions = sharedStructures.slice(0)\n\t\t\tdefinitions.unshift(0xe000)\n\t\t\tdefinitions.push(new Tag(sharedData.version, 0x53687264))\n\t\t\tencode(definitions)\n\t\t} else\n\t\t\tencode(new Tag(sharedData.version, 0x53687264))\n\t\t}\n\t}]\nfunction typedArrayEncoder(tag, size) {\n\tif (!isLittleEndianMachine && size > 1)\n\t\ttag -= 4 // the big endian equivalents are 4 less\n\treturn {\n\t\ttag: tag,\n\t\tencode: function writeExtBuffer(typedArray, encode) {\n\t\t\tlet length = typedArray.byteLength\n\t\t\tlet offset = typedArray.byteOffset || 0\n\t\t\tlet buffer = typedArray.buffer || typedArray\n\t\t\tencode(hasNodeBuffer ? Buffer.from(buffer, offset, length) :\n\t\t\t\tnew Uint8Array(buffer, offset, length))\n\t\t}\n\t}\n}\nfunction writeBuffer(buffer, makeRoom) {\n\tlet length = buffer.byteLength\n\tif (length < 0x18) {\n\t\ttarget[position++] = 0x40 + length\n\t} else if (length < 0x100) {\n\t\ttarget[position++] = 0x58\n\t\ttarget[position++] = length\n\t} else if (length < 0x10000) {\n\t\ttarget[position++] = 0x59\n\t\ttarget[position++] = length >> 8\n\t\ttarget[position++] = length & 0xff\n\t} else {\n\t\ttarget[position++] = 0x5a\n\t\ttargetView.setUint32(position, length)\n\t\tposition += 4\n\t}\n\tif (position + length >= target.length) {\n\t\tmakeRoom(position + length)\n\t}\n\t// if it is already a typed array (has an ArrayBuffer), use that, but if it is an ArrayBuffer itself,\n\t// must wrap it to set it.\n\ttarget.set(buffer.buffer ? buffer : new Uint8Array(buffer), position)\n\tposition += length\n}\n\nfunction insertIds(serialized, idsToInsert) {\n\t// insert the ids that need to be referenced for structured clones\n\tlet nextId\n\tlet distanceToMove = idsToInsert.length * 2\n\tlet lastEnd = serialized.length - distanceToMove\n\tidsToInsert.sort((a, b) => a.offset > b.offset ? 1 : -1)\n\tfor (let id = 0; id < idsToInsert.length; id++) {\n\t\tlet referee = idsToInsert[id]\n\t\treferee.id = id\n\t\tfor (let position of referee.references) {\n\t\t\tserialized[position++] = id >> 8\n\t\t\tserialized[position] = id & 0xff\n\t\t}\n\t}\n\twhile (nextId = idsToInsert.pop()) {\n\t\tlet offset = nextId.offset\n\t\tserialized.copyWithin(offset + distanceToMove, offset, lastEnd)\n\t\tdistanceToMove -= 2\n\t\tlet position = offset + distanceToMove\n\t\tserialized[position++] = 0xd8\n\t\tserialized[position++] = 28 // http://cbor.schmorp.de/value-sharing\n\t\tlastEnd = offset\n\t}\n\treturn serialized\n}\nfunction writeBundles(start, encode) {\n\ttargetView.setUint32(bundledStrings.position + start, position - bundledStrings.position - start + 1) // the offset to bundle\n\tlet writeStrings = bundledStrings\n\tbundledStrings = null\n\tencode(writeStrings[0])\n\tencode(writeStrings[1])\n}\n\nexport function addExtension(extension) {\n\tif (extension.Class) {\n\t\tif (!extension.encode)\n\t\t\tthrow new Error('Extension has no encode function')\n\t\textensionClasses.unshift(extension.Class)\n\t\textensions.unshift(extension)\n\t}\n\tdecodeAddExtension(extension)\n}\nlet defaultEncoder = new Encoder({ useRecords: false })\nexport const encode = defaultEncoder.encode\nexport const encodeAsIterable = defaultEncoder.encodeAsIterable\nexport const encodeAsAsyncIterable = defaultEncoder.encodeAsAsyncIterable\nexport { FLOAT32_OPTIONS } from './decode.js'\nimport { FLOAT32_OPTIONS } from './decode.js'\nexport const { NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT } = FLOAT32_OPTIONS\nexport const REUSE_BUFFER_MODE = 512\nexport const RESET_BUFFER_MODE = 1024\nexport const THROW_ON_ITERABLE = 2048\n\n\n", "import { Encoder } from './encode.js'\nimport { Decoder } from './decode.js'\n\n/**\n * Given an Iterable first argument, returns an Iterable where each value is encoded as a Buffer\n * If the argument is only Async Iterable, the return value will be an Async Iterable.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterator} objectIterator - iterable source, like a Readable object stream, an array, Set, or custom object\n * @param {options} [options] - cbor-x Encoder options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator>}\n */\nexport function encodeIter (objectIterator, options = {}) {\n  if (!objectIterator || typeof objectIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, or a Promise for an Async Iterable')\n  } else if (typeof objectIterator[Symbol.iterator] === 'function') {\n    return encodeIterSync(objectIterator, options)\n  } else if (typeof objectIterator.then === 'function' || typeof objectIterator[Symbol.asyncIterator] === 'function') {\n    return encodeIterAsync(objectIterator, options)\n  } else {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a Promise')\n  }\n}\n\nfunction * encodeIterSync (objectIterator, options) {\n  const encoder = new Encoder(options)\n  for (const value of objectIterator) {\n    yield encoder.encode(value)\n  }\n}\n\nasync function * encodeIterAsync (objectIterator, options) {\n  const encoder = new Encoder(options)\n  for await (const value of objectIterator) {\n    yield encoder.encode(value)\n  }\n}\n\n/**\n * Given an Iterable/Iterator input which yields buffers, returns an IterableIterator which yields sync decoded objects\n * Or, given an Async Iterable/Iterator which yields promises resolving in buffers, returns an AsyncIterableIterator.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterableIterator} bufferIterator\n * @param {object} [options] - Decoder options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator}\n */\nexport function decodeIter (bufferIterator, options = {}) {\n  if (!bufferIterator || typeof bufferIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a promise')\n  }\n\n  const decoder = new Decoder(options)\n  let incomplete\n  const parser = (chunk) => {\n    let yields\n    // if there's incomplete data from previous chunk, concatinate and try again\n    if (incomplete) {\n      chunk = Buffer.concat([incomplete, chunk])\n      incomplete = undefined\n    }\n\n    try {\n      yields = decoder.decodeMultiple(chunk)\n    } catch (err) {\n      if (err.incomplete) {\n        incomplete = chunk.slice(err.lastPosition)\n        yields = err.values\n      } else {\n        throw err\n      }\n    }\n    return yields\n  }\n\n  if (typeof bufferIterator[Symbol.iterator] === 'function') {\n    return (function * iter () {\n      for (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  } else if (typeof bufferIterator[Symbol.asyncIterator] === 'function') {\n    return (async function * iter () {\n      for await (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  }\n}\n"], "mappings": ";;;AAAA,IAAI;AACJ,IAAI;AACH,YAAU,IAAI,YAAY;AAC3B,SAAQ,OAAO;AAAC;AAChB,IAAI;AACJ,IAAI;AACJ,IAAI,WAAW;AAEf,IAAM,cAAc,CAAC;AACrB,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;AAC9B,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAE3B,IAAM,0BAA0B;AAChC,IAAM,YAAY,CAAC;AACnB,IAAI,eAAe;AAEnB,IAAI,aAAa;AAGjB,IAAI,gBAAgB;AAEpB,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,IAAI,iBAAiB,CAAC;AACtB,IAAI;AACJ,IAAI;AACJ,IAAI,iBAAiB;AACrB,IAAI,eAAe;AACnB,IAAI;AACJ,IAAI;AACJ,IAAI,oBAAoB,CAAC;AACzB,IAAI,yBAAyB,CAAC;AAC9B,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,iBAAiB;AAAA,EACpB,YAAY;AAAA,EACZ,eAAe;AAChB;AACA,IAAI,iBAAiB;AACrB,IAAI,4BAA4B;AAGhC,IAAI;AACH,MAAI,SAAS,EAAE;AAChB,SAAQ,OAAO;AAEd,8BAA4B;AAC7B;AAIO,IAAM,UAAN,MAAM,SAAQ;AAAA,EACpB,YAAY,SAAS;AACpB,QAAI,SAAS;AACZ,WAAK,QAAQ,UAAU,QAAQ,YAAY,CAAC,QAAQ,YAAY;AAC/D,gBAAQ,aAAa;AACrB,gBAAQ,gBAAgB;AAAA,MACzB;AACA,UAAI,QAAQ,eAAe,SAAS,QAAQ,kBAAkB;AAC7D,gBAAQ,gBAAgB;AACzB,UAAI,QAAQ;AACX,gBAAQ,YAAY,QAAQ;AAC7B,UAAI,QAAQ,aAAa,CAAC,QAAQ;AACjC,SAAC,QAAQ,aAAa,CAAC,GAAG,gBAAgB;AAC3C,UAAI,QAAQ,QAAQ;AACnB,aAAK,SAAS,oBAAI,IAAI;AACtB,iBAAS,CAAC,GAAE,CAAC,KAAK,OAAO,QAAQ,QAAQ,MAAM,EAAG,MAAK,OAAO,IAAI,GAAE,CAAC;AAAA,MACtE;AAAA,IACD;AACA,WAAO,OAAO,MAAM,OAAO;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,KAAK;AACd,WAAO,KAAK,SAAS,KAAK,OAAO,IAAI,GAAG,KAAK,MAAM;AAAA,EACpD;AAAA,EAEA,UAAU,KAAK;AACd,WAAO,KAAK,UAAU,KAAK,OAAO,eAAe,GAAG,IAAI,KAAK,OAAO,GAAG,IAAI;AAAA,EAC5E;AAAA,EAEA,WAAW,KAAK;AACf,QAAI,CAAC,KAAK,QAAS,QAAO;AAC1B,QAAI,MAAM,oBAAI,IAAI;AAClB,aAAS,CAAC,GAAE,CAAC,KAAK,OAAO,QAAQ,GAAG,EAAG,KAAI,IAAK,KAAK,QAAQ,eAAe,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,GAAI,CAAC;AACxG,WAAO;AAAA,EACR;AAAA,EAEA,WAAW,KAAK;AACf,QAAI,CAAC,KAAK,WAAW,IAAI,YAAY,QAAQ,MAAO,QAAO;AAC3D,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,oBAAI,IAAI;AACvB,eAAS,CAAC,GAAE,CAAC,KAAK,OAAO,QAAQ,KAAK,OAAO,EAAG,MAAK,QAAQ,IAAI,GAAE,CAAC;AAAA,IACrE;AACA,QAAI,MAAM,CAAC;AAEX,QAAI,QAAQ,CAAC,GAAE,MAAM,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,IAAK,CAAC;AACrF,WAAO;AAAA,EACR;AAAA,EAEA,UAAU,QAAQ,KAAK;AAEtB,QAAI,MAAM,KAAK,OAAO,MAAM;AAC5B,QAAI,KAAK,SAAS;AAEjB,cAAQ,IAAI,YAAY,MAAM;AAAA,QAC7B,KAAK;AAAS,iBAAO,IAAI,IAAI,OAAK,KAAK,WAAW,CAAC,CAAC;AAAA,MAErD;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAAA,EAEA,OAAO,QAAQ,KAAK;AACnB,QAAI,KAAK;AAER,aAAO,UAAU,MAAM;AACtB,oBAAY;AACZ,eAAO,OAAO,KAAK,OAAO,QAAQ,GAAG,IAAI,SAAQ,UAAU,OAAO,KAAK,gBAAgB,QAAQ,GAAG;AAAA,MACnG,CAAC;AAAA,IACF;AACA,aAAS,MAAM,KAAK,MAAM,OAAO;AACjC,eAAW;AACX,qBAAiB;AACjB,mBAAe;AACf,gBAAY;AACZ,cAAU;AACV,qBAAiB;AACjB,UAAM;AAIN,QAAI;AACH,iBAAW,OAAO,aAAa,OAAO,WAAW,IAAI,SAAS,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,IAClH,SAAQ,OAAO;AAEd,YAAM;AACN,UAAI,kBAAkB;AACrB,cAAM;AACP,YAAM,IAAI,MAAM,sDAAuD,UAAU,OAAO,UAAU,WAAY,OAAO,YAAY,OAAO,OAAO,OAAO;AAAA,IACvJ;AACA,QAAI,gBAAgB,UAAS;AAC5B,uBAAiB;AACjB,qBAAe,KAAK,iBAClB,KAAK,OAAO,IAAI,MAAM,KAAK,0BAA0B,EAAE,EAAE,OAAO,KAAK,YAAY,IAClF,KAAK;AACN,UAAI,KAAK,YAAY;AACpB,4BAAoB,KAAK;AACzB,eAAO,YAAY;AAAA,MACpB,WAAW,CAAC,qBAAqB,kBAAkB,SAAS,GAAG;AAC9D,4BAAoB,CAAC;AAAA,MACtB;AAAA,IACD,OAAO;AACN,uBAAiB;AACjB,UAAI,CAAC,qBAAqB,kBAAkB,SAAS;AACpD,4BAAoB,CAAC;AACtB,qBAAe;AAAA,IAChB;AACA,WAAO,YAAY;AAAA,EACpB;AAAA,EACA,eAAe,QAAQ,SAAS;AAC/B,QAAI,QAAQ,eAAe;AAC3B,QAAI;AACH,UAAI,OAAO,OAAO;AAClB,uBAAiB;AACjB,UAAI,QAAQ,OAAO,KAAK,OAAO,QAAQ,IAAI,IAAI,eAAe,OAAO,QAAQ,IAAI;AACjF,UAAI,SAAS;AACZ,YAAI,QAAQ,KAAK,MAAM,OAAO;AAC7B;AAAA,QACD;AACA,eAAM,WAAW,MAAM;AACtB,yBAAe;AACf,cAAI,QAAQ,YAAY,CAAC,MAAM,OAAO;AACrC;AAAA,UACD;AAAA,QACD;AAAA,MACD,OACK;AACJ,iBAAS,CAAE,KAAM;AACjB,eAAM,WAAW,MAAM;AACtB,yBAAe;AACf,iBAAO,KAAK,YAAY,CAAC;AAAA,QAC1B;AACA,eAAO;AAAA,MACR;AAAA,IACD,SAAQ,OAAO;AACd,YAAM,eAAe;AACrB,YAAM,SAAS;AACf,YAAM;AAAA,IACP,UAAE;AACD,uBAAiB;AACjB,kBAAY;AAAA,IACb;AAAA,EACD;AACD;AAIO,SAAS,cAAc;AAC7B,MAAI;AACH,QAAI,SAAS,KAAK;AAClB,QAAI,gBAAgB;AACnB,UAAI,YAAY,eAAe,oBAAoB;AAClD,YAAI,QAAQ,IAAI,MAAM,4BAA4B;AAClD,cAAM,aAAa;AACnB,cAAM;AAAA,MACP;AAEA,iBAAW,eAAe;AAC1B,uBAAiB;AAAA,IAClB;AAEA,QAAI,YAAY,QAAQ;AAEvB,0BAAoB;AACpB,YAAM;AACN,UAAI;AACH,uBAAe;AAAA,IACjB,WAAW,WAAW,QAAQ;AAE7B,UAAI,QAAQ,IAAI,MAAM,6BAA6B;AACnD,YAAM,aAAa;AACnB,YAAM;AAAA,IACP,WAAW,CAAC,gBAAgB;AAC3B,YAAM,IAAI,MAAM,0CAA0C;AAAA,IAC3D;AAEA,WAAO;AAAA,EACR,SAAQ,OAAO;AACd,gBAAY;AACZ,QAAI,iBAAiB,cAAc,MAAM,QAAQ,WAAW,0BAA0B,GAAG;AACxF,YAAM,aAAa;AAAA,IACpB;AACA,UAAM;AAAA,EACP;AACD;AAEO,SAAS,OAAO;AACtB,MAAI,QAAQ,IAAI,UAAU;AAC1B,MAAI,YAAY,SAAS;AACzB,UAAQ,QAAQ;AAChB,MAAI,QAAQ,IAAM;AACjB,YAAQ,OAAO;AAAA,MACd,KAAK;AACJ,gBAAQ,IAAI,UAAU;AACtB;AAAA,MACD,KAAK;AACJ,YAAI,aAAa,GAAG;AACnB,iBAAO,WAAW;AAAA,QACnB;AACA,gBAAQ,SAAS,UAAU,QAAQ;AACnC,oBAAY;AACZ;AAAA,MACD,KAAK;AACJ,YAAI,aAAa,GAAG;AACnB,cAAI,QAAQ,SAAS,WAAW,QAAQ;AACxC,cAAI,eAAe,aAAa,GAAG;AAElC,gBAAI,aAAa,QAAS,IAAI,QAAQ,IAAI,QAAS,IAAM,IAAI,WAAW,CAAC,KAAK,CAAE;AAChF,wBAAY;AACZ,oBAAS,aAAa,SAAS,QAAQ,IAAI,MAAM,SAAU,KAAK;AAAA,UACjE;AACA,sBAAY;AACZ,iBAAO;AAAA,QACR;AACA,gBAAQ,SAAS,UAAU,QAAQ;AACnC,oBAAY;AACZ;AAAA,MACD,KAAK;AACJ,YAAI,aAAa,GAAG;AACnB,cAAI,QAAQ,SAAS,WAAW,QAAQ;AACxC,sBAAY;AACZ,iBAAO;AAAA,QACR;AACA,YAAI,YAAY,GAAG;AAClB,cAAI,SAAS,UAAU,QAAQ,IAAI;AAClC,kBAAM,IAAI,MAAM,kFAAkF;AACnG,kBAAQ,SAAS,UAAU,WAAW,CAAC;AAAA,QACxC,WAAW,eAAe,eAAe;AACxC,kBAAQ,SAAS,UAAU,QAAQ,IAAI;AACvC,mBAAS,SAAS,UAAU,WAAW,CAAC;AAAA,QACzC;AACC,kBAAQ,SAAS,aAAa,QAAQ;AACvC,oBAAY;AACZ;AAAA,MACD,KAAK;AAEJ,gBAAO,WAAW;AAAA,UACjB,KAAK;AAAA;AAAA,UACL,KAAK;AACJ,kBAAM,IAAI,MAAM,0DAA0D;AAAA,UAC3E,KAAK;AACJ,gBAAI,QAAQ,CAAC;AACb,gBAAI,OAAO,IAAI;AACf,oBAAQ,QAAQ,KAAK,MAAM,WAAW;AACrC,kBAAI,KAAK,aAAc,OAAM,IAAI,MAAM,wBAAwB,YAAY,EAAE;AAC7E,oBAAM,GAAG,IAAI;AAAA,YACd;AACA,mBAAO,aAAa,IAAI,QAAQ,aAAa,IAAI,MAAM,KAAK,EAAE,IAAI,OAAO,OAAO,KAAK;AAAA,UACtF,KAAK;AACJ,gBAAI;AACJ,gBAAI,eAAe,eAAe;AACjC,kBAAI,SAAS,CAAC;AACd,kBAAIA,KAAI;AACR,kBAAI,eAAe,QAAQ;AAC1B,wBAAO,MAAM,KAAK,MAAM,WAAW;AAClC,sBAAIA,QAAO,WAAY,OAAM,IAAI,MAAM,0BAA0B,UAAU,EAAE;AAC7E,yBAAO,QAAQ,eAAe,UAAU,GAAG,CAAC,CAAC,IAAI,KAAK;AAAA,gBACvD;AAAA,cACD,OACK;AACJ,wBAAQ,MAAM,KAAK,MAAM,WAAW;AACnC,sBAAIA,QAAO,WAAY,OAAM,IAAI,MAAM,0BAA0B,UAAU,EAAE;AAC7E,yBAAO,QAAQ,GAAG,CAAC,IAAI,KAAK;AAAA,gBAC7B;AAAA,cACD;AACA,qBAAO;AAAA,YACR,OAAO;AACN,kBAAI,qBAAqB;AACxB,+BAAe,gBAAgB;AAC/B,sCAAsB;AAAA,cACvB;AACA,kBAAI,MAAM,oBAAI,IAAI;AAClB,kBAAI,eAAe,QAAQ;AAC1B,oBAAIA,KAAI;AACR,wBAAO,MAAM,KAAK,MAAM,WAAW;AAClC,sBAAIA,QAAO,YAAY;AACtB,0BAAM,IAAI,MAAM,oBAAoB,UAAU,EAAE;AAAA,kBACjD;AACA,sBAAI,IAAI,eAAe,UAAU,GAAG,GAAG,KAAK,CAAC;AAAA,gBAC9C;AAAA,cACD,OACK;AACJ,oBAAIA,KAAI;AACR,wBAAQ,MAAM,KAAK,MAAM,WAAW;AACnC,sBAAIA,QAAO,YAAY;AACtB,0BAAM,IAAI,MAAM,oBAAoB,UAAU,EAAE;AAAA,kBACjD;AACA,sBAAI,IAAI,KAAK,KAAK,CAAC;AAAA,gBACpB;AAAA,cACD;AACA,qBAAO;AAAA,YACR;AAAA,UACD,KAAK;AACJ,mBAAO;AAAA,UACR;AACC,kBAAM,IAAI,MAAM,8CAA8C,SAAS;AAAA,QACzE;AAAA,MACD;AACC,cAAM,IAAI,MAAM,mBAAmB,KAAK;AAAA,IAC1C;AAAA,EACD;AACA,UAAQ,WAAW;AAAA,IAClB,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO,CAAC;AAAA,IACT,KAAK;AACJ,aAAO,QAAQ,KAAK;AAAA,IACrB,KAAK;AACJ,UAAI,gBAAgB,UAAU;AAC7B,eAAO,UAAU,MAAM,WAAW,iBAAiB,YAAY,SAAS,cAAc;AAAA,MACvF;AACA,UAAI,gBAAgB,KAAK,SAAS,OAAO,QAAQ,IAAI;AAEpD,YAAI,SAAS,QAAQ,KAAK,gBAAgB,KAAK,IAAI,eAAe,KAAK;AACvE,YAAI,UAAU;AACb,iBAAO;AAAA,MACT;AACA,aAAO,gBAAgB,KAAK;AAAA,IAC7B,KAAK;AACJ,UAAI,SAAS,aAAc,OAAM,IAAI,MAAM,wBAAwB,YAAY,EAAE;AACjF,UAAI,QAAQ,IAAI,MAAM,KAAK;AAG3B,eAAS,IAAI,GAAG,IAAI,OAAO,IAAK,OAAM,CAAC,IAAI,KAAK;AAChD,aAAO;AAAA,IACR,KAAK;AACJ,UAAI,SAAS,WAAY,OAAM,IAAI,MAAM,oBAAoB,YAAY,EAAE;AAC3E,UAAI,eAAe,eAAe;AACjC,YAAI,SAAS,CAAC;AACd,YAAI,eAAe,OAAQ,UAAS,IAAI,GAAG,IAAI,OAAO,IAAK,QAAO,QAAQ,eAAe,UAAU,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK;AAAA,YAC/G,UAAS,IAAI,GAAG,IAAI,OAAO,IAAK,QAAO,QAAQ,KAAK,CAAC,CAAC,IAAI,KAAK;AACpE,eAAO;AAAA,MACR,OAAO;AACN,YAAI,qBAAqB;AACxB,yBAAe,gBAAgB;AAC/B,gCAAsB;AAAA,QACvB;AACA,YAAI,MAAM,oBAAI,IAAI;AAClB,YAAI,eAAe,OAAQ,UAAS,IAAI,GAAG,IAAI,OAAO,IAAK,KAAI,IAAI,eAAe,UAAU,KAAK,CAAC,GAAE,KAAK,CAAC;AAAA,YACrG,UAAS,IAAI,GAAG,IAAI,OAAO,IAAK,KAAI,IAAI,KAAK,GAAG,KAAK,CAAC;AAC3D,eAAO;AAAA,MACR;AAAA,IACD,KAAK;AACJ,UAAI,SAAS,oBAAoB;AAChC,YAAI,YAAY,kBAAkB,QAAQ,IAAM;AAEhD,YAAI,WAAW;AACd,cAAI,CAAC,UAAU,KAAM,WAAU,OAAO,sBAAsB,SAAS;AACrE,iBAAO,UAAU,KAAK;AAAA,QACvB;AACA,YAAI,QAAQ,OAAS;AACpB,cAAI,SAAS,kBAAkB;AAE9B,gBAAI,SAAS,eAAe;AAC5B,gBAAI,KAAK,KAAK;AACd,gBAAIC,aAAY,KAAK;AACrB,6BAAiB,IAAIA,UAAS;AAC9B,gBAAI,SAAS,CAAC;AACd,gBAAI,eAAe,OAAQ,UAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3D,kBAAI,MAAM,eAAe,UAAUA,WAAU,IAAI,CAAC,CAAC;AACnD,qBAAO,QAAQ,GAAG,CAAC,IAAI,KAAK;AAAA,YAC7B;AAAA,gBACK,UAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AACrC,kBAAI,MAAMA,WAAU,IAAI,CAAC;AACzB,qBAAO,QAAQ,GAAG,CAAC,IAAI,KAAK;AAAA,YAC7B;AACA,mBAAO;AAAA,UACR,WACS,SAAS,uBAAuB;AACxC,gBAAI,SAAS,eAAe;AAC5B,gBAAI,KAAK,KAAK;AACd,qBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,+BAAiB,MAAM,KAAK,CAAC;AAAA,YAC9B;AACA,mBAAO,KAAK;AAAA,UACb,WAAW,SAAS,oBAAoB;AACvC,mBAAO,cAAc;AAAA,UACtB;AACA,cAAI,eAAe,WAAW;AAC7B,uBAAW;AACX,wBAAY,kBAAkB,QAAQ,IAAM;AAC5C,gBAAI,WAAW;AACd,kBAAI,CAAC,UAAU;AACd,0BAAU,OAAO,sBAAsB,SAAS;AACjD,qBAAO,UAAU,KAAK;AAAA,YACvB;AAAA,UACD;AAAA,QACD;AAAA,MACD;AACA,UAAI,YAAY,kBAAkB,KAAK;AACvC,UAAI,WAAW;AACd,YAAI,UAAU;AACb,iBAAO,UAAU,IAAI;AAAA;AAErB,iBAAO,UAAU,KAAK,CAAC;AAAA,MACzB,OAAO;AACN,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,GAAG,IAAI,uBAAuB,QAAQ,KAAK;AACvD,cAAI,QAAQ,uBAAuB,CAAC,EAAE,OAAO,KAAK;AAClD,cAAI,UAAU;AACb,mBAAO;AAAA,QACT;AACA,eAAO,IAAI,IAAI,OAAO,KAAK;AAAA,MAC5B;AAAA,IACD,KAAK;AACJ,cAAQ,OAAO;AAAA,QACd,KAAK;AAAM,iBAAO;AAAA,QAClB,KAAK;AAAM,iBAAO;AAAA,QAClB,KAAK;AAAM,iBAAO;AAAA,QAClB,KAAK;AAAM;AAAA;AAAA,QACX,KAAK;AAAA,QACL;AACC,cAAI,eAAe,gBAAgB,gBAAgB,GAAG,KAAK;AAC3D,cAAI,gBAAgB;AACnB,mBAAO;AACR,gBAAM,IAAI,MAAM,mBAAmB,KAAK;AAAA,MAC1C;AAAA,IACD;AACC,UAAI,MAAM,KAAK,GAAG;AACjB,YAAI,QAAQ,IAAI,MAAM,6BAA6B;AACnD,cAAM,aAAa;AACnB,cAAM;AAAA,MACP;AACA,YAAM,IAAI,MAAM,wBAAwB,KAAK;AAAA,EAC/C;AACD;AACA,IAAM,YAAY;AAClB,SAAS,sBAAsB,WAAW;AACzC,MAAI,CAAC,UAAW,OAAM,IAAI,MAAM,4CAA4C;AAC5E,WAAS,aAAa;AAErB,QAAI,SAAS,IAAI,UAAU;AAE3B,aAAS,SAAS;AAClB,QAAI,SAAS,IAAM;AAClB,cAAQ,QAAQ;AAAA,QACf,KAAK;AACJ,mBAAS,IAAI,UAAU;AACvB;AAAA,QACD,KAAK;AACJ,mBAAS,SAAS,UAAU,QAAQ;AACpC,sBAAY;AACZ;AAAA,QACD,KAAK;AACJ,mBAAS,SAAS,UAAU,QAAQ;AACpC,sBAAY;AACZ;AAAA,QACD;AACC,gBAAM,IAAI,MAAM,oCAAoC,IAAI,WAAW,CAAC,CAAC;AAAA,MACvE;AAAA,IACD;AAEA,QAAI,iBAAiB,KAAK;AAC1B,WAAM,gBAAgB;AAErB,UAAI,eAAe,kBAAkB;AACpC,eAAO,eAAe,IAAI;AAC3B,uBAAiB,eAAe;AAAA,IACjC;AACA,QAAI,KAAK,eAAe,2BAA2B;AAClD,UAAI,QAAQ,KAAK,UAAU,SAAS,OAAO,KAAK,MAAM,GAAG,MAAM;AAC/D,uBAAiB,eAAe,SAC9B,IAAI,SAAS,KAAK,aAAa,MAAM,IAAI,OAAK,eAAe,UAAU,CAAC,CAAC,EAAE,IAAI,OAAK,UAAU,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,SAAU,MAAM,KAAK,UAAU,CAAC,IAAI,OAAQ,EAAE,KAAK,GAAG,IAAI,GAAG,IAClL,IAAI,SAAS,KAAK,aAAa,MAAM,IAAI,SAAO,UAAU,KAAK,GAAG,IAAI,QAAQ,GAAG,IAAI,SAAU,MAAM,KAAK,UAAU,GAAG,IAAI,OAAQ,EAAE,KAAK,GAAG,IAAI,GAAG;AACtJ,UAAI,KAAK;AACR,uBAAe,OAAO,KAAK;AAC5B,qBAAe,gBAAgB;AAC/B,WAAK,iBAAiB;AACtB,aAAO,eAAe,IAAI;AAAA,IAC3B;AACA,QAAI,SAAS,CAAC;AACd,QAAI,eAAe,OAAQ,UAAS,IAAI,GAAG,IAAI,QAAQ,IAAK,QAAO,QAAQ,eAAe,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK;AAAA,QACjH,UAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AACrC,aAAO,QAAQ,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK;AAAA,IACjC;AACA,WAAO;AAAA,EACR;AACA,YAAU,YAAY;AACtB,SAAO;AACR;AAEA,SAAS,QAAQ,KAAK;AAErB,MAAI,OAAO,QAAQ,SAAU,QAAO,QAAQ,cAAc,aAAa;AACvE,MAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa,OAAO,QAAQ,SAAU,QAAO,IAAI,SAAS;AACxG,MAAI,OAAO,KAAM,QAAO,MAAM;AAE9B,QAAM,IAAI,MAAM,gCAAgC,OAAO,GAAG;AAC3D;AAEA,IAAI,kBAAkB;AAKf,IAAI,8BAA8B;AAuCzC,SAAS,aAAa,QAAQ;AAC7B,MAAI;AACJ,MAAI,SAAS,IAAI;AAChB,QAAI,SAAS,gBAAgB,MAAM;AAClC,aAAO;AAAA,EACT;AACA,MAAI,SAAS,MAAM;AAClB,WAAO,QAAQ,OAAO,IAAI,SAAS,UAAU,YAAY,MAAM,CAAC;AACjE,QAAM,MAAM,WAAW;AACvB,QAAM,QAAQ,CAAC;AACf,WAAS;AACT,SAAO,WAAW,KAAK;AACtB,UAAM,QAAQ,IAAI,UAAU;AAC5B,SAAK,QAAQ,SAAU,GAAG;AAEzB,YAAM,KAAK,KAAK;AAAA,IACjB,YAAY,QAAQ,SAAU,KAAM;AAEnC,YAAM,QAAQ,IAAI,UAAU,IAAI;AAChC,YAAM,MAAO,QAAQ,OAAS,IAAK,KAAK;AAAA,IACzC,YAAY,QAAQ,SAAU,KAAM;AAEnC,YAAM,QAAQ,IAAI,UAAU,IAAI;AAChC,YAAM,QAAQ,IAAI,UAAU,IAAI;AAChC,YAAM,MAAO,QAAQ,OAAS,KAAO,SAAS,IAAK,KAAK;AAAA,IACzD,YAAY,QAAQ,SAAU,KAAM;AAEnC,YAAM,QAAQ,IAAI,UAAU,IAAI;AAChC,YAAM,QAAQ,IAAI,UAAU,IAAI;AAChC,YAAM,QAAQ,IAAI,UAAU,IAAI;AAChC,UAAI,QAAS,QAAQ,MAAS,KAAS,SAAS,KAAS,SAAS,IAAQ;AAC1E,UAAI,OAAO,OAAQ;AAClB,gBAAQ;AACR,cAAM,KAAO,SAAS,KAAM,OAAS,KAAM;AAC3C,eAAO,QAAU,OAAO;AAAA,MACzB;AACA,YAAM,KAAK,IAAI;AAAA,IAChB,OAAO;AACN,YAAM,KAAK,KAAK;AAAA,IACjB;AAEA,QAAI,MAAM,UAAU,MAAQ;AAC3B,gBAAU,aAAa,MAAM,QAAQ,KAAK;AAC1C,YAAM,SAAS;AAAA,IAChB;AAAA,EACD;AAEA,MAAI,MAAM,SAAS,GAAG;AACrB,cAAU,aAAa,MAAM,QAAQ,KAAK;AAAA,EAC3C;AAEA,SAAO;AACR;AACA,IAAI,eAAe,OAAO;AAC1B,SAAS,eAAe,QAAQ;AAC/B,MAAI,QAAQ;AACZ,MAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAM,OAAO,IAAI,UAAU;AAC3B,SAAK,OAAO,OAAQ,GAAG;AACtB,iBAAW;AACP;AAAA,IACD;AACA,UAAM,CAAC,IAAI;AAAA,EACZ;AACA,SAAO,aAAa,MAAM,QAAQ,KAAK;AAC5C;AACA,SAAS,gBAAgB,QAAQ;AAChC,MAAI,SAAS,GAAG;AACf,QAAI,SAAS,GAAG;AACf,UAAI,WAAW;AACd,eAAO;AAAA,WACH;AACJ,YAAI,IAAI,IAAI,UAAU;AACtB,aAAK,IAAI,OAAQ,GAAG;AACnB,sBAAY;AACZ;AAAA,QACD;AACA,eAAO,aAAa,CAAC;AAAA,MACtB;AAAA,IACD,OAAO;AACN,UAAI,IAAI,IAAI,UAAU;AACtB,UAAI,IAAI,IAAI,UAAU;AACtB,WAAK,IAAI,OAAQ,MAAM,IAAI,OAAQ,GAAG;AACrC,oBAAY;AACZ;AAAA,MACD;AACA,UAAI,SAAS;AACZ,eAAO,aAAa,GAAG,CAAC;AACzB,UAAI,IAAI,IAAI,UAAU;AACtB,WAAK,IAAI,OAAQ,GAAG;AACnB,oBAAY;AACZ;AAAA,MACD;AACA,aAAO,aAAa,GAAG,GAAG,CAAC;AAAA,IAC5B;AAAA,EACD,OAAO;AACN,QAAI,IAAI,IAAI,UAAU;AACtB,QAAI,IAAI,IAAI,UAAU;AACtB,QAAI,IAAI,IAAI,UAAU;AACtB,QAAI,IAAI,IAAI,UAAU;AACtB,SAAK,IAAI,OAAQ,MAAM,IAAI,OAAQ,MAAM,IAAI,OAAQ,MAAM,IAAI,OAAQ,GAAG;AACzE,kBAAY;AACZ;AAAA,IACD;AACA,QAAI,SAAS,GAAG;AACf,UAAI,WAAW;AACd,eAAO,aAAa,GAAG,GAAG,GAAG,CAAC;AAAA,WAC1B;AACJ,YAAI,IAAI,IAAI,UAAU;AACtB,aAAK,IAAI,OAAQ,GAAG;AACnB,sBAAY;AACZ;AAAA,QACD;AACA,eAAO,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAClC;AAAA,IACD,WAAW,SAAS,GAAG;AACtB,UAAI,IAAI,IAAI,UAAU;AACtB,UAAI,IAAI,IAAI,UAAU;AACtB,WAAK,IAAI,OAAQ,MAAM,IAAI,OAAQ,GAAG;AACrC,oBAAY;AACZ;AAAA,MACD;AACA,UAAI,SAAS;AACZ,eAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACrC,UAAI,IAAI,IAAI,UAAU;AACtB,WAAK,IAAI,OAAQ,GAAG;AACnB,oBAAY;AACZ;AAAA,MACD;AACA,aAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IACxC,OAAO;AACN,UAAI,IAAI,IAAI,UAAU;AACtB,UAAI,IAAI,IAAI,UAAU;AACtB,UAAI,IAAI,IAAI,UAAU;AACtB,UAAI,IAAI,IAAI,UAAU;AACtB,WAAK,IAAI,OAAQ,MAAM,IAAI,OAAQ,MAAM,IAAI,OAAQ,MAAM,IAAI,OAAQ,GAAG;AACzE,oBAAY;AACZ;AAAA,MACD;AACA,UAAI,SAAS,IAAI;AAChB,YAAI,WAAW;AACd,iBAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,aACtC;AACJ,cAAI,IAAI,IAAI,UAAU;AACtB,eAAK,IAAI,OAAQ,GAAG;AACnB,wBAAY;AACZ;AAAA,UACD;AACA,iBAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAC9C;AAAA,MACD,WAAW,SAAS,IAAI;AACvB,YAAI,IAAI,IAAI,UAAU;AACtB,YAAI,IAAI,IAAI,UAAU;AACtB,aAAK,IAAI,OAAQ,MAAM,IAAI,OAAQ,GAAG;AACrC,sBAAY;AACZ;AAAA,QACD;AACA,YAAI,SAAS;AACZ,iBAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACjD,YAAI,IAAI,IAAI,UAAU;AACtB,aAAK,IAAI,OAAQ,GAAG;AACnB,sBAAY;AACZ;AAAA,QACD;AACA,eAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MACpD,OAAO;AACN,YAAI,IAAI,IAAI,UAAU;AACtB,YAAI,IAAI,IAAI,UAAU;AACtB,YAAI,IAAI,IAAI,UAAU;AACtB,YAAI,IAAI,IAAI,UAAU;AACtB,aAAK,IAAI,OAAQ,MAAM,IAAI,OAAQ,MAAM,IAAI,OAAQ,MAAM,IAAI,OAAQ,GAAG;AACzE,sBAAY;AACZ;AAAA,QACD;AACA,YAAI,SAAS,IAAI;AAChB,cAAI,WAAW;AACd,mBAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,eAClD;AACJ,gBAAI,IAAI,IAAI,UAAU;AACtB,iBAAK,IAAI,OAAQ,GAAG;AACnB,0BAAY;AACZ;AAAA,YACD;AACA,mBAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,UAC1D;AAAA,QACD,OAAO;AACN,cAAI,IAAI,IAAI,UAAU;AACtB,cAAI,IAAI,IAAI,UAAU;AACtB,eAAK,IAAI,OAAQ,MAAM,IAAI,OAAQ,GAAG;AACrC,wBAAY;AACZ;AAAA,UACD;AACA,cAAI,SAAS;AACZ,mBAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7D,cAAI,IAAI,IAAI,UAAU;AACtB,eAAK,IAAI,OAAQ,GAAG;AACnB,wBAAY;AACZ;AAAA,UACD;AACA,iBAAO,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAChE;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEA,SAAS,QAAQ,QAAQ;AACxB,SAAO,eAAe;AAAA;AAAA,IAErB,WAAW,UAAU,MAAM,KAAK,KAAK,UAAU,YAAY,MAAM;AAAA,MACjE,IAAI,SAAS,UAAU,YAAY,MAAM;AAC3C;AASA,IAAI,WAAW,IAAI,aAAa,CAAC;AACjC,IAAI,UAAU,IAAI,WAAW,SAAS,QAAQ,GAAG,CAAC;AAClD,SAAS,aAAa;AACrB,MAAI,QAAQ,IAAI,UAAU;AAC1B,MAAI,QAAQ,IAAI,UAAU;AAC1B,MAAI,YAAY,QAAQ,QAAS;AACjC,MAAI,aAAa,IAAM;AACtB,QAAI,SAAU,QAAQ;AACrB,aAAO;AACR,WAAQ,QAAQ,MAAQ,YAAY;AAAA,EACrC;AACA,MAAI,aAAa,GAAG;AAEnB,QAAI,QAAS,QAAQ,MAAM,IAAK,UAAU,KAAK;AAC/C,WAAQ,QAAQ,MAAQ,CAAC,MAAM;AAAA,EAChC;AAEA,UAAQ,CAAC,IAAK,QAAQ;AAAA,GACnB,YAAY,KAAK;AACpB,UAAQ,CAAC,KAAM,QAAQ,MAAM;AAAA,EAC3B,SAAS;AACX,UAAQ,CAAC,IAAI,SAAS;AACtB,UAAQ,CAAC,IAAI;AACb,SAAO,SAAS,CAAC;AAClB;AAEA,IAAI,WAAW,IAAI,MAAM,IAAI;AAgEtB,IAAM,MAAN,MAAU;AAAA,EAChB,YAAY,OAAO,KAAK;AACvB,SAAK,QAAQ;AACb,SAAK,MAAM;AAAA,EACZ;AACD;AAEA,kBAAkB,CAAC,IAAI,CAAC,eAAe;AAEtC,SAAO,IAAI,KAAK,UAAU;AAC3B;AAEA,kBAAkB,CAAC,IAAI,CAAC,aAAa;AAEpC,SAAO,IAAI,KAAK,KAAK,MAAM,WAAW,GAAI,CAAC;AAC5C;AAEA,kBAAkB,CAAC,IAAI,CAAC,WAAW;AAElC,MAAI,QAAQ,OAAO,CAAC;AACpB,WAAS,IAAI,GAAG,IAAI,OAAO,YAAY,IAAI,GAAG,KAAK;AAClD,YAAQ,OAAO,OAAO,CAAC,CAAC,KAAK,SAAS,OAAO,CAAC;AAAA,EAC/C;AACA,SAAO;AACR;AAEA,kBAAkB,CAAC,IAAI,CAAC,WAAW;AAElC,SAAO,OAAO,EAAE,IAAI,kBAAkB,CAAC,EAAE,MAAM;AAChD;AACA,kBAAkB,CAAC,IAAI,CAAC,aAAa;AAEpC,SAAO,EAAE,SAAS,CAAC,IAAI,MAAM,SAAS,CAAC;AACxC;AAEA,kBAAkB,CAAC,IAAI,CAAC,aAAa;AAEpC,SAAO,SAAS,CAAC,IAAI,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AACxD;AAGA,IAAM,mBAAmB,CAAC,IAAI,cAAc;AAC3C,OAAK,KAAK;AACV,MAAI,oBAAoB,kBAAkB,EAAE;AAC5C,MAAI,qBAAqB,kBAAkB,UAAU;AACpD,KAAC,kBAAkB,sBAAsB,kBAAkB,oBAAoB,CAAC,IAAI,EAAE,IAAI;AAAA,EAC3F;AACA,oBAAkB,EAAE,IAAI;AAExB,YAAU,OAAO,sBAAsB,SAAS;AACjD;AACA,kBAAkB,uBAAuB,IAAI,CAAC,SAAS;AACtD,MAAI,SAAS,KAAK;AAClB,MAAI,YAAY,KAAK,CAAC;AACtB,mBAAiB,KAAK,CAAC,GAAG,SAAS;AACnC,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,QAAI,MAAM,UAAU,IAAI,CAAC;AACzB,WAAO,QAAQ,GAAG,CAAC,IAAI,KAAK,CAAC;AAAA,EAC9B;AACA,SAAO;AACR;AACA,kBAAkB,EAAE,IAAI,CAAC,UAAU;AAClC,MAAI;AACH,WAAO,eAAe,CAAC,EAAE,MAAM,eAAe,WAAW,eAAe,aAAa,KAAK;AAC3F,SAAO,IAAI,IAAI,OAAO,EAAE;AACzB;AACA,kBAAkB,EAAE,IAAI,CAAC,UAAU;AAClC,MAAI;AACH,WAAO,eAAe,CAAC,EAAE,MAAM,eAAe,WAAW,eAAe,aAAa,KAAK;AAC3F,SAAO,IAAI,IAAI,OAAO,EAAE;AACzB;AACA,IAAI,OAAO,EAAE,OAAO,OAAO;AAC3B,kBAAkB,EAAE,IAAI,CAAC,SAAS;AACjC,UAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACjD;AACA,IAAM,cAAc,CAACC,UAAS;AAC7B,MAAI,IAAI,UAAU,KAAK,KAAM;AAC5B,QAAI,QAAQ,IAAI,MAAM,+DAA+D;AACrF,QAAI,IAAI,SAAS;AAChB,YAAM,aAAa;AACpB,UAAM;AAAA,EACP;AACA,MAAI,kBAAkBA,MAAK;AAC3B,MAAI,CAAC,mBAAmB,CAAC,gBAAgB,QAAQ;AAChD,QAAI,QAAQ,IAAI,MAAM,+DAA+D;AACrF,UAAM,aAAa;AACnB,UAAM;AAAA,EACP;AACA,iBAAe,eAAe,gBAAgB,OAAO,aAAa,MAAM,gBAAgB,MAAM,CAAC,IAAI;AACnG,eAAa,WAAWA,MAAK;AAC7B,eAAa,WAAWA,MAAK;AAC7B,SAAOA,MAAK;AACb;AACA,YAAY,cAAc;AAC1B,kBAAkB,EAAE,IAAI;AAExB,kBAAkB,uBAAuB,IAAI,CAAC,SAAS;AACtD,MAAI,CAAC,cAAc;AAClB,QAAI,eAAe;AAClB,iBAAW;AAAA;AAEX,aAAO,IAAI,IAAI,MAAM,uBAAuB;AAAA,EAC9C;AACA,MAAI,OAAO,QAAQ;AAClB,WAAO,aAAa,MAAM,QAAQ,IAAI,IAAI,OAAQ,KAAK,OAAO,EAAG;AAClE,MAAI,QAAQ,IAAI,MAAM,kDAAkD;AACxE,MAAI,SAAS;AACZ,UAAM,aAAa;AACpB,QAAM;AACP;AAmBA,kBAAkB,EAAE,IAAI,CAACA,UAAS;AAEjC,MAAI,CAAC,cAAc;AAClB,mBAAe,oBAAI,IAAI;AACvB,iBAAa,KAAK;AAAA,EACnB;AACA,MAAI,KAAK,aAAa;AACtB,MAAI,mBAAmB;AACvB,MAAI,QAAQ,IAAI,QAAQ;AACxB,MAAIC;AAGJ,MAAK,SAAS,KAAM;AACnB,IAAAA,UAAS,CAAC;AAAA;AAEV,IAAAA,UAAS,CAAC;AAEX,MAAI,WAAW,EAAE,QAAAA,QAAO;AACxB,eAAa,IAAI,IAAI,QAAQ;AAC7B,MAAI,mBAAmBD,MAAK;AAC5B,MAAI,SAAS,MAAM;AAClB,QAAI,OAAO,eAAeC,OAAM,MAAM,OAAO,eAAe,gBAAgB,GAAG;AAK9E,iBAAW;AAEX,MAAAA,UAAS;AACT,mBAAa,IAAI,IAAI,EAAE,QAAAA,QAAO,CAAC;AAC/B,yBAAmBD,MAAK;AAAA,IACzB;AACA,WAAO,OAAO,OAAOC,SAAQ,gBAAgB;AAAA,EAC9C;AACA,WAAS,SAAS;AAClB,SAAO;AACR;AACA,kBAAkB,EAAE,EAAE,cAAc;AAEpC,kBAAkB,EAAE,IAAI,CAAC,OAAO;AAE/B,MAAI,WAAW,aAAa,IAAI,EAAE;AAClC,WAAS,OAAO;AAChB,SAAO,SAAS;AACjB;AAEA,kBAAkB,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,KAAK;AAAA,CAChD,kBAAkB,GAAG,IAAI,CAACD,UAAS;AAGnC,MAAI,eAAe,eAAe;AACjC,mBAAe,gBAAgB;AAC/B,0BAAsB;AAAA,EACvB;AACA,SAAOA,MAAK;AACb,GAAG,cAAc;AACjB,SAAS,QAAQ,GAAG,GAAG;AACtB,MAAI,OAAO,MAAM;AAChB,WAAO,IAAI;AACZ,MAAI,aAAa;AAChB,WAAO,EAAE,OAAO,CAAC;AAClB,SAAO,OAAO,OAAO,CAAC,GAAG,GAAG,CAAC;AAC9B;AACA,SAAS,kBAAkB;AAC1B,MAAI,CAAC,cAAc;AAClB,QAAI,eAAe;AAClB,iBAAW;AAAA;AAEX,YAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACA,SAAO;AACR;AACA,IAAM,qBAAqB;AAC3B,uBAAuB,KAAK,CAAC,KAAK,UAAU;AAC3C,MAAI,OAAO,OAAO,OAAO;AACxB,WAAO,QAAQ,gBAAgB,EAAE,SAAS,MAAM,GAAG,GAAG,KAAK;AAC5D,MAAI,OAAO,SAAS,OAAO;AAC1B,WAAO,QAAQ,gBAAgB,EAAE,SAAS,MAAM,KAAK,GAAG,KAAK;AAC9D,MAAI,OAAO,cAAc,OAAO;AAC/B,WAAO,QAAQ,gBAAgB,EAAE,SAAS,MAAM,UAAU,GAAG,KAAK;AACnE,MAAI,OAAO,OAAO,OAAO;AACxB,WAAO,QAAQ,OAAO,gBAAgB,EAAE,SAAS,MAAM,GAAG,CAAC;AAC5D,MAAI,OAAO,SAAS,OAAO;AAC1B,WAAO,QAAQ,OAAO,gBAAgB,EAAE,SAAS,MAAM,KAAK,CAAC;AAC9D,MAAI,OAAO,cAAc,OAAO;AAC/B,WAAO,QAAQ,OAAO,gBAAgB,EAAE,SAAS,MAAM,UAAU,CAAC;AACnE,MAAI,OAAO,oBAAoB;AAC9B,WAAO;AAAA,MACN;AAAA,MACA,YAAY,kBAAkB,MAAM,CAAC;AAAA,MACrC,SAAS;AAAA,IACV;AAAA,EACD;AACA,MAAI,OAAO;AACV,WAAO;AACT,CAAC;AAED,IAAM,wBAAwB,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK;AACzE,IAAM,cAAc;AAAA,EAAC;AAAA,EAAY;AAAA,EAAmB;AAAA,EAAa;AAAA,EACvE,OAAO,kBAAkB,cAAc,EAAE,MAAK,iBAAiB,IAAI;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAY;AAAA,EAC1G,OAAO,iBAAiB,cAAc,EAAE,MAAK,gBAAgB,IAAI;AAAA,EAAe;AAAA,EAAc;AAAY;AAC3G,IAAM,iBAAiB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAClE,SAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC5C,qBAAmB,YAAY,CAAC,GAAG,eAAe,CAAC,CAAC;AACrD;AACA,SAAS,mBAAmB,YAAY,KAAK;AAC5C,MAAI,WAAW,QAAQ,WAAW,KAAK,MAAM,GAAG,EAAE;AAClD,MAAI;AACJ,MAAI,OAAO,eAAe;AACzB,sBAAkB,WAAW;AAAA;AAE7B,iBAAa;AACd,WAAS,eAAe,GAAG,eAAe,GAAG,gBAAgB;AAC5D,QAAI,CAAC,gBAAgB,mBAAmB;AACvC;AACD,QAAI,YAAY,mBAAmB,IAAI,IAAI,mBAAmB,IAAI,IAAI,mBAAmB,IAAI,IAAI;AACjG,sBAAkB,eAAe,MAAO,MAAM,CAAE,IAAK,mBAAmB,KAAK,gBAAgB,wBAAyB,CAAC,WAAW;AACjI,UAAI,CAAC;AACJ,cAAM,IAAI,MAAM,yCAAyC,GAAG;AAC7D,UAAI,CAAC,eAAe,aAAa;AAEhC,YAAI,oBAAoB,KACvB,oBAAoB,KAAK,EAAE,OAAO,aAAa,MAC/C,oBAAoB,KAAK,EAAE,OAAO,aAAa,MAC/C,oBAAoB,KAAK,EAAE,OAAO,aAAa;AAC/C,iBAAO,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,cAAc,SAAS;AAAA,MACxF;AAEA,aAAO,IAAI,WAAW,WAAW,UAAU,MAAM,KAAK,QAAQ,CAAC,EAAE,MAAM;AAAA,IACxE,IAAI,YAAU;AACb,UAAI,CAAC;AACJ,cAAM,IAAI,MAAM,yCAAyC,GAAG;AAC7D,UAAI,KAAK,IAAI,SAAS,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AACzE,UAAI,WAAW,OAAO,UAAU;AAChC,UAAI,KAAK,IAAI,WAAW,QAAQ;AAChC,UAAI,SAAS,GAAG,QAAQ;AACxB,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAClC,WAAG,CAAC,IAAI,OAAO,KAAK,IAAI,KAAK,WAAW,YAAY;AAAA,MACrD;AACA,aAAO;AAAA,IACR;AAAA,EACD;AACD;AAEA,SAAS,gBAAgB;AACxB,MAAI,SAAS,eAAe;AAC5B,MAAI,iBAAiB,WAAW,KAAK;AACrC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAEhC,QAAI,eAAe,eAAe;AAClC,gBAAY;AAAA,EACb;AACA,MAAI,eAAe;AACnB,aAAW;AACX,mBAAiB,CAAC,aAAa,eAAe,CAAC,GAAG,aAAa,eAAe,CAAC,CAAC;AAChF,iBAAe,YAAY;AAC3B,iBAAe,YAAY;AAC3B,iBAAe,qBAAqB;AACpC,aAAW;AACX,SAAO,KAAK;AACb;AAEA,SAAS,iBAAiB;AACzB,MAAI,QAAQ,IAAI,UAAU,IAAI;AAC9B,MAAI,QAAQ,IAAM;AACjB,YAAQ,OAAO;AAAA,MACd,KAAK;AACJ,gBAAQ,IAAI,UAAU;AACtB;AAAA,MACD,KAAK;AACJ,gBAAQ,SAAS,UAAU,QAAQ;AACnC,oBAAY;AACZ;AAAA,MACD,KAAK;AACJ,gBAAQ,SAAS,UAAU,QAAQ;AACnC,oBAAY;AACZ;AAAA,IACF;AAAA,EACD;AACA,SAAO;AACR;AAEA,SAAS,aAAa;AACrB,MAAI,eAAe,WAAW;AAC7B,QAAI,aAAa,UAAU,MAAM;AAEhC,YAAM;AACN,aAAO,eAAe,UAAU;AAAA,IACjC,CAAC,KAAK,CAAC;AACP,QAAI,oBAAoB,WAAW,cAAc,CAAC;AAClD,mBAAe,gBAAgB,WAAW;AAC1C,mBAAe,eAAe,eAAe,WAAW;AACxD,QAAI,sBAAsB;AACzB,qBAAe,aAAa,oBAAoB;AAAA;AAEhD,wBAAkB,OAAO,MAAM,mBAAmB,CAAC,GAAG,kBAAkB,MAAM,EAAE,OAAO,iBAAiB,CAAC;AAAA,EAC3G;AACD;AAEA,SAAS,UAAU,UAAU;AAC5B,MAAI,cAAc;AAClB,MAAI,gBAAgB;AACpB,MAAI,sBAAsB;AAC1B,MAAI,sBAAsB;AAC1B,MAAI,oBAAoB;AACxB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,MAAI,oBAAoB;AACxB,MAAI,sBAAsB;AAG1B,MAAI,WAAW,IAAI,WAAW,IAAI,MAAM,GAAG,MAAM,CAAC;AAClD,MAAI,kBAAkB;AACtB,MAAI,eAAe;AACnB,MAAI,sBAAsB;AAC1B,MAAI,QAAQ,SAAS;AACrB,WAAS;AACT,aAAW;AACX,mBAAiB;AACjB,mBAAiB;AACjB,iBAAe;AACf,cAAY;AACZ,YAAU;AACV,iBAAe;AACf,mBAAiB;AACjB,QAAM;AACN,mBAAiB;AACjB,sBAAoB;AACpB,mBAAiB;AACjB,aAAW,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAClE,SAAO;AACR;AACO,SAAS,cAAc;AAC7B,QAAM;AACN,iBAAe;AACf,sBAAoB;AACrB;AAEO,SAAS,aAAa,WAAW;AACvC,oBAAkB,UAAU,GAAG,IAAI,UAAU;AAC9C;AAEO,SAAS,cAAc,QAAQ;AACrC,MAAI,OAAO,WAAY,cAAa,OAAO;AAC3C,MAAI,OAAO,aAAc,gBAAe,OAAO;AAC/C,MAAI,OAAO,cAAe,iBAAgB,OAAO;AAClD;AAEO,IAAM,SAAS,IAAI,MAAM,GAAG;AACnC,SAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,SAAO,CAAC,IAAI,EAAE,OAAO,KAAK,MAAM,QAAQ,IAAI,OAAO;AACpD;AACA,IAAI,iBAAiB,IAAI,QAAQ,EAAE,YAAY,MAAM,CAAC;AAC/C,IAAM,SAAS,eAAe;AAC9B,IAAM,iBAAiB,eAAe;AACtC,IAAM,kBAAkB;AAAA,EAC9B,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,aAAa;AACd;AACO,SAAS,aAAa,eAAe;AAC3C,WAAS,CAAC,IAAI;AACd,MAAI,aAAa,QAAS,QAAQ,CAAC,IAAI,QAAS,IAAM,QAAQ,CAAC,KAAK,CAAE;AACtE,UAAS,aAAa,iBAAiB,gBAAgB,IAAI,MAAM,SAAU,KAAK;AACjF;;;AClxCA,IAAI;AACJ,IAAI;AACH,gBAAc,IAAI,YAAY;AAC/B,SAAS,OAAO;AAAC;AACjB,IAAI;AAAJ,IAAgB;AAChB,IAAME,UAAS,OAAO,eAAe,YAAY,WAAW;AAC5D,IAAM,gBAAgB,OAAOA,YAAW;AACxC,IAAM,oBAAoB,gBAAgBA,QAAO,kBAAkB;AACnE,IAAM,YAAY,gBAAgBA,UAAS;AAC3C,IAAM,iBAAiB;AACvB,IAAM,kBAAkB,gBAAgB,aAAc;AAEtD,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAIC,YAAW;AACf,IAAI;AACJ,IAAIC,kBAAiB;AACrB,IAAM,kBAAkB;AACxB,IAAM,cAAc;AACpB,IAAM,gBAAgB,OAAO,WAAW;AACjC,IAAM,UAAN,cAAsB,QAAQ;AAAA,EACpC,YAAY,SAAS;AACpB,UAAM,OAAO;AACb,SAAK,SAAS;AACd,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAIC;AACJ,cAAU,WAAW,CAAC;AACtB,QAAI,aAAa,UAAU,UAAU,YAAY,SAAS,QAAQF,WAAU,UAAU;AACrF,aAAO,OAAO,UAAU,QAAQA,WAAU,QAAQ;AAAA,IACnD,IAAK,eAAe,YAAY,aAC/B,SAAS,QAAQA,WAAU;AAC1B,aAAO,YAAY,WAAW,QAAQ,OAAO,SAASA,SAAQ,CAAC,EAAE;AAAA,IAClE,IAAI;AAEL,QAAI,UAAU;AACd,QAAI,sBAAsB,QAAQ,cAAc,QAAQ;AACxD,QAAI,sBAAsB,QAAQ;AAClC,QAAI,uBAAuB;AAC1B,4BAAsB,sBAAsB,MAAM;AACnD,QAAI,sBAAsB;AACzB,YAAM,IAAI,MAAM,oCAAoC;AACrD,QAAI,eAAe,QAAQ;AAC3B,QAAI,cAAc;AACjB,4BAAsB;AAAA,IACvB;AACA,QAAI,CAAC,KAAK;AACT,WAAK,aAAa,CAAC;AACpB,QAAI,KAAK;AACR,WAAK,aAAa,KAAK;AACxB,QAAI,sBAAsBG,kBAAiB,eAAe,QAAQ;AAClE,QAAIC;AACJ,QAAI,cAAc;AACjB,MAAAA,yBAAwB,uBAAO,OAAO,IAAI;AAC1C,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK;AACpD,QAAAA,uBAAsB,aAAa,CAAC,CAAC,IAAI;AAAA,MAC1C;AAAA,IACD;AACA,QAAI,oBAAoB,CAAC;AACzB,QAAI,mBAAmB;AACvB,QAAI,uCAAuC;AAE3C,SAAK,YAAY,SAAS,OAAO,eAAe;AAE/C,UAAI,KAAK,WAAW,CAAC,KAAK,SAAS;AAElC,gBAAQ,MAAM,YAAY,MAAM;AAAA,UAC/B,KAAK;AACJ,oBAAQ,MAAM,IAAI,OAAK,KAAK,WAAW,CAAC,CAAC;AACzC;AAAA,QAIF;AAAA,MAED;AACA,aAAO,KAAK,OAAO,OAAO,aAAa;AAAA,IACxC;AAEA,SAAK,SAAS,SAAS,OAAO,eAAe;AAC5C,UAAI,CAAC,QAAQ;AACZ,iBAAS,IAAI,kBAAkB,IAAI;AACnC,qBAAa,IAAI,SAAS,OAAO,QAAQ,GAAG,IAAI;AAChD,QAAAJ,YAAW;AAAA,MACZ;AACA,gBAAU,OAAO,SAAS;AAC1B,UAAI,UAAUA,YAAW,MAAO;AAE/B,iBAAS,IAAI,kBAAkB,OAAO,MAAM;AAC5C,qBAAa,IAAI,SAAS,OAAO,QAAQ,GAAG,OAAO,MAAM;AACzD,kBAAU,OAAO,SAAS;AAC1B,QAAAA,YAAW;AAAA,MACZ,WAAW,kBAAkB;AAC5B,QAAAA,YAAYA,YAAW,IAAK;AAC7B,cAAQA;AACR,UAAI,QAAQ,wBAAwB;AACnC,mBAAW,UAAUA,WAAU,UAAU;AACzC,QAAAA,aAAY;AAAA,MACb;AACA,MAAAE,gBAAe,QAAQ,kBAAkB,oBAAI,IAAI,IAAI;AACrD,UAAI,QAAQ,iBAAiB,OAAO,UAAU,UAAU;AACvD,QAAAD,kBAAiB,CAAC;AAClB,QAAAA,gBAAe,OAAO;AAAA,MACvB;AACC,QAAAA,kBAAiB;AAElB,yBAAmB,QAAQ;AAC3B,UAAI,kBAAkB;AACrB,YAAI,iBAAiB,eAAe;AACnC,cAAI,aAAa,QAAQ,UAAU,KAAK,CAAC;AACzC,kBAAQ,aAAa,mBAAmB,WAAW,cAAc,CAAC;AAClE,kBAAQ,gBAAgB,WAAW;AACnC,cAAII,gBAAe,QAAQ,eAAe,WAAW;AACrD,cAAIA,eAAc;AACjB,YAAAD,yBAAwB,CAAC;AACzB,qBAAS,IAAI,GAAG,IAAIC,cAAa,QAAQ,IAAI,GAAG;AAC/C,cAAAD,uBAAsBC,cAAa,CAAC,CAAC,IAAI;AAAA,UAC3C;AAAA,QACD;AACA,YAAI,yBAAyB,iBAAiB;AAC9C,YAAI,yBAAyB,uBAAuB,CAAC;AACpD,mCAAyB;AAC1B,YAAI,CAAC,iBAAiB,aAAa;AAElC,2BAAiB,cAAc,uBAAO,OAAO,IAAI;AACjD,mBAAS,IAAI,GAAG,IAAI,wBAAwB,KAAK;AAChD,gBAAI,OAAO,iBAAiB,CAAC;AAE7B,gBAAI,CAAC;AACJ;AACD,gBAAI,gBAAgB,aAAa,iBAAiB;AAClD,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC5C,kBAAI,WAAW,aAAa,MAAM;AACjC,2BAAW,aAAa,IAAI;AAC7B,kBAAI,MAAM,KAAK,CAAC;AAChB,+BAAiB,WAAW,GAAG;AAC/B,kBAAI,CAAC,gBAAgB;AACpB,iCAAiB,WAAW,GAAG,IAAI,uBAAO,OAAO,IAAI;AAAA,cACtD;AACA,2BAAa;AAAA,YACd;AACA,uBAAW,aAAa,IAAI,IAAI;AAAA,UACjC;AAAA,QACD;AACA,YAAI,CAAC;AACJ,2BAAiB,SAAS;AAAA,MAC5B;AACA,UAAI;AACH,0BAAkB;AACnB,mBAAa,oBAAoB,CAAC;AAClC,MAAAF,mBAAkBC;AAClB,UAAI,QAAQ,MAAM;AACjB,YAAIE,gBAAe,oBAAI,IAAI;AAC3B,QAAAA,cAAa,SAAS,CAAC;AACvB,QAAAA,cAAa,UAAU;AACvB,QAAAA,cAAa,YAAY,QAAQ,2BAA2BF,yBAAwB,KAAK;AACzF,QAAAE,cAAa,YAAYF,0BAAyB;AAClD,QAAAE,cAAa,uBAAuB;AACpC,8BAAsB,OAAOA,aAAY;AACzC,YAAIA,cAAa,OAAO,SAAS,GAAG;AACnC,iBAAON,WAAU,IAAI;AACrB,iBAAOA,WAAU,IAAI;AACrB,2BAAiB,CAAC;AAClB,cAAI,cAAcM,cAAa;AAC/B,UAAAC,QAAO,WAAW;AAClB,2BAAiB,CAAC;AAClB,2BAAiB,CAAC;AAClB,UAAAJ,mBAAkB,OAAO,OAAOC,0BAAyB,IAAI;AAC7D,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AACnD,YAAAD,iBAAgB,YAAY,CAAC,CAAC,IAAI;AAAA,UACnC;AAAA,QACD;AAAA,MACD;AACA,wBAAkB,gBAAgB;AAClC,UAAI;AACH,YAAI;AACH;AACD,QAAAI,QAAO,KAAK;AACZ,YAAIN,iBAAgB;AACnB,uBAAa,OAAOM,OAAM;AAAA,QAC3B;AACA,gBAAQ,SAASP;AACjB,YAAIE,iBAAgBA,cAAa,aAAa;AAC7C,UAAAF,aAAYE,cAAa,YAAY,SAAS;AAC9C,cAAIF,YAAW;AACd,qBAASA,SAAQ;AAClB,kBAAQ,SAASA;AACjB,cAAI,aAAa,UAAU,OAAO,SAAS,OAAOA,SAAQ,GAAGE,cAAa,WAAW;AACrF,UAAAA,gBAAe;AACf,iBAAO;AAAA,QACR;AACA,YAAI,gBAAgB,mBAAmB;AACtC,iBAAO,QAAQ;AACf,iBAAO,MAAMF;AACb,iBAAO;AAAA,QACR;AACA,eAAO,OAAO,SAAS,OAAOA,SAAQ;AAAA,MACvC,UAAE;AACD,YAAI,kBAAkB;AACrB,cAAI,uCAAuC;AAC1C;AACD,cAAI,iBAAiB,SAAS;AAC7B,6BAAiB,SAAS;AAC3B,cAAI,mBAAmB,KAAO;AAE7B,6BAAiB,cAAc;AAC/B,mDAAuC;AACvC,+BAAmB;AACnB,gBAAI,kBAAkB,SAAS;AAC9B,kCAAoB,CAAC;AAAA,UACvB,WAAW,kBAAkB,SAAS,KAAK,CAAC,cAAc;AACzD,qBAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,IAAI,GAAG,KAAK;AACzD,gCAAkB,CAAC,EAAE,aAAa,IAAI;AAAA,YACvC;AACA,gCAAoB,CAAC;AAAA,UAEtB;AAAA,QACD;AACA,YAAI,mBAAmB,QAAQ,YAAY;AAC1C,cAAI,QAAQ,WAAW,SAAS,qBAAqB;AACpD,oBAAQ,aAAa,QAAQ,WAAW,MAAM,GAAG,mBAAmB;AAAA,UACrE;AAEA,cAAI,eAAe,OAAO,SAAS,OAAOA,SAAQ;AAClD,cAAI,QAAQ,iBAAiB,MAAM;AAClC,mBAAO,QAAQ,OAAO,KAAK;AAC5B,iBAAO;AAAA,QACR;AACA,YAAI,gBAAgB;AACnB,UAAAA,YAAW;AAAA,MACb;AAAA,IACD;AACA,SAAK,0BAA0B,MAAM;AACpC,6BAAuB,oBAAI,IAAI;AAC/B,UAAI,CAACI;AACJ,QAAAA,yBAAwB,uBAAO,OAAO,IAAI;AAC3C,aAAO,CAACI,aAAY;AACnB,YAAI,YAAYA,YAAWA,SAAQ,aAAa;AAChD,YAAIR,YAAW,KAAK,OAAOQ,SAAQ,0BAA0B,KAAK;AAClE,YAAI,CAAC;AACJ,yBAAe,KAAK,eAAe,CAAC;AACrC,iBAAS,CAAE,KAAK,MAAO,KAAK,sBAAsB;AACjD,cAAI,OAAO,QAAQ,WAAW;AAC7B,YAAAJ,uBAAsB,GAAG,IAAIJ;AAC7B,yBAAa,KAAK,GAAG;AACrB,8BAAkB;AAAA,UACnB;AAAA,QACD;AACA,eAAO,KAAK,cAAc,KAAK,iBAAiB,MAAM,OAAO;AAAA,QAAC;AAC9D,+BAAuB;AAAA,MACxB;AAAA,IACD;AACA,UAAMO,UAAS,CAAC,UAAU;AACzB,UAAIP,YAAW;AACd,iBAAS,SAASA,SAAQ;AAE3B,UAAI,OAAO,OAAO;AAClB,UAAI;AACJ,UAAI,SAAS,UAAU;AACtB,YAAIG,kBAAiB;AACpB,cAAI,iBAAiBA,iBAAgB,KAAK;AAC1C,cAAI,kBAAkB,GAAG;AACxB,gBAAI,iBAAiB;AACpB,qBAAOH,WAAU,IAAI,iBAAiB;AAAA,iBAClC;AACJ,qBAAOA,WAAU,IAAI;AACrB,kBAAI,iBAAiB;AACpB,gBAAAO,QAAQ,KAAK,kBAAmB,CAAC;AAAA;AAEjC,gBAAAA,QAAQ,iBAAiB,MAAO,CAAC;AAAA,YACnC;AACA;AAAA,UAeD,WAAW,wBAAwB,CAAC,QAAQ,MAAM;AACjD,gBAAI,SAAS,qBAAqB,IAAI,KAAK;AAC3C,gBAAI;AACH,qBAAO;AAAA;AAEP,mCAAqB,IAAI,OAAO;AAAA,gBAC/B,OAAO;AAAA,cACR,CAAC;AAAA,UACH;AAAA,QACD;AACA,YAAI,YAAY,MAAM;AACtB,YAAIN,mBAAkB,aAAa,KAAK,YAAY,MAAO;AAC1D,eAAKA,gBAAe,QAAQ,aAAa,iBAAiB;AACzD,gBAAI;AACJ,gBAAIQ,aAAYR,gBAAe,CAAC,IAAIA,gBAAe,CAAC,EAAE,SAAS,IAAIA,gBAAe,CAAC,EAAE,SAAS,KAAK;AACnG,gBAAID,YAAWS,YAAW;AACzB,uBAAS,SAAST,YAAWS,SAAQ;AACtC,mBAAOT,WAAU,IAAI;AACrB,mBAAOA,WAAU,IAAI;AACrB,mBAAOA,WAAU,IAAI;AAErB,mBAAOA,WAAU,IAAIC,gBAAe,WAAW,MAAO;AACtD,mBAAOD,WAAU,IAAI;AACrB,uBAAWA,YAAW;AACtB,YAAAA,aAAY;AACZ,gBAAIC,gBAAe,UAAU;AAC5B,2BAAa,OAAOM,OAAM;AAAA,YAC3B;AACA,YAAAN,kBAAiB,CAAC,IAAI,EAAE;AACxB,YAAAA,gBAAe,OAAO;AACtB,YAAAA,gBAAe,WAAW;AAAA,UAC3B;AACA,cAAI,UAAU,YAAY,KAAK,KAAK;AACpC,UAAAA,gBAAe,UAAU,IAAI,CAAC,KAAK;AACnC,iBAAOD,WAAU,IAAI,UAAU,MAAO;AACtC,UAAAO,QAAO,SAAS;AAChB;AAAA,QACD;AACA,YAAI;AAEJ,YAAI,YAAY,IAAM;AACrB,uBAAa;AAAA,QACd,WAAW,YAAY,KAAO;AAC7B,uBAAa;AAAA,QACd,WAAW,YAAY,OAAS;AAC/B,uBAAa;AAAA,QACd,OAAO;AACN,uBAAa;AAAA,QACd;AACA,YAAI,WAAW,YAAY;AAC3B,YAAIP,YAAW,WAAW;AACzB,mBAAS,SAASA,YAAW,QAAQ;AAEtC,YAAI,YAAY,MAAQ,CAAC,YAAY;AACpC,cAAI,GAAG,IAAI,IAAI,cAAcA,YAAW;AACxC,eAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC/B,iBAAK,MAAM,WAAW,CAAC;AACvB,gBAAI,KAAK,KAAM;AACd,qBAAO,aAAa,IAAI;AAAA,YACzB,WAAW,KAAK,MAAO;AACtB,qBAAO,aAAa,IAAI,MAAM,IAAI;AAClC,qBAAO,aAAa,IAAI,KAAK,KAAO;AAAA,YACrC,YACE,KAAK,WAAY,WAChB,KAAK,MAAM,WAAW,IAAI,CAAC,KAAK,WAAY,OAC7C;AACD,mBAAK,UAAY,KAAK,SAAW,OAAO,KAAK;AAC7C;AACA,qBAAO,aAAa,IAAI,MAAM,KAAK;AACnC,qBAAO,aAAa,IAAI,MAAM,KAAK,KAAO;AAC1C,qBAAO,aAAa,IAAI,MAAM,IAAI,KAAO;AACzC,qBAAO,aAAa,IAAI,KAAK,KAAO;AAAA,YACrC,OAAO;AACN,qBAAO,aAAa,IAAI,MAAM,KAAK;AACnC,qBAAO,aAAa,IAAI,MAAM,IAAI,KAAO;AACzC,qBAAO,aAAa,IAAI,KAAK,KAAO;AAAA,YACrC;AAAA,UACD;AACA,mBAAS,cAAcA,YAAW;AAAA,QACnC,OAAO;AACN,mBAAS,WAAW,OAAOA,YAAW,YAAY,QAAQ;AAAA,QAC3D;AAEA,YAAI,SAAS,IAAM;AAClB,iBAAOA,WAAU,IAAI,KAAO;AAAA,QAC7B,WAAW,SAAS,KAAO;AAC1B,cAAI,aAAa,GAAG;AACnB,mBAAO,WAAWA,YAAW,GAAGA,YAAW,GAAGA,YAAW,IAAI,MAAM;AAAA,UACpE;AACA,iBAAOA,WAAU,IAAI;AACrB,iBAAOA,WAAU,IAAI;AAAA,QACtB,WAAW,SAAS,OAAS;AAC5B,cAAI,aAAa,GAAG;AACnB,mBAAO,WAAWA,YAAW,GAAGA,YAAW,GAAGA,YAAW,IAAI,MAAM;AAAA,UACpE;AACA,iBAAOA,WAAU,IAAI;AACrB,iBAAOA,WAAU,IAAI,UAAU;AAC/B,iBAAOA,WAAU,IAAI,SAAS;AAAA,QAC/B,OAAO;AACN,cAAI,aAAa,GAAG;AACnB,mBAAO,WAAWA,YAAW,GAAGA,YAAW,GAAGA,YAAW,IAAI,MAAM;AAAA,UACpE;AACA,iBAAOA,WAAU,IAAI;AACrB,qBAAW,UAAUA,WAAU,MAAM;AACrC,UAAAA,aAAY;AAAA,QACb;AACA,QAAAA,aAAY;AAAA,MACb,WAAW,SAAS,UAAU;AAC7B,YAAI,CAAC,KAAK,kBAAkB,UAAU,MAAM,OAAO;AAElD,cAAI,QAAQ,IAAM;AACjB,mBAAOA,WAAU,IAAI;AAAA,UACtB,WAAW,QAAQ,KAAO;AACzB,mBAAOA,WAAU,IAAI;AACrB,mBAAOA,WAAU,IAAI;AAAA,UACtB,WAAW,QAAQ,OAAS;AAC3B,mBAAOA,WAAU,IAAI;AACrB,mBAAOA,WAAU,IAAI,SAAS;AAC9B,mBAAOA,WAAU,IAAI,QAAQ;AAAA,UAC9B,OAAO;AACN,mBAAOA,WAAU,IAAI;AACrB,uBAAW,UAAUA,WAAU,KAAK;AACpC,YAAAA,aAAY;AAAA,UACb;AAAA,QACD,WAAW,CAAC,KAAK,kBAAkB,SAAS,MAAM,OAAO;AACxD,cAAI,SAAS,KAAO;AACnB,mBAAOA,WAAU,IAAI,KAAO;AAAA,UAC7B,WAAW,SAAS,MAAQ;AAC3B,mBAAOA,WAAU,IAAI;AACrB,mBAAOA,WAAU,IAAI,CAAC;AAAA,UACvB,WAAW,SAAS,QAAU;AAC7B,mBAAOA,WAAU,IAAI;AACrB,uBAAW,UAAUA,WAAU,CAAC,KAAK;AACrC,YAAAA,aAAY;AAAA,UACb,OAAO;AACN,mBAAOA,WAAU,IAAI;AACrB,uBAAW,UAAUA,WAAU,CAAC,KAAK;AACrC,YAAAA,aAAY;AAAA,UACb;AAAA,QACD,OAAO;AACN,cAAI;AACJ,eAAK,aAAa,KAAK,cAAc,KAAK,QAAQ,cAAe,SAAS,aAAa;AACtF,mBAAOA,WAAU,IAAI;AACrB,uBAAW,WAAWA,WAAU,KAAK;AACrC,gBAAI;AACJ,gBAAI,aAAa;AAAA,aAEb,WAAW,QAAQ,QAAS,OAAOA,SAAQ,IAAI,QAAS,IAAM,OAAOA,YAAW,CAAC,KAAK,CAAE,MAAM,MAAO,UAAU;AAClH,cAAAA,aAAY;AACZ;AAAA,YACD;AACC,cAAAA;AAAA,UACF;AACA,iBAAOA,WAAU,IAAI;AACrB,qBAAW,WAAWA,WAAU,KAAK;AACrC,UAAAA,aAAY;AAAA,QACb;AAAA,MACD,WAAW,SAAS,UAAU;AAC7B,YAAI,CAAC;AACJ,iBAAOA,WAAU,IAAI;AAAA,aACjB;AACJ,cAAIE,eAAc;AACjB,gBAAI,UAAUA,cAAa,IAAI,KAAK;AACpC,gBAAI,SAAS;AACZ,qBAAOF,WAAU,IAAI;AACrB,qBAAOA,WAAU,IAAI;AACrB,qBAAOA,WAAU,IAAI;AACrB,kBAAI,CAAC,QAAQ,YAAY;AACxB,oBAAI,cAAcE,cAAa,gBAAgBA,cAAa,cAAc,CAAC;AAC3E,wBAAQ,aAAa,CAAC;AACtB,4BAAY,KAAK,OAAO;AAAA,cACzB;AACA,sBAAQ,WAAW,KAAKF,YAAW,KAAK;AACxC,cAAAA,aAAY;AACZ;AAAA,YACD;AACC,cAAAE,cAAa,IAAI,OAAO,EAAE,QAAQF,YAAW,MAAM,CAAC;AAAA,UACtD;AACA,cAAI,cAAc,MAAM;AACxB,cAAI,gBAAgB,QAAQ;AAC3B,wBAAY,KAAK;AAAA,UAClB,WAAW,gBAAgB,OAAO;AACjC,qBAAS,MAAM;AACf,gBAAI,SAAS,IAAM;AAClB,qBAAOA,WAAU,IAAI,MAAO;AAAA,YAC7B,OAAO;AACN,+BAAiB,MAAM;AAAA,YACxB;AACA,qBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,cAAAO,QAAO,MAAM,CAAC,CAAC;AAAA,YAChB;AAAA,UACD,WAAW,gBAAgB,KAAK;AAC/B,gBAAI,KAAK,gBAAgB,KAAK,qBAAqB,QAAQ,KAAK,kBAAkB;AAEjF,qBAAOP,WAAU,IAAI;AACrB,qBAAOA,WAAU,IAAI;AACrB,qBAAOA,WAAU,IAAI;AAAA,YACtB;AACA,qBAAS,MAAM;AACf,gBAAI,SAAS,IAAM;AAClB,qBAAOA,WAAU,IAAI,MAAO;AAAA,YAC7B,WAAW,SAAS,KAAO;AAC1B,qBAAOA,WAAU,IAAI;AACrB,qBAAOA,WAAU,IAAI;AAAA,YACtB,WAAW,SAAS,OAAS;AAC5B,qBAAOA,WAAU,IAAI;AACrB,qBAAOA,WAAU,IAAI,UAAU;AAC/B,qBAAOA,WAAU,IAAI,SAAS;AAAA,YAC/B,OAAO;AACN,qBAAOA,WAAU,IAAI;AACrB,yBAAW,UAAUA,WAAU,MAAM;AACrC,cAAAA,aAAY;AAAA,YACb;AACA,gBAAI,QAAQ,QAAQ;AACnB,uBAAS,CAAE,KAAK,UAAW,KAAK,OAAO;AACtC,gBAAAO,QAAO,QAAQ,UAAU,GAAG,CAAC;AAC7B,gBAAAA,QAAO,UAAU;AAAA,cAClB;AAAA,YACD,OAAO;AACN,uBAAS,CAAE,KAAK,UAAW,KAAK,OAAO;AACtC,gBAAAA,QAAO,GAAG;AACV,gBAAAA,QAAO,UAAU;AAAA,cAClB;AAAA,YACD;AAAA,UACD,OAAO;AACN,qBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AAClD,kBAAI,iBAAiB,iBAAiB,CAAC;AACvC,kBAAI,iBAAiB,gBAAgB;AACpC,oBAAI,YAAY,WAAW,CAAC;AAC5B,oBAAI,MAAM,UAAU;AACpB,oBAAI,OAAO;AACV,wBAAM,UAAU,UAAU,UAAU,OAAO,KAAK,MAAM,KAAK;AAC5D,oBAAI,MAAM,IAAM;AACf,yBAAOP,WAAU,IAAI,MAAO;AAAA,gBAC7B,WAAW,MAAM,KAAO;AACvB,yBAAOA,WAAU,IAAI;AACrB,yBAAOA,WAAU,IAAI;AAAA,gBACtB,WAAW,MAAM,OAAS;AACzB,yBAAOA,WAAU,IAAI;AACrB,yBAAOA,WAAU,IAAI,OAAO;AAC5B,yBAAOA,WAAU,IAAI,MAAM;AAAA,gBAC5B,WAAW,MAAM,IAAI;AACpB,yBAAOA,WAAU,IAAI;AACrB,6BAAW,UAAUA,WAAU,GAAG;AAClC,kBAAAA,aAAY;AAAA,gBACb;AACA,0BAAU,OAAO,KAAK,MAAM,OAAOO,SAAQ,QAAQ;AACnD;AAAA,cACD;AAAA,YACD;AACA,gBAAI,MAAM,OAAO,QAAQ,GAAG;AAC3B,kBAAI,iBAAiB;AACpB,oBAAI,QAAQ,IAAI,MAAM,2CAA2C;AACjE,sBAAM,qBAAqB;AAC3B,sBAAM;AAAA,cACP;AACA,qBAAOP,WAAU,IAAI;AACrB,uBAAS,SAAS,OAAO;AACxB,gBAAAO,QAAO,KAAK;AAAA,cACb;AACA,qBAAOP,WAAU,IAAI;AACrB;AAAA,YACD;AACA,gBAAI,MAAM,OAAO,aAAa,KAAK,OAAO,KAAK,GAAG;AACjD,kBAAI,QAAQ,IAAI,MAAM,gDAAgD;AACtE,oBAAM,qBAAqB;AAC3B,oBAAM;AAAA,YACP;AACA,gBAAI,KAAK,aAAa,MAAM,QAAQ;AACnC,oBAAM,OAAO,MAAM,OAAO;AAE1B,kBAAI,SAAS;AACZ,uBAAOO,QAAO,IAAI;AAAA,YACpB;AAGA,wBAAY,KAAK;AAAA,UAClB;AAAA,QACD;AAAA,MACD,WAAW,SAAS,WAAW;AAC9B,eAAOP,WAAU,IAAI,QAAQ,MAAO;AAAA,MACrC,WAAW,SAAS,UAAU;AAC7B,YAAI,QAAS,OAAO,CAAC,KAAG,OAAO,EAAE,KAAM,SAAS,GAAG;AAElD,iBAAOA,WAAU,IAAI;AACrB,qBAAW,aAAaA,WAAU,KAAK;AAAA,QACxC,WAAW,QAAQ,EAAE,OAAO,CAAC,KAAG,OAAO,EAAE,MAAM,QAAQ,GAAG;AAEzD,iBAAOA,WAAU,IAAI;AACrB,qBAAW,aAAaA,WAAU,CAAC,QAAQ,OAAO,CAAC,CAAC;AAAA,QACrD,OAAO;AAEN,cAAI,KAAK,oBAAoB;AAC5B,mBAAOA,WAAU,IAAI;AACrB,uBAAW,WAAWA,WAAU,OAAO,KAAK,CAAC;AAAA,UAC9C,OAAO;AACN,gBAAI,SAAS,OAAO,CAAC;AACpB,qBAAOA,WAAU,IAAI;AAAA,iBACjB;AACJ,qBAAOA,WAAU,IAAI;AACrB,sBAAQ,OAAO,EAAE,IAAI;AAAA,YACtB;AACA,gBAAI,QAAQ,CAAC;AACb,mBAAO,OAAO;AACb,oBAAM,KAAK,OAAO,QAAQ,OAAO,GAAI,CAAC,CAAC;AACvC,wBAAU,OAAO,CAAC;AAAA,YACnB;AACA,wBAAY,IAAI,WAAW,MAAM,QAAQ,CAAC,GAAG,QAAQ;AACrD;AAAA,UACD;AAAA,QACD;AACA,QAAAA,aAAY;AAAA,MACb,WAAW,SAAS,aAAa;AAChC,eAAOA,WAAU,IAAI;AAAA,MACtB,OAAO;AACN,cAAM,IAAI,MAAM,mBAAmB,IAAI;AAAA,MACxC;AAAA,IACD;AAEA,UAAM,cAAc,KAAK,eAAe,QAAQ,KAAK,kBAAkB,CAAC,WAAW;AAElF,UAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,UAAI,OAAO,OAAO,OAAO,MAAM;AAC/B,UAAI,SAAS,KAAK;AAClB,UAAI,SAAS,IAAM;AAClB,eAAOA,WAAU,IAAI,MAAO;AAAA,MAC7B,WAAW,SAAS,KAAO;AAC1B,eAAOA,WAAU,IAAI;AACrB,eAAOA,WAAU,IAAI;AAAA,MACtB,WAAW,SAAS,OAAS;AAC5B,eAAOA,WAAU,IAAI;AACrB,eAAOA,WAAU,IAAI,UAAU;AAC/B,eAAOA,WAAU,IAAI,SAAS;AAAA,MAC/B,OAAO;AACN,eAAOA,WAAU,IAAI;AACrB,mBAAW,UAAUA,WAAU,MAAM;AACrC,QAAAA,aAAY;AAAA,MACb;AACA,UAAI;AACJ,UAAI,QAAQ,QAAQ;AACnB,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAAO,QAAO,QAAQ,UAAU,KAAK,CAAC,CAAC,CAAC;AACjC,UAAAA,QAAO,KAAK,CAAC,CAAC;AAAA,QACf;AAAA,MACD,OAAO;AACN,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,UAAAA,QAAO,KAAK,CAAC,CAAC;AACd,UAAAA,QAAO,KAAK,CAAC,CAAC;AAAA,QACf;AAAA,MACD;AAAA,IACD,IACA,CAAC,WAAW;AACX,aAAOP,WAAU,IAAI;AACrB,UAAI,eAAeA,YAAW;AAC9B,MAAAA,aAAY;AACZ,UAAI,OAAO;AACX,UAAI,QAAQ,QAAQ;AACnB,iBAAS,OAAO,OAAQ,KAAI,OAAO,OAAO,mBAAmB,cAAc,OAAO,eAAe,GAAG,GAAG;AACtG,UAAAO,QAAO,QAAQ,UAAU,GAAG,CAAC;AAC7B,UAAAA,QAAO,OAAO,GAAG,CAAC;AAClB;AAAA,QACD;AAAA,MACD,OAAO;AACN,iBAAS,OAAO,OAAQ,KAAI,OAAO,OAAO,mBAAmB,cAAc,OAAO,eAAe,GAAG,GAAG;AACrG,UAAAA,QAAO,GAAG;AACV,UAAAA,QAAO,OAAO,GAAG,CAAC;AACnB;AAAA,QACD;AAAA,MACD;AACA,aAAO,iBAAiB,KAAK,IAAI,QAAQ;AACzC,aAAO,eAAe,KAAK,IAAI,OAAO;AAAA,IACvC,IACA,CAAC,QAAQ,eAAe;AACvB,UAAI,gBAAgB,aAAa,WAAW,gBAAgB,WAAW,cAAc,uBAAO,OAAO,IAAI;AACvG,UAAI,iBAAiB;AACrB,UAAI,SAAS;AACb,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,QAAQ;AAChB,eAAO,OAAO,KAAK,MAAM,EAAE,IAAI,OAAK,KAAK,UAAU,CAAC,CAAC;AACrD,iBAAS,KAAK;AACd,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,cAAI,MAAM,KAAK,CAAC;AAChB,2BAAiB,WAAW,GAAG;AAC/B,cAAI,CAAC,gBAAgB;AACpB,6BAAiB,WAAW,GAAG,IAAI,uBAAO,OAAO,IAAI;AACrD;AAAA,UACD;AACA,uBAAa;AAAA,QACd;AAAA,MACD,OAAO;AACN,iBAAS,OAAO,OAAQ,KAAI,OAAO,OAAO,mBAAmB,cAAc,OAAO,eAAe,GAAG,GAAG;AACtG,2BAAiB,WAAW,GAAG;AAC/B,cAAI,CAAC,gBAAgB;AACpB,gBAAI,WAAW,aAAa,IAAI,SAAU;AACzC,+BAAiB,WAAW,aAAa,IAAI;AAAA,YAC9C;AACA,6BAAiB,WAAW,GAAG,IAAI,uBAAO,OAAO,IAAI;AACrD;AAAA,UACD;AACA,uBAAa;AACb;AAAA,QACD;AAAA,MACD;AACA,UAAI,WAAW,WAAW,aAAa;AACvC,UAAI,aAAa,QAAW;AAC3B,oBAAY;AACZ,eAAOP,WAAU,IAAI;AACrB,eAAOA,WAAU,IAAK,YAAY,IAAK;AACvC,eAAOA,WAAU,IAAI,WAAW;AAAA,MACjC,OAAO;AACN,YAAI,CAAC;AACJ,iBAAO,WAAW,aAAa,WAAW,WAAW,OAAO,KAAK,MAAM;AACxE,YAAI,mBAAmB,QAAW;AACjC,qBAAW,WAAW;AACtB,cAAI,CAAC,UAAU;AACd,uBAAW;AACX,uBAAW,SAAS;AAAA,UACrB;AACA,cAAI,YAAY,gBAAgB;AAC/B,uBAAW,UAAU,WAAW,uBAAuB;AAAA,UACxD;AAAA,QACD,OAAO;AACN,qBAAW;AAAA,QACZ;AACA,mBAAW,QAAQ,IAAI;AACvB,YAAI,WAAW,qBAAqB;AACnC,iBAAOA,WAAU,IAAI;AACrB,iBAAOA,WAAU,IAAK,YAAY,IAAK;AACvC,iBAAOA,WAAU,IAAI,WAAW;AAChC,uBAAa,WAAW;AACxB,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,gBAAI,WAAW,aAAa,MAAM,UAAc,WAAW,aAAa,IAAI;AAC3E,yBAAW,aAAa,IAAI;AAC7B,yBAAa,WAAW,KAAK,CAAC,CAAC;AAAA,UAChC;AACA,qBAAW,aAAa,IAAI,WAAW;AACvC,4BAAkB;AAAA,QACnB,OAAO;AACN,qBAAW,aAAa,IAAI;AAC5B,qBAAW,UAAUA,WAAU,UAAU;AACzC,UAAAA,aAAY;AACZ,cAAI;AACH,gCAAoB,uCAAuC;AAE5D,cAAI,kBAAkB,UAAU,iBAAiB;AAChD,8BAAkB,MAAM,EAAE,aAAa,IAAI;AAC5C,4BAAkB,KAAK,UAAU;AACjC,2BAAiB,SAAS,CAAC;AAC3B,UAAAO,QAAO,QAAS,QAAQ;AACxB,UAAAA,QAAO,IAAI;AACX,cAAI,WAAY;AAChB,mBAAS,OAAO;AACf,gBAAI,OAAO,OAAO,mBAAmB,cAAc,OAAO,eAAe,GAAG;AAC3E,cAAAA,QAAO,OAAO,GAAG,CAAC;AACpB;AAAA,QACD;AAAA,MACD;AACA,UAAI,SAAS,IAAM;AAClB,eAAOP,WAAU,IAAI,MAAO;AAAA,MAC7B,OAAO;AACN,yBAAiB,MAAM;AAAA,MACxB;AACA,UAAI,WAAY;AAChB,eAAS,OAAO;AACf,YAAI,OAAO,OAAO,mBAAmB,cAAc,OAAO,eAAe,GAAG;AAC3E,UAAAO,QAAO,OAAO,GAAG,CAAC;AAAA,IACrB;AACA,UAAM,WAAW,CAAC,QAAQ;AACzB,UAAI;AACJ,UAAI,MAAM,UAAW;AAEpB,YAAK,MAAM,QAAS;AACnB,gBAAM,IAAI,MAAM,yDAAyD;AAC1E,kBAAU,KAAK;AAAA,UAAI;AAAA,UAClB,KAAK,MAAM,KAAK,KAAK,MAAM,UAAU,MAAM,WAAY,OAAO,IAAI,OAAQ,IAAI,IAAM,IAAI;AAAA,QAAM;AAAA,MAChG;AACC,mBAAY,KAAK,IAAK,MAAM,SAAU,GAAG,OAAO,SAAS,CAAC,KAAK,MAAM,KAAM;AAC5E,UAAI,YAAY,IAAI,kBAAkB,OAAO;AAC7C,mBAAa,IAAI,SAAS,UAAU,QAAQ,GAAG,OAAO;AACtD,UAAI,OAAO;AACV,eAAO,KAAK,WAAW,GAAG,OAAO,GAAG;AAAA;AAEpC,kBAAU,IAAI,OAAO,MAAM,OAAO,GAAG,CAAC;AACvC,MAAAP,aAAY;AACZ,cAAQ;AACR,gBAAU,UAAU,SAAS;AAC7B,aAAO,SAAS;AAAA,IACjB;AACA,QAAI,iBAAiB;AACrB,QAAI,0BAA0B;AAC9B,SAAK,mBAAmB,SAAS,OAAOQ,UAAS;AAChD,aAAO,cAAc,OAAOA,UAAS,sBAAsB;AAAA,IAC5D;AACA,SAAK,wBAAwB,SAAS,OAAOA,UAAS;AACrD,aAAO,cAAc,OAAOA,UAAS,2BAA2B;AAAA,IACjE;AAEA,cAAU,uBAAuB,QAAQ,mBAAmB,eAAe;AAC1E,UAAI,cAAc,OAAO;AACzB,UAAI,gBAAgB,QAAQ;AAC3B,YAAI,aAAa,QAAQ,eAAe;AACxC,YAAI;AACH,sBAAY,QAAQ,IAAI;AAAA;AAExB,4BAAkB,OAAO,KAAK,MAAM,EAAE,QAAQ,GAAI;AACnD,iBAAS,OAAO,QAAQ;AACvB,cAAI,QAAQ,OAAO,GAAG;AACtB,cAAI,CAAC,WAAY,CAAAD,QAAO,GAAG;AAC3B,cAAI,SAAS,OAAO,UAAU,UAAU;AACvC,gBAAI,kBAAkB,GAAG;AACxB,qBAAO,uBAAuB,OAAO,kBAAkB,GAAG,CAAC;AAAA;AAE3D,qBAAO,UAAU,OAAO,mBAAmB,GAAG;AAAA,UAChD,MAAO,CAAAA,QAAO,KAAK;AAAA,QACpB;AAAA,MACD,WAAW,gBAAgB,OAAO;AACjC,YAAI,SAAS,OAAO;AACpB,yBAAiB,MAAM;AACvB,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,UAAU,OAAO,UAAU,YAAYP,YAAW,QAAQ,iBAAiB;AAC9E,gBAAI,kBAAkB;AACrB,qBAAO,uBAAuB,OAAO,kBAAkB,OAAO;AAAA;AAE9D,qBAAO,UAAU,OAAO,mBAAmB,SAAS;AAAA,UACtD,MAAO,CAAAO,QAAO,KAAK;AAAA,QACpB;AAAA,MACD,WAAW,OAAO,OAAO,QAAQ,KAAK,CAAC,OAAO,QAAQ;AACrD,eAAOP,WAAU,IAAI;AACrB,iBAAS,SAAS,QAAQ;AACzB,cAAI,UAAU,OAAO,UAAU,YAAYA,YAAW,QAAQ,iBAAiB;AAC9E,gBAAI,kBAAkB;AACrB,qBAAO,uBAAuB,OAAO,kBAAkB,OAAO;AAAA;AAE9D,qBAAO,UAAU,OAAO,mBAAmB,SAAS;AAAA,UACtD,MAAO,CAAAO,QAAO,KAAK;AAAA,QACpB;AACA,eAAOP,WAAU,IAAI;AAAA,MACtB,WAAW,OAAO,MAAM,GAAE;AACzB,0BAAkB,OAAO,MAAM,EAAI;AACnC,cAAM,OAAO,SAAS,OAAOA,SAAQ;AACrC,cAAM;AACN,wBAAgB;AAAA,MACjB,WAAW,OAAO,OAAO,aAAa,GAAG;AACxC,eAAOA,WAAU,IAAI;AACrB,cAAM,OAAO,SAAS,OAAOA,SAAQ;AACrC,cAAM;AACN,wBAAgB;AAChB,eAAOA,WAAU,IAAI;AAAA,MACtB,OAAO;AACN,QAAAO,QAAO,MAAM;AAAA,MACd;AACA,UAAI,iBAAiBP,YAAW,MAAO,OAAM,OAAO,SAAS,OAAOA,SAAQ;AAAA,eACnEA,YAAW,QAAQ,gBAAgB;AAC3C,cAAM,OAAO,SAAS,OAAOA,SAAQ;AACrC,wBAAgB;AAAA,MACjB;AAAA,IACD;AACA,cAAU,UAAU,OAAO,mBAAmB,KAAK;AAClD,UAAI,UAAUA,YAAW;AACzB,UAAI;AACH,QAAAO,QAAO,KAAK;AACZ,YAAIP,YAAW,QAAQ,gBAAgB;AACtC,gBAAM,OAAO,SAAS,OAAOA,SAAQ;AACrC,0BAAgB;AAAA,QACjB;AAAA,MACD,SAAS,OAAO;AACf,YAAI,MAAM,oBAAoB;AAC7B,4BAAkB,GAAG,IAAI,CAAC;AAC1B,UAAAA,YAAW,QAAQ;AACnB,iBAAO,uBAAuB,KAAK,MAAM,OAAO,kBAAkB,GAAG,CAAC;AAAA,QACvE,MAAO,OAAM;AAAA,MACd;AAAA,IACD;AACA,aAAS,kBAAkB;AAC1B,uBAAiB;AACjB,cAAQ,OAAO,MAAM,iBAAiB;AAAA,IACvC;AACA,aAAS,cAAc,OAAOQ,UAAS,gBAAgB;AACtD,UAAIA,YAAWA,SAAQ;AACtB,yBAAiB,0BAA0BA,SAAQ;AAAA;AAEnD,yBAAiB;AAClB,UAAI,SAAS,OAAO,UAAU,UAAU;AACvC,gBAAQ,OAAO,MAAM,iBAAiB;AACtC,eAAO,eAAe,OAAO,QAAQ,sBAAsB,QAAQ,oBAAoB,CAAC,IAAI,IAAI;AAAA,MACjG;AACA,aAAO,CAAC,QAAQ,OAAO,KAAK,CAAC;AAAA,IAC9B;AAEA,oBAAgB,4BAA4B,OAAO,mBAAmB;AACrE,eAAS,gBAAgB,uBAAuB,OAAO,mBAAmB,IAAI,GAAG;AAChF,YAAI,cAAc,aAAa;AAC/B,YAAI,gBAAgB,aAAa,gBAAgB;AAChD,gBAAM;AAAA,iBACE,OAAO,YAAY,GAAG;AAC9B,cAAI,SAAS,aAAa,OAAO,EAAE,UAAU;AAC7C,cAAI;AACJ,iBAAO,EAAE,OAAO,MAAM,OAAO,KAAK,GAAG,MAAM;AAC1C,kBAAM,KAAK;AAAA,UACZ;AAAA,QACD,WAAW,aAAa,OAAO,aAAa,GAAG;AAC9C,yBAAe,cAAc,cAAc;AAC1C,4BAAgB;AAChB,gBAAI;AACH,qBAAO,4BAA4B,YAAY,kBAAkB,UAAU,kBAAkB,QAAQ,CAAC,EAAE;AAAA,gBACpG,OAAM,QAAQ,OAAO,UAAU;AAAA,UACrC;AAAA,QACD,OAAO;AACN,gBAAM;AAAA,QACP;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,UAAU,QAAQ;AAEjB,aAAS;AACT,iBAAa,IAAI,SAAS,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAC7E,IAAAR,YAAW;AAAA,EACZ;AAAA,EACA,kBAAkB;AACjB,QAAI,KAAK;AACR,WAAK,aAAa,CAAC;AACpB,QAAI,KAAK;AACR,WAAK,eAAe;AAAA,EACtB;AAAA,EACA,mBAAmB;AAClB,QAAI,cAAc,KAAK,iBAAiB;AACxC,SAAK,gBAAgB,cAAc;AACnC,QAAI,iBAAiB,KAAK,WAAW,MAAM,CAAC;AAC5C,QAAI,aAAa,IAAI,WAAW,gBAAgB,KAAK,cAAc,KAAK,aAAa;AACrF,QAAI,cAAc,KAAK;AAAA,MAAW;AAAA,MAChC,qBAAmB,kBAAkB,eAAe,WAAW,MAAM;AAAA,IAAW;AAClF,QAAI,gBAAgB,OAAO;AAE1B,mBAAa,KAAK,UAAU,KAAK,CAAC;AAClC,WAAK,aAAa,WAAW,cAAc,CAAC;AAC5C,WAAK,eAAe,WAAW;AAC/B,WAAK,gBAAgB,WAAW;AAChC,WAAK,WAAW,SAAS,KAAK,WAAW;AAAA,IAC1C,OAAO;AAEN,qBAAe,QAAQ,CAAC,WAAW,MAAM,KAAK,WAAW,CAAC,IAAI,SAAS;AAAA,IACxE;AAEA,WAAO;AAAA,EACR;AACD;AACA,SAAS,kBAAkB,QAAQ,YAAY;AAC9C,MAAI,SAAS;AACZ,WAAOA,WAAU,IAAI,aAAa;AAAA,WAC1B,SAAS,KAAO;AACxB,WAAOA,WAAU,IAAI,aAAa;AAClC,WAAOA,WAAU,IAAI;AAAA,EACtB,WAAW,SAAS,OAAS;AAC5B,WAAOA,WAAU,IAAI,aAAa;AAClC,WAAOA,WAAU,IAAI,UAAU;AAC/B,WAAOA,WAAU,IAAI,SAAS;AAAA,EAC/B,OAAO;AACN,WAAOA,WAAU,IAAI,aAAa;AAClC,eAAW,UAAUA,WAAU,MAAM;AACrC,IAAAA,aAAY;AAAA,EACb;AAED;AACA,IAAM,aAAN,MAAiB;AAAA,EAChB,YAAY,YAAY,QAAQ,SAAS;AACxC,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,UAAU;AAAA,EAChB;AACD;AAEA,SAAS,iBAAiB,QAAQ;AACjC,MAAI,SAAS;AACZ,WAAOA,WAAU,IAAI,MAAO;AAAA,WACpB,SAAS,KAAO;AACxB,WAAOA,WAAU,IAAI;AACrB,WAAOA,WAAU,IAAI;AAAA,EACtB,WAAW,SAAS,OAAS;AAC5B,WAAOA,WAAU,IAAI;AACrB,WAAOA,WAAU,IAAI,UAAU;AAC/B,WAAOA,WAAU,IAAI,SAAS;AAAA,EAC/B,OAAO;AACN,WAAOA,WAAU,IAAI;AACrB,eAAW,UAAUA,WAAU,MAAM;AACrC,IAAAA,aAAY;AAAA,EACb;AACD;AAEA,IAAM,kBAAkB,OAAO,SAAS,cAAc,WAAU;AAAC,IAAI;AACrE,SAAS,OAAO,QAAQ;AACvB,MAAI,kBAAkB;AACrB,WAAO;AACR,MAAI,MAAM,OAAO,OAAO,WAAW;AACnC,SAAO,QAAQ,UAAU,QAAQ;AAClC;AACA,SAAS,sBAAsB,OAAOM,eAAc;AACnD,UAAO,OAAO,OAAO;AAAA,IACpB,KAAK;AACJ,UAAI,MAAM,SAAS,GAAG;AACrB,YAAIA,cAAa,UAAU,KAAK,IAAI,MAAMA,cAAa,OAAO,UAAUA,cAAa;AACpF;AACD,YAAI,eAAeA,cAAa,IAAI,KAAK;AACzC,YAAI,cAAc;AACjB,cAAI,EAAE,aAAa,SAAS,GAAG;AAC9B,YAAAA,cAAa,OAAO,KAAK,KAAK;AAAA,UAC/B;AAAA,QACD,OAAO;AACN,UAAAA,cAAa,IAAI,OAAO;AAAA,YACvB,OAAO;AAAA,UACR,CAAC;AACD,cAAIA,cAAa,sBAAsB;AACtC,gBAAI,SAASA,cAAa,qBAAqB,IAAI,KAAK;AACxD,gBAAI;AACH,qBAAO;AAAA;AAEP,cAAAA,cAAa,qBAAqB,IAAI,OAAO;AAAA,gBAC5C,OAAO;AAAA,cACR,CAAC;AAAA,UACH;AAAA,QACD;AAAA,MACD;AACA;AAAA,IACD,KAAK;AACJ,UAAI,OAAO;AACV,YAAI,iBAAiB,OAAO;AAC3B,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC7C,kCAAsB,MAAM,CAAC,GAAGA,aAAY;AAAA,UAC7C;AAAA,QAED,OAAO;AACN,cAAI,cAAc,CAACA,cAAa,QAAQ;AACxC,mBAAS,OAAO,OAAO;AACtB,gBAAI,MAAM,eAAe,GAAG,GAAG;AAC9B,kBAAI;AACH,sCAAsB,KAAKA,aAAY;AACxC,oCAAsB,MAAM,GAAG,GAAGA,aAAY;AAAA,YAC/C;AAAA,UACD;AAAA,QACD;AAAA,MACD;AACA;AAAA,IACD,KAAK;AAAY,cAAQ,IAAI,KAAK;AAAA,EACnC;AACD;AACA,IAAMI,yBAAwB,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK;AAChF,mBAAmB;AAAA,EAAE;AAAA,EAAM;AAAA,EAAK;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAK;AAAA,EACnD;AAAA,EAAY;AAAA,EAAmB;AAAA,EAAa;AAAA,EAC5C,OAAO,kBAAkB,cAAc,WAAW;AAAA,EAAC,IAAI;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAY;AAAA,EAC9F,OAAO,iBAAiB,cAAc,WAAW;AAAA,EAAC,IAAI;AAAA,EACtD;AAAA,EAAc;AAAA,EAAc;AAAW;AAGxC,aAAa;AAAA,EAAC;AAAA;AAAA,IACb,KAAK;AAAA,IACL,OAAO,MAAMH,SAAQ;AACpB,UAAI,UAAU,KAAK,QAAQ,IAAI;AAC/B,WAAK,KAAK,kBAAkB,KAAK,gBAAgB,MAAM,MAAM,WAAW,KAAK,UAAU,YAAa;AAEnG,eAAOP,WAAU,IAAI;AACrB,mBAAW,UAAUA,WAAU,OAAO;AACtC,QAAAA,aAAY;AAAA,MACb,OAAO;AAEN,eAAOA,WAAU,IAAI;AACrB,mBAAW,WAAWA,WAAU,OAAO;AACvC,QAAAA,aAAY;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAAA,EAAG;AAAA;AAAA,IACF,KAAK;AAAA;AAAA,IACL,OAAO,KAAKO,SAAQ;AACnB,UAAI,QAAQ,MAAM,KAAK,GAAG;AAC1B,MAAAA,QAAO,KAAK;AAAA,IACb;AAAA,EACD;AAAA,EAAG;AAAA;AAAA,IACF,KAAK;AAAA;AAAA,IACL,OAAO,OAAOA,SAAQ;AACrB,MAAAA,QAAO,CAAE,MAAM,MAAM,MAAM,OAAQ,CAAC;AAAA,IACrC;AAAA,EACD;AAAA,EAAG;AAAA;AAAA,IACF,KAAK;AAAA;AAAA,IACL,OAAO,OAAOA,SAAQ;AACrB,MAAAA,QAAO,CAAE,UAAU,MAAM,QAAQ,MAAM,KAAM,CAAC;AAAA,IAC/C;AAAA,EACD;AAAA,EAAG;AAAA;AAAA,IACF,OAAO,KAAK;AACX,aAAO,IAAI;AAAA,IACZ;AAAA,IACA,OAAO,KAAKA,SAAQ;AACnB,MAAAA,QAAO,IAAI,KAAK;AAAA,IACjB;AAAA,EACD;AAAA,EAAG;AAAA;AAAA,IACF,OAAO,aAAaA,SAAQ,UAAU;AACrC,kBAAY,aAAa,QAAQ;AAAA,IAClC;AAAA,EACD;AAAA,EAAG;AAAA;AAAA,IACF,OAAO,YAAY;AAClB,UAAI,WAAW,gBAAgB,YAAY;AAC1C,YAAI,KAAK,iBAAiB,iBAAiB,KAAK,kBAAkB;AACjE,iBAAO;AAAA,MACT;AAAA,IACD;AAAA,IACA,OAAO,YAAYA,SAAQ,UAAU;AACpC,kBAAY,YAAY,QAAQ;AAAA,IACjC;AAAA,EACD;AAAA,EACC,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACvB,kBAAkB,IAAI,CAAC;AAAA,EACxB;AAAA,IACC,OAAO,YAAYA,SAAQ;AAC1B,UAAID,gBAAe,WAAW,gBAAgB,CAAC;AAC/C,UAAI,mBAAmB,WAAW,cAAc,CAAC;AACjD,UAAIA,cAAa,OAAO,SAAS,GAAG;AACnC,eAAON,WAAU,IAAI;AACrB,eAAOA,WAAU,IAAI;AACrB,yBAAiB,CAAC;AAClB,YAAI,cAAcM,cAAa;AAC/B,QAAAC,QAAO,WAAW;AAClB,yBAAiB,CAAC;AAClB,yBAAiB,CAAC;AAClB,0BAAkB,OAAO,OAAO,yBAAyB,IAAI;AAC7D,iBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AACnD,0BAAgB,YAAY,CAAC,CAAC,IAAI;AAAA,QACnC;AAAA,MACD;AACA,UAAI,kBAAkB;AACrB,mBAAW,UAAUP,WAAU,UAAU;AACzC,QAAAA,aAAY;AACZ,YAAI,cAAc,iBAAiB,MAAM,CAAC;AAC1C,oBAAY,QAAQ,KAAM;AAC1B,oBAAY,KAAK,IAAI,IAAI,WAAW,SAAS,UAAU,CAAC;AACxD,QAAAO,QAAO,WAAW;AAAA,MACnB;AACC,QAAAA,QAAO,IAAI,IAAI,WAAW,SAAS,UAAU,CAAC;AAAA,IAC/C;AAAA,EACD;AAAC;AACF,SAAS,kBAAkB,KAAK,MAAM;AACrC,MAAI,CAACG,0BAAyB,OAAO;AACpC,WAAO;AACR,SAAO;AAAA,IACN;AAAA,IACA,QAAQ,SAAS,eAAe,YAAYH,SAAQ;AACnD,UAAI,SAAS,WAAW;AACxB,UAAI,SAAS,WAAW,cAAc;AACtC,UAAI,SAAS,WAAW,UAAU;AAClC,MAAAA,QAAO,gBAAgBI,QAAO,KAAK,QAAQ,QAAQ,MAAM,IACxD,IAAI,WAAW,QAAQ,QAAQ,MAAM,CAAC;AAAA,IACxC;AAAA,EACD;AACD;AACA,SAAS,YAAY,QAAQ,UAAU;AACtC,MAAI,SAAS,OAAO;AACpB,MAAI,SAAS,IAAM;AAClB,WAAOX,WAAU,IAAI,KAAO;AAAA,EAC7B,WAAW,SAAS,KAAO;AAC1B,WAAOA,WAAU,IAAI;AACrB,WAAOA,WAAU,IAAI;AAAA,EACtB,WAAW,SAAS,OAAS;AAC5B,WAAOA,WAAU,IAAI;AACrB,WAAOA,WAAU,IAAI,UAAU;AAC/B,WAAOA,WAAU,IAAI,SAAS;AAAA,EAC/B,OAAO;AACN,WAAOA,WAAU,IAAI;AACrB,eAAW,UAAUA,WAAU,MAAM;AACrC,IAAAA,aAAY;AAAA,EACb;AACA,MAAIA,YAAW,UAAU,OAAO,QAAQ;AACvC,aAASA,YAAW,MAAM;AAAA,EAC3B;AAGA,SAAO,IAAI,OAAO,SAAS,SAAS,IAAI,WAAW,MAAM,GAAGA,SAAQ;AACpE,EAAAA,aAAY;AACb;AAEA,SAAS,UAAU,YAAY,aAAa;AAE3C,MAAI;AACJ,MAAI,iBAAiB,YAAY,SAAS;AAC1C,MAAI,UAAU,WAAW,SAAS;AAClC,cAAY,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE;AACvD,WAAS,KAAK,GAAG,KAAK,YAAY,QAAQ,MAAM;AAC/C,QAAI,UAAU,YAAY,EAAE;AAC5B,YAAQ,KAAK;AACb,aAASA,aAAY,QAAQ,YAAY;AACxC,iBAAWA,WAAU,IAAI,MAAM;AAC/B,iBAAWA,SAAQ,IAAI,KAAK;AAAA,IAC7B;AAAA,EACD;AACA,SAAO,SAAS,YAAY,IAAI,GAAG;AAClC,QAAI,SAAS,OAAO;AACpB,eAAW,WAAW,SAAS,gBAAgB,QAAQ,OAAO;AAC9D,sBAAkB;AAClB,QAAIA,YAAW,SAAS;AACxB,eAAWA,WAAU,IAAI;AACzB,eAAWA,WAAU,IAAI;AACzB,cAAU;AAAA,EACX;AACA,SAAO;AACR;AACA,SAAS,aAAa,OAAOO,SAAQ;AACpC,aAAW,UAAUN,gBAAe,WAAW,OAAOD,YAAWC,gBAAe,WAAW,QAAQ,CAAC;AACpG,MAAI,eAAeA;AACnB,EAAAA,kBAAiB;AACjB,EAAAM,QAAO,aAAa,CAAC,CAAC;AACtB,EAAAA,QAAO,aAAa,CAAC,CAAC;AACvB;AAEO,SAASK,cAAa,WAAW;AACvC,MAAI,UAAU,OAAO;AACpB,QAAI,CAAC,UAAU;AACd,YAAM,IAAI,MAAM,kCAAkC;AACnD,qBAAiB,QAAQ,UAAU,KAAK;AACxC,eAAW,QAAQ,SAAS;AAAA,EAC7B;AACA,eAAmB,SAAS;AAC7B;AACA,IAAI,iBAAiB,IAAI,QAAQ,EAAE,YAAY,MAAM,CAAC;AAC/C,IAAM,SAAS,eAAe;AAC9B,IAAM,mBAAmB,eAAe;AACxC,IAAM,wBAAwB,eAAe;AAG7C,IAAM,EAAE,OAAO,QAAQ,eAAe,YAAY,IAAI;AACtD,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;;;AClsC1B,SAAS,WAAY,gBAAgB,UAAU,CAAC,GAAG;AACxD,MAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAAU;AACzD,UAAM,IAAI,MAAM,wFAAwF;AAAA,EAC1G,WAAW,OAAO,eAAe,OAAO,QAAQ,MAAM,YAAY;AAChE,WAAO,eAAe,gBAAgB,OAAO;AAAA,EAC/C,WAAW,OAAO,eAAe,SAAS,cAAc,OAAO,eAAe,OAAO,aAAa,MAAM,YAAY;AAClH,WAAO,gBAAgB,gBAAgB,OAAO;AAAA,EAChD,OAAO;AACL,UAAM,IAAI,MAAM,4FAA4F;AAAA,EAC9G;AACF;AAEA,UAAW,eAAgB,gBAAgB,SAAS;AAClD,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,aAAW,SAAS,gBAAgB;AAClC,UAAM,QAAQ,OAAO,KAAK;AAAA,EAC5B;AACF;AAEA,gBAAiB,gBAAiB,gBAAgB,SAAS;AACzD,QAAM,UAAU,IAAI,QAAQ,OAAO;AACnC,mBAAiB,SAAS,gBAAgB;AACxC,UAAM,QAAQ,OAAO,KAAK;AAAA,EAC5B;AACF;AASO,SAAS,WAAY,gBAAgB,UAAU,CAAC,GAAG;AACxD,MAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAAU;AACzD,UAAM,IAAI,MAAM,4FAA4F;AAAA,EAC9G;AAEA,QAAMC,WAAU,IAAI,QAAQ,OAAO;AACnC,MAAI;AACJ,QAAM,SAAS,CAAC,UAAU;AACxB,QAAI;AAEJ,QAAI,YAAY;AACd,cAAQ,OAAO,OAAO,CAAC,YAAY,KAAK,CAAC;AACzC,mBAAa;AAAA,IACf;AAEA,QAAI;AACF,eAASA,SAAQ,eAAe,KAAK;AAAA,IACvC,SAAS,KAAK;AACZ,UAAI,IAAI,YAAY;AAClB,qBAAa,MAAM,MAAM,IAAI,YAAY;AACzC,iBAAS,IAAI;AAAA,MACf,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,eAAe,OAAO,QAAQ,MAAM,YAAY;AACzD,WAAQ,UAAW,OAAQ;AACzB,iBAAW,SAAS,gBAAgB;AAClC,eAAQ,OAAO,KAAK;AAAA,MACtB;AAAA,IACF,EAAG;AAAA,EACL,WAAW,OAAO,eAAe,OAAO,aAAa,MAAM,YAAY;AACrE,WAAQ,gBAAiB,OAAQ;AAC/B,uBAAiB,SAAS,gBAAgB;AACxC,eAAQ,OAAO,KAAK;AAAA,MACtB;AAAA,IACF,EAAG;AAAA,EACL;AACF;", "names": ["i", "structure", "read", "target", "<PERSON><PERSON><PERSON>", "position", "bundledStrings", "referenceMap", "packedObjectMap", "sharedPackedObjectMap", "sharedValues", "packedValues", "encode", "options", "maxBytes", "isLittleEndianMachine", "<PERSON><PERSON><PERSON>", "addExtension", "decoder"]}