var ce=Object.defineProperty;var ie=(g,x,j)=>x in g?ce(g,x,{enumerable:!0,configurable:!0,writable:!0,value:j}):g[x]=j;var Q=(g,x,j)=>ie(g,typeof x!="symbol"?x+"":x,j);import{r as l,u as de,a as c,j as e,__tla as oe}from"./index-DGS8NYvs.js";let y,he=Promise.all([(()=>{try{return oe}catch(g){}})()]).then(async()=>{class g{constructor(){Q(this,"events",{})}subscribe(n,d){return this.events[n]||(this.events[n]=[]),this.events[n].push(d),()=>{this.events[n]&&(this.events[n]=this.events[n].filter(N=>N!==d))}}publish(n,d){this.events[n]&&this.events[n].forEach(N=>N(d))}unsubscribe(n,d){this.events[n]&&(this.events[n]=this.events[n].filter(N=>N!==d))}clear(){this.events={}}}const x=new g;function j(){return l.useCallback((a,n)=>{x.publish(a,n)},[])}y=l.forwardRef(({onStateChange:a},n)=>{const[d,N]=l.useState(65),[k,V]=l.useState(!1),[f,J]=l.useState(-50),[A,K]=l.useState(!1),[p,B]=l.useState(!1),[O,L]=l.useState(!1),{mute:r,NREnabled:o,NBEnabled:h,ANEnabled:u,CTCSSSupressEnabled:m,setAudioPanelState:b}=de(),X=35,_=j(),q=l.useCallback(s=>{const v=document.getElementById("sMeter");if(!v)return;const t=v.getContext("2d");if(!t)return;v.width=300,v.height=40;const I=v.width,se=v.height;t.clearRect(0,0,I,se);const U=6,le=3,ae=8,C=15,te=25,ne=5,re=5,T=I/2;t.strokeStyle="#0071e3",t.lineWidth=2,t.beginPath(),t.moveTo(0,C),t.lineTo(T,C),t.stroke(),t.strokeStyle="#ff3b30",t.beginPath(),t.moveTo(T,C),t.lineTo(268,C),t.stroke();for(let i=0;i<30;i++){const S=i*(U+le);i<s?t.fillStyle=i<17?"#0071e3":"#ff3b30":t.fillStyle=i<17?"rgba(0, 113, 227, 0.2)":"rgba(255, 59, 48, 0.2)",t.fillRect(S,0,U,ae)}t.font="11px -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', sans-serif",t.textAlign="center";const G=["S1","3","5","7","9","+20","+40","+60dB"];for(let i=0;i<=16;i++){const S=i*16.6970588235;t.fillStyle=S<=T?"#0071e3":"#ff3b30",i%2===1?(t.fillRect(S,C,1,re+2),(i-1)/2<G.length&&t.fillText(G[(i-1)/2],S,te+8)):t.fillRect(S,C,1,ne)}},[]),F=l.useCallback(s=>{s=Math.min(Math.max(s,-100),0);const v=Math.round((s+100)*X/100);q(v)},[q]),w=l.useCallback(()=>{const s=!r;b({mute:s}),c.setMute(s),a==null||a({mute:s,NREnabled:o,NBEnabled:h,ANEnabled:u,CTCSSSupressEnabled:m}),_("muteStateChanged",s)},[r,o,h,u,m,b,a,_]),E=l.useCallback(s=>{N(s),c.setGain(Math.pow(10,(s-50)/50+2.6))},[]),M=l.useCallback(()=>{const s=!k;V(s),c.setSquelch(s)},[k]),Y=l.useCallback(s=>{J(s),c.setSquelchThreshold(s)},[]),D=l.useCallback(()=>{const s=!o;b({NREnabled:s}),c.decoder.set_nr(s),a==null||a({mute:r,NREnabled:s,NBEnabled:h,ANEnabled:u,CTCSSSupressEnabled:m})},[o,r,h,u,m,b,a]),P=l.useCallback(()=>{const s=!h;b({NBEnabled:s}),c.nb=s,c.decoder.set_nb(s),a==null||a({mute:r,NREnabled:o,NBEnabled:s,ANEnabled:u,CTCSSSupressEnabled:m})},[h,r,o,u,m,b,a]),W=l.useCallback(()=>{const s=!u;b({ANEnabled:s}),c.decoder.set_an(s),a==null||a({mute:r,NREnabled:o,NBEnabled:h,ANEnabled:s,CTCSSSupressEnabled:m})},[u,r,o,h,m,b,a]),z=l.useCallback(()=>{const s=!m;b({CTCSSSupressEnabled:s}),c.setCTCSSFilter(s),a==null||a({mute:r,NREnabled:o,NBEnabled:h,ANEnabled:u,CTCSSSupressEnabled:s})},[m,r,o,h,u,b,a]),Z=l.useCallback(s=>{K(s),c.setFT8Decoding(s)},[]),R=l.useCallback(()=>{p?(c.stopRecording(),B(!1),L(!0)):(c.startRecording(),B(!0),L(!1))},[p]),ee=l.useCallback(()=>{c.downloadRecording()},[]),$=l.useCallback(()=>{const s=c.getPowerDb()/150*100+c.smeter_offset;F(s)},[F]),H=l.useCallback(()=>{E(d)},[d,E]);return l.useImperativeHandle(n,()=>({updateSignalMeter:$,initializeAudio:H,handleMuteChange:w,handleNRChange:D,handleNBChange:P,handleANChange:W,handleCTCSSChange:z,handleSquelchChange:M,handleRecordingChange:R,NREnabled:o,NBEnabled:h,ANEnabled:u,CTCSSSupressEnabled:m,mute:r}),[$,H,w,D,P,W,z,M,R,o,h,u,m,r]),e.jsxs("div",{className:"audio-controls",children:[e.jsx("h2",{className:"section-title",children:"Audio Controls"}),e.jsxs("div",{className:"control-group",id:"volume-slider",children:[e.jsxs("div",{className:"control-header",children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"control-icon",children:[e.jsx("path",{d:"M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z",fill:"currentColor"}),e.jsx("path",{d:"M11.5 4.5a4 4 0 010 7M13 3a6 6 0 010 10",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]}),e.jsx("span",{className:"control-label",children:"Volume"}),e.jsxs("span",{className:"control-value",children:[d,"%"]})]}),e.jsxs("div",{className:"slider-container",children:[e.jsx("button",{className:"mute-button ".concat(r?"active":""),onClick:w,"aria-label":r?"Unmute":"Mute",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r?e.jsxs(e.Fragment,{children:[e.jsx("path",{d:"M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z",fill:"currentColor"}),e.jsx("path",{d:"M11 6l4 4m0-4l-4 4",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]}):e.jsxs(e.Fragment,{children:[e.jsx("path",{d:"M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z",fill:"currentColor"}),e.jsx("path",{d:"M11.5 4.5a4 4 0 010 7M13 3a6 6 0 010 10",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]})})}),e.jsxs("div",{className:"slider-wrapper",children:[e.jsx("input",{type:"range",value:d,onChange:s=>E(Number(s.target.value)),disabled:r,min:"0",max:"100",step:"1",className:"slider volume-slider"}),e.jsx("div",{className:"slider-track",style:{width:"".concat(d,"%")}})]})]})]}),e.jsxs("div",{className:"control-group",id:"squelch-slider",children:[e.jsxs("div",{className:"control-header",children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"control-icon",children:[e.jsx("path",{d:"M8 1a7 7 0 110 14A7 7 0 018 1zM8 5a3 3 0 110 6 3 3 0 010-6z",stroke:"currentColor",strokeWidth:"1.5"}),e.jsx("circle",{cx:"8",cy:"8",r:"1.5",fill:"currentColor"})]}),e.jsx("span",{className:"control-label",children:"Squelch"}),e.jsxs("span",{className:"control-value",children:[f," dB"]})]}),e.jsxs("div",{className:"slider-container",children:[e.jsx("button",{className:"squelch-button ".concat(k?"active":""),onClick:M,"aria-label":k?"Disable squelch":"Enable squelch",children:e.jsx("span",{children:"SQ"})}),e.jsxs("div",{className:"slider-wrapper",children:[e.jsx("input",{type:"range",value:f,onChange:s=>Y(Number(s.target.value)),min:"-150",max:"0",step:"1",className:"slider squelch-slider"}),e.jsx("div",{className:"slider-track green",style:{width:"".concat((f+150)/150*100,"%")}})]})]})]}),e.jsxs("div",{className:"decoder-section",children:[e.jsxs("div",{className:"section-header",children:[e.jsx("div",{className:"accent-dot bg-green"}),e.jsx("h3",{className:"section-label",children:"Decoder"})]}),e.jsx("div",{className:"decoder-controls",children:e.jsx("div",{className:"decoder-row",children:e.jsxs("label",{className:"decoder-label",children:[e.jsx("input",{type:"checkbox",checked:A,onChange:s=>Z(s.target.checked),className:"decoder-checkbox"}),e.jsx("span",{className:"checkmark"}),"FT8 Decoder"]})})}),A&&e.jsxs("div",{className:"ft8-panel",children:[e.jsxs("div",{className:"ft8-header",children:[e.jsx("span",{className:"ft8-title",children:"FT8 Messages"}),e.jsx("div",{className:"ft8-stats",children:e.jsx("span",{id:"farthest-distance",children:"0 km"})})]}),e.jsx("div",{id:"ft8MessagesList",className:"ft8-messages-list"})]})]}),e.jsxs("div",{className:"recording-section",children:[e.jsxs("div",{className:"section-header",children:[e.jsx("div",{className:"accent-dot bg-red"}),e.jsx("h3",{className:"section-label",children:"Recording"})]}),e.jsxs("div",{className:"recording-controls",children:[e.jsxs("button",{className:"recording-button ".concat(p?"recording":""),onClick:R,title:p?"Stop Recording":"Start Recording",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:p?e.jsx("rect",{x:"4",y:"4",width:"8",height:"8",fill:"currentColor",rx:"1"}):e.jsx("circle",{cx:"8",cy:"8",r:"4",fill:"currentColor"})}),e.jsx("span",{children:p?"Stop":"Record"})]}),O&&e.jsxs("button",{className:"download-button",onClick:ee,title:"Download Recording",children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M8 1v10m0 0l3-3m-3 3L5 8",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2 12v2a2 2 0 002 2h8a2 2 0 002-2v-2",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})]}),e.jsx("span",{children:"Download"})]})]})]})]})}),y.displayName="AudioPanel"});export{he as __tla,y as default};
