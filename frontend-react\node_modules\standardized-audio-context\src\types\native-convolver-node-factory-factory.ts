import { TNativeConvolverNodeFactory } from './native-convolver-node-factory';
import { TNotSupportedErrorFactory } from './not-supported-error-factory';
import { TOverwriteAccessorsFunction } from './overwrite-accessors-function';

export type TNativeConvolverNodeFactoryFactory = (
    createNotSupportedError: TNotSupportedErrorFactory,
    overwriteAccessors: TOverwriteAccessorsFunction
) => TNativeConvolverNodeFactory;
