
var Module = (function() {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  
  return (
function(Module) {
  Module = Module || {};

var Module=typeof Module!=="undefined"?Module:{};var Promise=function(){function noop(){}function bind(fn,thisArg){return function(){fn.apply(thisArg,arguments)}}function Promise(fn){if(!(this instanceof Promise))throw new TypeError("Promises must be constructed via new");if(typeof fn!=="function")throw new TypeError("not a function");this._state=0;this._handled=false;this._value=undefined;this._deferreds=[];doResolve(fn,this)}function handle(self,deferred){while(self._state===3){self=self._value}if(self._state===0){self._deferreds.push(deferred);return}self._handled=true;Promise._immediateFn(function(){var cb=self._state===1?deferred.onFulfilled:deferred.onRejected;if(cb===null){(self._state===1?resolve:reject)(deferred.promise,self._value);return}var ret;try{ret=cb(self._value)}catch(e){reject(deferred.promise,e);return}resolve(deferred.promise,ret)})}function resolve(self,newValue){try{if(newValue===self)throw new TypeError("A promise cannot be resolved with itself.");if(newValue&&(typeof newValue==="object"||typeof newValue==="function")){var then=newValue.then;if(newValue instanceof Promise){self._state=3;self._value=newValue;finale(self);return}else if(typeof then==="function"){doResolve(bind(then,newValue),self);return}}self._state=1;self._value=newValue;finale(self)}catch(e){reject(self,e)}}function reject(self,newValue){self._state=2;self._value=newValue;finale(self)}function finale(self){if(self._state===2&&self._deferreds.length===0){Promise._immediateFn(function(){if(!self._handled){Promise._unhandledRejectionFn(self._value)}})}for(var i=0,len=self._deferreds.length;i<len;i++){handle(self,self._deferreds[i])}self._deferreds=null}function Handler(onFulfilled,onRejected,promise){this.onFulfilled=typeof onFulfilled==="function"?onFulfilled:null;this.onRejected=typeof onRejected==="function"?onRejected:null;this.promise=promise}function doResolve(fn,self){var done=false;try{fn(function(value){if(done)return;done=true;resolve(self,value)},function(reason){if(done)return;done=true;reject(self,reason)})}catch(ex){if(done)return;done=true;reject(self,ex)}}Promise.prototype["catch"]=function(onRejected){return this.then(null,onRejected)};Promise.prototype.then=function(onFulfilled,onRejected){var prom=new this.constructor(noop);handle(this,new Handler(onFulfilled,onRejected,prom));return prom};Promise.all=function(arr){return new Promise(function(resolve,reject){if(!Array.isArray(arr)){return reject(new TypeError("Promise.all accepts an array"))}var args=Array.prototype.slice.call(arr);if(args.length===0)return resolve([]);var remaining=args.length;function res(i,val){try{if(val&&(typeof val==="object"||typeof val==="function")){var then=val.then;if(typeof then==="function"){then.call(val,function(val){res(i,val)},reject);return}}args[i]=val;if(--remaining===0){resolve(args)}}catch(ex){reject(ex)}}for(var i=0;i<args.length;i++){res(i,args[i])}})};Promise.resolve=function(value){if(value&&typeof value==="object"&&value.constructor===Promise){return value}return new Promise(function(resolve){resolve(value)})};Promise.reject=function(value){return new Promise(function(resolve,reject){reject(value)})};Promise.race=function(arr){return new Promise(function(resolve,reject){if(!Array.isArray(arr)){return reject(new TypeError("Promise.race accepts an array"))}for(var i=0,len=arr.length;i<len;i++){Promise.resolve(arr[i]).then(resolve,reject)}})};Promise._immediateFn=typeof setImmediate==="function"&&function(fn){setImmediate(fn)}||function(fn){setTimeout(fn,0)};Promise._unhandledRejectionFn=function _unhandledRejectionFn(err){if(typeof console!=="undefined"&&console){console.warn("Possible Unhandled Promise Rejection:",err)}};return Promise}();var readyPromiseResolve,readyPromiseReject;Module["ready"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=typeof window==="object";var ENVIRONMENT_IS_WORKER=typeof importScripts==="function";var ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof process.versions==="object"&&typeof process.versions.node==="string";var ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;var WebAssembly={Memory:function(opts){this.buffer=new ArrayBuffer(opts["initial"]*65536)},Module:function(binary){},Instance:function(module,info){this.exports=(
// EMSCRIPTEN_START_ASM
function instantiate(R){function c(d){d.set=function(a,b){this[a]=b};d.get=function(a){return this[a]};return d}function P(Q){var e=Q.a;var f=e.buffer;var g=new Int8Array(f);var h=new Int16Array(f);var i=new Int32Array(f);var j=new Uint8Array(f);var k=new Uint16Array(f);var l=new Uint32Array(f);var m=new Float32Array(f);var n=new Float64Array(f);var o=Math.imul;var p=Math.fround;var q=Math.abs;var r=Math.clz32;var s=Math.min;var t=Math.max;var u=Math.floor;var v=Math.ceil;var w=Math.trunc;var x=Math.sqrt;var y=Q.abort;var z=NaN;var A=Infinity;var B=Q.b;var C=Q.c;var D=Q.d;var E=Q.e;var F=Q.f;var G=Q.g;var H=Q.h;var I=Q.i;var J=Q.j;var K=Q.k;var L=Q.l;var M=5246752;
// EMSCRIPTEN_START_FUNCS
function ea(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,k=0,m=0,n=0,o=0;o=M-16|0;M=o;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{k:{if(a>>>0<=244){f=i[841];h=a>>>0<11?16:a+11&-8;c=h>>>3|0;b=f>>>c|0;if(b&3){d=c+((b^-1)&1)|0;b=d<<3;e=i[b+3412>>2];a=e+8|0;c=i[e+8>>2];b=b+3404|0;l:{if((c|0)==(b|0)){i[841]=Ba(d)&f;break l}i[c+12>>2]=b;i[b+8>>2]=c}b=d<<3;i[e+4>>2]=b|3;b=b+e|0;i[b+4>>2]=i[b+4>>2]|1;break a}n=i[843];if(n>>>0>=h>>>0){break k}if(b){a=2<<c;a=(0-a|a)&b<<c;b=(0-a&a)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;c=(c|a)+(b>>>a|0)|0;a=c<<3;g=i[a+3412>>2];b=i[g+8>>2];a=a+3404|0;m:{if((b|0)==(a|0)){f=Ba(c)&f;i[841]=f;break m}i[b+12>>2]=a;i[a+8>>2]=b}a=g+8|0;i[g+4>>2]=h|3;d=g+h|0;b=c<<3;e=b-h|0;i[d+4>>2]=e|1;i[b+g>>2]=e;if(n){b=n>>>3|0;c=(b<<3)+3404|0;g=i[846];b=1<<b;n:{if(!(b&f)){i[841]=b|f;b=c;break n}b=i[c+8>>2]}i[c+8>>2]=g;i[b+12>>2]=g;i[g+12>>2]=c;i[g+8>>2]=b}i[846]=d;i[843]=e;break a}m=i[842];if(!m){break k}b=(m&0-m)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;b=i[((c|a)+(b>>>a|0)<<2)+3668>>2];d=(i[b+4>>2]&-8)-h|0;c=b;while(1){o:{a=i[c+16>>2];if(!a){a=i[c+20>>2];if(!a){break o}}c=(i[a+4>>2]&-8)-h|0;e=c>>>0<d>>>0;d=e?c:d;b=e?a:b;c=a;continue}break}k=i[b+24>>2];e=i[b+12>>2];if((e|0)!=(b|0)){a=i[b+8>>2];i[a+12>>2]=e;i[e+8>>2]=a;break b}c=b+20|0;a=i[c>>2];if(!a){a=i[b+16>>2];if(!a){break j}c=b+16|0}while(1){g=c;e=a;c=a+20|0;a=i[c>>2];if(a){continue}c=e+16|0;a=i[e+16>>2];if(a){continue}break}i[g>>2]=0;break b}h=-1;if(a>>>0>4294967231){break k}a=a+11|0;h=a&-8;m=i[842];if(!m){break k}d=0-h|0;f=0;p:{if(h>>>0<256){break p}f=31;if(h>>>0>16777215){break p}a=a>>>8|0;g=a+1048320>>>16&8;a=a<<g;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|g))|0;f=(a<<1|h>>>a+21&1)+28|0}c=i[(f<<2)+3668>>2];q:{r:{s:{if(!c){a=0;break s}a=0;b=h<<((f|0)==31?0:25-(f>>>1|0)|0);while(1){t:{g=(i[c+4>>2]&-8)-h|0;if(g>>>0>=d>>>0){break t}e=c;d=g;if(d){break t}d=0;a=c;break r}g=i[c+20>>2];c=i[((b>>>29&4)+c|0)+16>>2];a=g?(g|0)==(c|0)?a:g:a;b=b<<1;if(c){continue}break}}if(!(a|e)){e=0;a=2<<f;a=(0-a|a)&m;if(!a){break k}b=(a&0-a)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;a=i[((c|a)+(b>>>a|0)<<2)+3668>>2]}if(!a){break q}}while(1){b=(i[a+4>>2]&-8)-h|0;c=b>>>0<d>>>0;d=c?b:d;e=c?a:e;b=i[a+16>>2];if(b){a=b}else{a=i[a+20>>2]}if(a){continue}break}}if(!e|i[843]-h>>>0<=d>>>0){break k}f=i[e+24>>2];b=i[e+12>>2];if((e|0)!=(b|0)){a=i[e+8>>2];i[a+12>>2]=b;i[b+8>>2]=a;break c}c=e+20|0;a=i[c>>2];if(!a){a=i[e+16>>2];if(!a){break i}c=e+16|0}while(1){g=c;b=a;c=a+20|0;a=i[c>>2];if(a){continue}c=b+16|0;a=i[b+16>>2];if(a){continue}break}i[g>>2]=0;break c}c=i[843];if(c>>>0>=h>>>0){d=i[846];b=c-h|0;u:{if(b>>>0>=16){i[843]=b;a=d+h|0;i[846]=a;i[a+4>>2]=b|1;i[c+d>>2]=b;i[d+4>>2]=h|3;break u}i[846]=0;i[843]=0;i[d+4>>2]=c|3;a=c+d|0;i[a+4>>2]=i[a+4>>2]|1}a=d+8|0;break a}k=i[844];if(k>>>0>h>>>0){b=k-h|0;i[844]=b;c=i[847];a=c+h|0;i[847]=a;i[a+4>>2]=b|1;i[c+4>>2]=h|3;a=c+8|0;break a}a=0;m=h+47|0;if(i[959]){c=i[961]}else{i[962]=-1;i[963]=-1;i[960]=4096;i[961]=4096;i[959]=o+12&-16^1431655768;i[964]=0;i[952]=0;c=4096}g=m+c|0;e=0-c|0;c=g&e;if(c>>>0<=h>>>0){break a}d=i[951];if(d){b=i[949];f=b+c|0;if(d>>>0<f>>>0|b>>>0>=f>>>0){break a}}if(j[3808]&4){break f}v:{w:{d=i[847];if(d){a=3812;while(1){b=i[a>>2];if(b>>>0<=d>>>0&d>>>0<b+i[a+4>>2]>>>0){break w}a=i[a+8>>2];if(a){continue}break}}b=T(0);if((b|0)==-1){break g}f=c;d=i[960];a=d-1|0;if(a&b){f=(c-b|0)+(a+b&0-d)|0}if(f>>>0<=h>>>0|f>>>0>2147483646){break g}d=i[951];if(d){a=i[949];e=a+f|0;if(e>>>0>d>>>0|a>>>0>=e>>>0){break g}}a=T(f);if((b|0)!=(a|0)){break v}break e}f=e&g-k;if(f>>>0>2147483646){break g}b=T(f);if((b|0)==(i[a>>2]+i[a+4>>2]|0)){break h}a=b}if(!((a|0)==-1|h+48>>>0<=f>>>0)){b=i[961];b=b+(m-f|0)&0-b;if(b>>>0>2147483646){b=a;break e}if((T(b)|0)!=-1){f=b+f|0;b=a;break e}T(0-f|0);break g}b=a;if((a|0)!=-1){break e}break g}e=0;break b}b=0;break c}if((b|0)!=-1){break e}}i[952]=i[952]|4}if(c>>>0>2147483646){break d}b=i[838];c=c+3&-4;a=b+c|0;x:{y:{if(!c|a>>>0>b>>>0){if(O()<<16>>>0>=a>>>0){break y}if(D(a|0)|0){break y}a=i[838]}else{a=b}i[840]=48;b=-1;break x}i[838]=a}if(O()<<16>>>0<a>>>0){if(!(D(a|0)|0)){break d}}i[838]=a;if((b|0)==-1|(a|0)==-1|a>>>0<=b>>>0){break d}f=a-b|0;if(f>>>0<=h+40>>>0){break d}}a=i[949]+f|0;i[949]=a;if(a>>>0>l[950]){i[950]=a}z:{A:{B:{g=i[847];if(g){a=3812;while(1){d=i[a>>2];c=i[a+4>>2];if((d+c|0)==(b|0)){break B}a=i[a+8>>2];if(a){continue}break}break A}a=i[845];if(!(a>>>0<=b>>>0?a:0)){i[845]=b}a=0;i[954]=f;i[953]=b;i[849]=-1;i[850]=i[959];i[956]=0;while(1){d=a<<3;c=d+3404|0;i[d+3412>>2]=c;i[d+3416>>2]=c;a=a+1|0;if((a|0)!=32){continue}break}d=f-40|0;a=b+8&7?-8-b&7:0;c=d-a|0;i[844]=c;a=a+b|0;i[847]=a;i[a+4>>2]=c|1;i[(b+d|0)+4>>2]=40;i[848]=i[963];break z}if(j[a+12|0]&8|d>>>0>g>>>0|b>>>0<=g>>>0){break A}i[a+4>>2]=c+f;a=g+8&7?-8-g&7:0;c=a+g|0;i[847]=c;b=i[844]+f|0;a=b-a|0;i[844]=a;i[c+4>>2]=a|1;i[(b+g|0)+4>>2]=40;i[848]=i[963];break z}if(l[845]>b>>>0){i[845]=b}d=b+f|0;c=3812;C:{while(1){if((d|0)!=i[c>>2]){a=3812;c=i[c+8>>2];if(c){continue}break C}break}a=3812;if(j[c+12|0]&8){break C}i[c>>2]=b;i[c+4>>2]=i[c+4>>2]+f;m=(b+8&7?-8-b&7:0)+b|0;i[m+4>>2]=h|3;e=d+(d+8&7?-8-d&7:0)|0;k=h+m|0;c=e-k|0;D:{if((e|0)==(g|0)){i[847]=k;a=i[844]+c|0;i[844]=a;i[k+4>>2]=a|1;break D}if(i[846]==(e|0)){i[846]=k;a=i[843]+c|0;i[843]=a;i[k+4>>2]=a|1;i[a+k>>2]=a;break D}a=i[e+4>>2];if((a&3)==1){f=a&-8;E:{if(a>>>0<=255){d=i[e+8>>2];a=a>>>3|0;b=i[e+12>>2];if((b|0)==(d|0)){i[841]=i[841]&Ba(a);break E}i[d+12>>2]=b;i[b+8>>2]=d;break E}h=i[e+24>>2];b=i[e+12>>2];F:{if((e|0)!=(b|0)){a=i[e+8>>2];i[a+12>>2]=b;i[b+8>>2]=a;break F}G:{a=e+20|0;d=i[a>>2];if(d){break G}a=e+16|0;d=i[a>>2];if(d){break G}b=0;break F}while(1){g=a;b=d;a=b+20|0;d=i[a>>2];if(d){continue}a=b+16|0;d=i[b+16>>2];if(d){continue}break}i[g>>2]=0}if(!h){break E}d=i[e+28>>2];a=(d<<2)+3668|0;H:{if(i[a>>2]==(e|0)){i[a>>2]=b;if(b){break H}i[842]=i[842]&Ba(d);break E}i[h+(i[h+16>>2]==(e|0)?16:20)>>2]=b;if(!b){break E}}i[b+24>>2]=h;a=i[e+16>>2];if(a){i[b+16>>2]=a;i[a+24>>2]=b}a=i[e+20>>2];if(!a){break E}i[b+20>>2]=a;i[a+24>>2]=b}e=e+f|0;c=c+f|0}i[e+4>>2]=i[e+4>>2]&-2;i[k+4>>2]=c|1;i[c+k>>2]=c;if(c>>>0<=255){a=c>>>3|0;b=(a<<3)+3404|0;c=i[841];a=1<<a;I:{if(!(c&a)){i[841]=a|c;a=b;break I}a=i[b+8>>2]}i[b+8>>2]=k;i[a+12>>2]=k;i[k+12>>2]=b;i[k+8>>2]=a;break D}a=31;if(c>>>0<=16777215){a=c>>>8|0;e=a+1048320>>>16&8;a=a<<e;d=a+520192>>>16&4;a=a<<d;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(e|d))|0;a=(a<<1|c>>>a+21&1)+28|0}i[k+28>>2]=a;i[k+16>>2]=0;i[k+20>>2]=0;e=(a<<2)+3668|0;J:{d=i[842];b=1<<a;K:{if(!(d&b)){i[842]=b|d;i[e>>2]=k;i[k+24>>2]=e;break K}a=c<<((a|0)==31?0:25-(a>>>1|0)|0);b=i[e>>2];while(1){d=b;if((i[b+4>>2]&-8)==(c|0)){break J}b=a>>>29|0;a=a<<1;e=d+(b&4)|0;b=i[e+16>>2];if(b){continue}break}i[e+16>>2]=k;i[k+24>>2]=d}i[k+12>>2]=k;i[k+8>>2]=k;break D}a=i[d+8>>2];i[a+12>>2]=k;i[d+8>>2]=k;i[k+24>>2]=0;i[k+12>>2]=d;i[k+8>>2]=a}a=m+8|0;break a}while(1){L:{c=i[a>>2];if(c>>>0<=g>>>0){e=c+i[a+4>>2]|0;if(e>>>0>g>>>0){break L}}a=i[a+8>>2];continue}break}d=f-40|0;a=b+8&7?-8-b&7:0;c=d-a|0;i[844]=c;a=a+b|0;i[847]=a;i[a+4>>2]=c|1;i[(b+d|0)+4>>2]=40;i[848]=i[963];a=(e+(e-39&7?39-e&7:0)|0)-47|0;c=a>>>0<g+16>>>0?g:a;i[c+4>>2]=27;a=i[956];i[c+16>>2]=i[955];i[c+20>>2]=a;a=i[954];i[c+8>>2]=i[953];i[c+12>>2]=a;i[955]=c+8;i[954]=f;i[953]=b;i[956]=0;a=c+24|0;while(1){i[a+4>>2]=7;b=a+8|0;a=a+4|0;if(b>>>0<e>>>0){continue}break}if((c|0)==(g|0)){break z}i[c+4>>2]=i[c+4>>2]&-2;e=c-g|0;i[g+4>>2]=e|1;i[c>>2]=e;if(e>>>0<=255){a=e>>>3|0;b=(a<<3)+3404|0;c=i[841];a=1<<a;M:{if(!(c&a)){i[841]=a|c;a=b;break M}a=i[b+8>>2]}i[b+8>>2]=g;i[a+12>>2]=g;i[g+12>>2]=b;i[g+8>>2]=a;break z}a=31;i[g+16>>2]=0;i[g+20>>2]=0;if(e>>>0<=16777215){a=e>>>8|0;d=a+1048320>>>16&8;a=a<<d;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|d))|0;a=(a<<1|e>>>a+21&1)+28|0}i[g+28>>2]=a;d=(a<<2)+3668|0;N:{c=i[842];b=1<<a;O:{if(!(c&b)){i[842]=b|c;i[d>>2]=g;i[g+24>>2]=d;break O}a=e<<((a|0)==31?0:25-(a>>>1|0)|0);b=i[d>>2];while(1){c=b;if((e|0)==(i[b+4>>2]&-8)){break N}b=a>>>29|0;a=a<<1;d=c+(b&4)|0;b=i[d+16>>2];if(b){continue}break}i[d+16>>2]=g;i[g+24>>2]=c}i[g+12>>2]=g;i[g+8>>2]=g;break z}a=i[c+8>>2];i[a+12>>2]=g;i[c+8>>2]=g;i[g+24>>2]=0;i[g+12>>2]=c;i[g+8>>2]=a}a=i[844];if(a>>>0<=h>>>0){break d}b=a-h|0;i[844]=b;c=i[847];a=c+h|0;i[847]=a;i[a+4>>2]=b|1;i[c+4>>2]=h|3;a=c+8|0;break a}a=0;i[840]=48;break a}P:{if(!f){break P}c=i[e+28>>2];a=(c<<2)+3668|0;Q:{if(i[a>>2]==(e|0)){i[a>>2]=b;if(b){break Q}m=Ba(c)&m;i[842]=m;break P}i[f+(i[f+16>>2]==(e|0)?16:20)>>2]=b;if(!b){break P}}i[b+24>>2]=f;a=i[e+16>>2];if(a){i[b+16>>2]=a;i[a+24>>2]=b}a=i[e+20>>2];if(!a){break P}i[b+20>>2]=a;i[a+24>>2]=b}R:{if(d>>>0<=15){a=d+h|0;i[e+4>>2]=a|3;a=a+e|0;i[a+4>>2]=i[a+4>>2]|1;break R}i[e+4>>2]=h|3;f=e+h|0;i[f+4>>2]=d|1;i[d+f>>2]=d;if(d>>>0<=255){a=d>>>3|0;b=(a<<3)+3404|0;c=i[841];a=1<<a;S:{if(!(c&a)){i[841]=a|c;a=b;break S}a=i[b+8>>2]}i[b+8>>2]=f;i[a+12>>2]=f;i[f+12>>2]=b;i[f+8>>2]=a;break R}a=31;if(d>>>0<=16777215){a=d>>>8|0;g=a+1048320>>>16&8;a=a<<g;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|g))|0;a=(a<<1|d>>>a+21&1)+28|0}i[f+28>>2]=a;i[f+16>>2]=0;i[f+20>>2]=0;b=(a<<2)+3668|0;T:{c=1<<a;U:{if(!(c&m)){i[842]=c|m;i[b>>2]=f;break U}a=d<<((a|0)==31?0:25-(a>>>1|0)|0);h=i[b>>2];while(1){b=h;if((i[b+4>>2]&-8)==(d|0)){break T}c=a>>>29|0;a=a<<1;c=(c&4)+b|0;h=i[c+16>>2];if(h){continue}break}i[c+16>>2]=f}i[f+24>>2]=b;i[f+12>>2]=f;i[f+8>>2]=f;break R}a=i[b+8>>2];i[a+12>>2]=f;i[b+8>>2]=f;i[f+24>>2]=0;i[f+12>>2]=b;i[f+8>>2]=a}a=e+8|0;break a}V:{if(!k){break V}c=i[b+28>>2];a=(c<<2)+3668|0;W:{if(i[a>>2]==(b|0)){i[a>>2]=e;if(e){break W}i[842]=Ba(c)&m;break V}i[k+(i[k+16>>2]==(b|0)?16:20)>>2]=e;if(!e){break V}}i[e+24>>2]=k;a=i[b+16>>2];if(a){i[e+16>>2]=a;i[a+24>>2]=e}a=i[b+20>>2];if(!a){break V}i[e+20>>2]=a;i[a+24>>2]=e}X:{if(d>>>0<=15){a=d+h|0;i[b+4>>2]=a|3;a=a+b|0;i[a+4>>2]=i[a+4>>2]|1;break X}i[b+4>>2]=h|3;e=b+h|0;i[e+4>>2]=d|1;i[e+d>>2]=d;if(n){a=n>>>3|0;c=(a<<3)+3404|0;g=i[846];a=1<<a;Y:{if(!(a&f)){i[841]=a|f;a=c;break Y}a=i[c+8>>2]}i[c+8>>2]=g;i[a+12>>2]=g;i[g+12>>2]=c;i[g+8>>2]=a}i[846]=e;i[843]=d}a=b+8|0}M=o+16|0;return a|0}function da(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0;a:{if(!a){break a}d=a-8|0;b=i[a-4>>2];a=b&-8;f=d+a|0;b:{if(b&1){break b}if(!(b&3)){break a}b=i[d>>2];d=d-b|0;if(d>>>0<l[845]){break a}a=a+b|0;if(i[846]!=(d|0)){if(b>>>0<=255){e=i[d+8>>2];b=b>>>3|0;c=i[d+12>>2];if((c|0)==(e|0)){i[841]=i[841]&Ba(b);break b}i[e+12>>2]=c;i[c+8>>2]=e;break b}h=i[d+24>>2];b=i[d+12>>2];c:{if((d|0)!=(b|0)){c=i[d+8>>2];i[c+12>>2]=b;i[b+8>>2]=c;break c}d:{e=d+20|0;c=i[e>>2];if(c){break d}e=d+16|0;c=i[e>>2];if(c){break d}b=0;break c}while(1){g=e;b=c;e=b+20|0;c=i[e>>2];if(c){continue}e=b+16|0;c=i[b+16>>2];if(c){continue}break}i[g>>2]=0}if(!h){break b}e=i[d+28>>2];c=(e<<2)+3668|0;e:{if(i[c>>2]==(d|0)){i[c>>2]=b;if(b){break e}i[842]=i[842]&Ba(e);break b}i[h+(i[h+16>>2]==(d|0)?16:20)>>2]=b;if(!b){break b}}i[b+24>>2]=h;c=i[d+16>>2];if(c){i[b+16>>2]=c;i[c+24>>2]=b}c=i[d+20>>2];if(!c){break b}i[b+20>>2]=c;i[c+24>>2]=b;break b}b=i[f+4>>2];if((b&3)!=3){break b}i[843]=a;i[f+4>>2]=b&-2;i[d+4>>2]=a|1;i[a+d>>2]=a;return}if(d>>>0>=f>>>0){break a}b=i[f+4>>2];if(!(b&1)){break a}f:{if(!(b&2)){if(i[847]==(f|0)){i[847]=d;a=i[844]+a|0;i[844]=a;i[d+4>>2]=a|1;if(i[846]!=(d|0)){break a}i[843]=0;i[846]=0;return}if(i[846]==(f|0)){i[846]=d;a=i[843]+a|0;i[843]=a;i[d+4>>2]=a|1;i[a+d>>2]=a;return}a=(b&-8)+a|0;g:{if(b>>>0<=255){e=i[f+8>>2];b=b>>>3|0;c=i[f+12>>2];if((c|0)==(e|0)){i[841]=i[841]&Ba(b);break g}i[e+12>>2]=c;i[c+8>>2]=e;break g}h=i[f+24>>2];b=i[f+12>>2];h:{if((f|0)!=(b|0)){c=i[f+8>>2];i[c+12>>2]=b;i[b+8>>2]=c;break h}i:{e=f+20|0;c=i[e>>2];if(c){break i}e=f+16|0;c=i[e>>2];if(c){break i}b=0;break h}while(1){g=e;b=c;e=b+20|0;c=i[e>>2];if(c){continue}e=b+16|0;c=i[b+16>>2];if(c){continue}break}i[g>>2]=0}if(!h){break g}e=i[f+28>>2];c=(e<<2)+3668|0;j:{if(i[c>>2]==(f|0)){i[c>>2]=b;if(b){break j}i[842]=i[842]&Ba(e);break g}i[h+(i[h+16>>2]==(f|0)?16:20)>>2]=b;if(!b){break g}}i[b+24>>2]=h;c=i[f+16>>2];if(c){i[b+16>>2]=c;i[c+24>>2]=b}c=i[f+20>>2];if(!c){break g}i[b+20>>2]=c;i[c+24>>2]=b}i[d+4>>2]=a|1;i[a+d>>2]=a;if(i[846]!=(d|0)){break f}i[843]=a;return}i[f+4>>2]=b&-2;i[d+4>>2]=a|1;i[a+d>>2]=a}if(a>>>0<=255){a=a>>>3|0;b=(a<<3)+3404|0;c=i[841];a=1<<a;k:{if(!(c&a)){i[841]=a|c;a=b;break k}a=i[b+8>>2]}i[b+8>>2]=d;i[a+12>>2]=d;i[d+12>>2]=b;i[d+8>>2]=a;return}e=31;i[d+16>>2]=0;i[d+20>>2]=0;if(a>>>0<=16777215){b=a>>>8|0;g=b+1048320>>>16&8;b=b<<g;e=b+520192>>>16&4;b=b<<e;c=b+245760>>>16&2;b=(b<<c>>>15|0)-(c|(e|g))|0;e=(b<<1|a>>>b+21&1)+28|0}i[d+28>>2]=e;g=(e<<2)+3668|0;l:{m:{c=i[842];b=1<<e;n:{if(!(c&b)){i[842]=b|c;i[g>>2]=d;i[d+24>>2]=g;break n}e=a<<((e|0)==31?0:25-(e>>>1|0)|0);b=i[g>>2];while(1){c=b;if((i[b+4>>2]&-8)==(a|0)){break m}b=e>>>29|0;e=e<<1;g=c+(b&4)|0;b=i[g+16>>2];if(b){continue}break}i[g+16>>2]=d;i[d+24>>2]=c}i[d+12>>2]=d;i[d+8>>2]=d;break l}a=i[c+8>>2];i[a+12>>2]=d;i[c+8>>2]=d;i[d+24>>2]=0;i[d+12>>2]=c;i[d+8>>2]=a}a=i[849]-1|0;i[849]=a?a:-1}}function Aa(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,h=0;f=M-16|0;M=f;i[f+12>>2]=a;a=M-16|0;i[a+8>>2]=i[f+12>>2];i[a+12>>2]=i[i[a+8>>2]+4>>2];c=i[a+12>>2];b=c;a:{b:{if(b&3){while(1){if(!j[b|0]){break b}b=b+1|0;if(b&3){continue}break}}while(1){a=b;b=b+4|0;e=i[a>>2];if(!((e^-1)&e-16843009&-2139062144)){continue}break}d=a-c|0;if(!(e&255)){break a}while(1){e=j[a+1|0];b=a+1|0;a=b;if(e){continue}break}}d=b-c|0}d=d+1|0;a=ea(d);b=0;c:{if(!a){break c}d:{if(d>>>0>=512){H(a|0,c|0,d|0)|0;break d}e=a+d|0;e:{if(!((a^c)&3)){f:{if(!(a&3)){b=a;break f}if(!d){b=a;break f}b=a;while(1){g[b|0]=j[c|0];c=c+1|0;b=b+1|0;if(!(b&3)){break f}if(b>>>0<e>>>0){continue}break}}d=e&-4;g:{if(d>>>0<64){break g}h=d+-64|0;if(h>>>0<b>>>0){break g}while(1){i[b>>2]=i[c>>2];i[b+4>>2]=i[c+4>>2];i[b+8>>2]=i[c+8>>2];i[b+12>>2]=i[c+12>>2];i[b+16>>2]=i[c+16>>2];i[b+20>>2]=i[c+20>>2];i[b+24>>2]=i[c+24>>2];i[b+28>>2]=i[c+28>>2];i[b+32>>2]=i[c+32>>2];i[b+36>>2]=i[c+36>>2];i[b+40>>2]=i[c+40>>2];i[b+44>>2]=i[c+44>>2];i[b+48>>2]=i[c+48>>2];i[b+52>>2]=i[c+52>>2];i[b+56>>2]=i[c+56>>2];i[b+60>>2]=i[c+60>>2];c=c- -64|0;b=b- -64|0;if(h>>>0>=b>>>0){continue}break}}if(b>>>0>=d>>>0){break e}while(1){i[b>>2]=i[c>>2];c=c+4|0;b=b+4|0;if(d>>>0>b>>>0){continue}break}break e}if(e>>>0<4){b=a;break e}d=e-4|0;if(d>>>0<a>>>0){b=a;break e}b=a;while(1){g[b|0]=j[c|0];g[b+1|0]=j[c+1|0];g[b+2|0]=j[c+2|0];g[b+3|0]=j[c+3|0];c=c+4|0;b=b+4|0;if(d>>>0>=b>>>0){continue}break}}if(b>>>0<e>>>0){while(1){g[b|0]=j[c|0];c=c+1|0;b=b+1|0;if((e|0)!=(b|0)){continue}break}}}b=a}M=f+16|0;return b|0}function ba(){var a=0;L(2952,1178);K(2964,1081,1,1,0);a=M-16|0;M=a;i[a+12>>2]=1076;C(2976,i[a+12>>2],1,-128,127);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1069;C(3e3,i[a+12>>2],1,-128,127);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1067;C(2988,i[a+12>>2],1,0,255);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1033;C(3012,i[a+12>>2],2,-32768,32767);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1024;C(3024,i[a+12>>2],2,0,65535);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1048;C(3036,i[a+12>>2],4,-2147483648,2147483647);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1039;C(3048,i[a+12>>2],4,0,-1);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1111;C(3060,i[a+12>>2],4,-2147483648,2147483647);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1102;C(3072,i[a+12>>2],4,0,-1);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1059;ca(3084,i[a+12>>2],-2147483648,2147483647);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1058;ca(3096,i[a+12>>2],0,-1);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1052;F(3108,i[a+12>>2],4);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1171;F(3120,i[a+12>>2],8);M=a+16|0;G(1900,1129);G(1988,1620);E(2076,4,1116);E(2168,2,1141);E(2260,4,1156);I(2304,1086);a=M-16|0;M=a;i[a+12>>2]=1551;B(2344,0,i[a+12>>2]);M=a+16|0;aa(1653);$(1581);_(1183);Z(1214);ia(1254);ha(1283);a=M-16|0;M=a;i[a+12>>2]=1690;B(2624,4,i[a+12>>2]);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1720;B(2664,5,i[a+12>>2]);M=a+16|0;aa(1385);$(1352);_(1451);Z(1417);ia(1518);ha(1484);a=M-16|0;M=a;i[a+12>>2]=1321;B(2704,6,i[a+12>>2]);M=a+16|0;a=M-16|0;M=a;i[a+12>>2]=1759;B(2744,7,i[a+12>>2]);M=a+16|0}function qa(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,k=0,l=0,m=0;if(S(a,i[b+8>>2],e)){if(!(i[b+28>>2]==1|i[b+4>>2]!=(c|0))){i[b+28>>2]=d}return}a:{if(S(a,i[b>>2],e)){if(!(i[b+16>>2]!=(c|0)&i[b+20>>2]!=(c|0))){if((d|0)!=1){break a}i[b+32>>2]=1;return}i[b+32>>2]=d;if(i[b+44>>2]!=4){f=a+16|0;m=f+(i[a+12>>2]<<3)|0;b:{c:{while(1){d:{if(f>>>0>=m>>>0){break d}h[b+52>>1]=0;W(f,b,c,c,1,e);if(j[b+54|0]){break d}e:{if(!j[b+53|0]){break e}if(j[b+52|0]){d=1;if(i[b+24>>2]==1){break c}l=1;k=1;if(j[a+8|0]&2){break e}break c}l=1;d=k;if(!(g[a+8|0]&1)){break c}}f=f+8|0;continue}break}d=k;a=4;if(!l){break b}}a=3}i[b+44>>2]=a;if(d&1){break a}}i[b+20>>2]=c;i[b+40>>2]=i[b+40>>2]+1;if(i[b+36>>2]!=1|i[b+24>>2]!=2){break a}g[b+54|0]=1;return}k=i[a+12>>2];f=a+16|0;U(f,b,c,d,e);if((k|0)<2){break a}k=f+(k<<3)|0;f=a+24|0;a=i[a+8>>2];if(!(!(a&2)&i[b+36>>2]!=1)){while(1){if(j[b+54|0]){break a}U(f,b,c,d,e);f=f+8|0;if(k>>>0>f>>>0){continue}break}break a}if(!(a&1)){while(1){if(j[b+54|0]|i[b+36>>2]==1){break a}U(f,b,c,d,e);f=f+8|0;if(k>>>0>f>>>0){continue}break a}}while(1){if(j[b+54|0]|i[b+36>>2]==1&i[b+24>>2]==1){break a}U(f,b,c,d,e);f=f+8|0;if(k>>>0>f>>>0){continue}break}}}function xa(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;f=M+-64|0;M=f;d=1;a:{if(S(a,b,0)){break a}d=0;if(!b){break a}d=M+-64|0;M=d;e=i[b>>2];g=i[e-4>>2];h=i[e-8>>2];i[d+16>>2]=2808;i[d+12>>2]=b;i[d+8>>2]=2856;e=0;ja(d+20|0,43);b=b+h|0;b:{if(S(g,2856,0)){i[d+56>>2]=1;N[i[i[g>>2]+20>>2]](g,d+8|0,b,b,1,0);e=i[d+32>>2]==1?b:0;break b}N[i[i[g>>2]+24>>2]](g,d+8|0,b,1,0);c:{switch(i[d+44>>2]){case 0:e=i[d+48>>2]==1?i[d+36>>2]==1?i[d+40>>2]==1?i[d+28>>2]:0:0:0;break b;case 1:break c;default:break b}}if(i[d+32>>2]!=1){if(i[d+48>>2]|i[d+36>>2]!=1|i[d+40>>2]!=1){break b}}e=i[d+24>>2]}M=d- -64|0;d=0;if(!e){break a}b=f+8|0;ja(b|4,52);i[f+56>>2]=1;i[f+20>>2]=-1;i[f+16>>2]=a;i[f+8>>2]=e;N[i[i[e>>2]+28>>2]](e,b,i[c>>2],1);a=i[f+32>>2];if((a|0)==1){i[c>>2]=i[f+24>>2]}d=(a|0)==1}M=f- -64|0;return d|0}function ja(a,b){var c=0;a:{if(!b){break a}g[a|0]=0;c=a+b|0;g[c-1|0]=0;if(b>>>0<3){break a}g[a+2|0]=0;g[a+1|0]=0;g[c-3|0]=0;g[c-2|0]=0;if(b>>>0<7){break a}g[a+3|0]=0;g[c-4|0]=0;if(b>>>0<9){break a}c=0-a&3;a=c+a|0;i[a>>2]=0;c=b-c&-4;b=c+a|0;i[b-4>>2]=0;if(c>>>0<9){break a}i[a+8>>2]=0;i[a+4>>2]=0;i[b-8>>2]=0;i[b-12>>2]=0;if(c>>>0<25){break a}i[a+24>>2]=0;i[a+20>>2]=0;i[a+16>>2]=0;i[a+12>>2]=0;i[b-16>>2]=0;i[b-20>>2]=0;i[b-24>>2]=0;i[b-28>>2]=0;b=c;c=a&4|24;b=b-c|0;if(b>>>0<32){break a}a=a+c|0;while(1){i[a+24>>2]=0;i[a+28>>2]=0;i[a+16>>2]=0;i[a+20>>2]=0;i[a+8>>2]=0;i[a+12>>2]=0;i[a>>2]=0;i[a+4>>2]=0;a=a+32|0;b=b-32|0;if(b>>>0>31){continue}break}}}function na(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var k=0,l=0,m=0,n=0,o=0,p=0;if(S(a,i[b+8>>2],f)){Y(b,c,d,e);return}l=j[b+53|0];k=i[a+12>>2];g[b+53|0]=0;m=j[b+52|0];g[b+52|0]=0;n=a+16|0;W(n,b,c,d,e,f);o=j[b+53|0];l=l|o;p=j[b+52|0];m=m|p;a:{if((k|0)<2){break a}n=n+(k<<3)|0;k=a+24|0;while(1){if(j[b+54|0]){break a}b:{if(p){if(i[b+24>>2]==1){break a}if(j[a+8|0]&2){break b}break a}if(!o){break b}if(!(g[a+8|0]&1)){break a}}h[b+52>>1]=0;W(k,b,c,d,e,f);o=j[b+53|0];l=o|l;p=j[b+52|0];m=p|m;k=k+8|0;if(n>>>0>k>>>0){continue}break}}g[b+53|0]=(l&255)!=0;g[b+52|0]=(m&255)!=0}function ua(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(S(a,i[b+8>>2],e)){if(!(i[b+28>>2]==1|i[b+4>>2]!=(c|0))){i[b+28>>2]=d}return}a:{if(S(a,i[b>>2],e)){if(!(i[b+16>>2]!=(c|0)&i[b+20>>2]!=(c|0))){if((d|0)!=1){break a}i[b+32>>2]=1;return}i[b+32>>2]=d;b:{if(i[b+44>>2]==4){break b}h[b+52>>1]=0;a=i[a+8>>2];N[i[i[a>>2]+20>>2]](a,b,c,c,1,e);if(j[b+53|0]){i[b+44>>2]=3;if(!j[b+52|0]){break b}break a}i[b+44>>2]=4}i[b+20>>2]=c;i[b+40>>2]=i[b+40>>2]+1;if(i[b+36>>2]!=1|i[b+24>>2]!=2){break a}g[b+54|0]=1;return}a=i[a+8>>2];N[i[i[a>>2]+24>>2]](a,b,c,d,e)}}function S(a,b,c){var d=0;if(!c){return i[a+4>>2]==i[b+4>>2]}if((a|0)==(b|0)){return 1}c=M-16|0;i[c+8>>2]=a;i[c+12>>2]=i[i[c+8>>2]+4>>2];a=i[c+12>>2];i[c+8>>2]=b;i[c+12>>2]=i[i[c+8>>2]+4>>2];d=i[c+12>>2];b=j[d|0];c=j[a|0];a:{if(!c|(b|0)!=(c|0)){break a}while(1){b=j[d+1|0];c=j[a+1|0];if(!c){break a}d=d+1|0;a=a+1|0;if((b|0)==(c|0)){continue}break}}return(b|0)==(c|0)}function oa(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(S(a,i[b+8>>2],e)){if(!(i[b+28>>2]==1|i[b+4>>2]!=(c|0))){i[b+28>>2]=d}return}a:{if(!S(a,i[b>>2],e)){break a}if(!(i[b+16>>2]!=(c|0)&i[b+20>>2]!=(c|0))){if((d|0)!=1){break a}i[b+32>>2]=1;return}i[b+20>>2]=c;i[b+32>>2]=d;i[b+40>>2]=i[b+40>>2]+1;if(!(i[b+36>>2]!=1|i[b+24>>2]!=2)){g[b+54|0]=1}i[b+44>>2]=4}}function Y(a,b,c,d){g[a+53|0]=1;a:{if(i[a+4>>2]!=(c|0)){break a}g[a+52|0]=1;c=i[a+16>>2];b:{if(!c){i[a+36>>2]=1;i[a+24>>2]=d;i[a+16>>2]=b;if(i[a+48>>2]!=1){break a}if((d|0)==1){break b}break a}if((b|0)==(c|0)){c=i[a+24>>2];if((c|0)==2){i[a+24>>2]=d;c=d}if(i[a+48>>2]!=1){break a}if((c|0)==1){break b}break a}i[a+36>>2]=i[a+36>>2]+1}g[a+54|0]=1}}function ra(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;if(S(a,i[b+8>>2],0)){X(b,c,d);return}e=i[a+12>>2];f=a+16|0;fa(f,b,c,d);a:{if((e|0)<2){break a}e=(e<<3)+f|0;a=a+24|0;while(1){fa(a,b,c,d);if(j[b+54|0]){break a}a=a+8|0;if(e>>>0>a>>>0){continue}break}}}function X(a,b,c){var d=0;d=i[a+16>>2];if(!d){i[a+36>>2]=1;i[a+24>>2]=c;i[a+16>>2]=b;return}a:{if((b|0)==(d|0)){if(i[a+24>>2]!=2){break a}i[a+24>>2]=c;return}g[a+54|0]=1;i[a+24>>2]=2;i[a+36>>2]=i[a+36>>2]+1}}function T(a){var b=0,c=0;b=i[838];c=a+3&-4;a=b+c|0;a:{if(a>>>0<=b>>>0?c:0){break a}if(a>>>0>O()<<16>>>0){if(!(D(a|0)|0)){break a}}i[838]=a;return b}i[840]=48;return-1}function fa(a,b,c,d){var e=0,f=0;e=i[a+4>>2];f=i[a>>2];a=0;a:{if(!c){break a}a=e>>8;if(!(e&1)){break a}a=i[a+i[c>>2]>>2]}N[i[i[f>>2]+28>>2]](f,b,a+c|0,e&2?d:2)}function wa(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(S(a,i[b+8>>2],f)){Y(b,c,d,e);return}a=i[a+8>>2];N[i[i[a>>2]+20>>2]](a,b,c,d,e,f)}function W(a,b,c,d,e,f){var g=0,h=0;g=i[a+4>>2];h=g>>8;a=i[a>>2];if(g&1){h=i[i[d>>2]+h>>2]}N[i[i[a>>2]+20>>2]](a,b,c,d+h|0,g&2?e:2,f)}function U(a,b,c,d,e){var f=0,g=0;f=i[a+4>>2];g=f>>8;a=i[a>>2];if(f&1){g=i[i[c>>2]+g>>2]}N[i[i[a>>2]+24>>2]](a,b,c+g|0,f&2?d:2,e)}function ta(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(S(a,i[b+8>>2],0)){X(b,c,d);return}a=i[a+8>>2];N[i[i[a>>2]+28>>2]](a,b,c,d)}function ma(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(S(a,i[b+8>>2],f)){Y(b,c,d,e)}}function ia(a){var b=0;b=M-16|0;M=b;i[b+12>>2]=a;B(2544,4,i[b+12>>2]);M=b+16|0}function ha(a){var b=0;b=M-16|0;M=b;i[b+12>>2]=a;B(2584,5,i[b+12>>2]);M=b+16|0}function aa(a){var b=0;b=M-16|0;M=b;i[b+12>>2]=a;B(2384,0,i[b+12>>2]);M=b+16|0}function _(a){var b=0;b=M-16|0;M=b;i[b+12>>2]=a;B(2464,2,i[b+12>>2]);M=b+16|0}function Z(a){var b=0;b=M-16|0;M=b;i[b+12>>2]=a;B(2504,3,i[b+12>>2]);M=b+16|0}function $(a){var b=0;b=M-16|0;M=b;i[b+12>>2]=a;B(2424,1,i[b+12>>2]);M=b+16|0}function sa(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(S(a,i[b+8>>2],0)){X(b,c,d)}}function Ba(a){var b=0;b=a&31;a=0-a&31;return(-1>>>b&-2)<<b|(-1<<a&-2)>>>a}
function ka(){var a=0;a=M-16|0;M=a;i[a+12>>2]=3356;ba();M=a+16|0}function ya(a,b,c){a=a|0;b=b|0;c=c|0;return S(a,b,0)|0}function ca(a,b,c,d){J(a|0,b|0,8,0,c|0,-1,d|0)}function la(a){a=a|0;a=M-a&-16;M=a;return a|0}function za(a){a=a|0;return a|0}function V(a){a=a|0;da(a)}function va(){return M|0}function pa(a){a=a|0;M=a}function ga(a){a=a|0}
// EMSCRIPTEN_END_FUNCS
var N=c([null,za,V,ga,ga,ya,V,xa,ma,oa,sa,V,wa,ua,ta,V,na,qa,ra]);function O(){return f.byteLength/65536|0}return{"m":ka,"n":Aa,"o":ba,"p":ea,"q":va,"r":pa,"s":la,"t":da,"u":N}}return P(R)}
// EMSCRIPTEN_END_ASM




)(asmLibraryArg)},instantiate:function(binary,info){return{then:function(ok){var module=new WebAssembly.Module(binary);ok({"instance":new WebAssembly.Instance(module)})}}},RuntimeError:Error};wasmBinary=[];if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}function setValue(ptr,value,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":HEAP8[ptr>>0]=value;break;case"i8":HEAP8[ptr>>0]=value;break;case"i16":HEAP16[ptr>>1]=value;break;case"i32":HEAP32[ptr>>2]=value;break;case"i64":tempI64=[value>>>0,(tempDouble=value,+Math.abs(tempDouble)>=1?tempDouble>0?(Math.min(+Math.floor(tempDouble/4294967296),4294967295)|0)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[ptr>>2]=tempI64[0],HEAP32[ptr+4>>2]=tempI64[1];break;case"float":HEAPF32[ptr>>2]=value;break;case"double":HEAPF64[ptr>>3]=value;break;default:abort("invalid type for setValue: "+type)}}function getValue(ptr,type,noSafe){type=type||"i8";if(type.charAt(type.length-1)==="*")type="i32";switch(type){case"i1":return HEAP8[ptr>>0];case"i8":return HEAP8[ptr>>0];case"i16":return HEAP16[ptr>>1];case"i32":return HEAP32[ptr>>2];case"i64":return HEAP32[ptr>>2];case"float":return HEAPF32[ptr>>2];case"double":return Number(HEAPF64[ptr>>3]);default:abort("invalid type for getValue: "+type)}return null}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}function getCFunc(ident){var func=Module["_"+ident];assert(func,"Cannot call unknown function "+ident+", make sure it is exported");return func}function ccall(ident,returnType,argTypes,args,opts){var toC={"string":function(str){var ret=0;if(str!==null&&str!==undefined&&str!==0){var len=(str.length<<2)+1;ret=stackAlloc(len);stringToUTF8(str,ret,len)}return ret},"array":function(arr){var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string")return UTF8ToString(ret);if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret}function cwrap(ident,returnType,argTypes,opts){argTypes=argTypes||[];var numericArgs=argTypes.every(function(type){return type==="number"});var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return function(){return ccall(ident,returnType,argTypes,arguments,opts)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}var UTF16Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf-16le"):undefined;function UTF16ToString(ptr,maxBytesToRead){var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder){return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr))}else{var str="";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str}}function stringToUTF16(str,outPtr,maxBytesToWrite){if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr}function lengthBytesUTF16(str){return str.length*2}function UTF32ToString(ptr,maxBytesToRead){var i=0;var str="";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str}function stringToUTF32(str,outPtr,maxBytesToWrite){if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr}function lengthBytesUTF32(str){var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len}function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||8388608;if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"]}else{wasmMemory=new WebAssembly.Memory({"initial":INITIAL_MEMORY/65536,"maximum":INITIAL_MEMORY/65536})}if(wasmMemory){buffer=wasmMemory.buffer}INITIAL_MEMORY=buffer.byteLength;updateGlobalBufferAndViews(buffer);var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}if(!Math.imul||Math.imul(4294967295,5)!==-5)Math.imul=function imul(a,b){var ah=a>>>16;var al=a&65535;var bh=b>>>16;var bl=b&65535;return al*bl+(ah*bl+al*bh<<16)|0};if(!Math.fround){var froundBuffer=new Float32Array(1);Math.fround=function(x){froundBuffer[0]=x;return froundBuffer[0]}}if(!Math.clz32)Math.clz32=function(x){var n=32;var y=x>>16;if(y){n-=16;x=y}y=x>>8;if(y){n-=8;x=y}y=x>>4;if(y){n-=4;x=y}y=x>>2;if(y){n-=2;x=y}y=x>>1;if(y)return n-2;return n-x};if(!Math.trunc)Math.trunc=function(x){return x<0?Math.ceil(x):Math.floor(x)};var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){{if(Module["onAbort"]){Module["onAbort"](what)}}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var memoryInitializer="opusnoWasm.js.mem";var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile="opusnoWasm.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;runMemoryInitializer();wasmTable=Module["asm"]["u"];addOnInit(Module["asm"]["m"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}var tempDouble;var tempI64;function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function __embind_register_bigint(primitiveType,name,size,minRange,maxRange){}function getShiftFromSize(size){switch(size){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+size)}}function embind_init_charCodes(){var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes}var embind_charCodes=undefined;function readLatin1String(ptr){var ret="";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret}var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var char_0=48;var char_9=57;function makeLegalFunctionName(name){if(undefined===name){return"_unknown"}name=name.replace(/[^a-zA-Z0-9_]/g,"$");var f=name.charCodeAt(0);if(f>=char_0&&f<=char_9){return"_"+name}else{return name}}function createNamedFunction(name,body){name=makeLegalFunctionName(name);return new Function("body","return function "+name+"() {\n"+'    "use strict";'+"    return body.apply(this, arguments);\n"+"};\n")(body)}function extendError(baseErrorType,errorName){var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+"\n"+stack.replace(/^Error(:[^\n]*)?\n/,"")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return this.name+": "+this.message}};return errorClass}var BindingError=undefined;function throwBindingError(message){throw new BindingError(message)}var InternalError=undefined;function registerType(rawType,registeredInstance,options){options=options||{};if(!("argPackAdvance"in registeredInstance)){throw new TypeError("registerType registeredInstance requires argPackAdvance")}var name=registeredInstance.name;if(!rawType){throwBindingError('type "'+name+'" must have a positive integer typeid pointer')}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError("Cannot register type '"+name+"' twice")}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(function(cb){cb()})}}function __embind_register_bool(rawType,name,size,trueValue,falseValue){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(wt){return!!wt},"toWireType":function(destructors,o){return o?trueValue:falseValue},"argPackAdvance":8,"readValueFromPointer":function(pointer){var heap;if(size===1){heap=HEAP8}else if(size===2){heap=HEAP16}else if(size===4){heap=HEAP32}else{throw new TypeError("Unknown boolean type size: "+name)}return this["fromWireType"](heap[pointer>>shift])},destructorFunction:null})}var emval_free_list=[];var emval_handle_array=[{},{value:undefined},{value:null},{value:true},{value:false}];function __emval_decref(handle){if(handle>4&&0===--emval_handle_array[handle].refcount){emval_handle_array[handle]=undefined;emval_free_list.push(handle)}}function count_emval_handles(){var count=0;for(var i=5;i<emval_handle_array.length;++i){if(emval_handle_array[i]!==undefined){++count}}return count}function get_first_emval(){for(var i=5;i<emval_handle_array.length;++i){if(emval_handle_array[i]!==undefined){return emval_handle_array[i]}}return null}function init_emval(){Module["count_emval_handles"]=count_emval_handles;Module["get_first_emval"]=get_first_emval}var Emval={toValue:function(handle){if(!handle){throwBindingError("Cannot use deleted val. handle = "+handle)}return emval_handle_array[handle].value},toHandle:function(value){switch(value){case undefined:{return 1}case null:{return 2}case true:{return 3}case false:{return 4}default:{var handle=emval_free_list.length?emval_free_list.pop():emval_handle_array.length;emval_handle_array[handle]={refcount:1,value:value};return handle}}}};function simpleReadValueFromPointer(pointer){return this["fromWireType"](HEAPU32[pointer>>2])}function __embind_register_emval(rawType,name){name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(handle){var rv=Emval.toValue(handle);__emval_decref(handle);return rv},"toWireType":function(destructors,value){return Emval.toHandle(value)},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:null})}function _embind_repr(v){if(v===null){return"null"}var t=typeof v;if(t==="object"||t==="array"||t==="function"){return v.toString()}else{return""+v}}function floatReadValueFromPointer(name,shift){switch(shift){case 2:return function(pointer){return this["fromWireType"](HEAPF32[pointer>>2])};case 3:return function(pointer){return this["fromWireType"](HEAPF64[pointer>>3])};default:throw new TypeError("Unknown float type: "+name)}}function __embind_register_float(rawType,name,size){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(value){return value},"toWireType":function(destructors,value){return value},"argPackAdvance":8,"readValueFromPointer":floatReadValueFromPointer(name,shift),destructorFunction:null})}function integerReadValueFromPointer(name,shift,signed){switch(shift){case 0:return signed?function readS8FromPointer(pointer){return HEAP8[pointer]}:function readU8FromPointer(pointer){return HEAPU8[pointer]};case 1:return signed?function readS16FromPointer(pointer){return HEAP16[pointer>>1]}:function readU16FromPointer(pointer){return HEAPU16[pointer>>1]};case 2:return signed?function readS32FromPointer(pointer){return HEAP32[pointer>>2]}:function readU32FromPointer(pointer){return HEAPU32[pointer>>2]};default:throw new TypeError("Unknown integer type: "+name)}}function __embind_register_integer(primitiveType,name,size,minRange,maxRange){name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var shift=getShiftFromSize(size);var fromWireType=function(value){return value};if(minRange===0){var bitshift=32-8*size;fromWireType=function(value){return value<<bitshift>>>bitshift}}var isUnsignedType=name.includes("unsigned");registerType(primitiveType,{name:name,"fromWireType":fromWireType,"toWireType":function(destructors,value){if(typeof value!=="number"&&typeof value!=="boolean"){throw new TypeError('Cannot convert "'+_embind_repr(value)+'" to '+this.name)}if(value<minRange||value>maxRange){throw new TypeError('Passing a number "'+_embind_repr(value)+'" from JS side to C/C++ side to an argument of type "'+name+'", which is outside the valid range ['+minRange+", "+maxRange+"]!")}return isUnsignedType?value>>>0:value|0},"argPackAdvance":8,"readValueFromPointer":integerReadValueFromPointer(name,shift,minRange!==0),destructorFunction:null})}function __embind_register_memory_view(rawType,dataTypeIndex,name){var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){handle=handle>>2;var heap=HEAPU32;var size=heap[handle];var data=heap[handle+1];return new TA(buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":decodeMemoryView,"argPackAdvance":8,"readValueFromPointer":decodeMemoryView},{ignoreDuplicateRegistrations:true})}function __embind_register_std_string(rawType,name){name=readLatin1String(name);var stdStringIsUTF8=name==="std::string";registerType(rawType,{name:name,"fromWireType":function(value){var length=HEAPU32[value>>2];var str;if(stdStringIsUTF8){var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[value+4+i])}str=a.join("")}_free(value);return str},"toWireType":function(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var getLength;var valueIsOfTypeString=typeof value==="string";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError("Cannot pass non-string to std::string")}if(stdStringIsUTF8&&valueIsOfTypeString){getLength=function(){return lengthBytesUTF8(value)}}else{getLength=function(){return value.length}}var length=getLength();var ptr=_malloc(4+length+1);HEAPU32[ptr>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr+4,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError("String has UTF-16 code units that do not fit in 8 bits")}HEAPU8[ptr+4+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+4+i]=value[i]}}}if(destructors!==null){destructors.push(_free,ptr)}return ptr},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr)}})}function __embind_register_std_wstring(rawType,charSize,name){name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=function(){return HEAPU16};shift=1}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=function(){return HEAPU32};shift=2}registerType(rawType,{name:name,"fromWireType":function(value){var length=HEAPU32[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},"toWireType":function(destructors,value){if(!(typeof value==="string")){throwBindingError("Cannot pass non-string to C++ string type "+name)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr)}})}function __embind_register_void(rawType,name){name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,"argPackAdvance":0,"fromWireType":function(){return undefined},"toWireType":function(destructors,o){return undefined}})}var _emscripten_memcpy_big=Uint8Array.prototype.copyWithin?function(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}:function(dest,src,num){HEAPU8.set(HEAPU8.subarray(src,src+num),dest)};function abortOnCannotGrowMemory(requestedSize){abort("OOM")}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;abortOnCannotGrowMemory(requestedSize)}embind_init_charCodes();BindingError=Module["BindingError"]=extendError(Error,"BindingError");InternalError=Module["InternalError"]=extendError(Error,"InternalError");init_emval();var asmLibraryArg={"j":__embind_register_bigint,"k":__embind_register_bool,"i":__embind_register_emval,"f":__embind_register_float,"c":__embind_register_integer,"b":__embind_register_memory_view,"g":__embind_register_std_string,"e":__embind_register_std_wstring,"l":__embind_register_void,"h":_emscripten_memcpy_big,"d":_emscripten_resize_heap,"a":wasmMemory};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["m"]).apply(null,arguments)};var ___getTypeName=Module["___getTypeName"]=function(){return(___getTypeName=Module["___getTypeName"]=Module["asm"]["n"]).apply(null,arguments)};var ___embind_register_native_and_builtin_types=Module["___embind_register_native_and_builtin_types"]=function(){return(___embind_register_native_and_builtin_types=Module["___embind_register_native_and_builtin_types"]=Module["asm"]["o"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["p"]).apply(null,arguments)};var stackSave=Module["stackSave"]=function(){return(stackSave=Module["stackSave"]=Module["asm"]["q"]).apply(null,arguments)};var stackRestore=Module["stackRestore"]=function(){return(stackRestore=Module["stackRestore"]=Module["asm"]["r"]).apply(null,arguments)};var stackAlloc=Module["stackAlloc"]=function(){return(stackAlloc=Module["stackAlloc"]=Module["asm"]["s"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["t"]).apply(null,arguments)};Module["ccall"]=ccall;Module["cwrap"]=cwrap;Module["setValue"]=setValue;Module["getValue"]=getValue;function runMemoryInitializer(){if(!memoryInitializer)return;if(!isDataURI(memoryInitializer)){memoryInitializer=locateFile(memoryInitializer)}if(ENVIRONMENT_IS_NODE||ENVIRONMENT_IS_SHELL){var data=readBinary(memoryInitializer);HEAPU8.set(data,1024)}else{addRunDependency("memory initializer");var applyMemoryInitializer=function(data){if(data.byteLength)data=new Uint8Array(data);HEAPU8.set(data,1024);if(Module["memoryInitializerRequest"])delete Module["memoryInitializerRequest"].response;removeRunDependency("memory initializer")};var doBrowserLoad=function(){readAsync(memoryInitializer,applyMemoryInitializer,function(){var e=new Error("could not load memory initializer "+memoryInitializer);readyPromiseReject(e)})};if(Module["memoryInitializerRequest"]){var useRequest=function(){var request=Module["memoryInitializerRequest"];var response=request.response;if(request.status!==200&&request.status!==0){console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+request.status+", retrying "+memoryInitializer);doBrowserLoad();return}applyMemoryInitializer(response)};if(Module["memoryInitializerRequest"].response){setTimeout(useRequest,0)}else{Module["memoryInitializerRequest"].addEventListener("load",useRequest)}}else{doBrowserLoad()}}}var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();


  return Module.ready
}
);
})();
export default Module;