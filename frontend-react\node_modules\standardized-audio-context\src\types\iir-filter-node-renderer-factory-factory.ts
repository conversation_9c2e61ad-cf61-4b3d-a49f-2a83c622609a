import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TIIRFilterNodeRendererFactory } from './iir-filter-node-renderer-factory';
import { TNativeAudioBufferSourceNodeFactory } from './native-audio-buffer-source-node-factory';
import { TNativeOfflineAudioContextConstructor } from './native-offline-audio-context-constructor';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';
import { TRenderNativeOfflineAudioContextFunction } from './render-native-offline-audio-context-function';

export type TIIRFilterNodeRendererFactoryFactory = (
    createNativeAudioBufferSourceNode: TNativeAudioBufferSourceNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    nativeOfflineAudioContextConstructor: null | TNativeOfflineAudioContextConstructor,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction,
    renderNativeOfflineAudioContext: TRenderNativeOfflineAudioContextFunction
) => TIIRFilterNodeRendererFactory;
