import { IOfflineAudioContextConstructor } from '../interfaces';
import { TBaseAudioContextConstructor } from './base-audio-context-constructor';
import { TCacheTestResultFunction } from './cache-test-result-function';
import { TCreateNativeOfflineAudioContextFunction } from './create-native-offline-audio-context-function';
import { TInvalidStateErrorFactory } from './invalid-state-error-factory';
import { TStartRenderingFunction } from './start-rendering-function';

export type TOfflineAudioContextConstructorFactory = (
    baseAudioContextConstructor: TBaseAudioContextConstructor,
    cacheTestResult: TCacheTestResultFunction,
    createInvalidStateError: TInvalidStateErrorFactory,
    createNativeOfflineAudioContext: TCreateNativeOfflineAudioContextFunction,
    startRendering: TStartRenderingFunction
) => IOfflineAudioContextConstructor;
