import { TCreateNativeOfflineAudioContextFunction } from './create-native-offline-audio-context-function';
import { TNativeOfflineAudioContextConstructor } from './native-offline-audio-context-constructor';
import { TNotSupportedErrorFactory } from './not-supported-error-factory';

export type TCreateNativeOfflineAudioContextFactory = (
    createNotSupportedError: TNotSupportedErrorFactory,
    nativeOfflineAudioContextConstructor: null | TNativeOfflineAudioContextConstructor
) => TCreateNativeOfflineAudioContextFunction;
