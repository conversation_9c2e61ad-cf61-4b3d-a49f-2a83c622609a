import { TBackupOfflineAudioContextStore } from './backup-offline-audio-context-store';
import { TGetOrCreateBackupOfflineAudioContextFunction } from './get-or-create-backup-offline-audio-context-function';
import { TNativeOfflineAudioContextConstructor } from './native-offline-audio-context-constructor';

export type TGetOrCreateBackupOfflineAudioContextFactory = (
    backupOfflineAudioContextStore: TBackupOfflineAudioContextStore,
    nativeOfflineAudioContextConstructor: null | TNativeOfflineAudioContextConstructor
) => TGetOrCreateBackupOfflineAudioContextFunction;
