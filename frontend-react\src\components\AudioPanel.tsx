import { useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { audio } from '../lib/backend';
import { useEventPublisher } from '../hooks/useEventBus';
import { useAppStore } from '../stores/appStore';

interface AudioPanelProps {
  currentFrequency: number;
  onStateChange?: (state: {
    NREnabled: boolean;
    NBEnabled: boolean;
    ANEnabled: boolean;
    CTCSSSupressEnabled: boolean;
    mute: boolean;
  }) => void;
}

export interface AudioPanelRef {
  updateSignalMeter: () => void;
  initializeAudio: () => void;
  handleMuteChange: () => void;
  handleNRChange: () => void;
  handleNBChange: () => void;
  handleANChange: () => void;
  handleCTCSSChange: () => void;
  handleSquelchChange: () => void;
  handleRecordingChange: () => void;
  NREnabled: boolean;
  NBEnabled: boolean;
  ANEnabled: boolean;
  CTCSSSupressEnabled: boolean;
  mute: boolean;
}

const AudioPanel = forwardRef<AudioPanelRef, AudioPanelProps>(({
  onStateChange
}, ref) => {
  // Local state
  const [volume, setVolume] = useState(65);
  const [squelchEnable, setSquelchEnable] = useState(false);
  const [squelch, setSquelch] = useState(-50);
  const [ft8Enabled, setFt8Enabled] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [canDownload, setCanDownload] = useState(false);

  // Global state from store
  const {
    mute,
    NREnabled,
    NBEnabled,
    ANEnabled,
    CTCSSSupressEnabled,
    setAudioPanelState
  } = useAppStore();

  // Refs and constants
  const numberOfDots = 35;
  
  const publishEvent = useEventPublisher();

  // S-meter drawing function
  const drawSMeter = useCallback((activeSegments: number) => {
    const canvas = document.getElementById("sMeter") as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    canvas.width = 300;
    canvas.height = 40;

    const width = canvas.width;
    const height = canvas.height;

    ctx.clearRect(0, 0, width, height);

    const segmentWidth = 6;
    const segmentGap = 3;
    const segmentHeight = 8;
    const lineY = 15;
    const labelY = 25;
    const tickHeight = 5;
    const longTickHeight = 5;

    const s9Position = width / 2;

    ctx.strokeStyle = "#0071e3";
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(0, lineY);
    ctx.lineTo(s9Position, lineY);
    ctx.stroke();

    ctx.strokeStyle = "#ff3b30";
    ctx.beginPath();
    ctx.moveTo(s9Position, lineY);
    ctx.lineTo(268, lineY);
    ctx.stroke();

    for (let i = 0; i < 30; i++) {
      const x = i * (segmentWidth + segmentGap);
      if (i < activeSegments) {
        ctx.fillStyle = i < 17 ? "#0071e3" : "#ff3b30";
      } else {
        ctx.fillStyle = i < 17 ? "rgba(0, 113, 227, 0.2)" : "rgba(255, 59, 48, 0.2)";
      }
      ctx.fillRect(x, 0, segmentWidth, segmentHeight);
    }

    ctx.font = "11px -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', sans-serif";
    ctx.textAlign = "center";

    const labels = ["S1", "3", "5", "7", "9", "+20", "+40", "+60dB"];

    for (let i = 0; i <= 16; i++) {
      const x = i * 16.6970588235;
      ctx.fillStyle = x <= s9Position ? "#0071e3" : "#ff3b30";

      if (i % 2 === 1) {
        ctx.fillRect(x, lineY, 1, longTickHeight + 2);
        if ((i - 1) / 2 < labels.length) {
          ctx.fillText(labels[(i - 1) / 2], x, labelY + 8);
        }
      } else {
        ctx.fillRect(x, lineY, 1, tickHeight);
      }
    }
  }, []);

  const setSignalStrength = useCallback((db: number) => {
    db = Math.min(Math.max(db, -100), 0);
    const activeSegments = Math.round(((db + 100) * numberOfDots) / 100);
    drawSMeter(activeSegments);
  }, [drawSMeter]);

  // Audio control handlers
  const handleMuteChange = useCallback(() => {
    const newMute = !mute;
    setAudioPanelState({ mute: newMute });
    audio.setMute(newMute);
    
    const state = { mute: newMute, NREnabled, NBEnabled, ANEnabled, CTCSSSupressEnabled };
    onStateChange?.(state);
    publishEvent('muteStateChanged', newMute);
  }, [mute, NREnabled, NBEnabled, ANEnabled, CTCSSSupressEnabled, setAudioPanelState, onStateChange, publishEvent]);

  const handleVolumeChange = useCallback((newVolume: number) => {
    setVolume(newVolume);
    audio.setGain(Math.pow(10, (newVolume - 50) / 50 + 2.6));
  }, []);

  const handleSquelchChange = useCallback(() => {
    const newSquelchEnable = !squelchEnable;
    setSquelchEnable(newSquelchEnable);
    audio.setSquelch(newSquelchEnable);
  }, [squelchEnable]);

  const handleSquelchMove = useCallback((newSquelch: number) => {
    setSquelch(newSquelch);
    audio.setSquelchThreshold(newSquelch);
  }, []);

  const handleNRChange = useCallback(() => {
    const newNREnabled = !NREnabled;
    setAudioPanelState({ NREnabled: newNREnabled });
    audio.decoder.set_nr(newNREnabled);
    
    const state = { mute, NREnabled: newNREnabled, NBEnabled, ANEnabled, CTCSSSupressEnabled };
    onStateChange?.(state);
  }, [NREnabled, mute, NBEnabled, ANEnabled, CTCSSSupressEnabled, setAudioPanelState, onStateChange]);

  const handleNBChange = useCallback(() => {
    const newNBEnabled = !NBEnabled;
    setAudioPanelState({ NBEnabled: newNBEnabled });
    audio.nb = newNBEnabled;
    audio.decoder.set_nb(newNBEnabled);
    
    const state = { mute, NREnabled, NBEnabled: newNBEnabled, ANEnabled, CTCSSSupressEnabled };
    onStateChange?.(state);
  }, [NBEnabled, mute, NREnabled, ANEnabled, CTCSSSupressEnabled, setAudioPanelState, onStateChange]);

  const handleANChange = useCallback(() => {
    const newANEnabled = !ANEnabled;
    setAudioPanelState({ ANEnabled: newANEnabled });
    audio.decoder.set_an(newANEnabled);
    
    const state = { mute, NREnabled, NBEnabled, ANEnabled: newANEnabled, CTCSSSupressEnabled };
    onStateChange?.(state);
  }, [ANEnabled, mute, NREnabled, NBEnabled, CTCSSSupressEnabled, setAudioPanelState, onStateChange]);

  const handleCTCSSChange = useCallback(() => {
    const newCTCSSSupressEnabled = !CTCSSSupressEnabled;
    setAudioPanelState({ CTCSSSupressEnabled: newCTCSSSupressEnabled });
    audio.setCTCSSFilter(newCTCSSSupressEnabled);
    
    const state = { mute, NREnabled, NBEnabled, ANEnabled, CTCSSSupressEnabled: newCTCSSSupressEnabled };
    onStateChange?.(state);
  }, [CTCSSSupressEnabled, mute, NREnabled, NBEnabled, ANEnabled, setAudioPanelState, onStateChange]);

  const handleFt8Decoder = useCallback((value: boolean) => {
    setFt8Enabled(value);
    audio.setFT8Decoding(value);
  }, []);

  const toggleRecording = useCallback(() => {
    if (!isRecording) {
      audio.startRecording();
      setIsRecording(true);
      setCanDownload(false);
    } else {
      audio.stopRecording();
      setIsRecording(false);
      setCanDownload(true);
    }
  }, [isRecording]);

  const downloadRecording = useCallback(() => {
    audio.downloadRecording();
  }, []);

  // Update signal meter periodically
  const updateSignalMeter = useCallback(() => {
    const newPower = (audio.getPowerDb() / 150) * 100 + audio.smeter_offset;
    setSignalStrength(newPower);
  }, [setSignalStrength]);

  // Initialize audio settings
  const initializeAudio = useCallback(() => {
    handleVolumeChange(volume);
  }, [volume, handleVolumeChange]);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    updateSignalMeter,
    initializeAudio,
    handleMuteChange,
    handleNRChange,
    handleNBChange,
    handleANChange,
    handleCTCSSChange,
    handleSquelchChange,
    handleRecordingChange: toggleRecording,
    NREnabled,
    NBEnabled,
    ANEnabled,
    CTCSSSupressEnabled,
    mute
  }), [
    updateSignalMeter,
    initializeAudio,
    handleMuteChange,
    handleNRChange,
    handleNBChange,
    handleANChange,
    handleCTCSSChange,
    handleSquelchChange,
    toggleRecording,
    NREnabled,
    NBEnabled,
    ANEnabled,
    CTCSSSupressEnabled,
    mute
  ]);

  return (
    <div className="audio-controls">
      <h2 className="section-title">Audio Controls</h2>
      
      {/* Volume Control */}
      <div className="control-group" id="volume-slider">
        <div className="control-header">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="control-icon">
            <path d="M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z" fill="currentColor"/>
            <path d="M11.5 4.5a4 4 0 010 7M13 3a6 6 0 010 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
          </svg>
          <span className="control-label">Volume</span>
          <span className="control-value">{volume}%</span>
        </div>
        <div className="slider-container">
          <button
            className={`mute-button ${mute ? 'active' : ''}`}
            onClick={handleMuteChange}
            aria-label={mute ? 'Unmute' : 'Mute'}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              {mute ? (
                <>
                  <path d="M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z" fill="currentColor"/>
                  <path d="M11 6l4 4m0-4l-4 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                </>
              ) : (
                <>
                  <path d="M9 2.5v11a.5.5 0 01-.85.35L5.6 11.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h2.6l2.55-2.35A.5.5 0 019 2.5z" fill="currentColor"/>
                  <path d="M11.5 4.5a4 4 0 010 7M13 3a6 6 0 010 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                </>
              )}
            </svg>
          </button>
          <div className="slider-wrapper">
            <input
              type="range"
              value={volume}
              onChange={(e) => handleVolumeChange(Number(e.target.value))}
              disabled={mute}
              min="0"
              max="100"
              step="1"
              className="slider volume-slider"
            />
            <div className="slider-track" style={{ width: `${volume}%` }}></div>
          </div>
        </div>
      </div>

      {/* Squelch Control */}
      <div className="control-group" id="squelch-slider">
        <div className="control-header">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="control-icon">
            <path d="M8 1a7 7 0 110 14A7 7 0 018 1zM8 5a3 3 0 110 6 3 3 0 010-6z" stroke="currentColor" strokeWidth="1.5"/>
            <circle cx="8" cy="8" r="1.5" fill="currentColor"/>
          </svg>
          <span className="control-label">Squelch</span>
          <span className="control-value">{squelch} dB</span>
        </div>
        <div className="slider-container">
          <button
            className={`squelch-button ${squelchEnable ? 'active' : ''}`}
            onClick={handleSquelchChange}
            aria-label={squelchEnable ? 'Disable squelch' : 'Enable squelch'}
          >
            <span>SQ</span>
          </button>
          <div className="slider-wrapper">
            <input
              type="range"
              value={squelch}
              onChange={(e) => handleSquelchMove(Number(e.target.value))}
              min="-150"
              max="0"
              step="1"
              className="slider squelch-slider"
            />
            <div className="slider-track green" style={{ width: `${((squelch + 150) / 150) * 100}%` }}></div>
          </div>
        </div>
      </div>

      {/* Decoder Options */}
      <div className="decoder-section">
        <div className="section-header">
          <div className="accent-dot bg-green"></div>
          <h3 className="section-label">Decoder</h3>
        </div>

        <div className="decoder-controls">
          <div className="decoder-row">
            <label className="decoder-label">
              <input
                type="checkbox"
                checked={ft8Enabled}
                onChange={(e) => handleFt8Decoder(e.target.checked)}
                className="decoder-checkbox"
              />
              <span className="checkmark"></span>
              FT8 Decoder
            </label>
          </div>
        </div>

        {ft8Enabled && (
          <div className="ft8-panel">
            <div className="ft8-header">
              <span className="ft8-title">FT8 Messages</span>
              <div className="ft8-stats">
                <span id="farthest-distance">0 km</span>
              </div>
            </div>
            <div id="ft8MessagesList" className="ft8-messages-list">
              {/* FT8 messages will be populated here */}
            </div>
          </div>
        )}
      </div>

      {/* Recording Controls */}
      <div className="recording-section">
        <div className="section-header">
          <div className="accent-dot bg-red"></div>
          <h3 className="section-label">Recording</h3>
        </div>

        <div className="recording-controls">
          <button
            className={`recording-button ${isRecording ? 'recording' : ''}`}
            onClick={toggleRecording}
            title={isRecording ? 'Stop Recording' : 'Start Recording'}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              {isRecording ? (
                <rect x="4" y="4" width="8" height="8" fill="currentColor" rx="1"/>
              ) : (
                <circle cx="8" cy="8" r="4" fill="currentColor"/>
              )}
            </svg>
            <span>{isRecording ? 'Stop' : 'Record'}</span>
          </button>

          {canDownload && (
            <button
              className="download-button"
              onClick={downloadRecording}
              title="Download Recording"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 1v10m0 0l3-3m-3 3L5 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 12v2a2 2 0 002 2h8a2 2 0 002-2v-2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
              </svg>
              <span>Download</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
});

AudioPanel.displayName = 'AudioPanel';

export default AudioPanel;
