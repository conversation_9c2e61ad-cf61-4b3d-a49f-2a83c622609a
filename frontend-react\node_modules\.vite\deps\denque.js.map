{"version": 3, "sources": ["../../denque/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * Custom implementation of a double ended queue.\n */\nfunction Denque(array, options) {\n  var options = options || {};\n  this._capacity = options.capacity;\n\n  this._head = 0;\n  this._tail = 0;\n\n  if (Array.isArray(array)) {\n    this._fromArray(array);\n  } else {\n    this._capacityMask = 0x3;\n    this._list = new Array(4);\n  }\n}\n\n/**\n * --------------\n *  PUBLIC API\n * -------------\n */\n\n/**\n * Returns the item at the specified index from the list.\n * 0 is the first element, 1 is the second, and so on...\n * Elements at negative values are that many from the end: -1 is one before the end\n * (the last element), -2 is two before the end (one before last), etc.\n * @param index\n * @returns {*}\n */\nDenque.prototype.peekAt = function peekAt(index) {\n  var i = index;\n  // expect a number or return undefined\n  if ((i !== (i | 0))) {\n    return void 0;\n  }\n  var len = this.size();\n  if (i >= len || i < -len) return undefined;\n  if (i < 0) i += len;\n  i = (this._head + i) & this._capacityMask;\n  return this._list[i];\n};\n\n/**\n * Alias for peekAt()\n * @param i\n * @returns {*}\n */\nDenque.prototype.get = function get(i) {\n  return this.peekAt(i);\n};\n\n/**\n * Returns the first item in the list without removing it.\n * @returns {*}\n */\nDenque.prototype.peek = function peek() {\n  if (this._head === this._tail) return undefined;\n  return this._list[this._head];\n};\n\n/**\n * Alias for peek()\n * @returns {*}\n */\nDenque.prototype.peekFront = function peekFront() {\n  return this.peek();\n};\n\n/**\n * Returns the item that is at the back of the queue without removing it.\n * Uses peekAt(-1)\n */\nDenque.prototype.peekBack = function peekBack() {\n  return this.peekAt(-1);\n};\n\n/**\n * Returns the current length of the queue\n * @return {Number}\n */\nObject.defineProperty(Denque.prototype, 'length', {\n  get: function length() {\n    return this.size();\n  }\n});\n\n/**\n * Return the number of items on the list, or 0 if empty.\n * @returns {number}\n */\nDenque.prototype.size = function size() {\n  if (this._head === this._tail) return 0;\n  if (this._head < this._tail) return this._tail - this._head;\n  else return this._capacityMask + 1 - (this._head - this._tail);\n};\n\n/**\n * Add an item at the beginning of the list.\n * @param item\n */\nDenque.prototype.unshift = function unshift(item) {\n  if (arguments.length === 0) return this.size();\n  var len = this._list.length;\n  this._head = (this._head - 1 + len) & this._capacityMask;\n  this._list[this._head] = item;\n  if (this._tail === this._head) this._growArray();\n  if (this._capacity && this.size() > this._capacity) this.pop();\n  if (this._head < this._tail) return this._tail - this._head;\n  else return this._capacityMask + 1 - (this._head - this._tail);\n};\n\n/**\n * Remove and return the first item on the list,\n * Returns undefined if the list is empty.\n * @returns {*}\n */\nDenque.prototype.shift = function shift() {\n  var head = this._head;\n  if (head === this._tail) return undefined;\n  var item = this._list[head];\n  this._list[head] = undefined;\n  this._head = (head + 1) & this._capacityMask;\n  if (head < 2 && this._tail > 10000 && this._tail <= this._list.length >>> 2) this._shrinkArray();\n  return item;\n};\n\n/**\n * Add an item to the bottom of the list.\n * @param item\n */\nDenque.prototype.push = function push(item) {\n  if (arguments.length === 0) return this.size();\n  var tail = this._tail;\n  this._list[tail] = item;\n  this._tail = (tail + 1) & this._capacityMask;\n  if (this._tail === this._head) {\n    this._growArray();\n  }\n  if (this._capacity && this.size() > this._capacity) {\n    this.shift();\n  }\n  if (this._head < this._tail) return this._tail - this._head;\n  else return this._capacityMask + 1 - (this._head - this._tail);\n};\n\n/**\n * Remove and return the last item on the list.\n * Returns undefined if the list is empty.\n * @returns {*}\n */\nDenque.prototype.pop = function pop() {\n  var tail = this._tail;\n  if (tail === this._head) return undefined;\n  var len = this._list.length;\n  this._tail = (tail - 1 + len) & this._capacityMask;\n  var item = this._list[this._tail];\n  this._list[this._tail] = undefined;\n  if (this._head < 2 && tail > 10000 && tail <= len >>> 2) this._shrinkArray();\n  return item;\n};\n\n/**\n * Remove and return the item at the specified index from the list.\n * Returns undefined if the list is empty.\n * @param index\n * @returns {*}\n */\nDenque.prototype.removeOne = function removeOne(index) {\n  var i = index;\n  // expect a number or return undefined\n  if ((i !== (i | 0))) {\n    return void 0;\n  }\n  if (this._head === this._tail) return void 0;\n  var size = this.size();\n  var len = this._list.length;\n  if (i >= size || i < -size) return void 0;\n  if (i < 0) i += size;\n  i = (this._head + i) & this._capacityMask;\n  var item = this._list[i];\n  var k;\n  if (index < size / 2) {\n    for (k = index; k > 0; k--) {\n      this._list[i] = this._list[i = (i - 1 + len) & this._capacityMask];\n    }\n    this._list[i] = void 0;\n    this._head = (this._head + 1 + len) & this._capacityMask;\n  } else {\n    for (k = size - 1 - index; k > 0; k--) {\n      this._list[i] = this._list[i = (i + 1 + len) & this._capacityMask];\n    }\n    this._list[i] = void 0;\n    this._tail = (this._tail - 1 + len) & this._capacityMask;\n  }\n  return item;\n};\n\n/**\n * Remove number of items from the specified index from the list.\n * Returns array of removed items.\n * Returns undefined if the list is empty.\n * @param index\n * @param count\n * @returns {array}\n */\nDenque.prototype.remove = function remove(index, count) {\n  var i = index;\n  var removed;\n  var del_count = count;\n  // expect a number or return undefined\n  if ((i !== (i | 0))) {\n    return void 0;\n  }\n  if (this._head === this._tail) return void 0;\n  var size = this.size();\n  var len = this._list.length;\n  if (i >= size || i < -size || count < 1) return void 0;\n  if (i < 0) i += size;\n  if (count === 1 || !count) {\n    removed = new Array(1);\n    removed[0] = this.removeOne(i);\n    return removed;\n  }\n  if (i === 0 && i + count >= size) {\n    removed = this.toArray();\n    this.clear();\n    return removed;\n  }\n  if (i + count > size) count = size - i;\n  var k;\n  removed = new Array(count);\n  for (k = 0; k < count; k++) {\n    removed[k] = this._list[(this._head + i + k) & this._capacityMask];\n  }\n  i = (this._head + i) & this._capacityMask;\n  if (index + count === size) {\n    this._tail = (this._tail - count + len) & this._capacityMask;\n    for (k = count; k > 0; k--) {\n      this._list[i = (i + 1 + len) & this._capacityMask] = void 0;\n    }\n    return removed;\n  }\n  if (index === 0) {\n    this._head = (this._head + count + len) & this._capacityMask;\n    for (k = count - 1; k > 0; k--) {\n      this._list[i = (i + 1 + len) & this._capacityMask] = void 0;\n    }\n    return removed;\n  }\n  if (i < size / 2) {\n    this._head = (this._head + index + count + len) & this._capacityMask;\n    for (k = index; k > 0; k--) {\n      this.unshift(this._list[i = (i - 1 + len) & this._capacityMask]);\n    }\n    i = (this._head - 1 + len) & this._capacityMask;\n    while (del_count > 0) {\n      this._list[i = (i - 1 + len) & this._capacityMask] = void 0;\n      del_count--;\n    }\n    if (index < 0) this._tail = i;\n  } else {\n    this._tail = i;\n    i = (i + count + len) & this._capacityMask;\n    for (k = size - (count + index); k > 0; k--) {\n      this.push(this._list[i++]);\n    }\n    i = this._tail;\n    while (del_count > 0) {\n      this._list[i = (i + 1 + len) & this._capacityMask] = void 0;\n      del_count--;\n    }\n  }\n  if (this._head < 2 && this._tail > 10000 && this._tail <= len >>> 2) this._shrinkArray();\n  return removed;\n};\n\n/**\n * Native splice implementation.\n * Remove number of items from the specified index from the list and/or add new elements.\n * Returns array of removed items or empty array if count == 0.\n * Returns undefined if the list is empty.\n *\n * @param index\n * @param count\n * @param {...*} [elements]\n * @returns {array}\n */\nDenque.prototype.splice = function splice(index, count) {\n  var i = index;\n  // expect a number or return undefined\n  if ((i !== (i | 0))) {\n    return void 0;\n  }\n  var size = this.size();\n  if (i < 0) i += size;\n  if (i > size) return void 0;\n  if (arguments.length > 2) {\n    var k;\n    var temp;\n    var removed;\n    var arg_len = arguments.length;\n    var len = this._list.length;\n    var arguments_index = 2;\n    if (!size || i < size / 2) {\n      temp = new Array(i);\n      for (k = 0; k < i; k++) {\n        temp[k] = this._list[(this._head + k) & this._capacityMask];\n      }\n      if (count === 0) {\n        removed = [];\n        if (i > 0) {\n          this._head = (this._head + i + len) & this._capacityMask;\n        }\n      } else {\n        removed = this.remove(i, count);\n        this._head = (this._head + i + len) & this._capacityMask;\n      }\n      while (arg_len > arguments_index) {\n        this.unshift(arguments[--arg_len]);\n      }\n      for (k = i; k > 0; k--) {\n        this.unshift(temp[k - 1]);\n      }\n    } else {\n      temp = new Array(size - (i + count));\n      var leng = temp.length;\n      for (k = 0; k < leng; k++) {\n        temp[k] = this._list[(this._head + i + count + k) & this._capacityMask];\n      }\n      if (count === 0) {\n        removed = [];\n        if (i != size) {\n          this._tail = (this._head + i + len) & this._capacityMask;\n        }\n      } else {\n        removed = this.remove(i, count);\n        this._tail = (this._tail - leng + len) & this._capacityMask;\n      }\n      while (arguments_index < arg_len) {\n        this.push(arguments[arguments_index++]);\n      }\n      for (k = 0; k < leng; k++) {\n        this.push(temp[k]);\n      }\n    }\n    return removed;\n  } else {\n    return this.remove(i, count);\n  }\n};\n\n/**\n * Soft clear - does not reset capacity.\n */\nDenque.prototype.clear = function clear() {\n  this._list = new Array(this._list.length);\n  this._head = 0;\n  this._tail = 0;\n};\n\n/**\n * Returns true or false whether the list is empty.\n * @returns {boolean}\n */\nDenque.prototype.isEmpty = function isEmpty() {\n  return this._head === this._tail;\n};\n\n/**\n * Returns an array of all queue items.\n * @returns {Array}\n */\nDenque.prototype.toArray = function toArray() {\n  return this._copyArray(false);\n};\n\n/**\n * -------------\n *   INTERNALS\n * -------------\n */\n\n/**\n * Fills the queue with items from an array\n * For use in the constructor\n * @param array\n * @private\n */\nDenque.prototype._fromArray = function _fromArray(array) {\n  var length = array.length;\n  var capacity = this._nextPowerOf2(length);\n\n  this._list = new Array(capacity);\n  this._capacityMask = capacity - 1;\n  this._tail = length;\n\n  for (var i = 0; i < length; i++) this._list[i] = array[i];\n};\n\n/**\n *\n * @param fullCopy\n * @param size Initialize the array with a specific size. Will default to the current list size\n * @returns {Array}\n * @private\n */\nDenque.prototype._copyArray = function _copyArray(fullCopy, size) {\n  var src = this._list;\n  var capacity = src.length;\n  var length = this.length;\n  size = size | length;\n\n  // No prealloc requested and the buffer is contiguous\n  if (size == length && this._head < this._tail) {\n    // Simply do a fast slice copy\n    return this._list.slice(this._head, this._tail);\n  }\n\n  var dest = new Array(size);\n\n  var k = 0;\n  var i;\n  if (fullCopy || this._head > this._tail) {\n    for (i = this._head; i < capacity; i++) dest[k++] = src[i];\n    for (i = 0; i < this._tail; i++) dest[k++] = src[i];\n  } else {\n    for (i = this._head; i < this._tail; i++) dest[k++] = src[i];\n  }\n\n  return dest;\n}\n\n/**\n * Grows the internal list array.\n * @private\n */\nDenque.prototype._growArray = function _growArray() {\n  if (this._head != 0) {\n    // double array size and copy existing data, head to end, then beginning to tail.\n    var newList = this._copyArray(true, this._list.length << 1);\n\n    this._tail = this._list.length;\n    this._head = 0;\n\n    this._list = newList;\n  } else {\n    this._tail = this._list.length;\n    this._list.length <<= 1;\n  }\n\n  this._capacityMask = (this._capacityMask << 1) | 1;\n};\n\n/**\n * Shrinks the internal list array.\n * @private\n */\nDenque.prototype._shrinkArray = function _shrinkArray() {\n  this._list.length >>>= 1;\n  this._capacityMask >>>= 1;\n};\n\n/**\n * Find the next power of 2, at least 4\n * @private\n * @param {number} num \n * @returns {number}\n */\nDenque.prototype._nextPowerOf2 = function _nextPowerOf2(num) {\n  var log2 = Math.log(num) / Math.log(2);\n  var nextPow2 = 1 << (log2 + 1);\n\n  return Math.max(nextPow2, 4);\n}\n\nmodule.exports = Denque;\n"], "mappings": ";;;;;AAAA;AAAA;AAKA,aAAS,OAAO,OAAO,SAAS;AAC9B,UAAI,UAAU,WAAW,CAAC;AAC1B,WAAK,YAAY,QAAQ;AAEzB,WAAK,QAAQ;AACb,WAAK,QAAQ;AAEb,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAK,WAAW,KAAK;AAAA,MACvB,OAAO;AACL,aAAK,gBAAgB;AACrB,aAAK,QAAQ,IAAI,MAAM,CAAC;AAAA,MAC1B;AAAA,IACF;AAgBA,WAAO,UAAU,SAAS,SAAS,OAAO,OAAO;AAC/C,UAAI,IAAI;AAER,UAAK,OAAO,IAAI,IAAK;AACnB,eAAO;AAAA,MACT;AACA,UAAI,MAAM,KAAK,KAAK;AACpB,UAAI,KAAK,OAAO,IAAI,CAAC,IAAK,QAAO;AACjC,UAAI,IAAI,EAAG,MAAK;AAChB,UAAK,KAAK,QAAQ,IAAK,KAAK;AAC5B,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AAOA,WAAO,UAAU,MAAM,SAAS,IAAI,GAAG;AACrC,aAAO,KAAK,OAAO,CAAC;AAAA,IACtB;AAMA,WAAO,UAAU,OAAO,SAAS,OAAO;AACtC,UAAI,KAAK,UAAU,KAAK,MAAO,QAAO;AACtC,aAAO,KAAK,MAAM,KAAK,KAAK;AAAA,IAC9B;AAMA,WAAO,UAAU,YAAY,SAAS,YAAY;AAChD,aAAO,KAAK,KAAK;AAAA,IACnB;AAMA,WAAO,UAAU,WAAW,SAAS,WAAW;AAC9C,aAAO,KAAK,OAAO,EAAE;AAAA,IACvB;AAMA,WAAO,eAAe,OAAO,WAAW,UAAU;AAAA,MAChD,KAAK,SAAS,SAAS;AACrB,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF,CAAC;AAMD,WAAO,UAAU,OAAO,SAAS,OAAO;AACtC,UAAI,KAAK,UAAU,KAAK,MAAO,QAAO;AACtC,UAAI,KAAK,QAAQ,KAAK,MAAO,QAAO,KAAK,QAAQ,KAAK;AAAA,UACjD,QAAO,KAAK,gBAAgB,KAAK,KAAK,QAAQ,KAAK;AAAA,IAC1D;AAMA,WAAO,UAAU,UAAU,SAAS,QAAQ,MAAM;AAChD,UAAI,UAAU,WAAW,EAAG,QAAO,KAAK,KAAK;AAC7C,UAAI,MAAM,KAAK,MAAM;AACrB,WAAK,QAAS,KAAK,QAAQ,IAAI,MAAO,KAAK;AAC3C,WAAK,MAAM,KAAK,KAAK,IAAI;AACzB,UAAI,KAAK,UAAU,KAAK,MAAO,MAAK,WAAW;AAC/C,UAAI,KAAK,aAAa,KAAK,KAAK,IAAI,KAAK,UAAW,MAAK,IAAI;AAC7D,UAAI,KAAK,QAAQ,KAAK,MAAO,QAAO,KAAK,QAAQ,KAAK;AAAA,UACjD,QAAO,KAAK,gBAAgB,KAAK,KAAK,QAAQ,KAAK;AAAA,IAC1D;AAOA,WAAO,UAAU,QAAQ,SAAS,QAAQ;AACxC,UAAI,OAAO,KAAK;AAChB,UAAI,SAAS,KAAK,MAAO,QAAO;AAChC,UAAI,OAAO,KAAK,MAAM,IAAI;AAC1B,WAAK,MAAM,IAAI,IAAI;AACnB,WAAK,QAAS,OAAO,IAAK,KAAK;AAC/B,UAAI,OAAO,KAAK,KAAK,QAAQ,OAAS,KAAK,SAAS,KAAK,MAAM,WAAW,EAAG,MAAK,aAAa;AAC/F,aAAO;AAAA,IACT;AAMA,WAAO,UAAU,OAAO,SAAS,KAAK,MAAM;AAC1C,UAAI,UAAU,WAAW,EAAG,QAAO,KAAK,KAAK;AAC7C,UAAI,OAAO,KAAK;AAChB,WAAK,MAAM,IAAI,IAAI;AACnB,WAAK,QAAS,OAAO,IAAK,KAAK;AAC/B,UAAI,KAAK,UAAU,KAAK,OAAO;AAC7B,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,KAAK,aAAa,KAAK,KAAK,IAAI,KAAK,WAAW;AAClD,aAAK,MAAM;AAAA,MACb;AACA,UAAI,KAAK,QAAQ,KAAK,MAAO,QAAO,KAAK,QAAQ,KAAK;AAAA,UACjD,QAAO,KAAK,gBAAgB,KAAK,KAAK,QAAQ,KAAK;AAAA,IAC1D;AAOA,WAAO,UAAU,MAAM,SAAS,MAAM;AACpC,UAAI,OAAO,KAAK;AAChB,UAAI,SAAS,KAAK,MAAO,QAAO;AAChC,UAAI,MAAM,KAAK,MAAM;AACrB,WAAK,QAAS,OAAO,IAAI,MAAO,KAAK;AACrC,UAAI,OAAO,KAAK,MAAM,KAAK,KAAK;AAChC,WAAK,MAAM,KAAK,KAAK,IAAI;AACzB,UAAI,KAAK,QAAQ,KAAK,OAAO,OAAS,QAAQ,QAAQ,EAAG,MAAK,aAAa;AAC3E,aAAO;AAAA,IACT;AAQA,WAAO,UAAU,YAAY,SAAS,UAAU,OAAO;AACrD,UAAI,IAAI;AAER,UAAK,OAAO,IAAI,IAAK;AACnB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,UAAU,KAAK,MAAO,QAAO;AACtC,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,QAAQ,IAAI,CAAC,KAAM,QAAO;AACnC,UAAI,IAAI,EAAG,MAAK;AAChB,UAAK,KAAK,QAAQ,IAAK,KAAK;AAC5B,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,UAAI;AACJ,UAAI,QAAQ,OAAO,GAAG;AACpB,aAAK,IAAI,OAAO,IAAI,GAAG,KAAK;AAC1B,eAAK,MAAM,CAAC,IAAI,KAAK,MAAM,IAAK,IAAI,IAAI,MAAO,KAAK,aAAa;AAAA,QACnE;AACA,aAAK,MAAM,CAAC,IAAI;AAChB,aAAK,QAAS,KAAK,QAAQ,IAAI,MAAO,KAAK;AAAA,MAC7C,OAAO;AACL,aAAK,IAAI,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK;AACrC,eAAK,MAAM,CAAC,IAAI,KAAK,MAAM,IAAK,IAAI,IAAI,MAAO,KAAK,aAAa;AAAA,QACnE;AACA,aAAK,MAAM,CAAC,IAAI;AAChB,aAAK,QAAS,KAAK,QAAQ,IAAI,MAAO,KAAK;AAAA,MAC7C;AACA,aAAO;AAAA,IACT;AAUA,WAAO,UAAU,SAAS,SAAS,OAAO,OAAO,OAAO;AACtD,UAAI,IAAI;AACR,UAAI;AACJ,UAAI,YAAY;AAEhB,UAAK,OAAO,IAAI,IAAK;AACnB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,UAAU,KAAK,MAAO,QAAO;AACtC,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,QAAQ,IAAI,CAAC,QAAQ,QAAQ,EAAG,QAAO;AAChD,UAAI,IAAI,EAAG,MAAK;AAChB,UAAI,UAAU,KAAK,CAAC,OAAO;AACzB,kBAAU,IAAI,MAAM,CAAC;AACrB,gBAAQ,CAAC,IAAI,KAAK,UAAU,CAAC;AAC7B,eAAO;AAAA,MACT;AACA,UAAI,MAAM,KAAK,IAAI,SAAS,MAAM;AAChC,kBAAU,KAAK,QAAQ;AACvB,aAAK,MAAM;AACX,eAAO;AAAA,MACT;AACA,UAAI,IAAI,QAAQ,KAAM,SAAQ,OAAO;AACrC,UAAI;AACJ,gBAAU,IAAI,MAAM,KAAK;AACzB,WAAK,IAAI,GAAG,IAAI,OAAO,KAAK;AAC1B,gBAAQ,CAAC,IAAI,KAAK,MAAO,KAAK,QAAQ,IAAI,IAAK,KAAK,aAAa;AAAA,MACnE;AACA,UAAK,KAAK,QAAQ,IAAK,KAAK;AAC5B,UAAI,QAAQ,UAAU,MAAM;AAC1B,aAAK,QAAS,KAAK,QAAQ,QAAQ,MAAO,KAAK;AAC/C,aAAK,IAAI,OAAO,IAAI,GAAG,KAAK;AAC1B,eAAK,MAAM,IAAK,IAAI,IAAI,MAAO,KAAK,aAAa,IAAI;AAAA,QACvD;AACA,eAAO;AAAA,MACT;AACA,UAAI,UAAU,GAAG;AACf,aAAK,QAAS,KAAK,QAAQ,QAAQ,MAAO,KAAK;AAC/C,aAAK,IAAI,QAAQ,GAAG,IAAI,GAAG,KAAK;AAC9B,eAAK,MAAM,IAAK,IAAI,IAAI,MAAO,KAAK,aAAa,IAAI;AAAA,QACvD;AACA,eAAO;AAAA,MACT;AACA,UAAI,IAAI,OAAO,GAAG;AAChB,aAAK,QAAS,KAAK,QAAQ,QAAQ,QAAQ,MAAO,KAAK;AACvD,aAAK,IAAI,OAAO,IAAI,GAAG,KAAK;AAC1B,eAAK,QAAQ,KAAK,MAAM,IAAK,IAAI,IAAI,MAAO,KAAK,aAAa,CAAC;AAAA,QACjE;AACA,YAAK,KAAK,QAAQ,IAAI,MAAO,KAAK;AAClC,eAAO,YAAY,GAAG;AACpB,eAAK,MAAM,IAAK,IAAI,IAAI,MAAO,KAAK,aAAa,IAAI;AACrD;AAAA,QACF;AACA,YAAI,QAAQ,EAAG,MAAK,QAAQ;AAAA,MAC9B,OAAO;AACL,aAAK,QAAQ;AACb,YAAK,IAAI,QAAQ,MAAO,KAAK;AAC7B,aAAK,IAAI,QAAQ,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC3C,eAAK,KAAK,KAAK,MAAM,GAAG,CAAC;AAAA,QAC3B;AACA,YAAI,KAAK;AACT,eAAO,YAAY,GAAG;AACpB,eAAK,MAAM,IAAK,IAAI,IAAI,MAAO,KAAK,aAAa,IAAI;AACrD;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAS,KAAK,SAAS,QAAQ,EAAG,MAAK,aAAa;AACvF,aAAO;AAAA,IACT;AAaA,WAAO,UAAU,SAAS,SAAS,OAAO,OAAO,OAAO;AACtD,UAAI,IAAI;AAER,UAAK,OAAO,IAAI,IAAK;AACnB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,IAAI,EAAG,MAAK;AAChB,UAAI,IAAI,KAAM,QAAO;AACrB,UAAI,UAAU,SAAS,GAAG;AACxB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,UAAU,UAAU;AACxB,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,kBAAkB;AACtB,YAAI,CAAC,QAAQ,IAAI,OAAO,GAAG;AACzB,iBAAO,IAAI,MAAM,CAAC;AAClB,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,iBAAK,CAAC,IAAI,KAAK,MAAO,KAAK,QAAQ,IAAK,KAAK,aAAa;AAAA,UAC5D;AACA,cAAI,UAAU,GAAG;AACf,sBAAU,CAAC;AACX,gBAAI,IAAI,GAAG;AACT,mBAAK,QAAS,KAAK,QAAQ,IAAI,MAAO,KAAK;AAAA,YAC7C;AAAA,UACF,OAAO;AACL,sBAAU,KAAK,OAAO,GAAG,KAAK;AAC9B,iBAAK,QAAS,KAAK,QAAQ,IAAI,MAAO,KAAK;AAAA,UAC7C;AACA,iBAAO,UAAU,iBAAiB;AAChC,iBAAK,QAAQ,UAAU,EAAE,OAAO,CAAC;AAAA,UACnC;AACA,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,iBAAK,QAAQ,KAAK,IAAI,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF,OAAO;AACL,iBAAO,IAAI,MAAM,QAAQ,IAAI,MAAM;AACnC,cAAI,OAAO,KAAK;AAChB,eAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,iBAAK,CAAC,IAAI,KAAK,MAAO,KAAK,QAAQ,IAAI,QAAQ,IAAK,KAAK,aAAa;AAAA,UACxE;AACA,cAAI,UAAU,GAAG;AACf,sBAAU,CAAC;AACX,gBAAI,KAAK,MAAM;AACb,mBAAK,QAAS,KAAK,QAAQ,IAAI,MAAO,KAAK;AAAA,YAC7C;AAAA,UACF,OAAO;AACL,sBAAU,KAAK,OAAO,GAAG,KAAK;AAC9B,iBAAK,QAAS,KAAK,QAAQ,OAAO,MAAO,KAAK;AAAA,UAChD;AACA,iBAAO,kBAAkB,SAAS;AAChC,iBAAK,KAAK,UAAU,iBAAiB,CAAC;AAAA,UACxC;AACA,eAAK,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,iBAAK,KAAK,KAAK,CAAC,CAAC;AAAA,UACnB;AAAA,QACF;AACA,eAAO;AAAA,MACT,OAAO;AACL,eAAO,KAAK,OAAO,GAAG,KAAK;AAAA,MAC7B;AAAA,IACF;AAKA,WAAO,UAAU,QAAQ,SAAS,QAAQ;AACxC,WAAK,QAAQ,IAAI,MAAM,KAAK,MAAM,MAAM;AACxC,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACf;AAMA,WAAO,UAAU,UAAU,SAAS,UAAU;AAC5C,aAAO,KAAK,UAAU,KAAK;AAAA,IAC7B;AAMA,WAAO,UAAU,UAAU,SAAS,UAAU;AAC5C,aAAO,KAAK,WAAW,KAAK;AAAA,IAC9B;AAcA,WAAO,UAAU,aAAa,SAAS,WAAW,OAAO;AACvD,UAAI,SAAS,MAAM;AACnB,UAAI,WAAW,KAAK,cAAc,MAAM;AAExC,WAAK,QAAQ,IAAI,MAAM,QAAQ;AAC/B,WAAK,gBAAgB,WAAW;AAChC,WAAK,QAAQ;AAEb,eAAS,IAAI,GAAG,IAAI,QAAQ,IAAK,MAAK,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAC1D;AASA,WAAO,UAAU,aAAa,SAAS,WAAW,UAAU,MAAM;AAChE,UAAI,MAAM,KAAK;AACf,UAAI,WAAW,IAAI;AACnB,UAAI,SAAS,KAAK;AAClB,aAAO,OAAO;AAGd,UAAI,QAAQ,UAAU,KAAK,QAAQ,KAAK,OAAO;AAE7C,eAAO,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,KAAK;AAAA,MAChD;AAEA,UAAI,OAAO,IAAI,MAAM,IAAI;AAEzB,UAAI,IAAI;AACR,UAAI;AACJ,UAAI,YAAY,KAAK,QAAQ,KAAK,OAAO;AACvC,aAAK,IAAI,KAAK,OAAO,IAAI,UAAU,IAAK,MAAK,GAAG,IAAI,IAAI,CAAC;AACzD,aAAK,IAAI,GAAG,IAAI,KAAK,OAAO,IAAK,MAAK,GAAG,IAAI,IAAI,CAAC;AAAA,MACpD,OAAO;AACL,aAAK,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAK,MAAK,GAAG,IAAI,IAAI,CAAC;AAAA,MAC7D;AAEA,aAAO;AAAA,IACT;AAMA,WAAO,UAAU,aAAa,SAAS,aAAa;AAClD,UAAI,KAAK,SAAS,GAAG;AAEnB,YAAI,UAAU,KAAK,WAAW,MAAM,KAAK,MAAM,UAAU,CAAC;AAE1D,aAAK,QAAQ,KAAK,MAAM;AACxB,aAAK,QAAQ;AAEb,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ,KAAK,MAAM;AACxB,aAAK,MAAM,WAAW;AAAA,MACxB;AAEA,WAAK,gBAAiB,KAAK,iBAAiB,IAAK;AAAA,IACnD;AAMA,WAAO,UAAU,eAAe,SAAS,eAAe;AACtD,WAAK,MAAM,YAAY;AACvB,WAAK,mBAAmB;AAAA,IAC1B;AAQA,WAAO,UAAU,gBAAgB,SAAS,cAAc,KAAK;AAC3D,UAAI,OAAO,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC;AACrC,UAAI,WAAW,KAAM,OAAO;AAE5B,aAAO,KAAK,IAAI,UAAU,CAAC;AAAA,IAC7B;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}