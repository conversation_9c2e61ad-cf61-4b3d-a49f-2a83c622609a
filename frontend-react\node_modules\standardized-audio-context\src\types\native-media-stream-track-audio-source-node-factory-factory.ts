import { TInvalidStateErrorFactory } from './invalid-state-error-factory';
import { TIsNativeOfflineAudioContextFunction } from './is-native-offline-audio-context-function';
import { TNativeMediaStreamTrackAudioSourceNodeFactory } from './native-media-stream-track-audio-source-node-factory';

export type TNativeMediaStreamTrackAudioSourceNodeFactoryFactory = (
    createInvalidStateError: TInvalidStateErrorFactory,
    isNativeOfflineAudioContext: TIsNativeOfflineAudioContextFunction
) => TNativeMediaStreamTrackAudioSourceNodeFactory;
