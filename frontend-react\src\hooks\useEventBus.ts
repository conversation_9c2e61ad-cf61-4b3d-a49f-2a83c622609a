import { useEffect, useCallback, useRef } from 'react';

type EventCallback<T = any> = (data: T) => void;

class EventBus {
  private events: Record<string, EventCallback[]> = {};

  subscribe<T = any>(eventName: string, callback: EventCallback<T>): () => void {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);

    // Return unsubscribe function
    return () => {
      if (this.events[eventName]) {
        this.events[eventName] = this.events[eventName].filter(cb => cb !== callback);
      }
    };
  }

  publish<T = any>(eventName: string, data?: T): void {
    if (this.events[eventName]) {
      this.events[eventName].forEach(callback => callback(data));
    }
  }

  unsubscribe(eventName: string, callback: EventCallback): void {
    if (this.events[eventName]) {
      this.events[eventName] = this.events[eventName].filter(cb => cb !== callback);
    }
  }

  clear(): void {
    this.events = {};
  }
}

// Global event bus instance
export const eventBus = new EventBus();

// React hook for subscribing to events
export function useEventBus<T = any>(
  eventName: string,
  callback: EventCallback<T>,
  deps: React.DependencyList = []
): void {
  const callbackRef = useRef(callback);
  
  // Update callback ref when dependencies change
  useEffect(() => {
    callbackRef.current = callback;
  }, deps);

  useEffect(() => {
    const unsubscribe = eventBus.subscribe(eventName, (data: T) => {
      callbackRef.current(data);
    });

    return unsubscribe;
  }, [eventName]);
}

// Hook for publishing events
export function useEventPublisher() {
  return useCallback(<T = any>(eventName: string, data?: T) => {
    eventBus.publish(eventName, data);
  }, []);
}

export default eventBus;
