import { TContextStore } from './context-store';
import { TIsAnyOfflineAudioContextFunction } from './is-any-offline-audio-context-function';
import { TIsNativeOfflineAudioContextFunction } from './is-native-offline-audio-context-function';

export type TIsAnyOfflineAudioContextFactory = (
    contextStore: TContextStore,
    isNativeOfflineAudioContext: TIsNativeOfflineAudioContextFunction
) => TIsAnyOfflineAudioContextFunction;
