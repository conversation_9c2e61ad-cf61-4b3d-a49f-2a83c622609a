import { TAudioDestinationNodeConstructor } from './audio-destination-node-constructor';
import { TAudioListenerFactory } from './audio-listener-factory';
import { TEventTargetConstructor } from './event-target-constructor';
import { TIsNativeOfflineAudioContextFunction } from './is-native-offline-audio-context-function';
import { TMinimalBaseAudioContextConstructor } from './minimal-base-audio-context-constructor';
import { TUnrenderedAudioWorkletNodeStore } from './unrendered-audio-worklet-node-store';
import { TWrapEventListenerFunction } from './wrap-event-listener-function';

export type TMinimalBaseAudioContextConstructorFactory = (
    audioDestinationNodeConstructor: TAudioDestinationNodeConstructor,
    createAudioListener: TAudioListenerFactory,
    eventTargetConstructor: TEventTargetConstructor,
    isNativeOfflineAudioContext: TIsNativeOfflineAudioContextFunction,
    unrenderedAudioWorkletNodeStore: TUnrenderedAudioWorkletNodeStore,
    wrapEventListener: TWrapEventListenerFunction
) => TMinimalBaseAudioContextConstructor;
