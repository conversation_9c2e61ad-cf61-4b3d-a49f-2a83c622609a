import {
    IExtendedExponentialRampToValueAutomationEvent,
    IExtendedLinearRampToValueAutomationEvent,
    ISetTargetAutomationEvent,
    ISetValueAutomationEvent,
    ISetValueCurveAutomationEvent
} from '../interfaces';

export type TPersistentAutomationEvent =
    | IExtendedExponentialRampToValueAutomationEvent
    | IExtendedLinearRampToValueAutomationEvent
    | ISetTargetAutomationEvent
    | ISetValueAutomationEvent
    | ISetValueCurveAutomationEvent;
