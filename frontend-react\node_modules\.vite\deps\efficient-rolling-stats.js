import {
  __commonJS
} from "./chunk-5WRI5ZAA.js";

// node_modules/efficient-rolling-stats/index.js
var require_efficient_rolling_stats = __commonJS({
  "node_modules/efficient-rolling-stats/index.js"(exports) {
    function RollingMin(WindowSize) {
      var DequeIndex = [], DequeValue = [], CurrentIndex = 0, T = WindowSize;
      function atEveryStepDo(CurrentValue) {
        if (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          DequeIndex.shift();
          DequeValue.shift();
        }
        while (DequeValue.length !== 0 && DequeValue[DequeValue.length - 1] > CurrentValue) {
          DequeIndex.pop();
          DequeValue.pop();
        }
        DequeIndex.push(CurrentIndex);
        DequeValue.push(CurrentValue);
        CurrentIndex++;
        return DequeValue[0];
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequeValue.splice(0, DequeValue.length);
        CurrentIndex = 0;
      };
      return atEveryStepDo;
    }
    function RollingMax(WindowSize) {
      var DequeIndex = [], DequeValue = [], CurrentIndex = 0, T = WindowSize;
      function atEveryStepDo(CurrentValue) {
        if (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          DequeIndex.shift();
          DequeValue.shift();
        }
        while (DequeValue.length !== 0 && DequeValue[DequeValue.length - 1] < CurrentValue) {
          DequeIndex.pop();
          DequeValue.pop();
        }
        DequeIndex.push(CurrentIndex);
        DequeValue.push(CurrentValue);
        CurrentIndex++;
        return DequeValue[0];
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequeValue.splice(0, DequeValue.length);
        CurrentIndex = 0;
      };
      return atEveryStepDo;
    }
    function RollingAvg(WindowSize) {
      var DequeValue = [], T = WindowSize, Sum2 = 0, prev2;
      function atEveryStepDo(CurrentValue) {
        if (DequeValue.length >= T) {
          Sum2 -= DequeValue.shift();
        }
        if (CurrentValue || CurrentValue === 0) {
          DequeValue.push(CurrentValue);
          Sum2 += CurrentValue;
        } else return prev2;
        return prev2 = DequeValue.length == 0 ? 0 : Sum2 / DequeValue.length;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        Sum2 = 0;
        DequeValue.splice(0, DequeValue.length);
      };
      return atEveryStepDo;
    }
    function RollingMinIndex(WindowSize) {
      var DequeIndex = [], DequeValue = [], T = WindowSize;
      function atEveryStepDo(CurrentValue, CurrentIndex) {
        while (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          DequeIndex.shift();
          DequeValue.shift();
        }
        while (DequeValue.length !== 0 && DequeValue[DequeValue.length - 1] > CurrentValue) {
          DequeIndex.pop();
          DequeValue.pop();
        }
        DequeIndex.push(CurrentIndex);
        DequeValue.push(CurrentValue);
        return DequeValue[0];
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequeValue.splice(0, DequeValue.length);
      };
      return atEveryStepDo;
    }
    function RollingMaxIndex(WindowSize) {
      var DequeIndex = [], DequeValue = [], T = WindowSize;
      function atEveryStepDo(CurrentValue, CurrentIndex) {
        while (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          DequeIndex.shift();
          DequeValue.shift();
        }
        while (DequeValue.length !== 0 && DequeValue[DequeValue.length - 1] < CurrentValue) {
          DequeIndex.pop();
          DequeValue.pop();
        }
        DequeIndex.push(CurrentIndex);
        DequeValue.push(CurrentValue);
        return DequeValue[0];
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequeValue.splice(0, DequeValue.length);
      };
      return atEveryStepDo;
    }
    function RollingAvgIndex(WindowSize) {
      var DequeIndex = [], DequeValue = [], T = WindowSize, Sum2 = 0;
      function atEveryStepDo(CurrentValue, CurrentIndex) {
        while (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          DequeIndex.shift();
          Sum2 -= DequeValue.shift();
        }
        if (CurrentValue || CurrentValue === 0) {
          DequeIndex.push(CurrentIndex);
          DequeValue.push(CurrentValue);
          Sum2 += CurrentValue;
        } else return prev;
        return prev = DequeValue.length == 0 ? 0 : Sum2 / DequeValue.length;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequeValue.splice(0, DequeValue.length);
        Sum2 = 0;
      };
      return atEveryStepDo;
    }
    function sortedIndex(array, value) {
      var low = 0, high = array.length;
      while (low < high) {
        var mid = low + high >>> 1;
        if (array[mid] < value) low = mid + 1;
        else high = mid;
      }
      return low;
    }
    function RollingMedian(WindowSize) {
      var DequeValue = [], T = WindowSize, SortedValues = [], LsortedIndex = sortedIndex;
      var prevmedian, findcenter = null, Sum2 = 0, Sum22 = 0, Sum3 = 0, Sum4 = 0, findmoments = null;
      function atEveryStepDo(CurrentValue) {
        if (DequeValue.length >= T) {
          var value = DequeValue.shift();
          var v = value, vv = v * v, vvv = vv * v, vvvv = vvv * v;
          Sum2 -= v;
          Sum22 -= vv;
          Sum3 -= vvv;
          Sum4 -= vvvv;
          var x = LsortedIndex(SortedValues, value);
          if (SortedValues[x] == value) SortedValues.splice(x, 1);
        }
        if (CurrentValue || CurrentValue === 0) {
          DequeValue.push(CurrentValue);
          SortedValues.splice(LsortedIndex(SortedValues, CurrentValue), 0, CurrentValue);
          findcenter = null;
          findmoments = null;
          var v = CurrentValue, vv = v * v, vvv = vv * v, vvvv = vvv * v;
          Sum2 += v;
          Sum22 += vv;
          Sum3 += vvv;
          Sum4 += vvvv;
        } else
          return prevmedian;
        if (SortedValues.length == 0) return prevmedian;
        if (SortedValues.length & 1)
          return prevmedian = SortedValues[SortedValues.length - 1 >>> 1];
        else {
          var half = (SortedValues.length >>> 1) - 1;
          return prevmedian = (SortedValues[half] + SortedValues[half + 1]) / 2;
        }
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeValue.splice(0, DequeValue.length);
        SortedValues.splice(0, SortedValues.length);
        findcenter = null;
        Sum2 = 0;
        Sum22 = 0;
        Sum3 = 0;
        Sum4 = 0;
        findmoments = null;
      };
      atEveryStepDo.avg = function() {
        return Sum2 / DequeValue.length;
      };
      atEveryStepDo.sum = function() {
        return Sum2;
      };
      atEveryStepDo.min = function() {
        return SortedValues[0];
      };
      atEveryStepDo.q1 = function() {
        if (SortedValues.length == 1) return SortedValues[0];
        if (SortedValues.length == 2) return SortedValues[0] * 0.75 + SortedValues[1] * 0.25;
        if (SortedValues.length == 3) return SortedValues[0] * 0.5 + SortedValues[1] * 0.5;
        if ((SortedValues.length - 1) % 4 == 0) {
          var n = SortedValues.length - 1 >> 2;
          return SortedValues[n - 1] * 0.25 + SortedValues[n] * 0.75;
        }
        if ((SortedValues.length - 3) % 4 == 0) {
          var n = SortedValues.length - 3 >> 2;
          return SortedValues[n] * 0.75 + SortedValues[n + 1] * 0.25;
        }
      };
      atEveryStepDo.moments_avg = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.average;
      };
      atEveryStepDo.variance = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.variance;
      };
      atEveryStepDo.standardDeviation = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.standardDeviation;
      };
      atEveryStepDo.skew = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.skew;
      };
      atEveryStepDo.kurtosis = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.kurtosis;
      };
      atEveryStepDo.exkurtosis = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.exkurtosis;
      };
      atEveryStepDo.moments = function() {
        var o = {};
        if (this.c > 0) {
          var c = DequeValue.length, ex = Sum2 / c, exx = Sum22 / c, exxx = Sum3 / c, exxxx = Sum4 / c, m1 = ex, m2 = Sum22 / c - ex;
          m3 = exxx - 3 * exx * ex + 2 * ex * ex * ex;
          m4 = exxxx - 3 * exxx * ex + 6 * exx * ex * ex - -3 * ex * ex * ex * ex;
          o.average = m1;
          o.variance = m2;
          o.standardDeviation = Math.pow(o.variance, 0.5);
          if (c > 2) {
            o.skew = Math.pow(c * (c - 1), 0.5) / (c - 2) * m3 / Math.pow(m2, 1.5);
          }
          if (m2 > 0) {
            o.exkurtosis = m4 / m2 / m2 - 3;
            o.kurtosis = m4 / m2 / m2;
          }
        }
        return o;
      };
      atEveryStepDo.median = function() {
        return prevmedian;
      };
      atEveryStepDo.q3 = function() {
        if (SortedValues.length == 1) return SortedValues[0];
        if (SortedValues.length == 2) return SortedValues[0] * 0.25 + SortedValues[1] * 0.75;
        if (SortedValues.length == 3) return SortedValues[1] * 0.5 + SortedValues[2] * 0.5;
        if ((SortedValues.length - 1) % 4 == 0) {
          var n3 = (SortedValues.length - 1 >> 2) * 3;
          return SortedValues[n3] * 0.75 + SortedValues[n3 + 1] * 0.25;
        }
        if ((SortedValues.length - 3) % 4 == 0) {
          var n3 = (SortedValues.length - 3 >> 2) * 3;
          return SortedValues[n3 + 1] * 0.25 + SortedValues[n3 + 2] * 0.75;
        }
      };
      atEveryStepDo.max = function() {
        return SortedValues[SortedValues.length - 1];
      };
      atEveryStepDo.center = function() {
        return (SortedValues[0] + SortedValues[SortedValues.length - 1]) / 2;
      };
      atEveryStepDo.medianskew = function() {
        return SortedValues[SortedValues.length - 1] - prevmedian - (prevmedian - SortedValues[0]);
      };
      atEveryStepDo.medianskew_bowleys_coef = function() {
        return (SortedValues[SortedValues.length - 1] - prevmedian) * (prevmedian - SortedValues[0]) / (SortedValues[SortedValues.length - 1] - SortedValues[0]);
      };
      atEveryStepDo.mediankurt = function() {
        var p90 = Math.round((SortedValues.length - 1) * 0.9);
        var p10 = Math.round((SortedValues.length - 1) * 0.1);
        return (SortedValues[SortedValues.length - 1] - prevmedian) * (prevmedian - SortedValues[0]) / (SortedValues[p90] - SortedValues[p10]);
      };
      atEveryStepDo.pabovecenter = function() {
        if (findcenter === null) findcenter = LsortedIndex(SortedValues, (SortedValues[0] + SortedValues[SortedValues.length - 1]) / 2);
        return (SortedValues.length - 1 - findcenter) / (SortedValues.length - 1);
      };
      atEveryStepDo.pbelowcenter = function() {
        if (findcenter === null) findcenter = LsortedIndex(SortedValues, (SortedValues[0] + SortedValues[SortedValues.length - 1]) / 2);
        return findcenter / (SortedValues.length - 1);
      };
      return atEveryStepDo;
    }
    function RollingMedianIndex(WindowSize) {
      var DequeValue = [], DequeIndex = [], T = WindowSize, SortedValues = [], LsortedIndex = sortedIndex;
      var prevmedian, findcenter = null, Sum2 = 0, Sum22 = 0, Sum3 = 0, Sum4 = 0, findmoments = null;
      function atEveryStepDo(CurrentValue, CurrentIndex) {
        while (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          var index = DequeIndex.shift();
          var value = DequeValue.shift();
          var v = value, vv = v * v, vvv = vv * v, vvvv = vvv * v;
          Sum2 -= v;
          Sum22 -= vv;
          Sum3 -= vvv;
          Sum4 -= vvvv;
          var x = LsortedIndex(SortedValues, value);
          if (SortedValues[x] == value) SortedValues.splice(x, 1);
        }
        if (CurrentValue || CurrentValue === 0) {
          DequeIndex.push(CurrentIndex);
          DequeValue.push(CurrentValue);
          SortedValues.splice(LsortedIndex(SortedValues, CurrentValue), 0, CurrentValue);
          findcenter = null;
          findmoments = null;
          var v = CurrentValue, vv = v * v, vvv = vv * v, vvvv = vvv * v;
          Sum2 += v;
          Sum22 += vv;
          Sum3 += vvv;
          Sum4 += vvvv;
        } else
          return prevmedian;
        if (SortedValues.length == 0) return prevmedian;
        if (SortedValues.length & 1)
          return prevmedian = SortedValues[SortedValues.length - 1 >>> 1];
        else {
          var half = (SortedValues.length >>> 1) - 1;
          return prevmedian = (SortedValues[half] + SortedValues[half + 1]) / 2;
        }
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeValue.splice(0, DequeValue.length);
        DequeIndex.splice(0, DequeIndex.length);
        SortedValues.splice(0, SortedValues.length);
        findcenter = null;
        Sum2 = 0;
        Sum22 = 0;
        Sum3 = 0;
        Sum4 = 0;
        findmoments = null;
      };
      atEveryStepDo.avg = function() {
        return Sum2 / DequeValue.length;
      };
      atEveryStepDo.sum = function() {
        return Sum2;
      };
      atEveryStepDo.min = function() {
        return SortedValues[0];
      };
      atEveryStepDo.q1 = function() {
        if (SortedValues.length == 1) return SortedValues[0];
        if (SortedValues.length == 2) return SortedValues[0] * 0.75 + SortedValues[1] * 0.25;
        if (SortedValues.length == 3) return SortedValues[0] * 0.5 + SortedValues[1] * 0.5;
        if ((SortedValues.length - 1) % 4 == 0) {
          var n = SortedValues.length - 1 >> 2;
          return SortedValues[n - 1] * 0.25 + SortedValues[n] * 0.75;
        }
        if ((SortedValues.length - 3) % 4 == 0) {
          var n = SortedValues.length - 3 >> 2;
          return SortedValues[n] * 0.75 + SortedValues[n + 1] * 0.25;
        }
      };
      atEveryStepDo.moments_avg = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.average;
      };
      atEveryStepDo.variance = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.variance;
      };
      atEveryStepDo.standardDeviation = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.standardDeviation;
      };
      atEveryStepDo.skew = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.skew;
      };
      atEveryStepDo.kurtosis = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.kurtosis;
      };
      atEveryStepDo.exkurtosis = function() {
        if (findmoments === null) findmoments = atEveryStepDo.moments();
        return findmoments.exkurtosis;
      };
      atEveryStepDo.moments = function() {
        var o = {};
        if (this.c > 0) {
          var c = DequeValue.length, ex = Sum2 / c, exx = Sum22 / c, exxx = Sum3 / c, exxxx = Sum4 / c, m1 = ex, m2 = Sum22 / c - ex;
          m3 = exxx - 3 * exx * ex + 2 * ex * ex * ex;
          m4 = exxxx - 3 * exxx * ex + 6 * exx * ex * ex - -3 * ex * ex * ex * ex;
          o.average = m1;
          o.variance = m2;
          o.standardDeviation = Math.pow(o.variance, 0.5);
          if (c > 2) {
            o.skew = Math.pow(c * (c - 1), 0.5) / (c - 2) * m3 / Math.pow(m2, 1.5);
          }
          if (m2 > 0) {
            o.exkurtosis = m4 / m2 / m2 - 3;
            o.kurtosis = m4 / m2 / m2;
          }
        }
        return o;
      };
      atEveryStepDo.median = function() {
        return prevmedian;
      };
      atEveryStepDo.q3 = function() {
        if (SortedValues.length == 1) return SortedValues[0];
        if (SortedValues.length == 2) return SortedValues[0] * 0.25 + SortedValues[1] * 0.75;
        if (SortedValues.length == 3) return SortedValues[1] * 0.5 + SortedValues[2] * 0.5;
        if ((SortedValues.length - 1) % 4 == 0) {
          var n3 = (SortedValues.length - 1 >> 2) * 3;
          return SortedValues[n3] * 0.75 + SortedValues[n3 + 1] * 0.25;
        }
        if ((SortedValues.length - 3) % 4 == 0) {
          var n3 = (SortedValues.length - 3 >> 2) * 3;
          return SortedValues[n3 + 1] * 0.25 + SortedValues[n3 + 2] * 0.75;
        }
      };
      atEveryStepDo.max = function() {
        return SortedValues[SortedValues.length - 1];
      };
      atEveryStepDo.center = function() {
        return (SortedValues[0] + SortedValues[SortedValues.length - 1]) / 2;
      };
      atEveryStepDo.medianskew = function() {
        return SortedValues[SortedValues.length - 1] - prevmedian - (prevmedian - SortedValues[0]);
      };
      atEveryStepDo.medianskew_bowleys_coef = function() {
        return (SortedValues[SortedValues.length - 1] - prevmedian) * (prevmedian - SortedValues[0]) / (SortedValues[SortedValues.length - 1] - SortedValues[0]);
      };
      atEveryStepDo.mediankurt = function() {
        var p90 = Math.round((SortedValues.length - 1) * 0.9);
        var p10 = Math.round((SortedValues.length - 1) * 0.1);
        return (SortedValues[SortedValues.length - 1] - prevmedian) * (prevmedian - SortedValues[0]) / (SortedValues[p90] - SortedValues[p10]);
      };
      atEveryStepDo.pabovecenter = function() {
        if (findcenter === null) findcenter = LsortedIndex(SortedValues, (SortedValues[0] + SortedValues[SortedValues.length - 1]) / 2);
        return (SortedValues.length - 1 - findcenter) / (SortedValues.length - 1);
      };
      atEveryStepDo.pbelowcenter = function() {
        if (findcenter === null) findcenter = LsortedIndex(SortedValues, (SortedValues[0] + SortedValues[SortedValues.length - 1]) / 2);
        return findcenter / (SortedValues.length - 1);
      };
      return atEveryStepDo;
    }
    function RollingSumPerIndex(WindowSize, UsualIndexSkipBetweenOccations) {
      var DequeIndex = [], DequeValue = [], T = WindowSize, Sum2 = 0, PrevIndex = false, U = UsualIndexSkipBetweenOccations;
      function atEveryStepDo(CurrentValue, CurrentIndex) {
        while (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T && DequeIndex[0] != CurrentIndex) {
          PrevIndex = DequeIndex.shift();
          Sum2 -= DequeValue.shift();
        }
        if (PrevIndex === false) PrevIndex = CurrentIndex - U;
        DequeIndex.push(CurrentIndex);
        DequeValue.push(CurrentValue);
        Sum2 += CurrentValue;
        var Div = CurrentIndex - PrevIndex;
        if (Div == 0) Div = U;
        return Sum2 / Div;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.setUsualIndexSkipBetweenOccations = function(UsualIndexSkipBetweenOccations2) {
        U = UsualIndexSkipBetweenOccations2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequeValue.splice(0, DequeValue.length);
        Sum2 = 0;
        PrevIndex = false;
      };
      return atEveryStepDo;
    }
    function Delay(WindowSize, UndefinedValue) {
      var DequeValue = [], T = WindowSize, U = UndefinedValue;
      function atEveryStepDo(CurrentValue) {
        var ret = U;
        if (DequeValue.length == T) {
          ret = DequeValue.shift();
        }
        DequeValue.push(CurrentValue);
        return ret;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.setUndefinedValue = function(WindowSize2) {
        U = UndefinedValue;
      };
      atEveryStepDo.reset = function(WindowSize2) {
        DequeValue.splice(0, DequeValue.length);
      };
      return atEveryStepDo;
    }
    function HideFirst(WindowSize, UndefinedValue) {
      var DequeValue = 1, T = WindowSize + 1, U = UndefinedValue;
      function atEveryStepDo(CurrentValue) {
        if (DequeValue == T) {
          return CurrentValue;
        }
        DequeValue++;
        return U;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2 + 1;
      };
      atEveryStepDo.setUndefinedValue = function(WindowSize2) {
        U = UndefinedValue;
      };
      atEveryStepDo.reset = function() {
        DequeValue = 0;
      };
      return atEveryStepDo;
    }
    function DelayIndex(WindowSize, UsualIndexSkipBetweenOccations, UndefinedValue) {
      var DequeIndex = [], DequeValue = [], T = WindowSize, PrevIndex = false, PrevValue = UndefinedValue, U = UsualIndexSkipBetweenOccations;
      function atEveryStepDo(CurrentValue, CurrentIndex) {
        DequeIndex.push(CurrentIndex);
        DequeValue.push(CurrentValue);
        if (PrevIndex === false) PrevIndex = CurrentIndex - U;
        if (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          PrevIndex = DequeIndex.shift();
          PrevValue = DequeValue.shift();
          while (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
            DequeIndex.shift();
            DequeValue.shift();
          }
        }
        return PrevValue;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.setUsualIndexSkipBetweenOccations = function(UsualIndexSkipBetweenOccations2) {
        U = UsualIndexSkipBetweenOccations2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequeValue.splice(0, DequeValue.length);
        PrevIndex = false;
        PrevValue = UndefinedValue;
      };
      return atEveryStepDo;
    }
    function PositiveLately(WindowSize) {
      var T = WindowSize, PositiveCount = -1;
      function atEveryStepDo(CurrentValue) {
        if (CurrentValue > 0) PositiveCount = 0;
        if (PositiveCount >= 0) PositiveCount++;
        if (PositiveCount > WindowSize) PositiveCount = -1;
        return PositiveCount > 0;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        PositiveCount = -1;
      };
      return atEveryStepDo;
    }
    function PositiveLatelyIndex(WindowSize) {
      var T = WindowSize, PrevIndex = false, PositiveCount = -1;
      function atEveryStepDo(CurrentValue, CurrentIndex) {
        if (CurrentValue > 0) {
          PrevIndex = CurrentIndex;
          PositiveCount = 0;
        }
        if (PositiveCount >= 0) PositiveCount++;
        if (PrevIndex != false && PrevIndex <= CurrentIndex - T) PositiveCount = -1;
        return PositiveCount > 0;
      }
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        PrevIndex = false;
        PositiveCount = -1;
      };
      return atEveryStepDo;
    }
    function Histogram(PreRoundingMultiplier) {
      var RD = PreRoundingMultiplier, hist = /* @__PURE__ */ new Map();
      ;
      function atEveryStepDo(CurrentPosition, CurrentAmount = 1) {
        var CurrentPositionRound = parseFloat((Math.round(CurrentPosition * RD) / RD).toFixed(12));
        if (hist.has(CurrentPositionRound))
          hist.set(CurrentPositionRound, parseFloat((hist.get(CurrentPositionRound) + CurrentAmount).toFixed(12)));
        else
          hist.set(CurrentPositionRound, CurrentAmount);
        return hist;
      }
      atEveryStepDo.hist = hist;
      atEveryStepDo.reset = function() {
        hist.clear();
      };
      return atEveryStepDo;
    }
    function RollingHistogram(WindowSize, PreRoundingMultiplier) {
      var DequePosition = [], DequeAmount = [], T = WindowSize, RD = PreRoundingMultiplier, hist = /* @__PURE__ */ new Map();
      ;
      function atEveryStepDo(CurrentPosition, CurrentAmount = 1) {
        if (DequePosition.length >= T) {
          var prevpos = DequePosition.shift();
          var prevamount = DequeAmount.shift();
          var newamount = parseFloat((hist.get(prevpos) - prevamount).toFixed(12));
          if (newamount !== 0)
            hist.set(prevpos, newamount);
          else
            hist.delete(prevpos);
        }
        var CurrentPositionRound = parseFloat((Math.round(CurrentPosition * RD) / RD).toFixed(12));
        DequePosition.push(CurrentPositionRound);
        DequeAmount.push(CurrentAmount);
        if (hist.has(CurrentPositionRound))
          hist.set(CurrentPositionRound, parseFloat((hist.get(CurrentPositionRound) + CurrentAmount).toFixed(12)));
        else
          hist.set(CurrentPositionRound, CurrentAmount);
        return hist;
      }
      atEveryStepDo.hist = hist;
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        Sum = 0;
        DequePosition.splice(0, DequePosition.length);
      };
      return atEveryStepDo;
    }
    function RollingHistogramIndex(WindowSize, PreRoundingMultiplier) {
      var DequeIndex = [], DequePosition = [], DequeAmount = [], T = WindowSize, RD = PreRoundingMultiplier, hist = /* @__PURE__ */ new Map();
      function atEveryStepDo(CurrentPosition, CurrentIndex, CurrentAmount = 1) {
        if (!CurrentAmount && CurrentAmount !== 0 || !CurrentPosition && CurrentPosition !== 0) return hist;
        while (DequeIndex.length !== 0 && DequeIndex[0] <= CurrentIndex - T) {
          DequeIndex.shift();
          var prevpos = DequePosition.shift();
          var prevamount = DequeAmount.shift();
          if (!prevpos && prevpos !== 0) console.log("nana1", { prevamount, prevpos });
          if (!prevamount && prevamount !== 0) console.log("nana2", { prevamount, prevpos });
          if (hist.has(prevpos)) {
            var newamount = parseFloat((hist.get(prevpos) - prevamount).toFixed(12));
            if (!newamount && newamount !== 0) console.log("nana3", { newamount, prevamount, prevpos });
            if (newamount !== 0)
              hist.set(prevpos, newamount);
            else
              hist.delete(prevpos);
          }
        }
        var CurrentPositionRound = parseFloat((Math.round(CurrentPosition * RD) / RD).toFixed(12));
        DequePosition.push(CurrentPositionRound);
        DequeAmount.push(CurrentAmount);
        DequeIndex.push(CurrentIndex);
        if (hist.has(CurrentPositionRound)) {
          let newamount2 = parseFloat((hist.get(CurrentPositionRound) + CurrentAmount).toFixed(12));
          hist.set(CurrentPositionRound, newamount2);
        } else
          hist.set(CurrentPositionRound, CurrentAmount);
        return hist;
      }
      atEveryStepDo.hist = hist;
      atEveryStepDo.setWindowSize = function(WindowSize2) {
        T = WindowSize2;
      };
      atEveryStepDo.reset = function() {
        DequeIndex.splice(0, DequeIndex.length);
        DequePosition.splice(0, DequePosition.length);
        Sum = 0;
      };
      return atEveryStepDo;
    }
    var Stats = exports;
    function AllStats(size, delay, timesize, usualtime, timedelay) {
      var list = [], add = function(v) {
        list.push(v);
        return v;
      };
      var avgtime = add(Stats.RollingAvg(size)), stdev = add(Stats.RollingAvg(size)), zavg = add(Stats.RollingAvg(size)), median = add(Stats.RollingMedian(size)), mstdev = add(Stats.RollingAvg(size)), mzavg = add(Stats.RollingAvg(size)), tzavg = add(Stats.RollingAvgIndex(timesize)), tstdev = add(Stats.RollingAvgIndex(timesize)), tmedian = add(Stats.RollingMedianIndex(timesize)), tmzavg = add(Stats.RollingAvgIndex(timesize)), tmstdev = add(Stats.RollingAvgIndex(timesize)), tsum = add(Stats.RollingSumPerIndex(timesize, usualtime)), value_delay = add(Stats.Delay(delay)), tvalue_delay = add(Stats.DelayIndex(timedelay, usualtime)), index_delay = add(Stats.Delay(delay)), tindex_delay = add(Stats.DelayIndex(timedelay, usualtime)), prev2 = false, count = 0, tcount = 0;
      function stats(n, t) {
        var o = {};
        o.median = median(n);
        o.min = median.min();
        o.max = median.max();
        o.q1 = median.q1();
        o.q3 = median.q3();
        o.avg = median.avg();
        o.stdev = Math.sqrt(stdev(Math.pow(n - o.avg, 2)));
        o.z = o.stdev == 0 ? 0 : (n - o.avg) / o.stdev;
        o.zavg = zavg(o.z);
        o.mstdev = Math.sqrt(mstdev(Math.pow(n - o.median, 2)));
        o.mz = o.mstdev == 0 ? 0 : 0.6745 * (n - o.median) / o.mstdev;
        o.mzavg = mzavg(o.mz);
        o.tmedian = tmedian(n, t);
        o.tmin = tmedian.min();
        o.tmax = tmedian.max();
        o.tavg = tmedian.avg();
        o.tcenter = tmedian.center();
        o.tq1 = tmedian.q1();
        o.tq3 = tmedian.q3();
        o.tsum = tsum(n, t);
        o.tstdev = Math.sqrt(tstdev(Math.pow(n - o.tavg, 2), t));
        o.tz = o.tstdev == 0 ? 0 : (n - o.tavg) / o.tstdev;
        o.tzavg = tzavg(o.tz, t);
        o.tmstdev = Math.sqrt(tmstdev(Math.pow(n - o.tmedian, 2), t));
        o.tmz = o.tmstdev == 0 ? 0 : 0.6745 * (n - o.tmedian) / o.tmstdev;
        o.tmzavg = tmzavg(o.tmz, t);
        if (prev2 === false) prev2 = t - usualtime;
        var delta = t - prev2;
        prev2 = t;
        o.avgtime = avgtime(delta);
        o.count = count;
        count++;
        o.tcount = tcount;
        tcount += delta;
        o.value_delay = value_delay(n);
        o.tvalue_delay = tvalue_delay(n, t);
        o.index_delay = index_delay(t);
        o.tindex_delay = tindex_delay(t, t);
        return o;
      }
      stats.reset = function() {
        for (var i = 0; i < list.length; i++) {
          list[i].reset();
        }
        ;
        prev2 = false;
        count = 0;
      };
      return stats;
    }
    function SimpleStats(size, delay) {
      var min = Stats.RollingMin(size), max = Stats.RollingMax(size), avg = Stats.RollingAvg(size), value_delay = Stats.Delay(delay);
      function stats(n) {
        var o = {};
        o.min = min(n);
        o.max = max(n);
        o.avg = avg(n);
        o.value_delay = value_delay(n);
        return o;
      }
      stats.reset = function() {
        min.reset();
        max.reset();
        avg.reset();
        value_delay.reset();
      };
      return stats;
    }
    function SimpleStatsNoDelay(size) {
      var min = Stats.RollingMin(size), max = Stats.RollingMax(size), avg = Stats.RollingAvg(size);
      function stats(n) {
        var o = {};
        o.min = min(n);
        o.max = max(n);
        o.avg = avg(n);
        return o;
      }
      stats.reset = function() {
        min.reset();
        max.reset();
        avg.reset();
      };
      return stats;
    }
    exports.RollingMin = RollingMin;
    exports.RollingMax = RollingMax;
    exports.RollingAvg = RollingAvg;
    exports.RollingMedian = RollingMedian;
    exports.RollingMinIndex = RollingMinIndex;
    exports.RollingMaxIndex = RollingMaxIndex;
    exports.RollingAvgIndex = RollingAvgIndex;
    exports.RollingMedianIndex = RollingMedianIndex;
    exports.RollingSumPerIndex = RollingSumPerIndex;
    exports.Delay = Delay;
    exports.DelayIndex = DelayIndex;
    exports.AllStats = AllStats;
    exports.SimpleStats = SimpleStats;
    exports.SimpleStatsNoDelay = SimpleStatsNoDelay;
    exports.PositiveLately = PositiveLately;
    exports.PositiveLatelyIndex = PositiveLatelyIndex;
    exports.Histogram = Histogram;
    exports.RollingHistogram = RollingHistogram;
    exports.RollingHistogramIndex = RollingHistogramIndex;
    exports.HideFirst = HideFirst;
  }
});
export default require_efficient_rolling_stats();
//# sourceMappingURL=efficient-rolling-stats.js.map
