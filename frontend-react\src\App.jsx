import React, { useState, useEffect, useRef, useCallback } from 'react'
import AppFooter from './components/AppFooter'
import ChatPanel from './components/ChatPanel'
import BookmarkDialog from './components/BookmarkDialog'
import WaterfallControls from './components/WaterfallControls'
import AudioPanel from './components/AudioPanel'
import DemodBandwidthPanel from './components/DemodBandwidthPanel'
import TutorialOverlay from './components/TutorialOverlay'
import AudioGateOverlay from './components/AudioGateOverlay'
import DisplayStack from './components/DisplayStack'
import ServerInfo from './components/ServerInfo'
import BandsMenu from './components/BandsMenu'
import MobileBottomBar from './components/MobileBottomBar'
import { useServerInfo } from './stores/serverInfo.jsx'
import { useFrequencySteps } from './stores/frequencySteps.jsx'
import { eventBus } from './eventBus'
import {
  init,
  audio,
  waterfall,
  events,
  FFTOffsetToFrequency,
  frequencyToFFTOffset,
  waterfallOffsetToFrequency,
} from './lib/backend'
import {
  constructLink,
  parseLink,
  storeInLocalStorage,
} from './lib/storage'

function App() {
  const [showTutorial, setShowTutorial] = useState(false)
  const [audioGateVisible, setAudioGateVisible] = useState(false)
  const [frequency, setFrequency] = useState('14.074')
  const [lastUpdated, setLastUpdated] = useState(0)
  
  const audioPanel = useRef(null)
  const displayStackComponent = useRef(null)
  const frequencyInputComponent = useRef(null)
  const passbandTunerComponent = useRef(null)
  const frequencyMarkerComponent = useRef(null)

  const handleFrequencyChange = useCallback((event) => {
    setFrequency(event.detail.frequency)
  }, [])

  const handleAudioGateClick = useCallback(() => {
    setAudioGateVisible(false)
  }, [])

  // Regular UI update tick
  const updateTick = useCallback(() => {
    // Update signal meter in audio panel
    if (audioPanel.current) {
      audioPanel.current.updateSignalMeter()
    }

    // Update other user displays
    if (events.getLastModified() > lastUpdated) {
      const myRange = audio.getAudioRange()
      const clients = events.getSignalClients()
      // Don't show our own tuning
      const myId = Object.keys(clients).reduce((a, b) => {
        const aRange = clients[a]
        const bRange = clients[b]
        const aDiff = Math.abs(aRange[1] - myRange[1])
        const bDiff = Math.abs(bRange[1] - myRange[1])
        return aDiff < bDiff ? a : b
      })
      delete clients[myId]
      waterfall.setClients(clients)
      requestAnimationFrame(() => {
        waterfall.updateGraduation()
        waterfall.drawClients()
      })
      setLastUpdated(events.getLastModified())
    }
  }, [lastUpdated])

  useEffect(() => {
    const interval = setInterval(updateTick, 100)
    return () => clearInterval(interval)
  }, [updateTick])

  return (
    <main className="main-container">
      <div className="h-screen overflow-hidden flex flex-col min-h-screen">
        <div className="w-full flex-grow overflow-y-auto">
          <div className="app-content">
            <div className="content-inner">
              {/* Server Information */}
              <div className="server-info-wrapper">
                <ServerInfo currentFrequency={parseFloat(frequency) * 1e3} />
              </div>

              <TutorialOverlay
                showTutorial={showTutorial}
                onComplete={() => setShowTutorial(false)}
              />

              <div className="display-stack-wrapper">
                <DisplayStack
                  ref={displayStackComponent}
                  frequencyInputComponent={frequencyInputComponent}
                  passbandTunerComponent={passbandTunerComponent}
                  frequencyMarkerComponent={frequencyMarkerComponent}
                  onChange={handleFrequencyChange}
                />
              </div>

              <AudioGateOverlay
                visible={audioGateVisible}
                onClick={handleAudioGateClick}
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}

export default App
