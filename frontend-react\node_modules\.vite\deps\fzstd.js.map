{"version": 3, "sources": ["../../fzstd/esm/index.mjs"], "sourcesContent": ["// Some numerical data is initialized as -1 even when it doesn't need initialization to help the JIT infer types\n// aliases for shorter compressed code (most minifers don't do this)\nvar ab = <PERSON>rrayBuffer, u8 = Uint8Array, u16 = Uint16Array, i16 = Int16Array, u32 = Uint32Array, i32 = Int32Array;\nvar slc = function (v, s, e) {\n    if (u8.prototype.slice)\n        return u8.prototype.slice.call(v, s, e);\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    var n = new u8(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\nvar fill = function (v, n, s, e) {\n    if (u8.prototype.fill)\n        return u8.prototype.fill.call(v, n, s, e);\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    for (; s < e; ++s)\n        v[s] = n;\n    return v;\n};\nvar cpw = function (v, t, s, e) {\n    if (u8.prototype.copyWithin)\n        return u8.prototype.copyWithin.call(v, t, s, e);\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    while (s < e) {\n        v[t++] = v[s++];\n    }\n};\n/**\n * Codes for errors generated within this library\n */\nexport var ZstdErrorCode = {\n    InvalidData: 0,\n    WindowSizeTooLarge: 1,\n    InvalidBlockType: 2,\n    FSEAccuracyTooHigh: 3,\n    DistanceTooFarBack: 4,\n    UnexpectedEOF: 5\n};\n// error codes\nvar ec = [\n    'invalid zstd data',\n    'window size too large (>2046MB)',\n    'invalid block type',\n    'FSE accuracy too high',\n    'match distance too far back',\n    'unexpected EOF'\n];\nvar err = function (ind, msg, nt) {\n    var e = new Error(msg || ec[ind]);\n    e.code = ind;\n    if (Error.captureStackTrace)\n        Error.captureStackTrace(e, err);\n    if (!nt)\n        throw e;\n    return e;\n};\nvar rb = function (d, b, n) {\n    var i = 0, o = 0;\n    for (; i < n; ++i)\n        o |= d[b++] << (i << 3);\n    return o;\n};\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\n// read Zstandard frame header\nvar rzfh = function (dat, w) {\n    var n3 = dat[0] | (dat[1] << 8) | (dat[2] << 16);\n    if (n3 == 0x2FB528 && dat[3] == 253) {\n        // Zstandard\n        var flg = dat[4];\n        //    single segment       checksum             dict flag     frame content flag\n        var ss = (flg >> 5) & 1, cc = (flg >> 2) & 1, df = flg & 3, fcf = flg >> 6;\n        if (flg & 8)\n            err(0);\n        // byte\n        var bt = 6 - ss;\n        // dict bytes\n        var db = df == 3 ? 4 : df;\n        // dictionary id\n        var di = rb(dat, bt, db);\n        bt += db;\n        // frame size bytes\n        var fsb = fcf ? (1 << fcf) : ss;\n        // frame source size\n        var fss = rb(dat, bt, fsb) + ((fcf == 1) && 256);\n        // window size\n        var ws = fss;\n        if (!ss) {\n            // window descriptor\n            var wb = 1 << (10 + (dat[5] >> 3));\n            ws = wb + (wb >> 3) * (dat[5] & 7);\n        }\n        if (ws > 2145386496)\n            err(1);\n        var buf = new u8((w == 1 ? (fss || ws) : w ? 0 : ws) + 12);\n        buf[0] = 1, buf[4] = 4, buf[8] = 8;\n        return {\n            b: bt + fsb,\n            y: 0,\n            l: 0,\n            d: di,\n            w: (w && w != 1) ? w : buf.subarray(12),\n            e: ws,\n            o: new i32(buf.buffer, 0, 3),\n            u: fss,\n            c: cc,\n            m: Math.min(131072, ws)\n        };\n    }\n    else if (((n3 >> 4) | (dat[3] << 20)) == 0x184D2A5) {\n        // skippable\n        return b4(dat, 4) + 8;\n    }\n    err(0);\n};\n// most significant bit for nonzero\nvar msb = function (val) {\n    var bits = 0;\n    for (; (1 << bits) <= val; ++bits)\n        ;\n    return bits - 1;\n};\n// read finite state entropy\nvar rfse = function (dat, bt, mal) {\n    // table pos\n    var tpos = (bt << 3) + 4;\n    // accuracy log\n    var al = (dat[bt] & 15) + 5;\n    if (al > mal)\n        err(3);\n    // size\n    var sz = 1 << al;\n    // probabilities symbols  repeat   index   high threshold\n    var probs = sz, sym = -1, re = -1, i = -1, ht = sz;\n    // optimization: single allocation is much faster\n    var buf = new ab(512 + (sz << 2));\n    var freq = new i16(buf, 0, 256);\n    // same view as freq\n    var dstate = new u16(buf, 0, 256);\n    var nstate = new u16(buf, 512, sz);\n    var bb1 = 512 + (sz << 1);\n    var syms = new u8(buf, bb1, sz);\n    var nbits = new u8(buf, bb1 + sz);\n    while (sym < 255 && probs > 0) {\n        var bits = msb(probs + 1);\n        var cbt = tpos >> 3;\n        // mask\n        var msk = (1 << (bits + 1)) - 1;\n        var val = ((dat[cbt] | (dat[cbt + 1] << 8) | (dat[cbt + 2] << 16)) >> (tpos & 7)) & msk;\n        // mask (1 fewer bit)\n        var msk1fb = (1 << bits) - 1;\n        // max small value\n        var msv = msk - probs - 1;\n        // small value\n        var sval = val & msk1fb;\n        if (sval < msv)\n            tpos += bits, val = sval;\n        else {\n            tpos += bits + 1;\n            if (val > msk1fb)\n                val -= msv;\n        }\n        freq[++sym] = --val;\n        if (val == -1) {\n            probs += val;\n            syms[--ht] = sym;\n        }\n        else\n            probs -= val;\n        if (!val) {\n            do {\n                // repeat byte\n                var rbt = tpos >> 3;\n                re = ((dat[rbt] | (dat[rbt + 1] << 8)) >> (tpos & 7)) & 3;\n                tpos += 2;\n                sym += re;\n            } while (re == 3);\n        }\n    }\n    if (sym > 255 || probs)\n        err(0);\n    var sympos = 0;\n    // sym step (coprime with sz - formula from zstd source)\n    var sstep = (sz >> 1) + (sz >> 3) + 3;\n    // sym mask\n    var smask = sz - 1;\n    for (var s = 0; s <= sym; ++s) {\n        var sf = freq[s];\n        if (sf < 1) {\n            dstate[s] = -sf;\n            continue;\n        }\n        // This is split into two loops in zstd to avoid branching, but as JS is higher-level that is unnecessary\n        for (i = 0; i < sf; ++i) {\n            syms[sympos] = s;\n            do {\n                sympos = (sympos + sstep) & smask;\n            } while (sympos >= ht);\n        }\n    }\n    // After spreading symbols, should be zero again\n    if (sympos)\n        err(0);\n    for (i = 0; i < sz; ++i) {\n        // next state\n        var ns = dstate[syms[i]]++;\n        // num bits\n        var nb = nbits[i] = al - msb(ns);\n        nstate[i] = (ns << nb) - sz;\n    }\n    return [(tpos + 7) >> 3, {\n            b: al,\n            s: syms,\n            n: nbits,\n            t: nstate\n        }];\n};\n// read huffman\nvar rhu = function (dat, bt) {\n    //  index  weight count\n    var i = 0, wc = -1;\n    //    buffer             header byte\n    var buf = new u8(292), hb = dat[bt];\n    // huffman weights\n    var hw = buf.subarray(0, 256);\n    // rank count\n    var rc = buf.subarray(256, 268);\n    // rank index\n    var ri = new u16(buf.buffer, 268);\n    // NOTE: at this point bt is 1 less than expected\n    if (hb < 128) {\n        // end byte, fse decode table\n        var _a = rfse(dat, bt + 1, 6), ebt = _a[0], fdt = _a[1];\n        bt += hb;\n        var epos = ebt << 3;\n        // last byte\n        var lb = dat[bt];\n        if (!lb)\n            err(0);\n        //  state1   state2   state1 bits   state2 bits\n        var st1 = 0, st2 = 0, btr1 = fdt.b, btr2 = btr1;\n        // fse pos\n        // pre-increment to account for original deficit of 1\n        var fpos = (++bt << 3) - 8 + msb(lb);\n        for (;;) {\n            fpos -= btr1;\n            if (fpos < epos)\n                break;\n            var cbt = fpos >> 3;\n            st1 += ((dat[cbt] | (dat[cbt + 1] << 8)) >> (fpos & 7)) & ((1 << btr1) - 1);\n            hw[++wc] = fdt.s[st1];\n            fpos -= btr2;\n            if (fpos < epos)\n                break;\n            cbt = fpos >> 3;\n            st2 += ((dat[cbt] | (dat[cbt + 1] << 8)) >> (fpos & 7)) & ((1 << btr2) - 1);\n            hw[++wc] = fdt.s[st2];\n            btr1 = fdt.n[st1];\n            st1 = fdt.t[st1];\n            btr2 = fdt.n[st2];\n            st2 = fdt.t[st2];\n        }\n        if (++wc > 255)\n            err(0);\n    }\n    else {\n        wc = hb - 127;\n        for (; i < wc; i += 2) {\n            var byte = dat[++bt];\n            hw[i] = byte >> 4;\n            hw[i + 1] = byte & 15;\n        }\n        ++bt;\n    }\n    // weight exponential sum\n    var wes = 0;\n    for (i = 0; i < wc; ++i) {\n        var wt = hw[i];\n        // bits must be at most 11, same as weight\n        if (wt > 11)\n            err(0);\n        wes += wt && (1 << (wt - 1));\n    }\n    // max bits\n    var mb = msb(wes) + 1;\n    // table size\n    var ts = 1 << mb;\n    // remaining sum\n    var rem = ts - wes;\n    // must be power of 2\n    if (rem & (rem - 1))\n        err(0);\n    hw[wc++] = msb(rem) + 1;\n    for (i = 0; i < wc; ++i) {\n        var wt = hw[i];\n        ++rc[hw[i] = wt && (mb + 1 - wt)];\n    }\n    // huf buf\n    var hbuf = new u8(ts << 1);\n    //    symbols                      num bits\n    var syms = hbuf.subarray(0, ts), nb = hbuf.subarray(ts);\n    ri[mb] = 0;\n    for (i = mb; i > 0; --i) {\n        var pv = ri[i];\n        fill(nb, i, pv, ri[i - 1] = pv + rc[i] * (1 << (mb - i)));\n    }\n    if (ri[0] != ts)\n        err(0);\n    for (i = 0; i < wc; ++i) {\n        var bits = hw[i];\n        if (bits) {\n            var code = ri[bits];\n            fill(syms, i, code, ri[bits] = code + (1 << (mb - bits)));\n        }\n    }\n    return [bt, {\n            n: nb,\n            b: mb,\n            s: syms\n        }];\n};\n// Tables generated using this:\n// https://gist.github.com/101arrowz/a979452d4355992cbf8f257cbffc9edd\n// default literal length table\nvar dllt = /*#__PURE__*/ rfse(/*#__PURE__*/ new u8([\n    81, 16, 99, 140, 49, 198, 24, 99, 12, 33, 196, 24, 99, 102, 102, 134, 70, 146, 4\n]), 0, 6)[1];\n// default match length table\nvar dmlt = /*#__PURE__*/ rfse(/*#__PURE__*/ new u8([\n    33, 20, 196, 24, 99, 140, 33, 132, 16, 66, 8, 33, 132, 16, 66, 8, 33, 68, 68, 68, 68, 68, 68, 68, 68, 36, 9\n]), 0, 6)[1];\n// default offset code table\nvar doct = /*#__PURE__ */ rfse(/*#__PURE__*/ new u8([\n    32, 132, 16, 66, 102, 70, 68, 68, 68, 68, 36, 73, 2\n]), 0, 5)[1];\n// bits to baseline\nvar b2bl = function (b, s) {\n    var len = b.length, bl = new i32(len);\n    for (var i = 0; i < len; ++i) {\n        bl[i] = s;\n        s += 1 << b[i];\n    }\n    return bl;\n};\n// literal length bits\nvar llb = /*#__PURE__ */ new u8(( /*#__PURE__ */new i32([\n    0, 0, 0, 0, 16843009, 50528770, 134678020, 202050057, 269422093\n])).buffer, 0, 36);\n// literal length baseline\nvar llbl = /*#__PURE__ */ b2bl(llb, 0);\n// match length bits\nvar mlb = /*#__PURE__ */ new u8(( /*#__PURE__ */new i32([\n    0, 0, 0, 0, 0, 0, 0, 0, 16843009, 50528770, 117769220, 185207048, 252579084, 16\n])).buffer, 0, 53);\n// match length baseline\nvar mlbl = /*#__PURE__ */ b2bl(mlb, 3);\n// decode huffman stream\nvar dhu = function (dat, out, hu) {\n    var len = dat.length, ss = out.length, lb = dat[len - 1], msk = (1 << hu.b) - 1, eb = -hu.b;\n    if (!lb)\n        err(0);\n    var st = 0, btr = hu.b, pos = (len << 3) - 8 + msb(lb) - btr, i = -1;\n    for (; pos > eb && i < ss;) {\n        var cbt = pos >> 3;\n        var val = (dat[cbt] | (dat[cbt + 1] << 8) | (dat[cbt + 2] << 16)) >> (pos & 7);\n        st = ((st << btr) | val) & msk;\n        out[++i] = hu.s[st];\n        pos -= (btr = hu.n[st]);\n    }\n    if (pos != eb || i + 1 != ss)\n        err(0);\n};\n// decode huffman stream 4x\n// TODO: use workers to parallelize\nvar dhu4 = function (dat, out, hu) {\n    var bt = 6;\n    var ss = out.length, sz1 = (ss + 3) >> 2, sz2 = sz1 << 1, sz3 = sz1 + sz2;\n    dhu(dat.subarray(bt, bt += dat[0] | (dat[1] << 8)), out.subarray(0, sz1), hu);\n    dhu(dat.subarray(bt, bt += dat[2] | (dat[3] << 8)), out.subarray(sz1, sz2), hu);\n    dhu(dat.subarray(bt, bt += dat[4] | (dat[5] << 8)), out.subarray(sz2, sz3), hu);\n    dhu(dat.subarray(bt), out.subarray(sz3), hu);\n};\n// read Zstandard block\nvar rzb = function (dat, st, out) {\n    var _a;\n    var bt = st.b;\n    //    byte 0        block type\n    var b0 = dat[bt], btype = (b0 >> 1) & 3;\n    st.l = b0 & 1;\n    var sz = (b0 >> 3) | (dat[bt + 1] << 5) | (dat[bt + 2] << 13);\n    // end byte for block\n    var ebt = (bt += 3) + sz;\n    if (btype == 1) {\n        if (bt >= dat.length)\n            return;\n        st.b = bt + 1;\n        if (out) {\n            fill(out, dat[bt], st.y, st.y += sz);\n            return out;\n        }\n        return fill(new u8(sz), dat[bt]);\n    }\n    if (ebt > dat.length)\n        return;\n    if (btype == 0) {\n        st.b = ebt;\n        if (out) {\n            out.set(dat.subarray(bt, ebt), st.y);\n            st.y += sz;\n            return out;\n        }\n        return slc(dat, bt, ebt);\n    }\n    if (btype == 2) {\n        //    byte 3        lit btype     size format\n        var b3 = dat[bt], lbt = b3 & 3, sf = (b3 >> 2) & 3;\n        // lit src size  lit cmp sz 4 streams\n        var lss = b3 >> 4, lcs = 0, s4 = 0;\n        if (lbt < 2) {\n            if (sf & 1)\n                lss |= (dat[++bt] << 4) | ((sf & 2) && (dat[++bt] << 12));\n            else\n                lss = b3 >> 3;\n        }\n        else {\n            s4 = sf;\n            if (sf < 2)\n                lss |= ((dat[++bt] & 63) << 4), lcs = (dat[bt] >> 6) | (dat[++bt] << 2);\n            else if (sf == 2)\n                lss |= (dat[++bt] << 4) | ((dat[++bt] & 3) << 12), lcs = (dat[bt] >> 2) | (dat[++bt] << 6);\n            else\n                lss |= (dat[++bt] << 4) | ((dat[++bt] & 63) << 12), lcs = (dat[bt] >> 6) | (dat[++bt] << 2) | (dat[++bt] << 10);\n        }\n        ++bt;\n        // add literals to end - can never overlap with backreferences because unused literals always appended\n        var buf = out ? out.subarray(st.y, st.y + st.m) : new u8(st.m);\n        // starting point for literals\n        var spl = buf.length - lss;\n        if (lbt == 0)\n            buf.set(dat.subarray(bt, bt += lss), spl);\n        else if (lbt == 1)\n            fill(buf, dat[bt++], spl);\n        else {\n            // huffman table\n            var hu = st.h;\n            if (lbt == 2) {\n                var hud = rhu(dat, bt);\n                // subtract description length\n                lcs += bt - (bt = hud[0]);\n                st.h = hu = hud[1];\n            }\n            else if (!hu)\n                err(0);\n            (s4 ? dhu4 : dhu)(dat.subarray(bt, bt += lcs), buf.subarray(spl), hu);\n        }\n        // num sequences\n        var ns = dat[bt++];\n        if (ns) {\n            if (ns == 255)\n                ns = (dat[bt++] | (dat[bt++] << 8)) + 0x7F00;\n            else if (ns > 127)\n                ns = ((ns - 128) << 8) | dat[bt++];\n            // symbol compression modes\n            var scm = dat[bt++];\n            if (scm & 3)\n                err(0);\n            var dts = [dmlt, doct, dllt];\n            for (var i = 2; i > -1; --i) {\n                var md = (scm >> ((i << 1) + 2)) & 3;\n                if (md == 1) {\n                    // rle buf\n                    var rbuf = new u8([0, 0, dat[bt++]]);\n                    dts[i] = {\n                        s: rbuf.subarray(2, 3),\n                        n: rbuf.subarray(0, 1),\n                        t: new u16(rbuf.buffer, 0, 1),\n                        b: 0\n                    };\n                }\n                else if (md == 2) {\n                    // accuracy log 8 for offsets, 9 for others\n                    _a = rfse(dat, bt, 9 - (i & 1)), bt = _a[0], dts[i] = _a[1];\n                }\n                else if (md == 3) {\n                    if (!st.t)\n                        err(0);\n                    dts[i] = st.t[i];\n                }\n            }\n            var _b = st.t = dts, mlt = _b[0], oct = _b[1], llt = _b[2];\n            var lb = dat[ebt - 1];\n            if (!lb)\n                err(0);\n            var spos = (ebt << 3) - 8 + msb(lb) - llt.b, cbt = spos >> 3, oubt = 0;\n            var lst = ((dat[cbt] | (dat[cbt + 1] << 8)) >> (spos & 7)) & ((1 << llt.b) - 1);\n            cbt = (spos -= oct.b) >> 3;\n            var ost = ((dat[cbt] | (dat[cbt + 1] << 8)) >> (spos & 7)) & ((1 << oct.b) - 1);\n            cbt = (spos -= mlt.b) >> 3;\n            var mst = ((dat[cbt] | (dat[cbt + 1] << 8)) >> (spos & 7)) & ((1 << mlt.b) - 1);\n            for (++ns; --ns;) {\n                var llc = llt.s[lst];\n                var lbtr = llt.n[lst];\n                var mlc = mlt.s[mst];\n                var mbtr = mlt.n[mst];\n                var ofc = oct.s[ost];\n                var obtr = oct.n[ost];\n                cbt = (spos -= ofc) >> 3;\n                var ofp = 1 << ofc;\n                var off = ofp + (((dat[cbt] | (dat[cbt + 1] << 8) | (dat[cbt + 2] << 16) | (dat[cbt + 3] << 24)) >>> (spos & 7)) & (ofp - 1));\n                cbt = (spos -= mlb[mlc]) >> 3;\n                var ml = mlbl[mlc] + (((dat[cbt] | (dat[cbt + 1] << 8) | (dat[cbt + 2] << 16)) >> (spos & 7)) & ((1 << mlb[mlc]) - 1));\n                cbt = (spos -= llb[llc]) >> 3;\n                var ll = llbl[llc] + (((dat[cbt] | (dat[cbt + 1] << 8) | (dat[cbt + 2] << 16)) >> (spos & 7)) & ((1 << llb[llc]) - 1));\n                cbt = (spos -= lbtr) >> 3;\n                lst = llt.t[lst] + (((dat[cbt] | (dat[cbt + 1] << 8)) >> (spos & 7)) & ((1 << lbtr) - 1));\n                cbt = (spos -= mbtr) >> 3;\n                mst = mlt.t[mst] + (((dat[cbt] | (dat[cbt + 1] << 8)) >> (spos & 7)) & ((1 << mbtr) - 1));\n                cbt = (spos -= obtr) >> 3;\n                ost = oct.t[ost] + (((dat[cbt] | (dat[cbt + 1] << 8)) >> (spos & 7)) & ((1 << obtr) - 1));\n                if (off > 3) {\n                    st.o[2] = st.o[1];\n                    st.o[1] = st.o[0];\n                    st.o[0] = off -= 3;\n                }\n                else {\n                    var idx = off - (ll != 0);\n                    if (idx) {\n                        off = idx == 3 ? st.o[0] - 1 : st.o[idx];\n                        if (idx > 1)\n                            st.o[2] = st.o[1];\n                        st.o[1] = st.o[0];\n                        st.o[0] = off;\n                    }\n                    else\n                        off = st.o[0];\n                }\n                for (var i = 0; i < ll; ++i) {\n                    buf[oubt + i] = buf[spl + i];\n                }\n                oubt += ll, spl += ll;\n                var stin = oubt - off;\n                if (stin < 0) {\n                    var len = -stin;\n                    var bs = st.e + stin;\n                    if (len > ml)\n                        len = ml;\n                    for (var i = 0; i < len; ++i) {\n                        buf[oubt + i] = st.w[bs + i];\n                    }\n                    oubt += len, ml -= len, stin = 0;\n                }\n                for (var i = 0; i < ml; ++i) {\n                    buf[oubt + i] = buf[stin + i];\n                }\n                oubt += ml;\n            }\n            if (oubt != spl) {\n                while (spl < buf.length) {\n                    buf[oubt++] = buf[spl++];\n                }\n            }\n            else\n                oubt = buf.length;\n            if (out)\n                st.y += oubt;\n            else\n                buf = slc(buf, 0, oubt);\n        }\n        else if (out) {\n            st.y += lss;\n            if (spl) {\n                for (var i = 0; i < lss; ++i) {\n                    buf[i] = buf[spl + i];\n                }\n            }\n        }\n        else if (spl)\n            buf = slc(buf, spl);\n        st.b = ebt;\n        return buf;\n    }\n    err(2);\n};\n// concat\nvar cct = function (bufs, ol) {\n    if (bufs.length == 1)\n        return bufs[0];\n    var buf = new u8(ol);\n    for (var i = 0, b = 0; i < bufs.length; ++i) {\n        var chk = bufs[i];\n        buf.set(chk, b);\n        b += chk.length;\n    }\n    return buf;\n};\n/**\n * Decompresses Zstandard data\n * @param dat The input data\n * @param buf The output buffer. If unspecified, the function will allocate\n *            exactly enough memory to fit the decompressed data. If your\n *            data has multiple frames and you know the output size, specifying\n *            it will yield better performance.\n * @returns The decompressed data\n */\nexport function decompress(dat, buf) {\n    var bufs = [], nb = +!buf;\n    var bt = 0, ol = 0;\n    for (; dat.length;) {\n        var st = rzfh(dat, nb || buf);\n        if (typeof st == 'object') {\n            if (nb) {\n                buf = null;\n                if (st.w.length == st.u) {\n                    bufs.push(buf = st.w);\n                    ol += st.u;\n                }\n            }\n            else {\n                bufs.push(buf);\n                st.e = 0;\n            }\n            for (; !st.l;) {\n                var blk = rzb(dat, st, buf);\n                if (!blk)\n                    err(5);\n                if (buf)\n                    st.e = st.y;\n                else {\n                    bufs.push(blk);\n                    ol += blk.length;\n                    cpw(st.w, 0, blk.length);\n                    st.w.set(blk, st.w.length - blk.length);\n                }\n            }\n            bt = st.b + (st.c * 4);\n        }\n        else\n            bt = st;\n        dat = dat.subarray(bt);\n    }\n    return cct(bufs, ol);\n}\n/**\n * Decompressor for Zstandard streamed data\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zstandard decompressor\n     * @param ondata The handler for stream data\n     */\n    function Decompress(ondata) {\n        this.ondata = ondata;\n        this.c = [];\n        this.l = 0;\n        this.z = 0;\n    }\n    /**\n     * Pushes data to be decompressed\n     * @param chunk The chunk of data to push\n     * @param final Whether or not this is the last chunk in the stream\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (typeof this.s == 'number') {\n            var sub = Math.min(chunk.length, this.s);\n            chunk = chunk.subarray(sub);\n            this.s -= sub;\n        }\n        var sl = chunk.length;\n        var ncs = sl + this.l;\n        if (!this.s) {\n            if (final) {\n                if (!ncs) {\n                    this.ondata(new u8(0), true);\n                    return;\n                }\n                // min for frame + one block\n                if (ncs < 5)\n                    err(5);\n            }\n            else if (ncs < 18) {\n                this.c.push(chunk);\n                this.l = ncs;\n                return;\n            }\n            if (this.l) {\n                this.c.push(chunk);\n                chunk = cct(this.c, ncs);\n                this.c = [];\n                this.l = 0;\n            }\n            if (typeof (this.s = rzfh(chunk)) == 'number')\n                return this.push(chunk, final);\n        }\n        if (typeof this.s != 'number') {\n            if (ncs < (this.z || 3)) {\n                if (final)\n                    err(5);\n                this.c.push(chunk);\n                this.l = ncs;\n                return;\n            }\n            if (this.l) {\n                this.c.push(chunk);\n                chunk = cct(this.c, ncs);\n                this.c = [];\n                this.l = 0;\n            }\n            if (!this.z && ncs < (this.z = (chunk[this.s.b] & 2) ? 4 : 3 + ((chunk[this.s.b] >> 3) | (chunk[this.s.b + 1] << 5) | (chunk[this.s.b + 2] << 13)))) {\n                if (final)\n                    err(5);\n                this.c.push(chunk);\n                this.l = ncs;\n                return;\n            }\n            else\n                this.z = 0;\n            for (;;) {\n                var blk = rzb(chunk, this.s);\n                if (!blk) {\n                    if (final)\n                        err(5);\n                    var adc = chunk.subarray(this.s.b);\n                    this.s.b = 0;\n                    this.c.push(adc), this.l += adc.length;\n                    return;\n                }\n                else {\n                    this.ondata(blk, false);\n                    cpw(this.s.w, 0, blk.length);\n                    this.s.w.set(blk, this.s.w.length - blk.length);\n                }\n                if (this.s.l) {\n                    var rest = chunk.subarray(this.s.b);\n                    this.s = this.s.c * 4;\n                    this.push(rest, final);\n                    return;\n                }\n            }\n        }\n        else if (final)\n            err(5);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n"], "mappings": ";;;AAEA,IAAI,KAAK;AAAT,IAAsB,KAAK;AAA3B,IAAuC,MAAM;AAA7C,IAA0D,MAAM;AAAhE,IAA+F,MAAM;AACrG,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG;AACzB,MAAI,GAAG,UAAU;AACb,WAAO,GAAG,UAAU,MAAM,KAAK,GAAG,GAAG,CAAC;AAC1C,MAAI,KAAK,QAAQ,IAAI;AACjB,QAAI;AACR,MAAI,KAAK,QAAQ,IAAI,EAAE;AACnB,QAAI,EAAE;AACV,MAAI,IAAI,IAAI,GAAG,IAAI,CAAC;AACpB,IAAE,IAAI,EAAE,SAAS,GAAG,CAAC,CAAC;AACtB,SAAO;AACX;AACA,IAAI,OAAO,SAAU,GAAG,GAAG,GAAG,GAAG;AAC7B,MAAI,GAAG,UAAU;AACb,WAAO,GAAG,UAAU,KAAK,KAAK,GAAG,GAAG,GAAG,CAAC;AAC5C,MAAI,KAAK,QAAQ,IAAI;AACjB,QAAI;AACR,MAAI,KAAK,QAAQ,IAAI,EAAE;AACnB,QAAI,EAAE;AACV,SAAO,IAAI,GAAG,EAAE;AACZ,MAAE,CAAC,IAAI;AACX,SAAO;AACX;AACA,IAAI,MAAM,SAAU,GAAG,GAAG,GAAG,GAAG;AAC5B,MAAI,GAAG,UAAU;AACb,WAAO,GAAG,UAAU,WAAW,KAAK,GAAG,GAAG,GAAG,CAAC;AAClD,MAAI,KAAK,QAAQ,IAAI;AACjB,QAAI;AACR,MAAI,KAAK,QAAQ,IAAI,EAAE;AACnB,QAAI,EAAE;AACV,SAAO,IAAI,GAAG;AACV,MAAE,GAAG,IAAI,EAAE,GAAG;AAAA,EAClB;AACJ;AAIO,IAAI,gBAAgB;AAAA,EACvB,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,eAAe;AACnB;AAEA,IAAI,KAAK;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAI,MAAM,SAAU,KAAK,KAAK,IAAI;AAC9B,MAAI,IAAI,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC;AAChC,IAAE,OAAO;AACT,MAAI,MAAM;AACN,UAAM,kBAAkB,GAAG,GAAG;AAClC,MAAI,CAAC;AACD,UAAM;AACV,SAAO;AACX;AACA,IAAI,KAAK,SAAU,GAAG,GAAG,GAAG;AACxB,MAAI,IAAI,GAAG,IAAI;AACf,SAAO,IAAI,GAAG,EAAE;AACZ,SAAK,EAAE,GAAG,MAAM,KAAK;AACzB,SAAO;AACX;AACA,IAAI,KAAK,SAAU,GAAG,GAAG;AAAE,UAAQ,EAAE,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,IAAM,EAAE,IAAI,CAAC,KAAK,KAAO,EAAE,IAAI,CAAC,KAAK,QAAS;AAAG;AAExG,IAAI,OAAO,SAAU,KAAK,GAAG;AACzB,MAAI,KAAK,IAAI,CAAC,IAAK,IAAI,CAAC,KAAK,IAAM,IAAI,CAAC,KAAK;AAC7C,MAAI,MAAM,WAAY,IAAI,CAAC,KAAK,KAAK;AAEjC,QAAI,MAAM,IAAI,CAAC;AAEf,QAAI,KAAM,OAAO,IAAK,GAAG,KAAM,OAAO,IAAK,GAAG,KAAK,MAAM,GAAG,MAAM,OAAO;AACzE,QAAI,MAAM;AACN,UAAI,CAAC;AAET,QAAI,KAAK,IAAI;AAEb,QAAI,KAAK,MAAM,IAAI,IAAI;AAEvB,QAAI,KAAK,GAAG,KAAK,IAAI,EAAE;AACvB,UAAM;AAEN,QAAI,MAAM,MAAO,KAAK,MAAO;AAE7B,QAAI,MAAM,GAAG,KAAK,IAAI,GAAG,KAAM,OAAO,KAAM;AAE5C,QAAI,KAAK;AACT,QAAI,CAAC,IAAI;AAEL,UAAI,KAAK,KAAM,MAAM,IAAI,CAAC,KAAK;AAC/B,WAAK,MAAM,MAAM,MAAM,IAAI,CAAC,IAAI;AAAA,IACpC;AACA,QAAI,KAAK;AACL,UAAI,CAAC;AACT,QAAI,MAAM,IAAI,IAAI,KAAK,IAAK,OAAO,KAAM,IAAI,IAAI,MAAM,EAAE;AACzD,QAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;AACjC,WAAO;AAAA,MACH,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAI,KAAK,KAAK,IAAK,IAAI,IAAI,SAAS,EAAE;AAAA,MACtC,GAAG;AAAA,MACH,GAAG,IAAI,IAAI,IAAI,QAAQ,GAAG,CAAC;AAAA,MAC3B,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG,KAAK,IAAI,QAAQ,EAAE;AAAA,IAC1B;AAAA,EACJ,YACW,MAAM,IAAM,IAAI,CAAC,KAAK,OAAQ,UAAW;AAEhD,WAAO,GAAG,KAAK,CAAC,IAAI;AAAA,EACxB;AACA,MAAI,CAAC;AACT;AAEA,IAAI,MAAM,SAAU,KAAK;AACrB,MAAI,OAAO;AACX,SAAQ,KAAK,QAAS,KAAK,EAAE;AACzB;AACJ,SAAO,OAAO;AAClB;AAEA,IAAI,OAAO,SAAU,KAAK,IAAI,KAAK;AAE/B,MAAI,QAAQ,MAAM,KAAK;AAEvB,MAAI,MAAM,IAAI,EAAE,IAAI,MAAM;AAC1B,MAAI,KAAK;AACL,QAAI,CAAC;AAET,MAAI,KAAK,KAAK;AAEd,MAAI,QAAQ,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK;AAEhD,MAAI,MAAM,IAAI,GAAG,OAAO,MAAM,EAAE;AAChC,MAAI,OAAO,IAAI,IAAI,KAAK,GAAG,GAAG;AAE9B,MAAI,SAAS,IAAI,IAAI,KAAK,GAAG,GAAG;AAChC,MAAI,SAAS,IAAI,IAAI,KAAK,KAAK,EAAE;AACjC,MAAI,MAAM,OAAO,MAAM;AACvB,MAAI,OAAO,IAAI,GAAG,KAAK,KAAK,EAAE;AAC9B,MAAI,QAAQ,IAAI,GAAG,KAAK,MAAM,EAAE;AAChC,SAAO,MAAM,OAAO,QAAQ,GAAG;AAC3B,QAAI,OAAO,IAAI,QAAQ,CAAC;AACxB,QAAI,MAAM,QAAQ;AAElB,QAAI,OAAO,KAAM,OAAO,KAAM;AAC9B,QAAI,OAAQ,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,IAAM,IAAI,MAAM,CAAC,KAAK,QAAS,OAAO,KAAM;AAEpF,QAAI,UAAU,KAAK,QAAQ;AAE3B,QAAI,MAAM,MAAM,QAAQ;AAExB,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO;AACP,cAAQ,MAAM,MAAM;AAAA,SACnB;AACD,cAAQ,OAAO;AACf,UAAI,MAAM;AACN,eAAO;AAAA,IACf;AACA,SAAK,EAAE,GAAG,IAAI,EAAE;AAChB,QAAI,OAAO,IAAI;AACX,eAAS;AACT,WAAK,EAAE,EAAE,IAAI;AAAA,IACjB;AAEI,eAAS;AACb,QAAI,CAAC,KAAK;AACN,SAAG;AAEC,YAAI,MAAM,QAAQ;AAClB,cAAO,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,KAAM;AACxD,gBAAQ;AACR,eAAO;AAAA,MACX,SAAS,MAAM;AAAA,IACnB;AAAA,EACJ;AACA,MAAI,MAAM,OAAO;AACb,QAAI,CAAC;AACT,MAAI,SAAS;AAEb,MAAI,SAAS,MAAM,MAAM,MAAM,KAAK;AAEpC,MAAI,QAAQ,KAAK;AACjB,WAAS,IAAI,GAAG,KAAK,KAAK,EAAE,GAAG;AAC3B,QAAI,KAAK,KAAK,CAAC;AACf,QAAI,KAAK,GAAG;AACR,aAAO,CAAC,IAAI,CAAC;AACb;AAAA,IACJ;AAEA,SAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACrB,WAAK,MAAM,IAAI;AACf,SAAG;AACC,iBAAU,SAAS,QAAS;AAAA,MAChC,SAAS,UAAU;AAAA,IACvB;AAAA,EACJ;AAEA,MAAI;AACA,QAAI,CAAC;AACT,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAErB,QAAI,KAAK,OAAO,KAAK,CAAC,CAAC;AAEvB,QAAI,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;AAC/B,WAAO,CAAC,KAAK,MAAM,MAAM;AAAA,EAC7B;AACA,SAAO,CAAE,OAAO,KAAM,GAAG;AAAA,IACjB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACT;AAEA,IAAI,MAAM,SAAU,KAAK,IAAI;AAEzB,MAAI,IAAI,GAAG,KAAK;AAEhB,MAAI,MAAM,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,EAAE;AAElC,MAAI,KAAK,IAAI,SAAS,GAAG,GAAG;AAE5B,MAAI,KAAK,IAAI,SAAS,KAAK,GAAG;AAE9B,MAAI,KAAK,IAAI,IAAI,IAAI,QAAQ,GAAG;AAEhC,MAAI,KAAK,KAAK;AAEV,QAAI,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AACtD,UAAM;AACN,QAAI,OAAO,OAAO;AAElB,QAAI,KAAK,IAAI,EAAE;AACf,QAAI,CAAC;AACD,UAAI,CAAC;AAET,QAAI,MAAM,GAAG,MAAM,GAAG,OAAO,IAAI,GAAG,OAAO;AAG3C,QAAI,QAAQ,EAAE,MAAM,KAAK,IAAI,IAAI,EAAE;AACnC,eAAS;AACL,cAAQ;AACR,UAAI,OAAO;AACP;AACJ,UAAI,MAAM,QAAQ;AAClB,cAAS,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,QAAQ;AACzE,SAAG,EAAE,EAAE,IAAI,IAAI,EAAE,GAAG;AACpB,cAAQ;AACR,UAAI,OAAO;AACP;AACJ,YAAM,QAAQ;AACd,cAAS,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,QAAQ;AACzE,SAAG,EAAE,EAAE,IAAI,IAAI,EAAE,GAAG;AACpB,aAAO,IAAI,EAAE,GAAG;AAChB,YAAM,IAAI,EAAE,GAAG;AACf,aAAO,IAAI,EAAE,GAAG;AAChB,YAAM,IAAI,EAAE,GAAG;AAAA,IACnB;AACA,QAAI,EAAE,KAAK;AACP,UAAI,CAAC;AAAA,EACb,OACK;AACD,SAAK,KAAK;AACV,WAAO,IAAI,IAAI,KAAK,GAAG;AACnB,UAAI,OAAO,IAAI,EAAE,EAAE;AACnB,SAAG,CAAC,IAAI,QAAQ;AAChB,SAAG,IAAI,CAAC,IAAI,OAAO;AAAA,IACvB;AACA,MAAE;AAAA,EACN;AAEA,MAAI,MAAM;AACV,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACrB,QAAI,KAAK,GAAG,CAAC;AAEb,QAAI,KAAK;AACL,UAAI,CAAC;AACT,WAAO,MAAO,KAAM,KAAK;AAAA,EAC7B;AAEA,MAAI,KAAK,IAAI,GAAG,IAAI;AAEpB,MAAI,KAAK,KAAK;AAEd,MAAI,MAAM,KAAK;AAEf,MAAI,MAAO,MAAM;AACb,QAAI,CAAC;AACT,KAAG,IAAI,IAAI,IAAI,GAAG,IAAI;AACtB,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACrB,QAAI,KAAK,GAAG,CAAC;AACb,MAAE,GAAG,GAAG,CAAC,IAAI,MAAO,KAAK,IAAI,EAAG;AAAA,EACpC;AAEA,MAAI,OAAO,IAAI,GAAG,MAAM,CAAC;AAEzB,MAAI,OAAO,KAAK,SAAS,GAAG,EAAE,GAAG,KAAK,KAAK,SAAS,EAAE;AACtD,KAAG,EAAE,IAAI;AACT,OAAK,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG;AACrB,QAAI,KAAK,GAAG,CAAC;AACb,SAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,KAAM,KAAK,EAAG;AAAA,EAC5D;AACA,MAAI,GAAG,CAAC,KAAK;AACT,QAAI,CAAC;AACT,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACrB,QAAI,OAAO,GAAG,CAAC;AACf,QAAI,MAAM;AACN,UAAI,OAAO,GAAG,IAAI;AAClB,WAAK,MAAM,GAAG,MAAM,GAAG,IAAI,IAAI,QAAQ,KAAM,KAAK,KAAM;AAAA,IAC5D;AAAA,EACJ;AACA,SAAO,CAAC,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACT;AAIA,IAAI,OAAqB,KAAmB,IAAI,GAAG;AAAA,EAC/C;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAK;AAAA,EAAK;AAAA,EAAI;AAAA,EAAK;AACnF,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;AAEX,IAAI,OAAqB,KAAmB,IAAI,GAAG;AAAA,EAC/C;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAA,EAAG;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAC9G,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;AAEX,IAAI,OAAsB,KAAmB,IAAI,GAAG;AAAA,EAChD;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AACtD,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;AAEX,IAAI,OAAO,SAAU,GAAG,GAAG;AACvB,MAAI,MAAM,EAAE,QAAQ,KAAK,IAAI,IAAI,GAAG;AACpC,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,OAAG,CAAC,IAAI;AACR,SAAK,KAAK,EAAE,CAAC;AAAA,EACjB;AACA,SAAO;AACX;AAEA,IAAI,MAAqB,IAAI,GAAmB,IAAI,IAAI;AAAA,EACpD;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAC1D,CAAC,EAAG,QAAQ,GAAG,EAAE;AAEjB,IAAI,OAAsB,KAAK,KAAK,CAAC;AAErC,IAAI,MAAqB,IAAI,GAAmB,IAAI,IAAI;AAAA,EACpD;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAG;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AACjF,CAAC,EAAG,QAAQ,GAAG,EAAE;AAEjB,IAAI,OAAsB,KAAK,KAAK,CAAC;AAErC,IAAI,MAAM,SAAU,KAAK,KAAK,IAAI;AAC9B,MAAI,MAAM,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,OAAO,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG;AAC1F,MAAI,CAAC;AACD,QAAI,CAAC;AACT,MAAI,KAAK,GAAG,MAAM,GAAG,GAAG,OAAO,OAAO,KAAK,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI;AAClE,SAAO,MAAM,MAAM,IAAI,MAAK;AACxB,QAAI,MAAM,OAAO;AACjB,QAAI,OAAO,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,IAAM,IAAI,MAAM,CAAC,KAAK,QAAS,MAAM;AAC5E,UAAO,MAAM,MAAO,OAAO;AAC3B,QAAI,EAAE,CAAC,IAAI,GAAG,EAAE,EAAE;AAClB,WAAQ,MAAM,GAAG,EAAE,EAAE;AAAA,EACzB;AACA,MAAI,OAAO,MAAM,IAAI,KAAK;AACtB,QAAI,CAAC;AACb;AAGA,IAAI,OAAO,SAAU,KAAK,KAAK,IAAI;AAC/B,MAAI,KAAK;AACT,MAAI,KAAK,IAAI,QAAQ,MAAO,KAAK,KAAM,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM;AACtE,MAAI,IAAI,SAAS,IAAI,MAAM,IAAI,CAAC,IAAK,IAAI,CAAC,KAAK,CAAE,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,EAAE;AAC5E,MAAI,IAAI,SAAS,IAAI,MAAM,IAAI,CAAC,IAAK,IAAI,CAAC,KAAK,CAAE,GAAG,IAAI,SAAS,KAAK,GAAG,GAAG,EAAE;AAC9E,MAAI,IAAI,SAAS,IAAI,MAAM,IAAI,CAAC,IAAK,IAAI,CAAC,KAAK,CAAE,GAAG,IAAI,SAAS,KAAK,GAAG,GAAG,EAAE;AAC9E,MAAI,IAAI,SAAS,EAAE,GAAG,IAAI,SAAS,GAAG,GAAG,EAAE;AAC/C;AAEA,IAAI,MAAM,SAAU,KAAK,IAAI,KAAK;AAC9B,MAAI;AACJ,MAAI,KAAK,GAAG;AAEZ,MAAI,KAAK,IAAI,EAAE,GAAG,QAAS,MAAM,IAAK;AACtC,KAAG,IAAI,KAAK;AACZ,MAAI,KAAM,MAAM,IAAM,IAAI,KAAK,CAAC,KAAK,IAAM,IAAI,KAAK,CAAC,KAAK;AAE1D,MAAI,OAAO,MAAM,KAAK;AACtB,MAAI,SAAS,GAAG;AACZ,QAAI,MAAM,IAAI;AACV;AACJ,OAAG,IAAI,KAAK;AACZ,QAAI,KAAK;AACL,WAAK,KAAK,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,KAAK,EAAE;AACnC,aAAO;AAAA,IACX;AACA,WAAO,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;AAAA,EACnC;AACA,MAAI,MAAM,IAAI;AACV;AACJ,MAAI,SAAS,GAAG;AACZ,OAAG,IAAI;AACP,QAAI,KAAK;AACL,UAAI,IAAI,IAAI,SAAS,IAAI,GAAG,GAAG,GAAG,CAAC;AACnC,SAAG,KAAK;AACR,aAAO;AAAA,IACX;AACA,WAAO,IAAI,KAAK,IAAI,GAAG;AAAA,EAC3B;AACA,MAAI,SAAS,GAAG;AAEZ,QAAI,KAAK,IAAI,EAAE,GAAG,MAAM,KAAK,GAAG,KAAM,MAAM,IAAK;AAEjD,QAAI,MAAM,MAAM,GAAG,MAAM,GAAG,KAAK;AACjC,QAAI,MAAM,GAAG;AACT,UAAI,KAAK;AACL,eAAQ,IAAI,EAAE,EAAE,KAAK,KAAO,KAAK,KAAO,IAAI,EAAE,EAAE,KAAK;AAAA;AAErD,cAAM,MAAM;AAAA,IACpB,OACK;AACD,WAAK;AACL,UAAI,KAAK;AACL,gBAAS,IAAI,EAAE,EAAE,IAAI,OAAO,GAAI,MAAO,IAAI,EAAE,KAAK,IAAM,IAAI,EAAE,EAAE,KAAK;AAAA,eAChE,MAAM;AACX,eAAQ,IAAI,EAAE,EAAE,KAAK,KAAO,IAAI,EAAE,EAAE,IAAI,MAAM,IAAK,MAAO,IAAI,EAAE,KAAK,IAAM,IAAI,EAAE,EAAE,KAAK;AAAA;AAExF,eAAQ,IAAI,EAAE,EAAE,KAAK,KAAO,IAAI,EAAE,EAAE,IAAI,OAAO,IAAK,MAAO,IAAI,EAAE,KAAK,IAAM,IAAI,EAAE,EAAE,KAAK,IAAM,IAAI,EAAE,EAAE,KAAK;AAAA,IACpH;AACA,MAAE;AAEF,QAAI,MAAM,MAAM,IAAI,SAAS,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC;AAE7D,QAAI,MAAM,IAAI,SAAS;AACvB,QAAI,OAAO;AACP,UAAI,IAAI,IAAI,SAAS,IAAI,MAAM,GAAG,GAAG,GAAG;AAAA,aACnC,OAAO;AACZ,WAAK,KAAK,IAAI,IAAI,GAAG,GAAG;AAAA,SACvB;AAED,UAAI,KAAK,GAAG;AACZ,UAAI,OAAO,GAAG;AACV,YAAI,MAAM,IAAI,KAAK,EAAE;AAErB,eAAO,MAAM,KAAK,IAAI,CAAC;AACvB,WAAG,IAAI,KAAK,IAAI,CAAC;AAAA,MACrB,WACS,CAAC;AACN,YAAI,CAAC;AACT,OAAC,KAAK,OAAO,KAAK,IAAI,SAAS,IAAI,MAAM,GAAG,GAAG,IAAI,SAAS,GAAG,GAAG,EAAE;AAAA,IACxE;AAEA,QAAI,KAAK,IAAI,IAAI;AACjB,QAAI,IAAI;AACJ,UAAI,MAAM;AACN,cAAM,IAAI,IAAI,IAAK,IAAI,IAAI,KAAK,KAAM;AAAA,eACjC,KAAK;AACV,aAAO,KAAK,OAAQ,IAAK,IAAI,IAAI;AAErC,UAAI,MAAM,IAAI,IAAI;AAClB,UAAI,MAAM;AACN,YAAI,CAAC;AACT,UAAI,MAAM,CAAC,MAAM,MAAM,IAAI;AAC3B,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,YAAI,KAAM,QAAS,KAAK,KAAK,IAAM;AACnC,YAAI,MAAM,GAAG;AAET,cAAI,OAAO,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AACnC,cAAI,CAAC,IAAI;AAAA,YACL,GAAG,KAAK,SAAS,GAAG,CAAC;AAAA,YACrB,GAAG,KAAK,SAAS,GAAG,CAAC;AAAA,YACrB,GAAG,IAAI,IAAI,KAAK,QAAQ,GAAG,CAAC;AAAA,YAC5B,GAAG;AAAA,UACP;AAAA,QACJ,WACS,MAAM,GAAG;AAEd,eAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,QAC9D,WACS,MAAM,GAAG;AACd,cAAI,CAAC,GAAG;AACJ,gBAAI,CAAC;AACT,cAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AAAA,QACnB;AAAA,MACJ;AACA,UAAI,KAAK,GAAG,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;AACzD,UAAI,KAAK,IAAI,MAAM,CAAC;AACpB,UAAI,CAAC;AACD,YAAI,CAAC;AACT,UAAI,QAAQ,OAAO,KAAK,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,MAAM,QAAQ,GAAG,OAAO;AACrE,UAAI,OAAQ,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,IAAI,KAAK;AAC7E,aAAO,QAAQ,IAAI,MAAM;AACzB,UAAI,OAAQ,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,IAAI,KAAK;AAC7E,aAAO,QAAQ,IAAI,MAAM;AACzB,UAAI,OAAQ,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,IAAI,KAAK;AAC7E,WAAK,EAAE,IAAI,EAAE,MAAK;AACd,YAAI,MAAM,IAAI,EAAE,GAAG;AACnB,YAAI,OAAO,IAAI,EAAE,GAAG;AACpB,YAAI,MAAM,IAAI,EAAE,GAAG;AACnB,YAAI,OAAO,IAAI,EAAE,GAAG;AACpB,YAAI,MAAM,IAAI,EAAE,GAAG;AACnB,YAAI,OAAO,IAAI,EAAE,GAAG;AACpB,eAAO,QAAQ,QAAQ;AACvB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,QAAS,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,IAAM,IAAI,MAAM,CAAC,KAAK,KAAO,IAAI,MAAM,CAAC,KAAK,SAAU,OAAO,KAAO,MAAM;AAC1H,eAAO,QAAQ,IAAI,GAAG,MAAM;AAC5B,YAAI,KAAK,KAAK,GAAG,MAAO,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,IAAM,IAAI,MAAM,CAAC,KAAK,QAAS,OAAO,MAAQ,KAAK,IAAI,GAAG,KAAK;AACnH,eAAO,QAAQ,IAAI,GAAG,MAAM;AAC5B,YAAI,KAAK,KAAK,GAAG,MAAO,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,IAAM,IAAI,MAAM,CAAC,KAAK,QAAS,OAAO,MAAQ,KAAK,IAAI,GAAG,KAAK;AACnH,eAAO,QAAQ,SAAS;AACxB,cAAM,IAAI,EAAE,GAAG,MAAO,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,QAAQ;AACtF,eAAO,QAAQ,SAAS;AACxB,cAAM,IAAI,EAAE,GAAG,MAAO,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,QAAQ;AACtF,eAAO,QAAQ,SAAS;AACxB,cAAM,IAAI,EAAE,GAAG,MAAO,IAAI,GAAG,IAAK,IAAI,MAAM,CAAC,KAAK,OAAQ,OAAO,MAAQ,KAAK,QAAQ;AACtF,YAAI,MAAM,GAAG;AACT,aAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC;AAChB,aAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC;AAChB,aAAG,EAAE,CAAC,IAAI,OAAO;AAAA,QACrB,OACK;AACD,cAAI,MAAM,OAAO,MAAM;AACvB,cAAI,KAAK;AACL,kBAAM,OAAO,IAAI,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG;AACvC,gBAAI,MAAM;AACN,iBAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC;AACpB,eAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC;AAChB,eAAG,EAAE,CAAC,IAAI;AAAA,UACd;AAEI,kBAAM,GAAG,EAAE,CAAC;AAAA,QACpB;AACA,iBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,cAAI,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC;AAAA,QAC/B;AACA,gBAAQ,IAAI,OAAO;AACnB,YAAI,OAAO,OAAO;AAClB,YAAI,OAAO,GAAG;AACV,cAAI,MAAM,CAAC;AACX,cAAI,KAAK,GAAG,IAAI;AAChB,cAAI,MAAM;AACN,kBAAM;AACV,mBAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,gBAAI,OAAO,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC;AAAA,UAC/B;AACA,kBAAQ,KAAK,MAAM,KAAK,OAAO;AAAA,QACnC;AACA,iBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,cAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC;AAAA,QAChC;AACA,gBAAQ;AAAA,MACZ;AACA,UAAI,QAAQ,KAAK;AACb,eAAO,MAAM,IAAI,QAAQ;AACrB,cAAI,MAAM,IAAI,IAAI,KAAK;AAAA,QAC3B;AAAA,MACJ;AAEI,eAAO,IAAI;AACf,UAAI;AACA,WAAG,KAAK;AAAA;AAER,cAAM,IAAI,KAAK,GAAG,IAAI;AAAA,IAC9B,WACS,KAAK;AACV,SAAG,KAAK;AACR,UAAI,KAAK;AACL,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,cAAI,CAAC,IAAI,IAAI,MAAM,CAAC;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ,WACS;AACL,YAAM,IAAI,KAAK,GAAG;AACtB,OAAG,IAAI;AACP,WAAO;AAAA,EACX;AACA,MAAI,CAAC;AACT;AAEA,IAAI,MAAM,SAAU,MAAM,IAAI;AAC1B,MAAI,KAAK,UAAU;AACf,WAAO,KAAK,CAAC;AACjB,MAAI,MAAM,IAAI,GAAG,EAAE;AACnB,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACzC,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,IAAI,KAAK,CAAC;AACd,SAAK,IAAI;AAAA,EACb;AACA,SAAO;AACX;AAUO,SAAS,WAAW,KAAK,KAAK;AACjC,MAAI,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;AACtB,MAAI,KAAK,GAAG,KAAK;AACjB,SAAO,IAAI,UAAS;AAChB,QAAI,KAAK,KAAK,KAAK,MAAM,GAAG;AAC5B,QAAI,OAAO,MAAM,UAAU;AACvB,UAAI,IAAI;AACJ,cAAM;AACN,YAAI,GAAG,EAAE,UAAU,GAAG,GAAG;AACrB,eAAK,KAAK,MAAM,GAAG,CAAC;AACpB,gBAAM,GAAG;AAAA,QACb;AAAA,MACJ,OACK;AACD,aAAK,KAAK,GAAG;AACb,WAAG,IAAI;AAAA,MACX;AACA,aAAO,CAAC,GAAG,KAAI;AACX,YAAI,MAAM,IAAI,KAAK,IAAI,GAAG;AAC1B,YAAI,CAAC;AACD,cAAI,CAAC;AACT,YAAI;AACA,aAAG,IAAI,GAAG;AAAA,aACT;AACD,eAAK,KAAK,GAAG;AACb,gBAAM,IAAI;AACV,cAAI,GAAG,GAAG,GAAG,IAAI,MAAM;AACvB,aAAG,EAAE,IAAI,KAAK,GAAG,EAAE,SAAS,IAAI,MAAM;AAAA,QAC1C;AAAA,MACJ;AACA,WAAK,GAAG,IAAK,GAAG,IAAI;AAAA,IACxB;AAEI,WAAK;AACT,UAAM,IAAI,SAAS,EAAE;AAAA,EACzB;AACA,SAAO,IAAI,MAAM,EAAE;AACvB;AAIA,IAAI,aAA4B,WAAY;AAKxC,WAASA,YAAW,QAAQ;AACxB,SAAK,SAAS;AACd,SAAK,IAAI,CAAC;AACV,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACb;AAMA,EAAAA,YAAW,UAAU,OAAO,SAAU,OAAO,OAAO;AAChD,QAAI,OAAO,KAAK,KAAK,UAAU;AAC3B,UAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,KAAK,CAAC;AACvC,cAAQ,MAAM,SAAS,GAAG;AAC1B,WAAK,KAAK;AAAA,IACd;AACA,QAAI,KAAK,MAAM;AACf,QAAI,MAAM,KAAK,KAAK;AACpB,QAAI,CAAC,KAAK,GAAG;AACT,UAAI,OAAO;AACP,YAAI,CAAC,KAAK;AACN,eAAK,OAAO,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3B;AAAA,QACJ;AAEA,YAAI,MAAM;AACN,cAAI,CAAC;AAAA,MACb,WACS,MAAM,IAAI;AACf,aAAK,EAAE,KAAK,KAAK;AACjB,aAAK,IAAI;AACT;AAAA,MACJ;AACA,UAAI,KAAK,GAAG;AACR,aAAK,EAAE,KAAK,KAAK;AACjB,gBAAQ,IAAI,KAAK,GAAG,GAAG;AACvB,aAAK,IAAI,CAAC;AACV,aAAK,IAAI;AAAA,MACb;AACA,UAAI,QAAQ,KAAK,IAAI,KAAK,KAAK,MAAM;AACjC,eAAO,KAAK,KAAK,OAAO,KAAK;AAAA,IACrC;AACA,QAAI,OAAO,KAAK,KAAK,UAAU;AAC3B,UAAI,OAAO,KAAK,KAAK,IAAI;AACrB,YAAI;AACA,cAAI,CAAC;AACT,aAAK,EAAE,KAAK,KAAK;AACjB,aAAK,IAAI;AACT;AAAA,MACJ;AACA,UAAI,KAAK,GAAG;AACR,aAAK,EAAE,KAAK,KAAK;AACjB,gBAAQ,IAAI,KAAK,GAAG,GAAG;AACvB,aAAK,IAAI,CAAC;AACV,aAAK,IAAI;AAAA,MACb;AACA,UAAI,CAAC,KAAK,KAAK,OAAO,KAAK,IAAK,MAAM,KAAK,EAAE,CAAC,IAAI,IAAK,IAAI,KAAM,MAAM,KAAK,EAAE,CAAC,KAAK,IAAM,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK,IAAM,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK,MAAO;AACjJ,YAAI;AACA,cAAI,CAAC;AACT,aAAK,EAAE,KAAK,KAAK;AACjB,aAAK,IAAI;AACT;AAAA,MACJ;AAEI,aAAK,IAAI;AACb,iBAAS;AACL,YAAI,MAAM,IAAI,OAAO,KAAK,CAAC;AAC3B,YAAI,CAAC,KAAK;AACN,cAAI;AACA,gBAAI,CAAC;AACT,cAAI,MAAM,MAAM,SAAS,KAAK,EAAE,CAAC;AACjC,eAAK,EAAE,IAAI;AACX,eAAK,EAAE,KAAK,GAAG,GAAG,KAAK,KAAK,IAAI;AAChC;AAAA,QACJ,OACK;AACD,eAAK,OAAO,KAAK,KAAK;AACtB,cAAI,KAAK,EAAE,GAAG,GAAG,IAAI,MAAM;AAC3B,eAAK,EAAE,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE,SAAS,IAAI,MAAM;AAAA,QAClD;AACA,YAAI,KAAK,EAAE,GAAG;AACV,cAAI,OAAO,MAAM,SAAS,KAAK,EAAE,CAAC;AAClC,eAAK,IAAI,KAAK,EAAE,IAAI;AACpB,eAAK,KAAK,MAAM,KAAK;AACrB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,WACS;AACL,UAAI,CAAC;AAAA,EACb;AACA,SAAOA;AACX,EAAE;", "names": ["Decompress"]}