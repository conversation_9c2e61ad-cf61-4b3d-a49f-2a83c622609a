import {
    ICancelAndHoldAutomationEvent,
    ICancelScheduledValuesAutomationEvent,
    IExponentialRampToValueAutomationEvent,
    ILinearRampToValueAutomationEvent,
    ISetTargetAutomationEvent,
    ISetValueAutomationEvent,
    ISetValueCurveAutomationEvent
} from '../interfaces';

export type TAutomationEvent =
    | ICancelAndHoldAutomationEvent
    | ICancelScheduledValuesAutomationEvent
    | IExponentialRampToValueAutomationEvent
    | ILinearRampToValueAutomationEvent
    | ISetTargetAutomationEvent
    | ISetValueAutomationEvent
    | ISetValueCurveAutomationEvent;
