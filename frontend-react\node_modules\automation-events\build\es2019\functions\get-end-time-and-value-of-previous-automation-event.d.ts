import { IExtendedExponentialRampToValueAutomationEvent, IExtendedLinearRampToValueAutomationEvent } from '../interfaces';
import { TPersistentAutomationEvent } from '../types';
export declare const getEndTimeAndValueOfPreviousAutomationEvent: (automationEvents: TPersistentAutomationEvent[], index: number, currentAutomationEvent: TPersistentAutomationEvent, nextAutomationEvent: IExtendedExponentialRampToValueAutomationEvent | IExtendedLinearRampToValueAutomationEvent, defaultValue: number) => [number, number];
//# sourceMappingURL=get-end-time-and-value-of-previous-automation-event.d.ts.map