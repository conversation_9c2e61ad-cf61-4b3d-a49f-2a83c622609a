import { TChannelMergerNodeRendererFactory } from './channel-merger-node-renderer-factory';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeChannelMergerNodeFactory } from './native-channel-merger-node-factory';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';

export type TChannelMergerNodeRendererFactoryFactory = (
    createNativeChannelMergerNode: TNativeChannelMergerNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction
) => TChannelMergerNodeRendererFactory;
