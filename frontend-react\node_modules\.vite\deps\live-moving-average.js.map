{"version": 3, "sources": ["../../live-moving-average/index.js"], "sourcesContent": ["'use strict'\n\nconst LiveMovingAverage = {\n\tpush: function (val) {\n\t\tif ('number' !== typeof val) throw new Error('val must be a number.')\n\n\t\tthis.sum -= this.data[this.dataI]\n\t\tthis.sum += val\n\n\t\tthis.data[this.dataI] = val\n\t\tthis.dataI = (this.dataI + 1) % this.size\n\n\t\treturn this\n\t},\n\n\tget: function () {\n\t\treturn this.sum / this.size\n\t}\n}\n\nconst createWindow = (size, fill = 0) => {\n    if ('number' !== typeof size) throw new Error('size must be a number.')\n    if ('number' !== typeof fill) throw new Error('fill must be a number.')\n\n\tconst w = Object.create(LiveMovingAverage)\n\n\tw.sum = size * fill\n\tw.size = size\n\tw.data = new Array(size)\n\tw.data.fill(fill)\n\tw.dataI = 0\n\n\treturn w\n}\n\nmodule.exports = createWindow\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,QAAM,oBAAoB;AAAA,MACzB,MAAM,SAAU,KAAK;AACpB,YAAI,aAAa,OAAO,IAAK,OAAM,IAAI,MAAM,uBAAuB;AAEpE,aAAK,OAAO,KAAK,KAAK,KAAK,KAAK;AAChC,aAAK,OAAO;AAEZ,aAAK,KAAK,KAAK,KAAK,IAAI;AACxB,aAAK,SAAS,KAAK,QAAQ,KAAK,KAAK;AAErC,eAAO;AAAA,MACR;AAAA,MAEA,KAAK,WAAY;AAChB,eAAO,KAAK,MAAM,KAAK;AAAA,MACxB;AAAA,IACD;AAEA,QAAM,eAAe,CAAC,MAAM,OAAO,MAAM;AACrC,UAAI,aAAa,OAAO,KAAM,OAAM,IAAI,MAAM,wBAAwB;AACtE,UAAI,aAAa,OAAO,KAAM,OAAM,IAAI,MAAM,wBAAwB;AAEzE,YAAM,IAAI,OAAO,OAAO,iBAAiB;AAEzC,QAAE,MAAM,OAAO;AACf,QAAE,OAAO;AACT,QAAE,OAAO,IAAI,MAAM,IAAI;AACvB,QAAE,KAAK,KAAK,IAAI;AAChB,QAAE,QAAQ;AAEV,aAAO;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}