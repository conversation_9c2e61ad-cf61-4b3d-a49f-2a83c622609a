import { TActiveAudioWorkletNodeInputsStore } from './active-audio-worklet-node-inputs-store';
import { TGetActiveAudioWorkletNodeInputsFunction } from './get-active-audio-worklet-node-inputs-function';
import { TGetValueForKeyFunction } from './get-value-for-key-function';

export type TGetActiveAudioWorkletNodeInputsFactory = (
    activeAudioWorkletNodeInputsStore: TActiveAudioWorkletNodeInputsStore,
    getValueForKey: TGetValueForKeyFunction
) => TGetActiveAudioWorkletNodeInputsFunction;
