import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeWaveShaperNodeFactory } from './native-wave-shaper-node-factory';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';
import { TWaveShaperNodeRendererFactory } from './wave-shaper-node-renderer-factory';

export type TWaveShaperNodeRendererFactoryFactory = (
    createNativeWaveShaperNode: TNativeWaveShaperNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction
) => TWaveShaperNodeRendererFactory;
