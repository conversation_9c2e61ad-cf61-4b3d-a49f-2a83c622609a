import { TConnectAudioParamFunction } from './connect-audio-param-function';
import { TConstantSourceNodeRendererFactory } from './constant-source-node-renderer-factory';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeConstantSourceNodeFactory } from './native-constant-source-node-factory';
import { TRenderAutomationFunction } from './render-automation-function';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';

export type TConstantSourceNodeRendererFactoryFactory = (
    connectAudioParam: TConnectAudioParamFunction,
    createNativeConstantSourceNode: TNativeConstantSourceNodeFactory,
    getNativeAudioNode: TGetNativeAudioNodeFunction,
    renderAutomation: TRenderAutomationFunction,
    renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction
) => TConstantSourceNodeRendererFactory;
